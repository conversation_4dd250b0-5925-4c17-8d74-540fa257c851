import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing official agency news and announcements
class AgencyNewsModel extends BaseModel {
  final String title;
  final String content;
  final String agency;
  final String category;
  final String source;
  final String? imageUrl;
  final String? documentUrl;
  final DateTime publishedDate;
  final DateTime? applicationDeadline;
  final String priority; // 'high', 'medium', 'low'
  final List<String> tags;
  final bool isPinned;
  final bool isBreaking;
  final bool isPremium;
  final int viewsCount;

  const AgencyNewsModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.title,
    required this.content,
    required this.agency,
    required this.category,
    required this.source,
    this.imageUrl,
    this.documentUrl,
    required this.publishedDate,
    this.applicationDeadline,
    required this.priority,
    required this.tags,
    this.isPinned = false,
    this.isBreaking = false,
    this.isPremium = false,
    this.viewsCount = 0,
  });

  @override
  List<Object?> get props => [
    ...super.props,
    title,
    content,
    agency,
    category,
    source,
    imageUrl,
    documentUrl,
    publishedDate,
    applicationDeadline,
    priority,
    tags,
    isPinned,
    isBreaking,
    isPremium,
    viewsCount,
  ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'title': title,
      'content': content,
      'agency': agency,
      'category': category,
      'source': source,
      'imageUrl': imageUrl,
      'documentUrl': documentUrl,
      'publishedDate': publishedDate.toIso8601String(),
      'applicationDeadline': applicationDeadline?.toIso8601String(),
      'priority': priority,
      'tags': tags,
      'isPinned': isPinned,
      'isBreaking': isBreaking,
      'viewsCount': viewsCount,
    };
  }

  factory AgencyNewsModel.fromJson(Map<String, dynamic> json) {
    return AgencyNewsModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      title: json['title'] as String,
      content: json['content'] as String,
      agency: json['agency'] as String,
      category: json['category'] as String,
      source: json['source'] as String,
      imageUrl: json['imageUrl'] as String?,
      documentUrl: json['documentUrl'] as String?,
      publishedDate: DateTime.parse(json['publishedDate'] as String),
      applicationDeadline:
          json['applicationDeadline'] != null
              ? DateTime.parse(json['applicationDeadline'] as String)
              : null,
      priority: json['priority'] as String,
      tags: List<String>.from(json['tags'] as List),
      isPinned: json['isPinned'] as bool? ?? false,
      isBreaking: json['isBreaking'] as bool? ?? false,
      viewsCount: json['viewsCount'] as int? ?? 0,
    );
  }

  @override
  AgencyNewsModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? title,
    String? content,
    String? agency,
    String? category,
    String? source,
    String? imageUrl,
    String? documentUrl,
    DateTime? publishedDate,
    DateTime? applicationDeadline,
    String? priority,
    List<String>? tags,
    bool? isPinned,
    bool? isBreaking,
    int? viewsCount,
  }) {
    return AgencyNewsModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      title: title ?? this.title,
      content: content ?? this.content,
      agency: agency ?? this.agency,
      category: category ?? this.category,
      source: source ?? this.source,
      imageUrl: imageUrl ?? this.imageUrl,
      documentUrl: documentUrl ?? this.documentUrl,
      publishedDate: publishedDate ?? this.publishedDate,
      applicationDeadline: applicationDeadline ?? this.applicationDeadline,
      priority: priority ?? this.priority,
      tags: tags ?? this.tags,
      isPinned: isPinned ?? this.isPinned,
      isBreaking: isBreaking ?? this.isBreaking,
      viewsCount: viewsCount ?? this.viewsCount,
    );
  }

  /// Get formatted time since publication
  String get timeSincePublished {
    final now = DateTime.now();
    final difference = now.difference(publishedDate);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Check if there's an upcoming deadline
  bool get hasUpcomingDeadline {
    if (applicationDeadline == null) return false;
    final now = DateTime.now();
    final daysUntilDeadline = applicationDeadline!.difference(now).inDays;
    return daysUntilDeadline <= 7 && daysUntilDeadline >= 0;
  }

  /// Get deadline urgency level
  String get deadlineUrgency {
    if (applicationDeadline == null) return 'none';
    final now = DateTime.now();
    final daysUntilDeadline = applicationDeadline!.difference(now).inDays;

    if (daysUntilDeadline < 0) return 'expired';
    if (daysUntilDeadline <= 1) return 'critical';
    if (daysUntilDeadline <= 3) return 'urgent';
    if (daysUntilDeadline <= 7) return 'important';
    return 'normal';
  }
}
