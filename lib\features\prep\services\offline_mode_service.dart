import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/quiz_question_model.dart';
import '../models/quiz_model.dart';

/// Service to manage offline mode and cached content
class OfflineModeService {
  static const String _cachedQuizzesKey = 'cached_quizzes';
  static const String _cachedQuestionsKey = 'cached_questions';
  static const String _lastSyncKey = 'last_sync_timestamp';
  static const String _offlineResultsKey = 'offline_results';

  static final OfflineModeService _instance = OfflineModeService._internal();
  factory OfflineModeService() => _instance;
  OfflineModeService._internal();

  final List<VoidCallback> _listeners = [];
  final Connectivity _connectivity = Connectivity();

  bool _isOnline = true;
  bool _isInitialized = false;
  DateTime? _lastSyncTime;
  Map<String, QuizModel> _cachedQuizzes = {};
  Map<String, QuizQuestionModel> _cachedQuestions = {};
  List<Map<String, dynamic>> _offlineResults = [];

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Check initial connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      _isOnline = connectivityResult != ConnectivityResult.none;

      // Listen for connectivity changes
      _connectivity.onConnectivityChanged.listen((
        List<ConnectivityResult> results,
      ) {
        _onConnectivityChanged(
          results.isNotEmpty ? results.first : ConnectivityResult.none,
        );
      });

      // Load cached data
      await _loadCachedData();

      _isInitialized = true;
      debugPrint('✅ Offline mode service initialized - Online: $_isOnline');
    } catch (e) {
      debugPrint('❌ Error initializing offline mode service: $e');
    }
  }

  /// Check if device is currently online
  bool get isOnline => _isOnline;

  /// Check if device is currently offline
  bool get isOffline => !_isOnline;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Check if cached content is available
  bool get hasCachedContent =>
      _cachedQuizzes.isNotEmpty || _cachedQuestions.isNotEmpty;

  /// Get count of cached quizzes
  int get cachedQuizzesCount => _cachedQuizzes.length;

  /// Get count of cached questions
  int get cachedQuestionsCount => _cachedQuestions.length;

  /// Check if specific quiz is cached
  bool isQuizCached(String quizId) => _cachedQuizzes.containsKey(quizId);

  /// Check if specific question is cached
  bool isQuestionCached(String questionId) =>
      _cachedQuestions.containsKey(questionId);

  /// Cache a quiz for offline use
  Future<void> cacheQuiz(QuizModel quiz) async {
    try {
      _cachedQuizzes[quiz.id] = quiz;
      await _saveCachedData();
      _notifyListeners();
      debugPrint('✅ Quiz cached: ${quiz.title}');
    } catch (e) {
      debugPrint('❌ Error caching quiz: $e');
    }
  }

  /// Cache questions for offline use
  Future<void> cacheQuestions(List<QuizQuestionModel> questions) async {
    try {
      for (final question in questions) {
        _cachedQuestions[question.id] = question;
      }
      await _saveCachedData();
      _notifyListeners();
      debugPrint('✅ ${questions.length} questions cached');
    } catch (e) {
      debugPrint('❌ Error caching questions: $e');
    }
  }

  /// Get cached quiz by ID
  QuizModel? getCachedQuiz(String quizId) {
    return _cachedQuizzes[quizId];
  }

  /// Get cached question by ID
  QuizQuestionModel? getCachedQuestion(String questionId) {
    return _cachedQuestions[questionId];
  }

  /// Get all cached quizzes
  List<QuizModel> getAllCachedQuizzes() {
    return _cachedQuizzes.values.toList();
  }

  /// Get all cached questions
  List<QuizQuestionModel> getAllCachedQuestions() {
    return _cachedQuestions.values.toList();
  }

  /// Store quiz result for offline sync
  Future<void> storeOfflineResult(QuizSessionModel result) async {
    try {
      _offlineResults.add(result.toMap());
      await _saveOfflineResults();
      debugPrint('✅ Quiz result stored for offline sync');
    } catch (e) {
      debugPrint('❌ Error storing offline result: $e');
    }
  }

  /// Get pending offline results
  List<Map<String, dynamic>> getPendingOfflineResults() {
    return List.from(_offlineResults);
  }

  /// Clear synced offline results
  Future<void> clearSyncedResults() async {
    _offlineResults.clear();
    await _saveOfflineResults();
  }

  /// Update sync timestamp
  Future<void> updateSyncTime() async {
    _lastSyncTime = DateTime.now();
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastSyncKey, _lastSyncTime!.toIso8601String());
    _notifyListeners();
  }

  /// Clear all cached content
  Future<void> clearCache() async {
    _cachedQuizzes.clear();
    _cachedQuestions.clear();
    _offlineResults.clear();

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_cachedQuizzesKey);
    await prefs.remove(_cachedQuestionsKey);
    await prefs.remove(_offlineResultsKey);

    _notifyListeners();
    debugPrint('✅ Cache cleared');
  }

  /// Get cache size in MB (approximate)
  Future<double> getCacheSizeMB() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesData = prefs.getString(_cachedQuizzesKey) ?? '';
      final questionsData = prefs.getString(_cachedQuestionsKey) ?? '';
      final resultsData = prefs.getString(_offlineResultsKey) ?? '';

      final totalBytes =
          quizzesData.length + questionsData.length + resultsData.length;
      return totalBytes / (1024 * 1024); // Convert to MB
    } catch (e) {
      debugPrint('❌ Error calculating cache size: $e');
      return 0.0;
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    if (wasOnline != _isOnline) {
      debugPrint('📶 Connectivity changed - Online: $_isOnline');
      _notifyListeners();

      // If we just came online, trigger sync
      if (_isOnline && _offlineResults.isNotEmpty) {
        _triggerSync();
      }
    }
  }

  /// Trigger sync when coming back online
  void _triggerSync() {
    // This would typically call a sync service
    debugPrint(
      '🔄 Triggering sync of ${_offlineResults.length} offline results',
    );
  }

  /// Load cached data from storage
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load cached quizzes
      final quizzesJson = prefs.getString(_cachedQuizzesKey);
      if (quizzesJson != null) {
        final Map<String, dynamic> quizzesMap = jsonDecode(quizzesJson);
        _cachedQuizzes = quizzesMap.map(
          (key, value) => MapEntry(key, QuizModel.fromMap(value)),
        );
      }

      // Load cached questions
      final questionsJson = prefs.getString(_cachedQuestionsKey);
      if (questionsJson != null) {
        final Map<String, dynamic> questionsMap = jsonDecode(questionsJson);
        _cachedQuestions = questionsMap.map(
          (key, value) => MapEntry(key, QuizQuestionModel.fromMap(value)),
        );
      }

      // Load offline results
      final resultsJson = prefs.getString(_offlineResultsKey);
      if (resultsJson != null) {
        _offlineResults = List<Map<String, dynamic>>.from(
          jsonDecode(resultsJson),
        );
      }

      // Load last sync time
      final lastSyncString = prefs.getString(_lastSyncKey);
      if (lastSyncString != null) {
        _lastSyncTime = DateTime.parse(lastSyncString);
      }

      debugPrint(
        '✅ Cached data loaded - Quizzes: ${_cachedQuizzes.length}, Questions: ${_cachedQuestions.length}',
      );
    } catch (e) {
      debugPrint('❌ Error loading cached data: $e');
    }
  }

  /// Save cached data to storage
  Future<void> _saveCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save cached quizzes
      final quizzesMap = _cachedQuizzes.map(
        (key, value) => MapEntry(key, value.toMap()),
      );
      await prefs.setString(_cachedQuizzesKey, jsonEncode(quizzesMap));

      // Save cached questions
      final questionsMap = _cachedQuestions.map(
        (key, value) => MapEntry(key, value.toMap()),
      );
      await prefs.setString(_cachedQuestionsKey, jsonEncode(questionsMap));
    } catch (e) {
      debugPrint('❌ Error saving cached data: $e');
    }
  }

  /// Save offline results to storage
  Future<void> _saveOfflineResults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_offlineResultsKey, jsonEncode(_offlineResults));
    } catch (e) {
      debugPrint('❌ Error saving offline results: $e');
    }
  }

  /// Add listener for offline mode changes
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// Remove listener
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// Notify all listeners of changes
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// Dispose resources
  void dispose() {
    _listeners.clear();
  }
}
