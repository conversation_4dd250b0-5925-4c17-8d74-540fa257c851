import '../models/quiz_question_model.dart';
import 'comprehensive_agency_quiz_database.dart';

/// Comprehensive quiz questions database for Nigerian military recruitment preparation
class QuizQuestionsDatabase {
  /// English Language Questions (JSS2-SS3 Level)
  static List<QuizQuestionModel> get englishQuestions => [
    // Comprehension Questions
    QuizQuestionModel(
      id: 'eng_001',
      question:
          'Read the passage: "The Nigerian military has played a crucial role in maintaining peace and security in West Africa." What is the main idea of this sentence?',
      options: [
        'Nigerian military is weak',
        'West Africa has no peace',
        'Nigerian military contributes to regional stability',
        'Military is not important',
      ],
      correctAnswerIndex: 2,
      category: 'English Language',
      subcategory: 'Comprehension',
      difficulty: 'intermediate',
      explanation:
          'The sentence emphasizes the positive role of the Nigerian military in regional peace and security.',
    ),

    QuizQuestionModel(
      id: 'eng_002',
      question:
          'Choose the correct meaning of "discipline" in military context:',
      options: [
        'Punishment only',
        'Orderly conduct and training',
        'Fighting skills',
        'Physical exercise',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'beginner',
      explanation:
          'In military context, discipline refers to orderly conduct, training, and adherence to rules.',
    ),

    QuizQuestionModel(
      id: 'eng_003',
      question:
          'Identify the grammatical error: "The soldiers was marching in formation."',
      options: [
        'No error',
        'Subject-verb disagreement',
        'Wrong tense',
        'Missing article',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'beginner',
      explanation:
          '"Soldiers" is plural, so the verb should be "were" not "was".',
    ),

    QuizQuestionModel(
      id: 'eng_004',
      question: 'What is the synonym of "courage" in military service?',
      options: ['Fear', 'Bravery', 'Weakness', 'Confusion'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'beginner',
      explanation:
          'Courage and bravery have the same meaning - showing no fear in dangerous situations.',
    ),

    QuizQuestionModel(
      id: 'eng_005',
      question:
          'Complete the sentence: "If I _____ a soldier, I would serve my country with honor."',
      options: ['am', 'was', 'were', 'will be'],
      correctAnswerIndex: 2,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'intermediate',
      explanation:
          'This is a conditional sentence (hypothetical), so "were" is correct.',
    ),

    QuizQuestionModel(
      id: 'eng_006',
      question:
          'What does the phrase "esprit de corps" mean in military context?',
      options: [
        'Individual achievement',
        'Team spirit and unity',
        'Physical training',
        'Military equipment',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'advanced',
      explanation:
          'Esprit de corps refers to the spirit of unity and common purpose in a group.',
    ),

    QuizQuestionModel(
      id: 'eng_007',
      question:
          'Choose the correct passive voice: "The commander gave orders to the troops."',
      options: [
        'Orders were given to the troops by the commander',
        'Orders are given to the troops by the commander',
        'The troops were giving orders by the commander',
        'The commander was given orders by the troops',
      ],
      correctAnswerIndex: 0,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'intermediate',
      explanation:
          'In passive voice, the object becomes the subject: "Orders were given..."',
    ),

    QuizQuestionModel(
      id: 'eng_008',
      question: 'What is the antonym of "retreat" in military terms?',
      options: ['Advance', 'Surrender', 'Defend', 'Hide'],
      correctAnswerIndex: 0,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'beginner',
      explanation:
          'Retreat means to move back, while advance means to move forward.',
    ),

    QuizQuestionModel(
      id: 'eng_009',
      question:
          'Identify the figure of speech: "The soldier fought like a lion."',
      options: ['Metaphor', 'Simile', 'Personification', 'Hyperbole'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Literature',
      difficulty: 'intermediate',
      explanation: 'A simile compares two things using "like" or "as".',
    ),

    QuizQuestionModel(
      id: 'eng_010',
      question: 'Choose the correct spelling:',
      options: ['Lieutenent', 'Lieutenant', 'Leutenant', 'Lieutanant'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Spelling',
      difficulty: 'intermediate',
      explanation: 'The correct spelling is "Lieutenant" - a military rank.',
    ),

    QuizQuestionModel(
      id: 'eng_011',
      question: 'What type of sentence is this: "Stand at attention!"',
      options: ['Declarative', 'Interrogative', 'Imperative', 'Exclamatory'],
      correctAnswerIndex: 2,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'beginner',
      explanation: 'An imperative sentence gives a command or instruction.',
    ),

    QuizQuestionModel(
      id: 'eng_012',
      question:
          'Choose the word that best completes: "The military _____ is responsible for national defense."',
      options: ['personal', 'personnel', 'personel', 'personell'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'intermediate',
      explanation:
          'Personnel refers to the people employed in an organization.',
    ),

    // Additional English Questions for Premium Users
    QuizQuestionModel(
      id: 'eng_013',
      question: 'What is the meaning of "reconnaissance" in military terms?',
      options: ['Attack', 'Retreat', 'Gathering information', 'Defense'],
      correctAnswerIndex: 2,
      category: 'English Language',
      subcategory: 'Military Vocabulary',
      difficulty: 'intermediate',
      explanation:
          'Reconnaissance means gathering information about enemy positions and movements.',
    ),

    QuizQuestionModel(
      id: 'eng_014',
      question: 'Choose the correct spelling:',
      options: ['Liutenant', 'Lieutenant', 'Leutenant', 'Lieutenent'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Spelling',
      difficulty: 'beginner',
      explanation: 'Lieutenant is the correct spelling for this military rank.',
    ),

    QuizQuestionModel(
      id: 'eng_015',
      question: 'What does "deploy" mean in military context?',
      options: [
        'To retreat',
        'To position troops strategically',
        'To surrender',
        'To celebrate',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Military Vocabulary',
      difficulty: 'intermediate',
      explanation:
          'Deploy means to position troops or equipment strategically for military action.',
    ),

    QuizQuestionModel(
      id: 'eng_016',
      question:
          'Identify the error: "The soldiers march everyday to the parade ground."',
      options: [
        'No error',
        'Should be "every day"',
        'Should be "marches"',
        'Should be "marched"',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'intermediate',
      explanation:
          '"Every day" (two words) is correct when used as an adverb. "Everyday" (one word) is an adjective.',
    ),

    QuizQuestionModel(
      id: 'eng_017',
      question: 'What is the plural of "court-martial"?',
      options: [
        'court-martials',
        'courts-martial',
        'court-martiales',
        'courts-martials',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'advanced',
      explanation:
          'The plural of compound nouns like "court-martial" is "courts-martial".',
    ),

    QuizQuestionModel(
      id: 'eng_018',
      question: 'Choose the synonym for "valor":',
      options: ['Cowardice', 'Bravery', 'Weakness', 'Fear'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'intermediate',
      explanation: 'Valor means courage and bravery, especially in battle.',
    ),

    QuizQuestionModel(
      id: 'eng_019',
      question: 'What does "AWOL" stand for?',
      options: [
        'Always Working On Leave',
        'Absent Without Official Leave',
        'Armed With Official License',
        'All Weapons On Location',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Military Acronyms',
      difficulty: 'intermediate',
      explanation:
          'AWOL stands for "Absent Without Official Leave" - being away from duty without permission.',
    ),

    QuizQuestionModel(
      id: 'eng_020',
      question: 'Choose the correct form: "The regiment _____ been deployed."',
      options: ['have', 'has', 'had', 'having'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'intermediate',
      explanation:
          'Regiment is a collective noun treated as singular, so it takes "has".',
    ),
  ];

  /// Mathematics Questions (JSS2-SS3 Level)
  static List<QuizQuestionModel> get mathematicsQuestions => [
    QuizQuestionModel(
      id: 'math_001',
      question:
          'If a military unit has 120 soldiers and 25% are officers, how many officers are there?',
      options: ['20', '25', '30', '35'],
      correctAnswerIndex: 2,
      category: 'Mathematics',
      subcategory: 'Percentage',
      difficulty: 'beginner',
      explanation: '25% of 120 = (25/100) × 120 = 30 officers',
    ),

    QuizQuestionModel(
      id: 'math_002',
      question: 'A soldier runs 5km in 25 minutes. What is his speed in km/h?',
      options: ['10 km/h', '12 km/h', '15 km/h', '20 km/h'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Speed and Time',
      difficulty: 'intermediate',
      explanation:
          'Speed = Distance/Time = 5km ÷ (25/60)h = 5 ÷ 0.417 = 12 km/h',
    ),

    QuizQuestionModel(
      id: 'math_003',
      question: 'Solve: 3x + 15 = 48',
      options: ['x = 11', 'x = 13', 'x = 15', 'x = 17'],
      correctAnswerIndex: 0,
      category: 'Mathematics',
      subcategory: 'Algebra',
      difficulty: 'intermediate',
      explanation:
          '3x + 15 = 48, so 3x = 48 - 15 = 33, therefore x = 33 ÷ 3 = 11',
    ),

    QuizQuestionModel(
      id: 'math_004',
      question:
          'What is the area of a rectangular parade ground that is 50m long and 30m wide?',
      options: ['1500 m²', '1600 m²', '1400 m²', '1200 m²'],
      correctAnswerIndex: 0,
      category: 'Mathematics',
      subcategory: 'Geometry',
      difficulty: 'beginner',
      explanation: 'Area of rectangle = length × width = 50m × 30m = 1500 m²',
    ),

    QuizQuestionModel(
      id: 'math_005',
      question:
          'If 8 soldiers can complete a task in 6 days, how many days will 12 soldiers take?',
      options: ['3 days', '4 days', '5 days', '9 days'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Ratio and Proportion',
      difficulty: 'intermediate',
      explanation:
          'Using inverse proportion: 8 × 6 = 12 × x, so x = 48 ÷ 12 = 4 days',
    ),

    QuizQuestionModel(
      id: 'math_006',
      question: 'What is 15% of 200?',
      options: ['25', '30', '35', '40'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Percentage',
      difficulty: 'beginner',
      explanation: '15% of 200 = (15/100) × 200 = 0.15 × 200 = 30',
    ),

    QuizQuestionModel(
      id: 'math_007',
      question: 'Find the value of x: 2x - 7 = 13',
      options: ['x = 8', 'x = 10', 'x = 12', 'x = 15'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Algebra',
      difficulty: 'beginner',
      explanation:
          '2x - 7 = 13, so 2x = 13 + 7 = 20, therefore x = 20 ÷ 2 = 10',
    ),

    QuizQuestionModel(
      id: 'math_008',
      question:
          'What is the perimeter of a square military base with sides of 25m each?',
      options: ['75m', '100m', '125m', '150m'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Geometry',
      difficulty: 'beginner',
      explanation: 'Perimeter of square = 4 × side length = 4 × 25m = 100m',
    ),

    QuizQuestionModel(
      id: 'math_009',
      question:
          'A military convoy travels 240km in 4 hours. What is the average speed?',
      options: ['50 km/h', '60 km/h', '70 km/h', '80 km/h'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Speed and Time',
      difficulty: 'beginner',
      explanation:
          'Average speed = Total distance ÷ Total time = 240km ÷ 4h = 60 km/h',
    ),

    QuizQuestionModel(
      id: 'math_010',
      question:
          'What is the simple interest on ₦5000 at 8% per annum for 3 years?',
      options: ['₦1000', '₦1200', '₦1500', '₦1800'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Simple Interest',
      difficulty: 'intermediate',
      explanation: 'SI = (P × R × T) ÷ 100 = (5000 × 8 × 3) ÷ 100 = ₦1200',
    ),

    // Additional Mathematics Questions for Premium Users
    QuizQuestionModel(
      id: 'math_011',
      question:
          'If a soldier runs 5 km in 25 minutes, what is his speed in km/h?',
      options: ['10 km/h', '12 km/h', '15 km/h', '20 km/h'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Speed and Distance',
      difficulty: 'intermediate',
      explanation:
          'Speed = Distance/Time = 5 km / (25/60) hours = 5 × (60/25) = 12 km/h',
    ),

    QuizQuestionModel(
      id: 'math_012',
      question:
          'What is the area of a rectangular parade ground that is 50m × 30m?',
      options: ['1200 m²', '1500 m²', '1600 m²', '1800 m²'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Area and Perimeter',
      difficulty: 'beginner',
      explanation: 'Area of rectangle = length × width = 50m × 30m = 1500 m²',
    ),

    QuizQuestionModel(
      id: 'math_013',
      question: 'If 3x + 7 = 22, what is the value of x?',
      options: ['3', '5', '7', '9'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Algebra',
      difficulty: 'intermediate',
      explanation: '3x + 7 = 22, so 3x = 22 - 7 = 15, therefore x = 15/3 = 5',
    ),

    QuizQuestionModel(
      id: 'math_014',
      question: 'What is 2³ × 2²?',
      options: ['16', '32', '64', '128'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Indices',
      difficulty: 'intermediate',
      explanation: '2³ × 2² = 2^(3+2) = 2⁵ = 32',
    ),

    QuizQuestionModel(
      id: 'math_015',
      question:
          'A military convoy travels 240 km in 4 hours. What is the average speed?',
      options: ['50 km/h', '60 km/h', '70 km/h', '80 km/h'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Speed and Distance',
      difficulty: 'beginner',
      explanation:
          'Average speed = Total distance / Total time = 240 km / 4 hours = 60 km/h',
    ),

    QuizQuestionModel(
      id: 'math_016',
      question: 'What is the perimeter of a square with side length 8 meters?',
      options: ['24 m', '32 m', '40 m', '64 m'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Area and Perimeter',
      difficulty: 'beginner',
      explanation: 'Perimeter of square = 4 × side length = 4 × 8m = 32m',
    ),

    QuizQuestionModel(
      id: 'math_017',
      question:
          'If a military unit loses 20% of its strength and has 80 soldiers left, what was the original strength?',
      options: ['90', '100', '110', '120'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Percentage',
      difficulty: 'intermediate',
      explanation:
          'If 20% is lost, then 80% remains. So 80% = 80 soldiers, therefore 100% = 80 ÷ 0.8 = 100 soldiers',
    ),

    QuizQuestionModel(
      id: 'math_018',
      question: 'What is the square root of 144?',
      options: ['10', '12', '14', '16'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Square Roots',
      difficulty: 'beginner',
      explanation: '√144 = 12 because 12 × 12 = 144',
    ),

    QuizQuestionModel(
      id: 'math_019',
      question:
          'If 5 soldiers can dig a trench in 8 hours, how long will it take 10 soldiers?',
      options: ['2 hours', '4 hours', '6 hours', '16 hours'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Ratio and Proportion',
      difficulty: 'intermediate',
      explanation:
          'Using inverse proportion: 5 × 8 = 10 × x, so x = 40 ÷ 10 = 4 hours',
    ),

    QuizQuestionModel(
      id: 'math_020',
      question: 'What is 25% of 80?',
      options: ['15', '20', '25', '30'],
      correctAnswerIndex: 1,
      category: 'Mathematics',
      subcategory: 'Percentage',
      difficulty: 'beginner',
      explanation: '25% of 80 = (25/100) × 80 = 0.25 × 80 = 20',
    ),

    // Add more English questions
    QuizQuestionModel(
      id: 'eng_013',
      question:
          'Choose the correct form: "Neither the captain nor the soldiers _____ present."',
      options: ['was', 'were', 'is', 'are'],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'advanced',
      explanation:
          'With "neither...nor", the verb agrees with the subject closer to it (soldiers - plural).',
    ),

    QuizQuestionModel(
      id: 'eng_014',
      question: 'What does "reconnaissance" mean in military terms?',
      options: [
        'Retreat from battle',
        'Gathering information about enemy',
        'Medical treatment',
        'Equipment maintenance',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Vocabulary',
      difficulty: 'advanced',
      explanation:
          'Reconnaissance is military observation to gather information about enemy positions.',
    ),

    QuizQuestionModel(
      id: 'eng_015',
      question:
          'Identify the error: "The recruits must past the physical fitness test."',
      options: [
        'No error',
        'Wrong verb form',
        'Missing article',
        'Wrong preposition',
      ],
      correctAnswerIndex: 1,
      category: 'English Language',
      subcategory: 'Grammar',
      difficulty: 'intermediate',
      explanation:
          'Should be "pass" not "past". Past is a noun/adjective, pass is a verb.',
    ),
  ];

  /// Nigerian Agency History Questions
  static List<QuizQuestionModel> get agencyHistoryQuestions => [
    // Nigerian Army History
    QuizQuestionModel(
      id: 'army_001',
      question: 'When was the Nigerian Army established?',
      options: ['1960', '1963', '1956', '1958'],
      correctAnswerIndex: 0,
      category: 'Military History',
      subcategory: 'Nigerian Army',
      difficulty: 'intermediate',
      explanation:
          'The Nigerian Army was established in 1960 when Nigeria gained independence.',
      agency: 'Nigerian Army',
      tags: ['army', 'history', 'establishment'],
    ),

    QuizQuestionModel(
      id: 'army_002',
      question: 'Who was the first indigenous Chief of Army Staff?',
      options: [
        'General Aguiyi-Ironsi',
        'General Gowon',
        'General Obasanjo',
        'General Buhari',
      ],
      correctAnswerIndex: 0,
      category: 'Military History',
      subcategory: 'Nigerian Army',
      difficulty: 'advanced',
      explanation:
          'Major General Johnson Aguiyi-Ironsi was the first indigenous Chief of Army Staff.',
      agency: 'Nigerian Army',
      tags: ['army', 'leadership', 'history'],
    ),

    // Nigerian Navy History
    QuizQuestionModel(
      id: 'navy_001',
      question: 'When was the Nigerian Navy established?',
      options: ['1956', '1958', '1960', '1963'],
      correctAnswerIndex: 0,
      category: 'Military History',
      subcategory: 'Nigerian Navy',
      difficulty: 'intermediate',
      explanation:
          'The Nigerian Navy was established in 1956 as the Royal Nigerian Navy.',
      agency: 'Nigerian Navy',
      tags: ['navy', 'history', 'establishment'],
    ),

    QuizQuestionModel(
      id: 'navy_002',
      question: 'What is the motto of the Nigerian Navy?',
      options: [
        'Unity and Progress',
        'Onward Together',
        'Victory at Sea',
        'Discipline and Courage',
      ],
      correctAnswerIndex: 1,
      category: 'Military History',
      subcategory: 'Nigerian Navy',
      difficulty: 'beginner',
      explanation: 'The Nigerian Navy motto is "Onward Together".',
      agency: 'Nigerian Navy',
      tags: ['navy', 'motto', 'values'],
    ),

    // Nigerian Air Force History
    QuizQuestionModel(
      id: 'airforce_001',
      question: 'When was the Nigerian Air Force established?',
      options: ['1960', '1963', '1964', '1965'],
      correctAnswerIndex: 2,
      category: 'Military History',
      subcategory: 'Nigerian Air Force',
      difficulty: 'intermediate',
      explanation: 'The Nigerian Air Force was established on April 18, 1964.',
      agency: 'Nigerian Air Force',
      tags: ['airforce', 'history', 'establishment'],
    ),

    QuizQuestionModel(
      id: 'airforce_002',
      question: 'What is the motto of the Nigerian Air Force?',
      options: [
        'Service Before Self',
        'Courage in the Air',
        'Sky is Our Limit',
        'Wings of Victory',
      ],
      correctAnswerIndex: 0,
      category: 'Military History',
      subcategory: 'Nigerian Air Force',
      difficulty: 'beginner',
      explanation: 'The Nigerian Air Force motto is "Service Before Self".',
      agency: 'Nigerian Air Force',
      tags: ['airforce', 'motto', 'values'],
    ),

    // Nigerian Police History
    QuizQuestionModel(
      id: 'police_001',
      question: 'When was the Nigeria Police Force established?',
      options: ['1930', '1943', '1960', '1967'],
      correctAnswerIndex: 1,
      category: 'Security History',
      subcategory: 'Nigerian Police',
      difficulty: 'intermediate',
      explanation: 'The Nigeria Police Force was established in 1943.',
      agency: 'Nigerian Police',
      tags: ['police', 'history', 'establishment'],
    ),

    QuizQuestionModel(
      id: 'police_002',
      question: 'What is the motto of the Nigeria Police Force?',
      options: [
        'To Serve and Protect',
        'Police is Your Friend',
        'Law and Order',
        'Justice for All',
      ],
      correctAnswerIndex: 1,
      category: 'Security History',
      subcategory: 'Nigerian Police',
      difficulty: 'beginner',
      explanation: 'The Nigeria Police Force motto is "Police is Your Friend".',
      agency: 'Nigerian Police',
      tags: ['police', 'motto', 'community'],
    ),

    // Nigerian Customs Service History
    QuizQuestionModel(
      id: 'customs_001',
      question: 'When was the Nigeria Customs Service established?',
      options: ['1891', '1901', '1960', '1963'],
      correctAnswerIndex: 0,
      category: 'Security History',
      subcategory: 'Nigerian Customs',
      difficulty: 'advanced',
      explanation:
          'The Nigeria Customs Service was established in 1891 during the colonial era.',
      agency: 'Nigerian Customs',
      tags: ['customs', 'history', 'trade'],
    ),

    // Nigerian Immigration Service History
    QuizQuestionModel(
      id: 'immigration_001',
      question: 'When was the Nigeria Immigration Service established?',
      options: ['1958', '1963', '1960', '1967'],
      correctAnswerIndex: 1,
      category: 'Security History',
      subcategory: 'Nigerian Immigration',
      difficulty: 'intermediate',
      explanation: 'The Nigeria Immigration Service was established in 1963.',
      agency: 'Nigerian Immigration',
      tags: ['immigration', 'history', 'borders'],
    ),

    // FRSC History
    QuizQuestionModel(
      id: 'frsc_001',
      question: 'When was the Federal Road Safety Corps (FRSC) established?',
      options: ['1988', '1990', '1985', '1992'],
      correctAnswerIndex: 0,
      category: 'Security History',
      subcategory: 'FRSC',
      difficulty: 'intermediate',
      explanation: 'The Federal Road Safety Corps was established in 1988.',
      agency: 'FRSC',
      tags: ['frsc', 'road safety', 'history'],
    ),

    // NSCDC History
    QuizQuestionModel(
      id: 'nscdc_001',
      question:
          'When was the Nigeria Security and Civil Defence Corps (NSCDC) established?',
      options: ['1967', '1970', '1988', '1990'],
      correctAnswerIndex: 0,
      category: 'Security History',
      subcategory: 'NSCDC',
      difficulty: 'intermediate',
      explanation:
          'The NSCDC was established in 1967 but became a paramilitary organization later.',
      agency: 'NSCDC',
      tags: ['nscdc', 'civil defence', 'history'],
    ),

    // Fire Service History
    QuizQuestionModel(
      id: 'fire_001',
      question:
          'What is the primary responsibility of the Federal Fire Service?',
      options: [
        'Crime prevention',
        'Fire prevention and firefighting',
        'Border control',
        'Traffic management',
      ],
      correctAnswerIndex: 1,
      category: 'Security History',
      subcategory: 'Fire Service',
      difficulty: 'beginner',
      explanation:
          'The Federal Fire Service is responsible for fire prevention, firefighting, and rescue operations.',
      agency: 'Fire Service',
      tags: ['fire service', 'emergency', 'rescue'],
    ),

    // Additional Nigerian Military History Questions for Premium Users
    QuizQuestionModel(
      id: 'army_006',
      question: 'Who was the first indigenous Chief of Army Staff of Nigeria?',
      options: [
        'General Yakubu Gowon',
        'General Johnson Aguiyi-Ironsi',
        'General Olusegun Obasanjo',
        'General Murtala Mohammed',
      ],
      correctAnswerIndex: 1,
      category: 'Military History',
      subcategory: 'Nigerian Army',
      difficulty: 'intermediate',
      explanation:
          'General Johnson Aguiyi-Ironsi was the first indigenous Chief of Army Staff and later became Head of State.',
      agency: 'Nigerian Army',
      tags: ['army', 'leadership', 'history'],
    ),

    QuizQuestionModel(
      id: 'army_007',
      question:
          'In which year did Nigeria participate in the ECOMOG peacekeeping mission in Liberia?',
      options: ['1988', '1990', '1992', '1995'],
      correctAnswerIndex: 1,
      category: 'Military History',
      subcategory: 'Nigerian Army',
      difficulty: 'advanced',
      explanation:
          'Nigeria led the ECOMOG peacekeeping mission in Liberia starting in 1990.',
      agency: 'Nigerian Army',
      tags: ['peacekeeping', 'ecomog', 'liberia'],
    ),

    QuizQuestionModel(
      id: 'navy_003',
      question:
          'What is the rank structure equivalent to Colonel in the Nigerian Navy?',
      options: ['Captain', 'Commander', 'Commodore', 'Admiral'],
      correctAnswerIndex: 0,
      category: 'Military History',
      subcategory: 'Nigerian Navy',
      difficulty: 'intermediate',
      explanation:
          'In the Nigerian Navy, Captain is equivalent to Colonel in the Army.',
      agency: 'Nigerian Navy',
      tags: ['navy', 'ranks', 'structure'],
    ),

    QuizQuestionModel(
      id: 'airforce_003',
      question:
          'What type of aircraft was first used by the Nigerian Air Force?',
      options: [
        'Fighter jets',
        'Transport aircraft',
        'Helicopters',
        'Training aircraft',
      ],
      correctAnswerIndex: 3,
      category: 'Military History',
      subcategory: 'Nigerian Air Force',
      difficulty: 'advanced',
      explanation:
          'The Nigerian Air Force started with training aircraft before acquiring other types.',
      agency: 'Nigerian Air Force',
      tags: ['airforce', 'aircraft', 'history'],
    ),

    QuizQuestionModel(
      id: 'military_001',
      question:
          'What does the Nigerian military code of conduct emphasize most?',
      options: [
        'Personal gain',
        'Discipline and loyalty',
        'Political involvement',
        'Individual freedom',
      ],
      correctAnswerIndex: 1,
      category: 'Military History',
      subcategory: 'General Military',
      difficulty: 'beginner',
      explanation:
          'The Nigerian military code of conduct emphasizes discipline, loyalty, and service to the nation.',
      agency: 'All',
      tags: ['military', 'conduct', 'values'],
    ),

    QuizQuestionModel(
      id: 'military_002',
      question:
          'Which Nigerian military operation was conducted to restore peace in Sierra Leone?',
      options: [
        'Operation Python Dance',
        'Operation Restore Hope',
        'Operation Khukri',
        'Operation Palliser',
      ],
      correctAnswerIndex: 2,
      category: 'Military History',
      subcategory: 'General Military',
      difficulty: 'advanced',
      explanation:
          'Operation Khukri was a Nigerian-led military operation in Sierra Leone.',
      agency: 'All',
      tags: ['operations', 'sierra leone', 'peacekeeping'],
    ),

    QuizQuestionModel(
      id: 'nda_002',
      question:
          'What is the duration of the regular course at the Nigerian Defence Academy?',
      options: ['3 years', '4 years', '5 years', '6 years'],
      correctAnswerIndex: 2,
      category: 'Military History',
      subcategory: 'NDA',
      difficulty: 'intermediate',
      explanation:
          'The Nigerian Defence Academy regular course is 5 years duration.',
      agency: 'NDA',
      tags: ['nda', 'training', 'duration'],
    ),

    QuizQuestionModel(
      id: 'dssc_002',
      question: 'What does DSSC stand for in Nigerian military context?',
      options: [
        'Defence Staff Security College',
        'Defence Services Staff College',
        'Defence Strategic Studies Centre',
        'Defence Systems Support Command',
      ],
      correctAnswerIndex: 1,
      category: 'Military History',
      subcategory: 'DSSC',
      difficulty: 'intermediate',
      explanation:
          'DSSC stands for Defence Services Staff College, located in Jaji.',
      agency: 'DSSC',
      tags: ['dssc', 'staff college', 'training'],
    ),
  ];

  /// Get questions by category
  static List<QuizQuestionModel> getQuestionsByCategory(String category) {
    switch (category.toLowerCase()) {
      case 'english language':
      case 'english':
        return englishQuestions;
      case 'mathematics':
      case 'math':
        return mathematicsQuestions;
      case 'military history':
      case 'agency history':
      case 'history':
        return agencyHistoryQuestions;
      default:
        return [];
    }
  }

  /// Get agency-specific questions
  static List<QuizQuestionModel> getAgencyQuestions(String? agency) {
    if (agency == null) return [];

    // First try to get from comprehensive database
    final comprehensiveQuestions =
        ComprehensiveAgencyQuizDatabase.getQuestionsForAgency(agency);
    if (comprehensiveQuestions.isNotEmpty) {
      return comprehensiveQuestions;
    }

    // Fallback to legacy agency history questions
    return agencyHistoryQuestions
        .where((question) => question.matchesAgency(agency))
        .toList();
  }

  /// Get random agency-specific questions with specified count
  static List<QuizQuestionModel> getRandomAgencyQuestions(
    String? agency,
    int count,
  ) {
    if (agency == null) return [];

    // First try to get from comprehensive database
    final comprehensiveQuestions =
        ComprehensiveAgencyQuizDatabase.getRandomQuestionsForAgency(
          agency,
          count,
        );
    if (comprehensiveQuestions.isNotEmpty) {
      return comprehensiveQuestions;
    }

    // Fallback to legacy agency history questions
    final agencyQuestions =
        agencyHistoryQuestions
            .where((question) => question.matchesAgency(agency))
            .toList();

    if (agencyQuestions.length <= count) return agencyQuestions;

    final shuffled = List<QuizQuestionModel>.from(agencyQuestions)..shuffle();
    return shuffled.take(count).toList();
  }

  /// Get all questions
  static List<QuizQuestionModel> getAllQuestions() {
    return [
      ...englishQuestions,
      ...mathematicsQuestions,
      ...agencyHistoryQuestions,
    ];
  }

  /// Get random questions from a category
  static List<QuizQuestionModel> getRandomQuestions(
    String category,
    int count,
  ) {
    final questions = getQuestionsByCategory(category);
    if (questions.length <= count) return questions;

    final shuffled = List<QuizQuestionModel>.from(questions)..shuffle();
    return shuffled.take(count).toList();
  }

  /// Get mixed quiz questions (English, Math, and Agency-specific)
  static List<QuizQuestionModel> getMixedQuizQuestions(
    String? userAgency, {
    int englishCount = 10,
    int mathCount = 10,
    int historyCount = 5,
  }) {
    final questions = <QuizQuestionModel>[];

    // Add English questions
    final englishShuffled = List<QuizQuestionModel>.from(englishQuestions)
      ..shuffle();
    questions.addAll(englishShuffled.take(englishCount));

    // Add Math questions
    final mathShuffled = List<QuizQuestionModel>.from(mathematicsQuestions)
      ..shuffle();
    questions.addAll(mathShuffled.take(mathCount));

    // Add agency-specific history questions
    final agencyQuestions = getAgencyQuestions(userAgency);
    if (agencyQuestions.isNotEmpty) {
      final historyShuffled = List<QuizQuestionModel>.from(agencyQuestions)
        ..shuffle();
      questions.addAll(historyShuffled.take(historyCount));
    }

    // Shuffle the final list
    questions.shuffle();
    return questions;
  }

  /// Get questions by difficulty
  static List<QuizQuestionModel> getQuestionsByDifficulty(String difficulty) {
    return getAllQuestions()
        .where(
          (question) =>
              question.difficulty.toLowerCase() == difficulty.toLowerCase(),
        )
        .toList();
  }

  /// Search questions by keyword
  static List<QuizQuestionModel> searchQuestions(String keyword) {
    final lowercaseKeyword = keyword.toLowerCase();
    return getAllQuestions().where((question) {
      return question.question.toLowerCase().contains(lowercaseKeyword) ||
          question.category.toLowerCase().contains(lowercaseKeyword) ||
          question.subcategory.toLowerCase().contains(lowercaseKeyword) ||
          question.tags.any(
            (tag) => tag.toLowerCase().contains(lowercaseKeyword),
          );
    }).toList();
  }
}
