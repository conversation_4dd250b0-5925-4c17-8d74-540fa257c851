import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/core/services/premium_service.dart';
import 'package:fit_4_force/core/widgets/user_progress_widgets.dart';
import 'package:fit_4_force/features/community/models/post_model.dart';
import 'package:fit_4_force/features/community/screens/agency_forum_screen.dart';
import 'package:fit_4_force/features/community/screens/badges_screen.dart';
import 'package:fit_4_force/features/community/screens/study_groups_screen.dart';
import 'package:fit_4_force/features/community/screens/success_stories_screen.dart';
import 'package:fit_4_force/features/community/screens/post_detail_screen.dart';
import 'package:fit_4_force/features/community/widgets/photo_post_creator.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
import 'package:fit_4_force/core/config/app_routes.dart';

class CommunityScreen extends StatefulWidget {
  final UserModel user;

  const CommunityScreen({super.key, required this.user});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final UserProgressService _progressService = UserProgressService();
  Map<String, dynamic> _communityProgress = {};
  bool _isLoading = true;

  // Mock data for posts - mix of user's own posts and others' posts
  List<PostModel> _posts = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializePosts();
    _loadCommunityProgress();
  }

  void _initializePosts() {
    // Create posts with agency-based visibility
    // Only show posts from the same agency as the user
    final userAgency = widget.user.targetAgency;

    _posts = [
      // User's own post (so they can see their own content and receive likes)
      PostModel(
        id: 'user_post_1',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        userId: widget.user.id,
        userName: widget.user.fullName,
        userProfileImageUrl: widget.user.profileImageUrl,
        title: 'My Journey to the $userAgency',
        content:
            'I wanted to share my preparation journey and some tips that have helped me along the way. The physical training has been challenging but rewarding, and I\'ve learned so much about discipline and perseverance. Here are some strategies that have worked for me...',
        agency: userAgency,
        tags: const ['Personal Experience', 'Tips', 'Motivation'],
        likesCount: 12,
        commentsCount: 8,
        isLikedByCurrentUser: false, // Can't like own post
        icon: _getAgencyIcon(userAgency),
        color: _getAgencyColor(userAgency),
      ),

      // Other users' posts from the same agency
      if (userAgency == 'Nigerian Army') ...[
        PostModel(
          id: '1',
          createdAt: DateTime.now().subtract(const Duration(hours: 2)),
          userId: 'user1',
          userName: 'John Doe',
          userProfileImageUrl: null,
          title: 'Tips for Nigerian Army Physical Test',
          content:
              'I recently passed the Nigerian Army physical test with flying colors. Here are some tips that helped me prepare: 1) Start with basic cardio 2) Focus on push-ups and sit-ups 3) Practice long-distance running...',
          agency: 'Nigerian Army',
          tags: const ['Physical Test', 'Tips', 'Training'],
          likesCount: 24,
          commentsCount: 15,
          isLikedByCurrentUser: true,
          icon: Icons.fitness_center,
          color: Colors.green,
        ),
        PostModel(
          id: '2',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          userId: 'user2',
          userName: 'Sarah Military',
          userProfileImageUrl: null,
          title: 'Nigerian Army Interview Experience',
          content:
              'Just completed my Nigerian Army interview. The panel asked about current affairs, military history, and my motivation for joining. They were professional and fair throughout the process...',
          agency: 'Nigerian Army',
          tags: const ['Interview', 'Experience', 'Nigerian Army'],
          likesCount: 42,
          commentsCount: 23,
          isLikedByCurrentUser: false,
          imageUrl: 'assets/images/content/interview.jpg',
          icon: Icons.question_answer,
          color: Colors.green,
        ),
      ] else if (userAgency == 'Navy') ...[
        PostModel(
          id: '3',
          createdAt: DateTime.now().subtract(const Duration(hours: 4)),
          userId: 'user3',
          userName: 'Michael Ocean',
          userProfileImageUrl: null,
          title: 'Navy Swimming Test Preparation',
          content:
              'The Navy swimming test is crucial. I\'ve been training at the local pool for 3 months. Focus on endurance swimming and treading water. Don\'t underestimate the physical demands...',
          agency: 'Navy',
          tags: const ['Swimming', 'Physical Test', 'Navy'],
          likesCount: 18,
          commentsCount: 9,
          isLikedByCurrentUser: false,
          icon: Icons.pool,
          color: Colors.blue,
        ),
        PostModel(
          id: '4',
          createdAt: DateTime.now().subtract(const Duration(days: 2)),
          userId: 'user4',
          userName: 'Emma Naval',
          userProfileImageUrl: null,
          title: 'Navy Technical Exam Study Guide',
          content:
              'Here\'s my comprehensive study guide for the Navy technical examination. Covers mathematics, physics, and basic engineering principles that are commonly tested...',
          agency: 'Navy',
          tags: const ['Technical Exam', 'Study Guide', 'Navy'],
          likesCount: 35,
          commentsCount: 18,
          isLikedByCurrentUser: true,
          icon: Icons.engineering,
          color: Colors.blue,
        ),
      ] else if (userAgency == 'Air Force') ...[
        PostModel(
          id: '5',
          createdAt: DateTime.now().subtract(const Duration(hours: 6)),
          userId: 'user5',
          userName: 'James Pilot',
          userProfileImageUrl: null,
          title: 'Air Force Aptitude Test Experience',
          content:
              'Just took the Air Force aptitude test. Heavy focus on mathematics, physics, and logical reasoning. The aviation knowledge section was challenging but fair...',
          agency: 'Air Force',
          tags: const ['Aptitude Test', 'Experience', 'Air Force'],
          likesCount: 28,
          commentsCount: 12,
          isLikedByCurrentUser: false,
          icon: Icons.flight,
          color: Colors.lightBlue,
        ),
      ] else if (userAgency == 'DSSC') ...[
        PostModel(
          id: '6',
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          userId: 'user6',
          userName: 'Dr. Patricia Scholar',
          userProfileImageUrl: null,
          title: 'DSSC Research Proposal Tips',
          content:
              'Successfully submitted my DSSC research proposal. Key points: clear methodology, relevant literature review, and practical applications. The selection committee values innovation...',
          agency: 'DSSC',
          tags: const ['Research', 'Proposal', 'DSSC'],
          likesCount: 45,
          commentsCount: 22,
          isLikedByCurrentUser: true,
          icon: Icons.school,
          color: Colors.purple,
        ),
      ] else ...[
        // Generic posts for other agencies
        PostModel(
          id: '7',
          createdAt: DateTime.now().subtract(const Duration(hours: 3)),
          userId: 'user7',
          userName: 'Alex General',
          userProfileImageUrl: null,
          title: '$userAgency Preparation Journey',
          content:
              'Sharing my preparation experience for $userAgency. The journey has been challenging but rewarding. Focus on physical fitness, mental preparation, and staying updated with current affairs...',
          agency: userAgency,
          tags: const ['Preparation', 'Experience', 'Tips'],
          likesCount: 20,
          commentsCount: 8,
          isLikedByCurrentUser: false,
          icon: _getAgencyIcon(userAgency),
          color: _getAgencyColor(userAgency),
        ),
      ],
    ];
  }

  Future<void> _loadCommunityProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _communityProgress = progress['community'] ?? {};
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final supportsMultiPane = ResponsiveUtils.supportsMultiPane(context);

    return Scaffold(
      body:
          supportsMultiPane && layoutType != LayoutType.mobilePortrait
              ? _buildMultiPaneLayout(context)
              : _buildSinglePaneLayout(context),
      floatingActionButton:
          PremiumService().canCreateCommunityPosts(widget.user)
              ? FloatingActionButton(
                backgroundColor: AppTheme.primaryColor,
                child: const Icon(Icons.add),
                onPressed: () {
                  _showCreatePostOptions();
                },
              )
              : null,
    );
  }

  Widget _buildSinglePaneLayout(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildFeedTab(),
              _buildAgencyForumsTab(),
              _buildStudyGroupsTab(),
              _buildMyPostsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMultiPaneLayout(BuildContext context) {
    return ResponsiveMultiPaneLayout(
      primaryPane: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFeedTab(),
                _buildAgencyForumsTab(),
                _buildStudyGroupsTab(),
                _buildMyPostsTab(),
              ],
            ),
          ),
        ],
      ),
      secondaryPane: _buildSidePanel(context),
      primaryFlex: 2,
      secondaryFlex: 1,
    );
  }

  Widget _buildSidePanel(BuildContext context) {
    final postsCreated = _communityProgress['postsCreated'] ?? 0;
    final commentsPosted = _communityProgress['commentsPosted'] ?? 0;
    final likesReceived = _communityProgress['likesReceived'] ?? 0;
    final studyGroupsJoined = _communityProgress['studyGroupsJoined'] ?? 0;

    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Your Community Stats',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          if (_isLoading)
            const CircularProgressIndicator()
          else
            Column(
              children: [
                ProgressStatsCard(
                  title: 'Posts Created',
                  value: postsCreated,
                  icon: Icons.article,
                  color: Colors.blue,
                  emptyStateMessage: 'Share your first post!',
                ),
                SizedBox(
                  height: ResponsiveUtils.getResponsiveSpacing(context) / 2,
                ),
                ProgressStatsCard(
                  title: 'Comments Posted',
                  value: commentsPosted,
                  icon: Icons.comment,
                  color: Colors.green,
                  emptyStateMessage: 'Join the conversation!',
                ),
                SizedBox(
                  height: ResponsiveUtils.getResponsiveSpacing(context) / 2,
                ),
                ProgressStatsCard(
                  title: 'Likes Received',
                  value: likesReceived,
                  icon: Icons.favorite,
                  color: Colors.red,
                  emptyStateMessage: 'Get your first like!',
                ),
                SizedBox(
                  height: ResponsiveUtils.getResponsiveSpacing(context) / 2,
                ),
                ProgressStatsCard(
                  title: 'Study Groups',
                  value: studyGroupsJoined,
                  icon: Icons.group,
                  color: Colors.purple,
                  emptyStateMessage: 'Join a study group!',
                ),
              ],
            ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          ResponsiveText(
            'Quick Actions',
            mobileFontSize: 16.0,
            tabletFontSize: 18.0,
            desktopFontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Badges', Icons.emoji_events, () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => BadgesScreen(user: widget.user),
              ),
            );
          }),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Success Stories', Icons.star, () {
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => SuccessStoriesScreen(user: widget.user),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ResponsiveCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14.0,
                  tabletFontSize: 15.0,
                  desktopFontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);

    return Container(
      padding: padding,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF9C27B0), // Purple
            Color(0xFF7B1FA2), // Deep Purple
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveText(
                        _getShortAgencyName(widget.user.targetAgency),
                        mobileFontSize: isSmallScreen ? 22.0 : 24.0,
                        tabletFontSize: 26.0,
                        desktopFontSize: 28.0,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        style: const TextStyle(
                          letterSpacing: 0.5,
                          shadows: [
                            Shadow(
                              color: Colors.black38,
                              blurRadius: 3,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: spacing / 4),
                      ResponsiveText(
                        'Connect with fellow ${_getAgencyAspiriantName(widget.user.targetAgency)} aspirants',
                        mobileFontSize: isSmallScreen ? 12.0 : 13.0,
                        tabletFontSize: 14.0,
                        desktopFontSize: 15.0,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                        style: const TextStyle(
                          letterSpacing: 0.3,
                          shadows: [
                            Shadow(
                              color: Colors.black26,
                              blurRadius: 2,
                              offset: Offset(0, 1),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Decorative squares
                Row(
                  children: [
                    for (int i = 0; i < 4; i++) ...[
                      Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.white, width: 1.5),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          _getQuickActionIcon(i),
                          color: const Color(0xFF9C27B0),
                          size: 20,
                        ),
                      ),
                      if (i < 3) const SizedBox(width: 10),
                    ],
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (!PremiumService().canCreateCommunityPosts(widget.user))
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15 * 255),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2 * 255),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2 * 255),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.workspace_premium,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Upgrade to Premium',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Create posts and join discussions',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white.withValues(alpha: 0.8 * 255),
                            ),
                          ),
                        ],
                      ),
                    ),
                    BaseButton(
                      text: 'Upgrade',
                      icon: Icons.arrow_forward,
                      backgroundColor: Colors.white,
                      textColor: const Color(0xFF9C27B0),
                      height: 36,
                      onPressed: () {
                        Navigator.of(context).pushNamed(AppRoutes.premium);
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF9C27B0),
        unselectedLabelColor: AppTheme.textSecondaryLight,
        indicatorColor: const Color(0xFF9C27B0),
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'FEED'),
          Tab(text: 'FORUMS'),
          Tab(text: 'STUDY GROUPS'),
          Tab(text: 'MY POSTS'),
        ],
      ),
    );
  }

  Widget _buildFeedTab() {
    // Filter posts to show only those from the user's agency
    final filteredPosts =
        _posts
            .where((post) => post.agency == widget.user.targetAgency)
            .toList();

    return filteredPosts.isEmpty
        ? _buildEmptyState(
          'No posts yet for ${widget.user.targetAgency}',
          'Be the first to share something with your agency community!',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: filteredPosts.length,
          itemBuilder: (context, index) {
            return _buildPostCard(filteredPosts[index]);
          },
        );
  }

  Widget _buildAgencyForumsTab() {
    // For agency-specific community, just show the user's agency
    final userAgency = widget.user.targetAgency;
    final agencyPosts =
        _posts.where((post) => post.agency == userAgency).toList();

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildAgencyCard(
          agency: userAgency,
          postsCount: agencyPosts.length,
          color: _getAgencyColor(userAgency),
          icon: _getAgencyIcon(userAgency),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.grey[600], size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Agency Forum',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'This is your dedicated $userAgency community space where you can connect with fellow aspirants, share experiences, and get advice specific to your target agency.',
                style: TextStyle(color: Colors.grey[600], fontSize: 14),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStudyGroupsTab() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.3 * 255),
          ),
          const SizedBox(height: 16),
          const Text(
            'Study Groups',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Form study groups with fellow aspirants to prepare together',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          BaseButton(
            text: 'Explore Study Groups',
            icon: Icons.people,
            backgroundColor: AppTheme.primaryColor,
            onPressed: () async {
              // Track study group participation
              await _progressService.incrementValue(
                'community',
                'studyGroupsJoined',
              );

              // Check for achievements
              final newStudyGroupsJoined =
                  (_communityProgress['studyGroupsJoined'] ?? 0) + 1;
              if (newStudyGroupsJoined == 1) {
                await _progressService.addAchievement(
                  'community',
                  'First Study Group Joined',
                );
              } else if (newStudyGroupsJoined == 5) {
                await _progressService.addAchievement(
                  'community',
                  'Study Group Enthusiast - 5 Groups',
                );
              }

              // Reload progress
              await _loadCommunityProgress();

              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => StudyGroupsScreen(user: widget.user),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyPostsTab() {
    final myPosts =
        _posts
            .where(
              (post) =>
                  post.userId == widget.user.id &&
                  post.agency == widget.user.targetAgency,
            )
            .toList();

    return myPosts.isEmpty
        ? _buildEmptyState(
          'No posts yet for ${widget.user.targetAgency}',
          PremiumService().canCreateCommunityPosts(widget.user)
              ? 'Share your knowledge and experiences with your agency community!'
              : 'Upgrade to premium to create posts and share your experiences.',
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: myPosts.length,
          itemBuilder: (context, index) {
            return _buildPostCard(myPosts[index]);
          },
        );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.forum_outlined, size: 80, color: Colors.grey[300]),
            const SizedBox(height: 24),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryDark,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryLight,
              ),
              textAlign: TextAlign.center,
            ),
            if (!PremiumService().canCreateCommunityPosts(widget.user)) ...[
              const SizedBox(height: 24),
              BaseButton(
                text: 'Upgrade to Premium',
                icon: Icons.workspace_premium,
                backgroundColor: const Color(0xFF9C27B0),
                onPressed: () {
                  Navigator.of(context).pushNamed(AppRoutes.premium);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPostCard(PostModel post) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: post.color.withValues(alpha: 0.1 * 255),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              _navigateToPostDetail(post);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Post header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      // User avatar
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: post.color.withValues(
                          alpha: 0.2 * 255,
                        ),
                        backgroundImage:
                            post.userProfileImageUrl != null
                                ? NetworkImage(post.userProfileImageUrl!)
                                : null,
                        child:
                            post.userProfileImageUrl == null
                                ? Text(
                                  post.userName.substring(0, 1).toUpperCase(),
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: post.color,
                                  ),
                                )
                                : null,
                      ),
                      const SizedBox(width: 12),
                      // User name and post info
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              post.userName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 15,
                                letterSpacing: 0.2,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getAgencyColor(
                                      post.agency,
                                    ).withValues(alpha: 0.1 * 255),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    post.agency,
                                    style: TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: _getAgencyColor(post.agency),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _formatTimeAgo(post.createdAt),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // More options
                      IconButton(
                        icon: const Icon(Icons.more_vert, size: 20),
                        onPressed: () {
                          _showPostOptions(post);
                        },
                      ),
                    ],
                  ),
                ),
                // Post title
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Text(
                    post.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      letterSpacing: 0.3,
                      color: Colors.black87,
                    ),
                  ),
                ),
                // Post content
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post.content,
                        style: const TextStyle(
                          fontSize: 15,
                          height: 1.4,
                          color: Colors.black87,
                          letterSpacing: 0.2,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (post.content.length > 150) ...[
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () => _navigateToPostDetail(post),
                          child: Text(
                            'Read more',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                // Post images (Facebook-style multiple image display)
                if (post.imageUrls.isNotEmpty)
                  _buildImageGrid(post.imageUrls)
                else if (post.imageUrl != null)
                  Container(
                    height: 200,
                    width: double.infinity,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(post.imageUrl!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                // Tags
                if (post.tags.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          post.tags.map((tag) {
                            return Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.grey[300]!,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '#$tag',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[700],
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ),
                // Post actions with enhanced agency community feel
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    border: Border(
                      top: BorderSide(color: Colors.grey[200]!, width: 1),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Engagement stats
                      if (post.likesCount > 0 || post.commentsCount > 0)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: Row(
                            children: [
                              if (post.likesCount > 0) ...[
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.red.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.favorite,
                                        size: 14,
                                        color: Colors.red,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${post.likesCount}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.red,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                              if (post.commentsCount > 0) ...[
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.chat_bubble,
                                        size: 14,
                                        color: Colors.blue,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${post.commentsCount}',
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                              const Spacer(),
                              // Agency indicator
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: _getAgencyColor(
                                    post.agency,
                                  ).withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: _getAgencyColor(
                                      post.agency,
                                    ).withOpacity(0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.group,
                                      size: 12,
                                      color: _getAgencyColor(post.agency),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Same Agency',
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: _getAgencyColor(post.agency),
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      // Action buttons
                      Row(
                        children: [
                          // Like button
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                _toggleLike(post);
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      post.isLikedByCurrentUser
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      size: 20,
                                      color:
                                          post.isLikedByCurrentUser
                                              ? Colors.red
                                              : Colors.grey[600],
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Like',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color:
                                            post.isLikedByCurrentUser
                                                ? Colors.red
                                                : Colors.grey[600],
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Comment button
                          Expanded(
                            child: InkWell(
                              onTap:
                                  PremiumService().canCreateCommunityPosts(
                                        widget.user,
                                      )
                                      ? () => _navigateToPostDetail(post)
                                      : null,
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.chat_bubble_outline,
                                      size: 20,
                                      color:
                                          PremiumService()
                                                  .canCreateCommunityPosts(
                                                    widget.user,
                                                  )
                                              ? Colors.grey[600]
                                              : Colors.grey[400],
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Comment',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color:
                                            PremiumService()
                                                    .canCreateCommunityPosts(
                                                      widget.user,
                                                    )
                                                ? Colors.grey[600]
                                                : Colors.grey[400],
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          // Share button
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                _sharePost(post);
                              },
                              borderRadius: BorderRadius.circular(8),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.share,
                                      size: 20,
                                      color: Colors.grey[600],
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      'Share',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAgencyCard({
    required String agency,
    required int postsCount,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white,
            Color.lerp(Colors.white, color, 0.05) ?? Colors.white,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.1 * 255), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            _navigateToAgencyForum(agency);
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Agency icon
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.15 * 255),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: color.withValues(alpha: 0.3 * 255),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.05 * 255),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: color.withValues(alpha: 0.8 * 255),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                // Agency info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        agency,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$postsCount posts',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                // Arrow icon
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCreatePostOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'Create New Post',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),

              // Text Post Option
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.text_fields, color: Colors.blue),
                ),
                title: const Text('Text Post'),
                subtitle: const Text('Share your thoughts and experiences'),
                onTap: () {
                  Navigator.pop(context);
                  _showCreatePostDialog();
                },
              ),

              // Photo Post Option (Premium)
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color:
                        PremiumService().canCreatePhotoPosts(widget.user)
                            ? Colors.green.withOpacity(0.1)
                            : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.photo_camera,
                    color:
                        PremiumService().canCreatePhotoPosts(widget.user)
                            ? Colors.green
                            : Colors.grey,
                  ),
                ),
                title: Row(
                  children: [
                    const Text('Photo Post'),
                    if (!PremiumService().canCreatePhotoPosts(widget.user)) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Colors.purple, Colors.blue],
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'PREMIUM',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                subtitle: Text(
                  PremiumService().canCreatePhotoPosts(widget.user)
                      ? 'Share photos with your posts'
                      : 'Upgrade to premium to share photos',
                ),
                enabled: PremiumService().canCreatePhotoPosts(widget.user),
                onTap: () {
                  Navigator.pop(context);
                  if (PremiumService().canCreatePhotoPosts(widget.user)) {
                    _openPhotoPostCreator();
                  }
                },
              ),

              const SizedBox(height: 10),
            ],
          ),
        );
      },
    );
  }

  void _openPhotoPostCreator() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => PhotoPostCreator(
              user: widget.user,
              agency: widget.user.targetAgency,
              onPostCreated: (title, content, imagePaths) {
                _createPhotoPost(title, content, imagePaths);
              },
            ),
      ),
    );
  }

  void _createPhotoPost(String title, String content, List<String> imagePaths) {
    setState(() {
      _posts.insert(
        0,
        PostModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          createdAt: DateTime.now(),
          userId: widget.user.id,
          userName: widget.user.fullName,
          userProfileImageUrl: widget.user.profileImageUrl,
          title: title,
          content: content,
          agency: widget.user.targetAgency,
          tags: ['Photo Post'],
          likesCount: 0,
          commentsCount: 0,
          isLikedByCurrentUser: false,
          imageUrls: imagePaths,
          postType: 'image',
          isPremiumContent: true,
          icon: _getAgencyIcon(widget.user.targetAgency),
          color: _getAgencyColor(widget.user.targetAgency),
        ),
      );
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Photo post created successfully!')),
    );
  }

  void _showCreatePostDialog() {
    final titleController = TextEditingController();
    final contentController = TextEditingController();
    String selectedAgency = widget.user.targetAgency;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Create Post'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    hintText: 'Enter post title...',
                  ),
                  maxLength: 100,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: contentController,
                  decoration: const InputDecoration(
                    labelText: 'Content',
                    hintText: 'Share your thoughts...',
                    alignLabelWithHint: true,
                  ),
                  maxLines: 5,
                  maxLength: 1000,
                ),
                const SizedBox(height: 16),
                // Agency selection - disabled since posts are agency-specific
                TextFormField(
                  enabled: false,
                  initialValue: selectedAgency,
                  decoration: InputDecoration(
                    labelText: 'Agency',
                    suffixIcon: Icon(
                      Icons.lock_outline,
                      color: Colors.grey[400],
                    ),
                    helperText: 'Posts are specific to your target agency',
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () async {
                // Create post
                if (titleController.text.isNotEmpty &&
                    contentController.text.isNotEmpty) {
                  // Add post to the list
                  setState(() {
                    _posts.insert(
                      0,
                      PostModel(
                        id: DateTime.now().millisecondsSinceEpoch.toString(),
                        createdAt: DateTime.now(),
                        userId: widget.user.id,
                        userName: widget.user.fullName,
                        userProfileImageUrl: widget.user.profileImageUrl,
                        title: titleController.text,
                        content: contentController.text,
                        agency: selectedAgency,
                        tags: [],
                        likesCount: 0,
                        commentsCount: 0,
                        isLikedByCurrentUser: false,
                        icon: _getAgencyIcon(selectedAgency),
                        color: _getAgencyColor(selectedAgency),
                      ),
                    );
                  });

                  // Update community progress
                  await _progressService.incrementValue(
                    'community',
                    'postsCreated',
                  );

                  // Check for achievements
                  final newPostsCreated =
                      (_communityProgress['postsCreated'] ?? 0) + 1;
                  if (newPostsCreated == 1) {
                    await _progressService.addAchievement(
                      'community',
                      'First Post Created',
                    );
                  } else if (newPostsCreated == 10) {
                    await _progressService.addAchievement(
                      'community',
                      'Community Contributor - 10 Posts',
                    );
                  } else if (newPostsCreated == 50) {
                    await _progressService.addAchievement(
                      'community',
                      'Community Leader - 50 Posts',
                    );
                  }

                  // Reload progress
                  await _loadCommunityProgress();

                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post created successfully!')),
                  );
                }
              },
              child: const Text('POST'),
            ),
          ],
        );
      },
    );
  }

  void _navigateToPostDetail(PostModel post) {
    // Navigate to Facebook-style post detail screen with comments
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PostDetailScreen(post: post, user: widget.user),
      ),
    );
  }

  void _navigateToAgencyForum(String agency) {
    // Navigate to agency forum screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => AgencyForumScreen(
              user: widget.user,
              agency: agency,
              color: _getAgencyColor(agency),
              icon: _getAgencyIcon(agency),
            ),
      ),
    );
  }

  void _toggleLike(PostModel post) async {
    final wasLiked = post.isLikedByCurrentUser;

    setState(() {
      final index = _posts.indexWhere((p) => p.id == post.id);
      if (index != -1) {
        final updatedPost = post.copyWith(
          isLikedByCurrentUser: !post.isLikedByCurrentUser,
          likesCount:
              post.isLikedByCurrentUser
                  ? post.likesCount - 1
                  : post.likesCount + 1,
        );
        _posts[index] = updatedPost;
      }
    });

    // Handle progress tracking outside setState
    if (!wasLiked) {
      // User is giving a like
      await _progressService.incrementValue('community', 'likesGiven');

      // If this is the current user's own post receiving a like (from another user perspective)
      if (post.userId == widget.user.id) {
        await _progressService.incrementValue('community', 'likesReceived');

        // Check for likes received achievements
        final newLikesReceived = (_communityProgress['likesReceived'] ?? 0) + 1;
        if (newLikesReceived == 1) {
          await _progressService.addAchievement(
            'community',
            'First Like Received',
          );
        } else if (newLikesReceived == 10) {
          await _progressService.addAchievement(
            'community',
            'Popular Post - 10 Likes',
          );
        } else if (newLikesReceived == 50) {
          await _progressService.addAchievement(
            'community',
            'Community Star - 50 Likes',
          );
        }
      }

      // Check for likes given achievements
      final newLikesGiven = (_communityProgress['likesGiven'] ?? 0) + 1;
      if (newLikesGiven == 1) {
        await _progressService.addAchievement('community', 'First Like Given');
      } else if (newLikesGiven == 25) {
        await _progressService.addAchievement(
          'community',
          'Supportive Community Member - 25 Likes Given',
        );
      }
    } else {
      // User is removing a like
      final currentLikesGiven = (_communityProgress['likesGiven'] ?? 0);
      if (currentLikesGiven > 0) {
        await _progressService.updateProgress('community', {
          'likesGiven': currentLikesGiven - 1,
        });
      }
    }

    // Reload progress to update UI
    await _loadCommunityProgress();
  }

  void _sharePost(PostModel post) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Sharing post: ${post.title}')));
  }

  void _savePost(PostModel post) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Post saved: ${post.title}')));
  }

  void _showPostOptions(PostModel post) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.bookmark_border),
                title: const Text('Save Post'),
                onTap: () {
                  Navigator.pop(context);
                  _savePost(post);
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('Share Post'),
                onTap: () {
                  Navigator.pop(context);
                  _sharePost(post);
                },
              ),
              if (post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Edit Post'),
                  onTap: () {
                    Navigator.pop(context);
                    // Show edit post dialog
                  },
                ),
              if (post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'Delete Post',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _showDeleteConfirmation(post);
                  },
                ),
              ListTile(
                leading: const Icon(Icons.flag),
                title: const Text('Report Post'),
                onTap: () {
                  Navigator.pop(context);
                  // Show report dialog
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _showDeleteConfirmation(PostModel post) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Delete Post'),
          content: const Text(
            'Are you sure you want to delete this post? This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () {
                // Delete post
                setState(() {
                  _posts.removeWhere((p) => p.id == post.id);
                });
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Post deleted successfully!')),
                );
              },
              child: const Text('DELETE', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.lightBlue;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData _getAgencyIcon(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Navy':
        return Icons.sailing;
      case 'Air Force':
        return Icons.airplanemode_active;
      case 'DSSC':
        return Icons.school;
      case 'NDA':
        return Icons.shield;
      case 'NSCDC':
        return Icons.security;
      case 'EFCC':
        return Icons.gavel;
      default:
        return Icons.group;
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _getShortAgencyName(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return 'Army\nCommunity';
      case 'Nigerian Navy':
        return 'Navy\nCommunity';
      case 'Nigerian Air Force':
        return 'Air Force\nCommunity';
      case 'DSSC':
        return 'DSSC\nCommunity';
      case 'NDA':
        return 'NDA\nCommunity';
      case 'NSCDC':
        return 'NSCDC\nCommunity';
      case 'EFCC':
        return 'EFCC\nCommunity';
      default:
        return '$agency\nCommunity';
    }
  }

  String _getAgencyAspiriantName(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return 'army';
      case 'Nigerian Navy':
        return 'navy';
      case 'Nigerian Air Force':
        return 'air force';
      case 'DSSC':
        return 'DSSC';
      case 'NDA':
        return 'NDA';
      case 'NSCDC':
        return 'NSCDC';
      case 'EFCC':
        return 'EFCC';
      default:
        return agency.toLowerCase();
    }
  }

  IconData _getQuickActionIcon(int index) {
    switch (index) {
      case 0:
        return Icons.forum;
      case 1:
        return Icons.people;
      case 2:
        return Icons.emoji_events;
      case 3:
        return Icons.star;
      default:
        return Icons.circle;
    }
  }

  // Facebook-style image grid for multiple images
  Widget _buildImageGrid(List<String> imageUrls) {
    final imageCount = imageUrls.length;

    if (imageCount == 1) {
      // Single image - full width
      return Container(
        height: 300,
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: AssetImage(imageUrls[0]),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else if (imageCount == 2) {
      // Two images - side by side
      return Container(
        height: 250,
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                  image: DecorationImage(
                    image: AssetImage(imageUrls[0]),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                  image: DecorationImage(
                    image: AssetImage(imageUrls[1]),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    } else if (imageCount == 3) {
      // Three images - first large, two small on right
      return Container(
        height: 250,
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                  image: DecorationImage(
                    image: AssetImage(imageUrls[0]),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(8),
                        ),
                        image: DecorationImage(
                          image: AssetImage(imageUrls[1]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(8),
                        ),
                        image: DecorationImage(
                          image: AssetImage(imageUrls[2]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      // Four or more images - 2x2 grid with overflow indicator
      return Container(
        height: 250,
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                        ),
                        image: DecorationImage(
                          image: AssetImage(imageUrls[0]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(8),
                        ),
                        image: DecorationImage(
                          image: AssetImage(imageUrls[1]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 2),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(8),
                        ),
                        image: DecorationImage(
                          image: AssetImage(imageUrls[2]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.only(
                              bottomRight: Radius.circular(8),
                            ),
                            image: DecorationImage(
                              image: AssetImage(imageUrls[3]),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        if (imageCount > 4)
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                bottomRight: Radius.circular(8),
                              ),
                              color: Colors.black54,
                            ),
                            child: Center(
                              child: Text(
                                '+${imageCount - 4}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }
}
