import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/nigerian_exam_pattern_model.dart';
import '../data/nigerian_questions_database.dart';
import '../data/nigerian_exam_patterns.dart';

/// Enhanced Quiz Service with Nigerian Exam Patterns and Adaptive Questioning
class EnhancedQuizService extends ChangeNotifier {
  // Current quiz state
  List<NigerianQuestionModel> _questions = [];
  int _currentQuestionIndex = 0;
  final Map<int, int> _userAnswers = {};
  final Map<int, double> _answerTimes = {};
  Timer? _quizTimer;
  Timer? _questionTimer;
  
  // Quiz configuration
  late NigerianExamPatternModel _examPattern;
  late QuizMode _quizMode;
  late String _agencyCode;
  late String _category;
  
  // Adaptive questioning
  AdaptiveQuestioningModel? _adaptiveModel;
  int _consecutiveCorrect = 0;
  int _consecutiveIncorrect = 0;
  DifficultyLevel _currentDifficulty = DifficultyLevel.intermediate;
  
  // Performance tracking
  final Map<String, TopicPerformanceModel> _topicPerformance = {};
  DateTime? _quizStartTime;
  DateTime? _questionStartTime;
  
  // Getters
  List<NigerianQuestionModel> get questions => _questions;
  int get currentQuestionIndex => _currentQuestionIndex;
  NigerianQuestionModel? get currentQuestion => 
    _currentQuestionIndex < _questions.length ? _questions[_currentQuestionIndex] : null;
  Map<int, int> get userAnswers => _userAnswers;
  NigerianExamPatternModel get examPattern => _examPattern;
  QuizMode get quizMode => _quizMode;
  bool get isQuizCompleted => _currentQuestionIndex >= _questions.length;
  int get totalQuestions => _questions.length;
  int get answeredQuestions => _userAnswers.length;
  
  // Time tracking
  int _remainingTime = 0;
  int get remainingTime => _remainingTime;
  double get questionTimeElapsed => _questionStartTime != null 
    ? DateTime.now().difference(_questionStartTime!).inSeconds.toDouble()
    : 0.0;

  /// Initialize quiz with Nigerian exam pattern
  Future<void> initializeQuiz({
    required String agencyCode,
    required String category,
    required QuizMode mode,
    int? questionCount,
    DifficultyLevel? initialDifficulty,
  }) async {
    _agencyCode = agencyCode;
    _category = category;
    _quizMode = mode;
    _currentDifficulty = initialDifficulty ?? DifficultyLevel.intermediate;
    
    // Get exam pattern for agency
    _examPattern = NigerianExamPatterns.getExamPattern(agencyCode);
    
    // Load questions based on mode
    await _loadQuestions(questionCount);
    
    // Initialize adaptive model if needed
    if (mode == QuizMode.adaptive) {
      _initializeAdaptiveModel();
    }
    
    // Set up timing
    _setupQuizTiming();
    
    _quizStartTime = DateTime.now();
    _startQuestionTimer();
    
    notifyListeners();
  }

  /// Load questions based on quiz configuration
  Future<void> _loadQuestions(int? questionCount) async {
    if (_quizMode == QuizMode.adaptive) {
      // For adaptive mode, load questions dynamically
      _questions = [];
      await _loadNextAdaptiveQuestion();
    } else {
      // Load all questions for the category
      _questions = NigerianQuestionsDatabase.getQuestions(
        agencyCode: _agencyCode,
        category: _category,
        difficulty: _quizMode == QuizMode.practice ? null : _currentDifficulty,
        limit: questionCount ?? _getDefaultQuestionCount(),
      );
    }
  }

  /// Get default question count based on exam pattern
  int _getDefaultQuestionCount() {
    final section = _examPattern.sections.firstWhere(
      (s) => s.name.toLowerCase() == _category.toLowerCase(),
      orElse: () => _examPattern.sections.first,
    );
    return section.questionCount;
  }

  /// Setup quiz timing based on mode and pattern
  void _setupQuizTiming() {
    if (_quizMode == QuizMode.timed) {
      final section = _examPattern.sections.firstWhere(
        (s) => s.name.toLowerCase() == _category.toLowerCase(),
        orElse: () => _examPattern.sections.first,
      );
      _remainingTime = section.timeAllocation * 60; // Convert to seconds
      _startQuizTimer();
    }
  }

  /// Start quiz timer for timed mode
  void _startQuizTimer() {
    _quizTimer?.cancel();
    _quizTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_remainingTime > 0) {
        _remainingTime--;
        notifyListeners();
      } else {
        _timeUp();
      }
    });
  }

  /// Start question timer for tracking answer time
  void _startQuestionTimer() {
    _questionStartTime = DateTime.now();
  }

  /// Handle time up scenario
  void _timeUp() {
    _quizTimer?.cancel();
    _questionTimer?.cancel();
    // Auto-submit quiz
    _completeQuiz();
  }

  /// Answer current question
  void answerQuestion(int selectedOption) {
    if (isQuizCompleted || currentQuestion == null) return;
    
    final question = currentQuestion!;
    final answerTime = questionTimeElapsed;
    
    // Store answer and time
    _userAnswers[_currentQuestionIndex] = selectedOption;
    _answerTimes[_currentQuestionIndex] = answerTime;
    
    // Check if answer is correct
    final isCorrect = selectedOption == question.correctAnswer;
    
    // Update adaptive model
    if (_quizMode == QuizMode.adaptive) {
      _updateAdaptiveModel(isCorrect);
    }
    
    // Update topic performance
    _updateTopicPerformance(question, isCorrect, answerTime);
    
    // Provide immediate feedback for practice mode
    if (_quizMode == QuizMode.practice) {
      _showImmediateFeedback(question, isCorrect);
    }
    
    notifyListeners();
  }

  /// Move to next question
  Future<void> nextQuestion() async {
    if (_quizMode == QuizMode.adaptive) {
      await _loadNextAdaptiveQuestion();
    } else {
      _currentQuestionIndex++;
    }
    
    if (!isQuizCompleted) {
      _startQuestionTimer();
    } else {
      _completeQuiz();
    }
    
    notifyListeners();
  }

  /// Load next question for adaptive mode
  Future<void> _loadNextAdaptiveQuestion() async {
    if (_adaptiveModel == null) return;
    
    // Determine next difficulty
    final nextDifficulty = _adaptiveModel!.getNextDifficulty();
    
    // Get weak topics to focus on
    final weakTopics = _adaptiveModel!.getWeakTopics();
    final targetCategory = weakTopics.isNotEmpty ? weakTopics.first : _category;
    
    // Load next question
    final nextQuestions = NigerianQuestionsDatabase.getQuestions(
      agencyCode: _agencyCode,
      category: targetCategory,
      difficulty: nextDifficulty,
      limit: 1,
    );
    
    if (nextQuestions.isNotEmpty) {
      _questions.add(nextQuestions.first);
      _currentQuestionIndex = _questions.length - 1;
    } else {
      // No more questions available, end quiz
      _completeQuiz();
    }
  }

  /// Initialize adaptive questioning model
  void _initializeAdaptiveModel() {
    _adaptiveModel = AdaptiveQuestioningModel(
      userId: 'current_user', // Replace with actual user ID
      agencyCode: _agencyCode,
      topicMastery: {},
      difficultyPerformance: {},
      consecutiveCorrect: 0,
      consecutiveIncorrect: 0,
      currentLevel: _currentDifficulty,
      recentTopics: [],
    );
  }

  /// Update adaptive model based on answer
  void _updateAdaptiveModel(bool isCorrect) {
    if (isCorrect) {
      _consecutiveCorrect++;
      _consecutiveIncorrect = 0;
    } else {
      _consecutiveIncorrect++;
      _consecutiveCorrect = 0;
    }
    
    // Update current difficulty based on performance
    _currentDifficulty = _adaptiveModel!.getNextDifficulty();
  }

  /// Update topic performance tracking
  void _updateTopicPerformance(
    NigerianQuestionModel question, 
    bool isCorrect, 
    double answerTime
  ) {
    final topicKey = '${question.category}_${question.agencyCode}';
    
    if (!_topicPerformance.containsKey(topicKey)) {
      _topicPerformance[topicKey] = TopicPerformanceModel(
        topicName: question.category,
        category: question.category,
        totalQuestions: 0,
        correctAnswers: 0,
        incorrectAnswers: 0,
        averageTime: 0.0,
        masteryLevel: 0.0,
        weakAreas: [],
        strongAreas: [],
        lastPracticed: DateTime.now(),
        practiceCount: 0,
      );
    }
    
    final current = _topicPerformance[topicKey]!;
    final newTotal = current.totalQuestions + 1;
    final newCorrect = current.correctAnswers + (isCorrect ? 1 : 0);
    final newIncorrect = current.incorrectAnswers + (isCorrect ? 0 : 1);
    final newAverageTime = ((current.averageTime * current.totalQuestions) + answerTime) / newTotal;
    final newMastery = newCorrect / newTotal;
    
    _topicPerformance[topicKey] = TopicPerformanceModel(
      topicName: current.topicName,
      category: current.category,
      totalQuestions: newTotal,
      correctAnswers: newCorrect,
      incorrectAnswers: newIncorrect,
      averageTime: newAverageTime,
      masteryLevel: newMastery,
      weakAreas: newMastery < 0.7 ? [question.category] : [],
      strongAreas: newMastery >= 0.8 ? [question.category] : [],
      lastPracticed: DateTime.now(),
      practiceCount: current.practiceCount + 1,
    );
  }

  /// Show immediate feedback for practice mode
  void _showImmediateFeedback(NigerianQuestionModel question, bool isCorrect) {
    // This would trigger UI feedback
    // Implementation depends on UI framework
  }

  /// Complete the quiz and calculate results
  void _completeQuiz() {
    _quizTimer?.cancel();
    _questionTimer?.cancel();
    
    // Calculate final results
    final results = _calculateResults();
    
    // Save performance data
    _savePerformanceData(results);
    
    notifyListeners();
  }

  /// Calculate quiz results
  Map<String, dynamic> _calculateResults() {
    int correctAnswers = 0;
    double totalTime = 0.0;
    
    for (int i = 0; i < _questions.length; i++) {
      if (_userAnswers.containsKey(i)) {
        if (_userAnswers[i] == _questions[i].correctAnswer) {
          correctAnswers++;
        }
        totalTime += _answerTimes[i] ?? 0.0;
      }
    }
    
    final accuracy = answeredQuestions > 0 ? correctAnswers / answeredQuestions : 0.0;
    final averageTime = answeredQuestions > 0 ? totalTime / answeredQuestions : 0.0;
    final passed = correctAnswers >= _examPattern.passingScore;
    
    return {
      'total_questions': totalQuestions,
      'answered_questions': answeredQuestions,
      'correct_answers': correctAnswers,
      'accuracy': accuracy,
      'average_time': averageTime,
      'total_time': totalTime,
      'passed': passed,
      'passing_score': _examPattern.passingScore,
      'topic_performance': _topicPerformance,
    };
  }

  /// Save performance data for analytics
  void _savePerformanceData(Map<String, dynamic> results) {
    // Implementation would save to local storage or backend
    // This data can be used for progress tracking and recommendations
  }

  /// Get detailed explanation for a question
  String getQuestionExplanation(int questionIndex) {
    if (questionIndex < _questions.length) {
      return _questions[questionIndex].explanation;
    }
    return '';
  }

  /// Get performance analytics
  Map<String, TopicPerformanceModel> getTopicPerformance() {
    return Map.from(_topicPerformance);
  }

  /// Reset quiz state
  void resetQuiz() {
    _questions.clear();
    _currentQuestionIndex = 0;
    _userAnswers.clear();
    _answerTimes.clear();
    _topicPerformance.clear();
    _quizTimer?.cancel();
    _questionTimer?.cancel();
    _remainingTime = 0;
    _consecutiveCorrect = 0;
    _consecutiveIncorrect = 0;
    _currentDifficulty = DifficultyLevel.intermediate;
    
    notifyListeners();
  }

  @override
  void dispose() {
    _quizTimer?.cancel();
    _questionTimer?.cancel();
    super.dispose();
  }
}
