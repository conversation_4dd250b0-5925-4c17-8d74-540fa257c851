import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/user_progress_service.dart';
import '../../../core/widgets/user_progress_widgets.dart';
import '../models/quiz_question_model.dart';

class QuizResultScreen extends StatefulWidget {
  final QuizSessionModel session;
  final List<QuizQuestionModel> questions;

  const QuizResultScreen({
    super.key,
    required this.session,
    required this.questions,
  });

  @override
  State<QuizResultScreen> createState() => _QuizResultScreenState();
}

class _QuizResultScreenState extends State<QuizResultScreen> {
  final UserProgressService _progressService = UserProgressService();
  Map<String, dynamic> _userProgress = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProgress();
  }

  Future<void> _loadUserProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _userProgress = progress;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final scorePercentage = widget.session.scorePercentage;
    final isPassed = widget.session.isPassed;

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Quiz Results'),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final academicsData = _userProgress['academics'] ?? {};

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quiz Results'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed:
                () => Navigator.of(context).popUntil((route) => route.isFirst),
            icon: const Icon(Icons.home),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Score Card
            Card(
              elevation: 4,
              color: isPassed ? Colors.green.shade50 : Colors.red.shade50,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    Icon(
                      isPassed
                          ? Icons.celebration
                          : Icons.sentiment_dissatisfied,
                      size: 64,
                      color: isPassed ? Colors.green : Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      isPassed ? 'Congratulations!' : 'Keep Practicing!',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color:
                            isPassed
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${scorePercentage.toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color:
                            isPassed
                                ? Colors.green.shade700
                                : Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Grade: ${widget.session.grade}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color:
                            isPassed
                                ? Colors.green.shade700
                                : Colors.red.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Progress Statistics from UserProgressService
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Your Progress',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ProgressStatsCard(
                            title: 'Total Quizzes',
                            value: academicsData['totalQuizzesCompleted'] ?? 0,
                            icon: Icons.quiz,
                            color: AppTheme.primaryColor,
                            emptyStateMessage: 'First quiz completed!',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ProgressStatsCard(
                            title: 'Average Score',
                            value:
                                '${((academicsData['averageQuizScore'] ?? 0.0) as double).toStringAsFixed(1)}%',
                            icon: Icons.grade,
                            color: _getScoreColor(
                              (academicsData['averageQuizScore'] ?? 0.0)
                                  as double,
                            ),
                            emptyStateMessage: 'Building average...',
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ProgressStatsCard(
                            title: 'Study Hours',
                            value: academicsData['totalStudyHours'] ?? 0,
                            icon: Icons.schedule,
                            color: Colors.purple,
                            emptyStateMessage: 'Time to study!',
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ProgressStatsCard(
                            title: 'Readiness',
                            value:
                                '${((academicsData['readiness'] ?? 0.0) as double).toStringAsFixed(1)}%',
                            icon: Icons.trending_up,
                            color: _getReadinessColor(
                              (academicsData['readiness'] ?? 0.0) as double,
                            ),
                            emptyStateMessage: 'Getting ready...',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Achievements
            if ((academicsData['achievements'] as List?)?.isNotEmpty ??
                false) ...[
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Recent Achievements',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      AchievementProgressWidget(
                        achievements: List<String>.from(
                          academicsData['achievements'] ?? [],
                        ),
                        category: 'academics',
                        maxDisplay: 3,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),
            ],

            // Statistics
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Quiz Statistics',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildStatRow(
                      'Total Questions',
                      '${widget.session.totalQuestions}',
                    ),
                    _buildStatRow(
                      'Correct Answers',
                      '${widget.session.correctAnswers}',
                    ),
                    _buildStatRow(
                      'Incorrect Answers',
                      '${widget.session.totalQuestions - widget.session.correctAnswers}',
                    ),
                    _buildStatRow(
                      'Time Spent',
                      _formatTime(widget.session.totalTimeSpent),
                    ),
                    _buildStatRow(
                      'Average Time per Question',
                      _formatTime(
                        widget.session.totalTimeSpent ~/
                            widget.session.totalQuestions,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Performance Analysis
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Performance Analysis',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildPerformanceAnalysis(),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Question Review
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Question Review',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widget.questions.length,
                      itemBuilder: (context, index) {
                        return _buildQuestionReview(index);
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        () => Navigator.of(
                          context,
                        ).popUntil((route) => route.isFirst),
                    icon: const Icon(Icons.home),
                    label: const Text('Back to Dashboard'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      // Navigate to retake quiz
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retake Quiz'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppTheme.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  // Helper methods for colors
  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  Color _getReadinessColor(double readiness) {
    if (readiness >= 80) return Colors.green;
    if (readiness >= 60) return Colors.orange;
    return Colors.red;
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceAnalysis() {
    final categoryPerformance = <String, Map<String, int>>{};

    // Analyze performance by category
    for (int i = 0; i < widget.questions.length; i++) {
      final question = widget.questions[i];
      final answer = widget.session.answers[i];

      categoryPerformance[question.category] ??= {'correct': 0, 'total': 0};
      categoryPerformance[question.category]!['total'] =
          categoryPerformance[question.category]!['total']! + 1;

      if (answer.isCorrect) {
        categoryPerformance[question.category]!['correct'] =
            categoryPerformance[question.category]!['correct']! + 1;
      }
    }

    return Column(
      children:
          categoryPerformance.entries.map((entry) {
            final category = entry.key;
            final correct = entry.value['correct']!;
            final total = entry.value['total']!;
            final percentage = (correct / total) * 100;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        category,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        '$correct/$total (${percentage.toStringAsFixed(1)}%)',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color:
                              percentage >= 70 ? Colors.green : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: percentage / 100,
                    backgroundColor: Colors.grey.shade300,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      percentage >= 70 ? Colors.green : Colors.orange,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
    );
  }

  Widget _buildQuestionReview(int index) {
    final question = widget.questions[index];
    final answer = widget.session.answers[index];
    final isCorrect = answer.isCorrect;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(
          color: isCorrect ? Colors.green.shade300 : Colors.red.shade300,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(12),
        color: isCorrect ? Colors.green.shade50 : Colors.red.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isCorrect ? Icons.check_circle : Icons.cancel,
                color: isCorrect ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Question ${index + 1}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color:
                      isCorrect ? Colors.green.shade800 : Colors.red.shade800,
                ),
              ),
              const Spacer(),
              Text(
                '${answer.timeSpent}s',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            question.question,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          if (answer.selectedAnswerIndex >= 0) ...[
            Text(
              'Your Answer: ${question.options[answer.selectedAnswerIndex]}',
              style: TextStyle(
                fontSize: 14,
                color: isCorrect ? Colors.green.shade700 : Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else ...[
            const Text(
              'Your Answer: No answer (Time expired)',
              style: TextStyle(
                fontSize: 14,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          if (!isCorrect) ...[
            const SizedBox(height: 4),
            Text(
              'Correct Answer: ${question.correctAnswer}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}m ${remainingSeconds}s';
  }
}
