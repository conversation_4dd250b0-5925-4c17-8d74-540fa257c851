import 'package:flutter/material.dart';
import '../models/onboarding_page_model.dart';

class OnboardingPage extends StatefulWidget {
  final OnboardingPageModel page;
  final Animation<double> fadeAnimation;
  final Animation<Offset> slideAnimation;
  final Animation<double> logoAnimation;
  final bool isFirstPage;
  final bool isSmallScreen;

  const OnboardingPage({
    super.key,
    required this.page,
    required this.fadeAnimation,
    required this.slideAnimation,
    required this.logoAnimation,
    required this.isFirstPage,
    required this.isSmallScreen,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  late AnimationController _textController;
  late AnimationController _imageController;
  late Animation<double> _textAnimation;
  late Animation<double> _imageAnimation;

  @override
  void initState() {
    super.initState();
    
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _imageController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: const Interval(0.3, 1.0, curve: Curves.easeOut),
    ));

    _imageAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _imageController,
      curve: Curves.elasticOut,
    ));

    // Start animations with delay
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _imageController.forward();
      }
    });
    
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _imageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: widget.isSmallScreen ? 20.0 : 32.0,
        vertical: widget.isSmallScreen ? 16.0 : 24.0,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image/Logo section
          Expanded(
            flex: 3,
            child: Center(
              child: AnimatedBuilder(
                animation: widget.isFirstPage ? widget.logoAnimation : _imageAnimation,
                builder: (context, child) {
                  final animation = widget.isFirstPage ? widget.logoAnimation : _imageAnimation;
                  return Transform.scale(
                    scale: animation.value,
                    child: FadeTransition(
                      opacity: animation,
                      child: _buildImageSection(screenWidth),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Content section
          Expanded(
            flex: 2,
            child: SlideTransition(
              position: widget.slideAnimation,
              child: FadeTransition(
                opacity: widget.fadeAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Title
                    AnimatedBuilder(
                      animation: _textAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, 20 * (1 - _textAnimation.value)),
                          child: Opacity(
                            opacity: _textAnimation.value,
                            child: _buildTitle(),
                          ),
                        );
                      },
                    ),
                    
                    SizedBox(height: widget.isSmallScreen ? 12 : 16),
                    
                    // Subtitle
                    AnimatedBuilder(
                      animation: _textAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, 30 * (1 - _textAnimation.value)),
                          child: Opacity(
                            opacity: _textAnimation.value * 0.9,
                            child: _buildSubtitle(),
                          ),
                        );
                      },
                    ),
                    
                    SizedBox(height: widget.isSmallScreen ? 16 : 24),
                    
                    // Description
                    AnimatedBuilder(
                      animation: _textAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, 40 * (1 - _textAnimation.value)),
                          child: Opacity(
                            opacity: _textAnimation.value * 0.8,
                            child: _buildDescription(),
                          ),
                        );
                      },
                    ),
                    
                    // Bullet points (if available)
                    if (widget.page.bulletPoints != null) ...[
                      SizedBox(height: widget.isSmallScreen ? 16 : 20),
                      AnimatedBuilder(
                        animation: _textAnimation,
                        builder: (context, child) {
                          return Transform.translate(
                            offset: Offset(0, 50 * (1 - _textAnimation.value)),
                            child: Opacity(
                              opacity: _textAnimation.value * 0.7,
                              child: _buildBulletPoints(),
                            ),
                          );
                        },
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(double screenWidth) {
    final imageSize = widget.isSmallScreen 
        ? screenWidth * 0.6 
        : screenWidth * 0.5;
    
    if (widget.isFirstPage) {
      // Special treatment for logo on first page
      return Container(
        width: imageSize,
        height: imageSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: RadialGradient(
            colors: [
              Colors.white.withValues(alpha: 0.2 * 255),
              Colors.transparent,
            ],
          ),
        ),
        child: Center(
          child: Container(
            width: imageSize * 0.7,
            height: imageSize * 0.7,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2 * 255),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipOval(
              child: Image.asset(
                widget.page.imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    Icons.shield,
                    size: imageSize * 0.4,
                    color: widget.page.backgroundColor,
                  );
                },
              ),
            ),
          ),
        ),
      );
    } else {
      // Regular image for other pages
      return Container(
        width: imageSize,
        height: imageSize * 0.8,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2 * 255),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Image.asset(
            widget.page.imagePath,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      widget.page.accentColor,
                      widget.page.backgroundColor,
                    ],
                  ),
                ),
                child: Icon(
                  widget.page.icon ?? Icons.military_tech,
                  size: imageSize * 0.4,
                  color: Colors.white,
                ),
              );
            },
          ),
        ),
      );
    }
  }

  Widget _buildTitle() {
    return Text(
      widget.page.title,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: widget.isSmallScreen ? 28 : 32,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        height: 1.2,
        letterSpacing: 0.5,
      ),
    );
  }

  Widget _buildSubtitle() {
    return Text(
      widget.page.subtitle,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: widget.isSmallScreen ? 16 : 18,
        fontWeight: FontWeight.w500,
        color: Colors.white.withValues(alpha: 0.9 * 255),
        fontStyle: FontStyle.italic,
        letterSpacing: 0.3,
      ),
    );
  }

  Widget _buildDescription() {
    return Text(
      widget.page.description,
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: widget.isSmallScreen ? 14 : 16,
        color: Colors.white.withValues(alpha: 0.8 * 255),
        height: 1.5,
        letterSpacing: 0.2,
      ),
    );
  }

  Widget _buildBulletPoints() {
    return Column(
      children: widget.page.bulletPoints!.map((point) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: widget.page.accentColor,
                size: 16,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  point,
                  style: TextStyle(
                    fontSize: widget.isSmallScreen ? 13 : 14,
                    color: Colors.white.withValues(alpha: 0.8 * 255),
                    height: 1.3,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}
