import 'package:fit_4_force/shared/models/subscription_model.dart';
import 'package:fit_4_force/shared/services/base_service.dart';
import 'package:fit_4_force/shared/services/payment_service.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:fit_4_force/features/fitness/services/notification_service.dart';
import 'package:fit_4_force/features/fitness/models/notification_model.dart';

class SubscriptionService extends BaseService {
  // Use the real PaymentService for payment processing
  final PaymentService _paymentService = PaymentService(AuthService());
  final BuildContext? _context;

  SubscriptionService({BuildContext? context}) : _context = context;

  // Generate a unique ID for the subscription
  String generateId() {
    return const Uuid().v4();
  }

  @override
  String get collectionName => 'subscriptions';

  // Initialize Paystack
  Future<void> initPaystack() async {
    // PaystackService initializes itself in its constructor
  }

  // Get current subscription
  Future<SubscriptionModel?> getCurrentSubscription() async {
    try {
      if (!isAuthenticated) return null;

      final subscriptionQuery =
          await Supabase.instance.client
              .from(collectionName)
              .select()
              .eq('user_id', currentUserId!)
              .eq('is_active', true)
              .order('expiry_date', ascending: false)
              .limit(1)
              .maybeSingle();

      if (subscriptionQuery == null) return null;

      return SubscriptionModel.fromJson(subscriptionQuery);
    } catch (e) {
      rethrow;
    }
  }

  // Check if user has active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      // Check if subscription is expired
      final now = DateTime.now();
      return subscription.expiryDate.isAfter(now);
    } catch (e) {
      return false;
    }
  }

  // Create a new subscription
  Future<SubscriptionModel> createSubscription({
    required String userId,
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      final subscription = SubscriptionModel(
        id: generateId(),
        createdAt: DateTime.now(),
        userId: userId,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
        isActive: true,
        paymentMethod: 'Paystack',
        autoRenew: false,
      );

      await Supabase.instance.client
          .from(collectionName)
          .insert(subscription.toJson());

      // Update user's premium status
      await Supabase.instance.client
          .from('users')
          .update({
            'is_premium': true,
            'premium_expiry_date': expiryDate.toIso8601String(),
          })
          .eq('id', userId);

      return subscription;
    } catch (e) {
      rethrow;
    }
  }

  // Cancel subscription
  Future<void> cancelSubscription(String subscriptionId) async {
    try {
      await update(subscriptionId, {
        'is_active': false,
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Update user's premium status
      if (!isAuthenticated) return;

      await Supabase.instance.client
          .from('users')
          .update({'is_premium': false, 'premium_expiry_date': null})
          .eq('id', currentUserId!);
    } catch (e) {
      rethrow;
    }
  }

  // Renew subscription
  Future<SubscriptionModel> renewSubscription({
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    try {
      if (!isAuthenticated) {
        throw Exception('User not authenticated');
      }

      return await createSubscription(
        userId: currentUserId!,
        transactionReference: transactionReference,
        startDate: startDate,
        expiryDate: expiryDate,
        amount: amount,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Process payment with MockPaymentService
  Future<Map<String, dynamic>> processPayment({
    required String email,
    required double amount,
    required String reference,
    required String fullName,
  }) async {
    try {
      if (_context == null) {
        throw Exception('Context is required for payment checkout');
      }

      // Use the MockPaymentService to process the payment
      final success = await _paymentService.processPayment(
        context: _context, // We've already checked that _context is not null
        email: email,
        fullName: fullName,
        amount: amount,
        onSuccess: (ref) {
          // Handle success callback
        },
        onError: (error) {
          // Handle error callback
        },
      );

      if (success) {
        // Payment successful
        return {
          'success': true,
          'reference': reference,
          'message': 'Payment successful',
        };
      } else {
        // Payment failed
        return {'success': false, 'message': 'Payment failed or was cancelled'};
      }
    } catch (e) {
      return {'success': false, 'message': e.toString()};
    }
  }

  // Get subscription history
  Future<List<SubscriptionModel>> getSubscriptionHistory() async {
    try {
      if (!isAuthenticated) return [];

      final subscriptionsQuery = await Supabase.instance.client
          .from(collectionName)
          .select()
          .eq('user_id', currentUserId!)
          .order('created_at', ascending: false);

      return (subscriptionsQuery as List)
          .map((data) => SubscriptionModel.fromJson(data))
          .toList();
    } catch (e) {
      rethrow;
    }
  }

  // Check if subscription is about to expire (within 3 days)
  Future<bool> isSubscriptionAboutToExpire() async {
    try {
      final subscription = await getCurrentSubscription();
      if (subscription == null) return false;

      final now = DateTime.now();
      final threeDaysFromNow = now.add(const Duration(days: 3));

      return subscription.expiryDate.isBefore(threeDaysFromNow) &&
          subscription.expiryDate.isAfter(now);
    } catch (e) {
      return false;
    }
  }

  // Automated check for expired/expiring subscriptions and reminders
  Future<void> checkAndHandleSubscriptionRenewal() async {
    final subscription = await getCurrentSubscription();
    if (subscription == null) return;

    final now = DateTime.now();
    final isExpired = subscription.expiryDate.isBefore(now);
    final isAboutToExpire = await isSubscriptionAboutToExpire();

    // Send reminder if about to expire (within 3 days)
    if (isAboutToExpire && !isExpired) {
      NotificationService().addNotification(
        NotificationModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'Subscription Expiring Soon',
          message:
              'Your subscription will expire on ${subscription.expiryDate.toLocal().toString().split(' ')[0]}. Tap to renew.',
          timestamp: now,
          type: NotificationType.reminder,
        ),
      );
    }

    // Send reminder if expired
    if (isExpired) {
      NotificationService().addNotification(
        NotificationModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'Subscription Expired',
          message:
              'Your subscription has expired. Renew to continue enjoying premium features.',
          timestamp: now,
          type: NotificationType.reminder,
        ),
      );
    }

    // Auto-renew if enabled and expired
    if (isExpired && subscription.autoRenew) {
      // You may want to trigger payment flow here
      // For now, just add a notification (replace with real payment logic)
      NotificationService().addNotification(
        NotificationModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'Auto-Renewal Attempted',
          message:
              'We attempted to auto-renew your subscription. Please check your payment method.',
          timestamp: now,
          type: NotificationType.reminder,
        ),
      );
      // TODO: Integrate payment and renewal logic here
    }
  }

  // Verify Paystack transaction on backend
  Future<bool> verifyPaystackTransactionOnBackend(String reference) async {
    try {
      // Replace with your backend endpoint URL
      const backendUrl = 'https://your-backend.com/api/verify-paystack';
      final response = await Supabase.instance.client.rpc(
        'call_external_api',
        params: {
          'url': backendUrl,
          'method': 'POST',
          'body': {'reference': reference},
        },
      );
      // Expecting backend to return { success: true/false }
      if (response != null && response['success'] == true) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  // Example usage in renewal (call this after payment):
  Future<bool> renewSubscriptionWithVerification({
    required String transactionReference,
    required DateTime startDate,
    required DateTime expiryDate,
    required double amount,
  }) async {
    final verified = await verifyPaystackTransactionOnBackend(
      transactionReference,
    );
    if (!verified) {
      // Optionally notify user of failed verification
      NotificationService().addNotification(
        NotificationModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          title: 'Payment Verification Failed',
          message: 'We could not verify your payment. Please try again.',
          timestamp: DateTime.now(),
          type: NotificationType.reminder,
        ),
      );
      return false;
    }
    await renewSubscription(
      transactionReference: transactionReference,
      startDate: startDate,
      expiryDate: expiryDate,
      amount: amount,
    );
    return true;
  }
}
