import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A utility class for consistent UI components and styling
class AppUI {
  // Prevent instantiation
  AppUI._();

  /// Standard padding values
  static const EdgeInsets paddingXXSmall = EdgeInsets.all(
    AppTheme.spacingXXSmall,
  );
  static const EdgeInsets paddingXSmall = EdgeInsets.all(
    AppTheme.spacingXSmall,
  );
  static const EdgeInsets paddingSmall = EdgeInsets.all(AppTheme.spacingSmall);
  static const EdgeInsets paddingMedium = EdgeInsets.all(
    AppTheme.spacingMedium,
  );
  static const EdgeInsets paddingLarge = EdgeInsets.all(AppTheme.spacingLarge);
  static const EdgeInsets paddingXLarge = EdgeInsets.all(
    AppTheme.spacingXLarge,
  );
  static const EdgeInsets paddingXXLarge = EdgeInsets.all(
    AppTheme.spacingXXLarge,
  );

  /// Standard horizontal padding values
  static const EdgeInsets paddingHorizontalXXSmall = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingXXSmall,
  );
  static const EdgeInsets paddingHorizontalXSmall = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingXSmall,
  );
  static const EdgeInsets paddingHorizontalSmall = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingSmall,
  );
  static const EdgeInsets paddingHorizontalMedium = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingMedium,
  );
  static const EdgeInsets paddingHorizontalLarge = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingLarge,
  );
  static const EdgeInsets paddingHorizontalXLarge = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingXLarge,
  );
  static const EdgeInsets paddingHorizontalXXLarge = EdgeInsets.symmetric(
    horizontal: AppTheme.spacingXXLarge,
  );

  /// Standard vertical padding values
  static const EdgeInsets paddingVerticalXXSmall = EdgeInsets.symmetric(
    vertical: AppTheme.spacingXXSmall,
  );
  static const EdgeInsets paddingVerticalXSmall = EdgeInsets.symmetric(
    vertical: AppTheme.spacingXSmall,
  );
  static const EdgeInsets paddingVerticalSmall = EdgeInsets.symmetric(
    vertical: AppTheme.spacingSmall,
  );
  static const EdgeInsets paddingVerticalMedium = EdgeInsets.symmetric(
    vertical: AppTheme.spacingMedium,
  );
  static const EdgeInsets paddingVerticalLarge = EdgeInsets.symmetric(
    vertical: AppTheme.spacingLarge,
  );
  static const EdgeInsets paddingVerticalXLarge = EdgeInsets.symmetric(
    vertical: AppTheme.spacingXLarge,
  );
  static const EdgeInsets paddingVerticalXXLarge = EdgeInsets.symmetric(
    vertical: AppTheme.spacingXXLarge,
  );

  /// Standard border radius values
  static final BorderRadius borderRadiusSmall = BorderRadius.circular(
    AppTheme.borderRadiusSmall,
  );
  static final BorderRadius borderRadiusMedium = BorderRadius.circular(
    AppTheme.borderRadiusMedium,
  );
  static final BorderRadius borderRadiusLarge = BorderRadius.circular(
    AppTheme.borderRadiusLarge,
  );
  static final BorderRadius borderRadiusXLarge = BorderRadius.circular(
    AppTheme.borderRadiusXLarge,
  );
  static final BorderRadius borderRadiusXXLarge = BorderRadius.circular(
    AppTheme.borderRadiusXXLarge,
  );
  static final BorderRadius borderRadiusCircular = BorderRadius.circular(
    AppTheme.borderRadiusCircular,
  );

  // ========================================
  // UNIVERSAL SHADOW SYSTEM - PERFECT FOR ALL COMPONENTS
  // ========================================

  /// PERFECT UNIVERSAL SHADOW - Very light gray shadows
  static List<BoxShadow> get universalShadow => [
    BoxShadow(
      color: const Color(
        0xFF9E9E9E,
      ).withValues(alpha: 0.08 * 255), // Light gray instead of black
      blurRadius: 3,
      offset: const Offset(0, 1),
      spreadRadius: 0,
    ),
  ];

  /// All shadow variations use the same perfect shadow
  static List<BoxShadow> get shadowSmall => universalShadow;
  static List<BoxShadow> get shadowMedium => universalShadow;
  static List<BoxShadow> get shadowLarge => universalShadow;
  static List<BoxShadow> get shadowElevated => universalShadow;
  static List<BoxShadow> get minimalShadow => universalShadow;
  static List<BoxShadow> get materialCardShadow => universalShadow;

  /// No colored shadows - Always use neutral gray (deprecated)
  @deprecated
  static List<BoxShadow> coloredShadow(Color color, {double intensity = 0.3}) =>
      materialCardShadow;

  /// Arctic Blue themed shadow - very subtle gray tint
  static List<BoxShadow> get arcticBlueShadow => [
    BoxShadow(
      color: const Color(0xFF9E9E9E).withValues(alpha: 0.06 * 255), // Light gray instead of blue
      blurRadius: 4,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  /// Standard card decoration
  static BoxDecoration cardDecoration({
    Color? color,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
    Gradient? gradient,
  }) {
    return BoxDecoration(
      color: color ?? Colors.white,
      borderRadius: borderRadius ?? borderRadiusXLarge,
      boxShadow: boxShadow ?? shadowMedium,
      border: border,
      gradient: gradient,
    );
  }

  /// Standard gradient decoration
  static BoxDecoration gradientDecoration({
    required List<Color> colors,
    AlignmentGeometry begin = Alignment.topLeft,
    AlignmentGeometry end = Alignment.bottomRight,
    BorderRadius? borderRadius,
    List<BoxShadow>? boxShadow,
    Border? border,
  }) {
    return BoxDecoration(
      gradient: LinearGradient(colors: colors, begin: begin, end: end),
      borderRadius: borderRadius ?? borderRadiusXLarge,
      boxShadow: boxShadow ?? shadowMedium,
      border: border,
    );
  }

  /// Arctic Blue background gradients - soft and comfortable
  static BoxDecoration get arcticBlueBackgroundGradient => BoxDecoration(
    gradient: LinearGradient(
      colors: [AppTheme.backgroundLight, AppTheme.backgroundDark],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ),
  );

  /// Clean Material Design card - Arctic Blue with minimal shadows
  static BoxDecoration get materialCard => BoxDecoration(
    color: AppTheme.backgroundSurface,
    borderRadius: borderRadiusLarge,
    boxShadow: materialCardShadow,
  );

  /// Material Design elevated card - Slightly more elevation
  static BoxDecoration get materialElevatedCard => BoxDecoration(
    color: AppTheme.backgroundSurface,
    borderRadius: borderRadiusLarge,
    boxShadow: shadowMedium,
  );

  /// Material Design card with border - Clean and accessible
  static BoxDecoration get materialBorderedCard => BoxDecoration(
    color: AppTheme.backgroundSurface,
    borderRadius: borderRadiusLarge,
    boxShadow: materialCardShadow,
    border: Border.all(color: AppTheme.backgroundAccent, width: 1),
  );

  /// Deprecated - Use materialCard instead
  @deprecated
  static BoxDecoration get arcticBlueCardDecoration => materialCard;

  /// Deprecated - Use materialElevatedCard instead
  @deprecated
  static BoxDecoration get arcticBlueGradientCard => materialElevatedCard;

  /// Clean Material Design button style - No excessive elevation
  static ButtonStyle buttonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
    double? elevation,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? AppTheme.primaryColor,
      foregroundColor: foregroundColor ?? Colors.white,
      elevation: elevation ?? 2.0, // Minimal elevation for Material Design
      shadowColor: Colors.black.withValues(alpha: 0.1 * 255),
      padding: padding ?? paddingHorizontalMedium.add(paddingVerticalSmall),
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
      ),
    );
  }

  /// Standard text button style
  static ButtonStyle textButtonStyle({
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
  }) {
    return TextButton.styleFrom(
      foregroundColor: foregroundColor ?? AppTheme.primaryColor,
      padding: padding ?? paddingHorizontalSmall.add(paddingVerticalXSmall),
    );
  }

  /// Standard outlined button style
  static ButtonStyle outlinedButtonStyle({
    Color? foregroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    EdgeInsetsGeometry? padding,
  }) {
    return OutlinedButton.styleFrom(
      foregroundColor: foregroundColor ?? AppTheme.primaryColor,
      side: BorderSide(color: borderColor ?? AppTheme.primaryColor),
      padding: padding ?? paddingHorizontalMedium.add(paddingVerticalSmall),
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
      ),
    );
  }

  /// Standard input decoration
  static InputDecoration inputDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    BorderRadius? borderRadius,
    Color? fillColor,
    bool filled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: filled,
      fillColor: fillColor ?? Colors.white,
      contentPadding: paddingMedium,
      border: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: AppTheme.primaryColor, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: borderRadius ?? borderRadiusLarge,
        borderSide: const BorderSide(color: AppTheme.errorColor),
      ),
    );
  }
}
