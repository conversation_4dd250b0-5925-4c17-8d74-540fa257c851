import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/subscription/bloc/subscription_bloc.dart';
import 'package:fit_4_force/shared/services/supabase_subscription_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart'
    as shared;
import 'package:fit_4_force/shared/providers/supabase_provider.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/core/config/environment_config.dart';
import 'package:fit_4_force/core/services/backend_service_manager.dart';
import 'package:fit_4_force/core/testing/backend_test_suite.dart';
import 'package:fit_4_force/core/services/service_locator.dart';
import 'package:fit_4_force/features/splash/widgets/splash_screen_with_onboarding.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logger
  final logger = Logger();

  // Print environment information in debug mode only
  if (kDebugMode) {
    EnvironmentConfig.printEnvironmentInfo();
  }

  // Initialize Supabase with faster timeout and error handling
  try {
    logger.i('🚀 Initializing Supabase...');
    await SupabaseConfig.initialize();
    logger.i('✅ Supabase initialized successfully');
  } catch (e) {
    logger.e('❌ Error initializing Supabase: $e');
    // Don't crash the app, continue with limited functionality
  }

  // Initialize basic service locator only (defer heavy services)
  await setupBasicServiceLocator();

  // Start the app immediately - defer other initialization
  runApp(
    SupabaseProvider(
      client: SupabaseConfig.client,
      child: const Fit4ForceApp(),
    ),
  );

  // Initialize other services in background after app starts
  _initializeBackgroundServices(logger);
}

/// Initialize heavy services in background after app starts
void _initializeBackgroundServices(Logger logger) async {
  try {
    // Complete service locator setup
    await setupServiceLocator();

    // Initialize backend services after app starts
    logger.i('🔧 Initializing backend services in background...');
    await BackendServiceManager().initialize();
    logger.i('✅ Backend services initialized');

    // Run tests only if specifically needed (not by default)
    if (kDebugMode &&
        const bool.fromEnvironment('RUN_TESTS', defaultValue: false)) {
      logger.i('🧪 Running backend tests...');
      final testSuite = BackendTestSuite();
      final testResults = await testSuite.runTests();
      testSuite.logTestResults(testResults);
    }
  } catch (e) {
    logger.e('❌ Error in background services: $e');
  }
}

class Fit4ForceApp extends StatelessWidget {
  const Fit4ForceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) {
            final authBloc = AuthBloc();

            // Check for stored authentication when app starts
            Future.delayed(const Duration(milliseconds: 100), () {
            });

            return authBloc;
          },
        ),
        BlocProvider(
          create:
              (context) => SubscriptionBloc(
                SupabaseSubscriptionService(
                  authService: getIt<shared.SupabaseAuthService>(),
                ),
              ),
        ),
      ],
      child: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          // Handle authentication state changes
          final navigationService = NavigationService();
          if (state is Authenticated) {
            // User is authenticated, navigate to home
            navigationService.navigateToAndRemoveUntil(AppRoutes.home);
          } else if (state is Unauthenticated) {
            // User is not authenticated, navigate to login
            navigationService.navigateToAndRemoveUntil(AppRoutes.login);
          }
        },
        builder: (context, state) {
          return MaterialApp(
            title: EnvironmentConfig.appName,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            debugShowCheckedModeBanner: false,

            // Set up responsive design system
            builder: (context, child) {
              // ResponsiveDesignSystem can be accessed directly without init
              return child ?? const SizedBox.shrink();
            },

            // Use the proper splash screen with onboarding
            home: const SplashScreenWithOnboarding(),

            // Set up navigation service
            navigatorKey: NavigationService().navigatorKey,

            // Define all app routes
            routes: AppRoutes.getRoutes(),

            // Handle unknown routes
            onUnknownRoute: (settings) {
              return MaterialPageRoute(
                builder:
                    (context) => Scaffold(
                      appBar: AppBar(title: const Text('Page Not Found')),
                      body: const Center(
                        child: Text('The requested page could not be found.'),
                      ),
                    ),
              );
            },
          );
        },
      ),
    );
  }
}
