import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service for preventing downloads and protecting content
class DownloadPreventionService {
  static DownloadPreventionService? _instance;
  static DownloadPreventionService get instance =>
      _instance ??= DownloadPreventionService._();

  DownloadPreventionService._();

  final List<String> _protectedScreens = [
    'PrepDashboardScreen',
    'QuizScreen',
    'StudyMaterialViewer',
    'WorkoutDetailScreen',
    'ExerciseDetailScreen',
    'FitnessScreen',
    'StudyTipsScreen',
    'MockExamScreen',
  ];

  /// Check if downloads are allowed for the current screen
  bool isDownloadAllowed(String screenName) {
    return !_protectedScreens.contains(screenName);
  }

  /// Show download prevention message
  void showDownloadPreventionMessage(
    BuildContext context, {
    String? customMessage,
  }) {
    HapticFeedback.mediumImpact();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.block, color: Colors.red),
                SizedBox(width: 8),
                Text('Download Disabled'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.security,
                  size: 64,
                  color: Colors.red.withValues(alpha: 0.7 * 255),
                ),
                SizedBox(height: 16),
                Text(
                  customMessage ??
                      'Downloads are disabled for this content to protect our educational materials.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 16),
                ),
                SizedBox(height: 12),
                Text(
                  'This helps us maintain the quality and value of our premium content.',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('I Understand'),
              ),
            ],
          ),
    );
  }

  /// Show quick snackbar message for download prevention
  void showQuickDownloadMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.block, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Downloads disabled to protect content',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16),
      ),
    );
  }

  /// Disable right-click context menu (for web)
  void disableContextMenu() {
    // This would be implemented for web builds
    // For now, we'll handle it in the widget level
  }

  /// Prevent text selection in protected content
  Widget preventTextSelection(Widget child) {
    return SelectionContainer.disabled(child: child);
  }

  /// Wrap content to prevent downloads and copying
  Widget protectContent({
    required Widget child,
    required String screenName,
    bool allowTextSelection = false,
  }) {
    Widget protectedChild = child;

    // Disable text selection if not allowed
    if (!allowTextSelection) {
      protectedChild = preventTextSelection(protectedChild);
    }

    // Add gesture detection for download attempts
    return GestureDetector(
      onLongPress: () {
        // Detect potential copy/save gestures
        HapticFeedback.heavyImpact();
      },
      child: Container(child: protectedChild),
    );
  }

  /// Check if screen should be protected
  bool shouldProtectScreen(String screenName) {
    return _protectedScreens.contains(screenName);
  }

  /// Add screen to protection list
  void addProtectedScreen(String screenName) {
    if (!_protectedScreens.contains(screenName)) {
      _protectedScreens.add(screenName);
    }
  }

  /// Remove screen from protection list
  void removeProtectedScreen(String screenName) {
    _protectedScreens.remove(screenName);
  }

  /// Get list of protected screens
  List<String> get protectedScreens => List.unmodifiable(_protectedScreens);
}

/// Widget that prevents downloads and protects content
class ProtectedContent extends StatelessWidget {
  final Widget child;
  final String screenName;
  final bool allowTextSelection;
  final bool showWatermark;
  final String? customWatermark;

  const ProtectedContent({
    super.key,
    required this.child,
    required this.screenName,
    this.allowTextSelection = false,
    this.showWatermark = true,
    this.customWatermark,
  });

  @override
  Widget build(BuildContext context) {
    final downloadService = DownloadPreventionService.instance;

    if (!downloadService.shouldProtectScreen(screenName)) {
      return child;
    }

    Widget protectedChild = downloadService.protectContent(
      child: child,
      screenName: screenName,
      allowTextSelection: allowTextSelection,
    );

    // Add watermark if enabled
    if (showWatermark) {
      protectedChild = Stack(
        children: [protectedChild, _buildWatermark(context)],
      );
    }

    return protectedChild;
  }

  Widget _buildWatermark(BuildContext context) {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          child: CustomPaint(
            painter: WatermarkPainter(
              text: customWatermark ?? 'Fit4Force Premium Content',
            ),
          ),
        ),
      ),
    );
  }
}

/// Custom painter for watermark
class WatermarkPainter extends CustomPainter {
  final String text;

  WatermarkPainter({required this.text});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.grey.withValues(alpha: 0.1 * 255)
          ..style = PaintingStyle.fill;

    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          color: Colors.grey.withValues(alpha: 0.15 * 255),
          fontSize: 24,
          fontWeight: FontWeight.w300,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Draw watermark diagonally across the screen
    canvas.save();
    canvas.translate(size.width / 2, size.height / 2);
    canvas.rotate(-0.5); // Rotate 45 degrees

    final offset = Offset(-textPainter.width / 2, -textPainter.height / 2);
    textPainter.paint(canvas, offset);

    canvas.restore();
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Mixin for screens that need download prevention
mixin DownloadPreventionMixin<T extends StatefulWidget> on State<T> {
  final DownloadPreventionService _downloadService =
      DownloadPreventionService.instance;

  String get screenName => T.toString();

  void preventDownload({String? customMessage}) {
    _downloadService.showDownloadPreventionMessage(
      context,
      customMessage: customMessage,
    );
  }

  void showQuickDownloadMessage() {
    _downloadService.showQuickDownloadMessage(context);
  }

  bool get isDownloadAllowed => _downloadService.isDownloadAllowed(screenName);

  Widget protectContent(Widget child, {bool allowTextSelection = false}) {
    return ProtectedContent(
      screenName: screenName,
      allowTextSelection: allowTextSelection,
      child: child,
    );
  }
}
