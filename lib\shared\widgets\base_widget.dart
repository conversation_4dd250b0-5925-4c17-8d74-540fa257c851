import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';
import 'package:fit_4_force/core/theme/app_text.dart';
import 'package:fit_4_force/core/theme/app_widgets.dart';

class BaseButton extends StatelessWidget {
  final String text;
  final VoidCallback onPressed;
  final bool isLoading;
  final bool isOutlined;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final IconData? icon;

  const BaseButton({
    super.key,
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    this.isOutlined = false,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = backgroundColor ?? AppTheme.primaryColor;
    final effectiveTextColor = textColor ?? Colors.white;

    return SizedBox(
      width: width,
      height: height,
      child:
          isOutlined
              ? OutlinedButton(
                onPressed: isLoading ? null : onPressed,
                style: AppUI.outlinedButtonStyle(
                  foregroundColor: effectiveBackgroundColor,
                  borderColor: effectiveBackgroundColor,
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusLarge,
                  ),
                ),
                child: _buildChild(context),
              )
              : ElevatedButton(
                onPressed: isLoading ? null : onPressed,
                style: AppUI.buttonStyle(
                  backgroundColor: effectiveBackgroundColor,
                  foregroundColor: effectiveTextColor,
                  borderRadius: BorderRadius.circular(
                    AppTheme.borderRadiusLarge,
                  ),
                  elevation: AppTheme.elevationSmall,
                ),
                child: _buildChild(context),
              ),
    );
  }

  Widget _buildChild(BuildContext context) {
    if (isLoading) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            isOutlined
                ? (backgroundColor ?? AppTheme.primaryColor)
                : Colors.white,
          ),
        ),
      );
    }

    final textColor =
        isOutlined
            ? (backgroundColor ?? AppTheme.primaryColor)
            : (this.textColor ?? Colors.white);

    final textStyle = AppText.buttonMedium(
      context,
      color: textColor,
      fontWeight: AppTheme.fontWeightSemiBold,
    );

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20, color: textColor),
          SizedBox(width: AppTheme.spacingSmall),
          Text(text, style: textStyle),
        ],
      );
    }

    return Text(text, style: textStyle);
  }
}

class BaseTextField extends StatelessWidget {
  final String label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final bool obscureText;
  final TextInputType keyboardType;
  final Widget? prefix;
  final Widget? suffix;
  final int? maxLines;
  final int? minLines;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;

  const BaseTextField({
    super.key,
    required this.label,
    this.hint,
    this.controller,
    this.validator,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.prefix,
    this.suffix,
    this.maxLines = 1,
    this.minLines,
    this.onChanged,
    this.onSubmitted,
  });

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      validator: validator,
      obscureText: obscureText,
      keyboardType: keyboardType,
      maxLines: maxLines,
      minLines: minLines,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      style: AppText.bodyMedium(
        context,
        color: Colors.black87,
      ), // Dark text for white background
      decoration: AppUI.inputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefix,
        suffixIcon: suffix,
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
        fillColor: Colors.white.withValues(alpha: 0.95 * 255),
      ),
    );
  }
}

class BaseCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final double? elevation;
  final VoidCallback? onTap;

  const BaseCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.elevation,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return AppWidgets.card(
      context: context,
      backgroundColor: backgroundColor,
      borderRadius: BorderRadius.circular(AppTheme.borderRadiusXLarge),
      padding: padding ?? AppUI.paddingMedium,
      boxShadow: AppUI.shadowMedium,
      onTap: onTap,
      child: child,
    );
  }
}

class BaseLoadingIndicator extends StatelessWidget {
  final Color? color;
  final double? size;

  const BaseLoadingIndicator({super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    return AppWidgets.loadingIndicator(
      color: color ?? AppTheme.primaryColor,
      size: size ?? 24,
    );
  }
}

class BaseAvatar extends StatelessWidget {
  final double radius;
  final String? imageUrl;
  final String initials;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isUploading;

  const BaseAvatar({
    super.key,
    required this.radius,
    this.imageUrl,
    required this.initials,
    this.onTap,
    this.backgroundColor,
    this.textColor,
    this.isUploading = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: isUploading ? null : onTap,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircleAvatar(
            radius: radius,
            backgroundColor: backgroundColor ?? AppTheme.primaryLightColor,
            backgroundImage: imageUrl != null ? NetworkImage(imageUrl!) : null,
            child:
                imageUrl == null
                    ? Text(
                      initials,
                      style: TextStyle(
                        fontSize: radius * 0.8,
                        fontWeight: AppTheme.fontWeightBold,
                        color: textColor ?? Colors.white,
                        letterSpacing: 0.5,
                      ),
                    )
                    : null,
          ),
          if (isUploading)
            Container(
              width: radius * 2,
              height: radius * 2,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5 * 255),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SizedBox(
                  width: radius * 0.8,
                  height: radius * 0.8,
                  child: CircularProgressIndicator(
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white,
                    ),
                    strokeWidth: 2,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
