import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/services/email_service.dart';

/// Test widget for testing email functionality
/// 
/// This is for development purposes only. Remove before production.
class EmailTestWidget extends StatefulWidget {
  const EmailTestWidget({super.key});

  @override
  State<EmailTestWidget> createState() => _EmailTestWidgetState();
}

class _EmailTestWidgetState extends State<EmailTestWidget> {
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _testWelcomeEmail() async {
    if (_emailController.text.isEmpty || _nameController.text.isEmpty) {
      _showSnackBar('Please enter both email and name', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await EmailService.sendWelcomeEmail(
        userEmail: _emailController.text.trim(),
        fullName: _nameController.text.trim(),
      );

      if (success) {
        _showSnackBar('Welcome email sent successfully!', Colors.green);
      } else {
        _showSnackBar('Failed to send welcome email', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testPremiumEmail() async {
    if (_emailController.text.isEmpty || _nameController.text.isEmpty) {
      _showSnackBar('Please enter both email and name', Colors.red);
      return;
    }

    setState(() => _isLoading = true);

    try {
      final success = await EmailService.sendPremiumUpgradeEmail(
        userEmail: _emailController.text.trim(),
        fullName: _nameController.text.trim(),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        isYearly: false,
        transactionReference: 'TEST_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (success) {
        _showSnackBar('Premium upgrade email sent successfully!', Colors.green);
      } else {
        _showSnackBar('Failed to send premium upgrade email', Colors.red);
      }
    } catch (e) {
      _showSnackBar('Error: $e', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Test'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Email Test Configuration',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Before testing emails, make sure to:',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. Set up EmailJS account and get service ID'),
                    const Text('2. Create email templates in EmailJS dashboard'),
                    const Text('3. Update EmailConfig with your credentials'),
                    const Text('4. Test in debug mode (emails are logged, not sent)'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailController,
              keyboardType: TextInputType.emailAddress,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testWelcomeEmail,
              icon: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.waving_hand),
              label: const Text('Test Welcome Email'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _isLoading ? null : _testPremiumEmail,
              icon: _isLoading 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.workspace_premium),
              label: const Text('Test Premium Upgrade Email'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.all(16),
              ),
            ),
            const SizedBox(height: 24),
            Card(
              color: Colors.grey[100],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700]),
                        const SizedBox(width: 8),
                        Text(
                          'Development Mode',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'In debug mode, emails are logged to console instead of being sent. '
                      'Check the debug console to see email content and verify the data is correct.',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
