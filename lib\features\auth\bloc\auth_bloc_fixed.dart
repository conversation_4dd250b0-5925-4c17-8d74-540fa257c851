import 'package:fit_4_force/shared/bloc/base_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/core/services/persistent_auth_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Events
abstract class AuthEvent extends BaseEvent {}

class CheckAuthStatusEvent extends AuthEvent {
  @override
  List<Object?> get props => [];
}

class RestoreAuthEvent extends AuthEvent {
  final UserModel user;

  RestoreAuthEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class AuthenticatedEvent extends AuthEvent {
  final UserModel user;

  AuthenticatedEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class SignOutEvent extends AuthEvent {}

class UpdateProfileEvent extends AuthEvent {
  final UserModel updatedUser;

  UpdateProfileEvent(this.updatedUser);

  @override
  List<Object?> get props => [updatedUser];
}

// States
abstract class AuthState extends BaseState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class Authenticated extends AuthState {
  final UserModel user;

  Authenticated(this.user);

  @override
  List<Object?> get props => [user];
}

class Unauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

// Bloc
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  final PersistentAuthService _persistentAuth = PersistentAuthService();

  AuthBloc() : super(AuthInitial()) {
    on<AuthenticatedEvent>(_handleAuthenticated);
    on<CheckAuthStatusEvent>(_handleCheckAuthStatus);
    on<RestoreAuthEvent>(_handleRestoreAuth);
    on<SignOutEvent>(_handleSignOut);
    on<UpdateProfileEvent>(_handleUpdateProfile);
  }

  @override
  Future<void> handleEvent(AuthEvent event, Emitter<AuthState> emit) async {
    if (event is AuthenticatedEvent) {
      await _handleAuthenticated(event, emit);
    } else if (event is CheckAuthStatusEvent) {
      await _handleCheckAuthStatus(event, emit);
    } else if (event is RestoreAuthEvent) {
      await _handleRestoreAuth(event, emit);
    } else if (event is SignOutEvent) {
      await _handleSignOut(event, emit);
    } else if (event is UpdateProfileEvent) {
      await _handleUpdateProfile(event, emit);
    }
  }

  Future<void> _handleAuthenticated(
    AuthenticatedEvent event,
    Emitter<AuthState> emit,
  ) async {
    // Save user session for persistence
    await _persistentAuth.saveUserSession(event.user);
    emit(Authenticated(event.user));
  }

  Future<void> _handleCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // Check for stored user session (persistent login)
      final storedUser = await _persistentAuth.getStoredUser();

      if (storedUser != null) {
        // User has a valid stored session
        emit(Authenticated(storedUser));
      } else {
        emit(Unauthenticated());
      }
    } catch (e) {
      emit(AuthError('Failed to check authentication status'));
    }
  }

  Future<void> _handleRestoreAuth(
    RestoreAuthEvent event,
    Emitter<AuthState> emit,
  ) async {
    // Directly restore the user session without additional API calls
    await _persistentAuth.saveUserSession(event.user);
    emit(Authenticated(event.user));
  }

  Future<void> _handleSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // Clear persistent session
      await _persistentAuth.clearUserSession();

      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError('Failed to sign out'));
    }
  }

  Future<void> _handleUpdateProfile(
    UpdateProfileEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      // Update stored user data
      await _persistentAuth.updateStoredUser(event.updatedUser);
      emit(Authenticated(event.updatedUser));
    } catch (e) {
      emit(AuthError('Failed to update profile'));
    }
  }
}
