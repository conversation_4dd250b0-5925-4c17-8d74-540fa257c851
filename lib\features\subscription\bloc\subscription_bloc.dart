import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/bloc/base_bloc.dart';
import 'package:fit_4_force/shared/models/subscription_model.dart';
import 'package:fit_4_force/shared/services/supabase_subscription_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Events
abstract class SubscriptionEvent extends BaseEvent {}

class CheckSubscriptionEvent extends SubscriptionEvent {}

class SubscribeEvent extends SubscriptionEvent {
  final String email;
  final String fullName;
  final BuildContext context;

  SubscribeEvent({
    required this.email,
    required this.fullName,
    required this.context,
  });

  @override
  List<Object?> get props => [email, fullName];
}

class RenewSubscriptionEvent extends SubscriptionEvent {
  final String email;
  final String fullName;
  final BuildContext context;

  RenewSubscriptionEvent({
    required this.email,
    required this.fullName,
    required this.context,
  });

  @override
  List<Object?> get props => [email, fullName];
}

class CancelSubscriptionEvent extends SubscriptionEvent {
  final String subscriptionId;

  CancelSubscriptionEvent({required this.subscriptionId});

  @override
  List<Object?> get props => [subscriptionId];
}

class LoadSubscriptionHistoryEvent extends SubscriptionEvent {}

// States
abstract class SubscriptionState extends BaseState {}

class SubscriptionInitial extends SubscriptionState {}

class SubscriptionLoading extends SubscriptionState {}

class SubscriptionActive extends SubscriptionState {
  final SubscriptionModel subscription;

  SubscriptionActive(this.subscription);

  @override
  List<Object?> get props => [subscription];
}

class SubscriptionInactive extends SubscriptionState {}

class SubscriptionProcessing extends SubscriptionState {}

class SubscriptionSuccess extends SubscriptionState {
  final SubscriptionModel subscription;

  SubscriptionSuccess(this.subscription);

  @override
  List<Object?> get props => [subscription];
}

class SubscriptionError extends SubscriptionState {
  final String message;

  SubscriptionError(this.message);

  @override
  List<Object?> get props => [message];
}

class SubscriptionHistoryLoaded extends SubscriptionState {
  final List<SubscriptionModel> subscriptions;

  SubscriptionHistoryLoaded(this.subscriptions);

  @override
  List<Object?> get props => [subscriptions];
}

// Bloc
class SubscriptionBloc extends BaseBloc<SubscriptionEvent, SubscriptionState> {
  final SupabaseSubscriptionService _subscriptionService;

  SubscriptionBloc(this._subscriptionService) : super(SubscriptionInitial()) {
    on<CheckSubscriptionEvent>(_handleCheckSubscription);
    on<SubscribeEvent>(_handleSubscribe);
    on<RenewSubscriptionEvent>(_handleRenewSubscription);
    on<CancelSubscriptionEvent>(_handleCancelSubscription);
    on<LoadSubscriptionHistoryEvent>(_handleLoadSubscriptionHistory);
  }

  @override
  Future<void> handleEvent(
    SubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    if (event is CheckSubscriptionEvent) {
      await _handleCheckSubscription(event, emit);
    } else if (event is SubscribeEvent) {
      await _handleSubscribe(event, emit);
    } else if (event is RenewSubscriptionEvent) {
      await _handleRenewSubscription(event, emit);
    } else if (event is CancelSubscriptionEvent) {
      await _handleCancelSubscription(event, emit);
    } else if (event is LoadSubscriptionHistoryEvent) {
      await _handleLoadSubscriptionHistory(event, emit);
    }
  }

  Future<void> _handleCheckSubscription(
    CheckSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      emit(SubscriptionLoading());

      final subscription = await _subscriptionService.getCurrentSubscription();

      if (subscription != null && subscription.isActive) {
        final now = DateTime.now();
        if (subscription.expiryDate.isAfter(now)) {
          emit(SubscriptionActive(subscription));
        } else {
          emit(SubscriptionInactive());
        }
      } else {
        emit(SubscriptionInactive());
      }
    } catch (e) {
      emit(SubscriptionError(e.toString()));
    }
  }

  Future<void> _handleSubscribe(
    SubscribeEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      emit(SubscriptionProcessing());

      // Ensure user is authenticated and has a valid userId
      final userId = _subscriptionService.currentUserId;
      if (userId == null) {
        emit(SubscriptionError('User not authenticated. Please log in again.'));
        return;
      }

      // Generate a unique reference for this transaction
      final reference = 'FIT4FORCE_${DateTime.now().millisecondsSinceEpoch}';

      // Process payment
      final paymentResult = await _subscriptionService.processPayment(
        context: event.context,
        email: event.email,
        fullName: event.fullName,
        amount: AppConfig.premiumSubscriptionPrice,
        reference: reference,
      );

      if (paymentResult['success']) {
        // Payment successful, create subscription
        final now = DateTime.now();
        final expiryDate = now.add(
          const Duration(days: 30),
        ); // 1 month subscription

        final subscription = await _subscriptionService.createSubscription(
          userId: userId,
          transactionReference: paymentResult['reference'] as String,
          startDate: now,
          expiryDate: expiryDate,
          amount: AppConfig.premiumSubscriptionPrice,
        );

        emit(SubscriptionSuccess(subscription));
      } else {
        emit(SubscriptionError(paymentResult['message'] as String));
      }
    } catch (e) {
      emit(SubscriptionError(e.toString()));
    }
  }

  Future<void> _handleRenewSubscription(
    RenewSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      emit(SubscriptionProcessing());

      // Generate a unique reference for this transaction
      final reference =
          'FIT4FORCE_RENEW_${DateTime.now().millisecondsSinceEpoch}';

      // Process payment
      final paymentResult = await _subscriptionService.processPayment(
        context: event.context,
        email: event.email,
        fullName: event.fullName,
        amount: AppConfig.premiumSubscriptionPrice,
        reference: reference,
      );

      if (paymentResult['success']) {
        // Payment successful, renew subscription
        final now = DateTime.now();
        final expiryDate = now.add(
          const Duration(days: 30),
        ); // 1 month subscription

        final subscription = await _subscriptionService.renewSubscription(
          transactionReference: paymentResult['reference'] as String,
          startDate: now,
          expiryDate: expiryDate,
          amount: AppConfig.premiumSubscriptionPrice,
        );

        emit(SubscriptionSuccess(subscription));
      } else {
        emit(SubscriptionError(paymentResult['message'] as String));
      }
    } catch (e) {
      emit(SubscriptionError(e.toString()));
    }
  }

  Future<void> _handleCancelSubscription(
    CancelSubscriptionEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      emit(SubscriptionLoading());

      await _subscriptionService.cancelSubscription(event.subscriptionId);

      emit(SubscriptionInactive());
    } catch (e) {
      emit(SubscriptionError(e.toString()));
    }
  }

  Future<void> _handleLoadSubscriptionHistory(
    LoadSubscriptionHistoryEvent event,
    Emitter<SubscriptionState> emit,
  ) async {
    try {
      emit(SubscriptionLoading());

      final subscriptions = await _subscriptionService.getSubscriptionHistory();

      emit(SubscriptionHistoryLoaded(subscriptions));
    } catch (e) {
      emit(SubscriptionError(e.toString()));
    }
  }
}
