import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';

/// Advanced Analytics Screen with Progress Trends and Insights
class AdvancedAnalyticsScreen extends StatefulWidget {
  const AdvancedAnalyticsScreen({super.key});

  @override
  State<AdvancedAnalyticsScreen> createState() =>
      _AdvancedAnalyticsScreenState();
}

class _AdvancedAnalyticsScreenState extends State<AdvancedAnalyticsScreen>
    with SingleTickerProviderStateMixin {
  final UserProgressService _progressService = UserProgressService();
  late TabController _tabController;
  Map<String, dynamic> _userProgress = {};
  bool _isLoading = true;
  String _selectedTimeframe = '7d';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadUserProgress();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _userProgress = progress;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Advanced Analytics'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Trends'),
            Tab(text: 'Achievements'),
            Tab(text: 'Insights'),
          ],
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildTrendsTab(),
                  _buildAchievementsTab(),
                  _buildInsightsTab(),
                ],
              ),
    );
  }

  Widget _buildOverviewTab() {
    final overallProgress = _userProgress['overall'] ?? {};
    final fitnessProgress = _userProgress['fitness'] ?? {};
    final academicsProgress = _userProgress['academics'] ?? {};
    final communityProgress = _userProgress['community'] ?? {};
    final profileProgress = _userProgress['profile'] ?? {};

    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Overall Progress Summary
          _buildSectionHeader('Progress Summary'),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildOverviewCard(
                          'Overall Level',
                          '${overallProgress['level'] ?? 1}',
                          Icons.trending_up,
                          Colors.purple,
                          subtitle:
                              '${overallProgress['experiencePoints'] ?? 0} XP',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildOverviewCard(
                          'Total Points',
                          '${overallProgress['totalPoints'] ?? 0}',
                          Icons.stars,
                          Colors.amber,
                          subtitle: 'All activities',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: _buildOverviewCard(
                          'Achievements',
                          '${overallProgress['totalAchievements'] ?? 0}',
                          Icons.emoji_events,
                          Colors.orange,
                          subtitle: 'Unlocked',
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildOverviewCard(
                          'Profile Complete',
                          '${(profileProgress['profileCompleteness'] ?? 0.0).toInt()}%',
                          Icons.person,
                          Colors.green,
                          subtitle: 'Profile status',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Category Breakdown
          _buildSectionHeader('Category Breakdown'),
          _buildCategoryBreakdown(
            fitnessProgress,
            academicsProgress,
            communityProgress,
          ),

          const SizedBox(height: 24),

          // Recent Activity
          _buildSectionHeader('Recent Activity'),
          _buildRecentActivity(),
        ],
      ),
    );
  }

  Widget _buildTrendsTab() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeframe selector
          _buildTimeframeSelector(),

          const SizedBox(height: 24),

          // Progress trends charts
          _buildSectionHeader('Progress Trends'),
          _buildProgressTrends(),

          const SizedBox(height: 24),

          // Streak analysis
          _buildSectionHeader('Streak Analysis'),
          _buildStreakAnalysis(),

          const SizedBox(height: 24),

          // Performance comparison
          _buildSectionHeader('Performance Comparison'),
          _buildPerformanceComparison(),
        ],
      ),
    );
  }

  Widget _buildAchievementsTab() {
    final allAchievements = <String>[];

    // Collect all achievements from different categories
    final fitnessAchievements =
        _userProgress['fitness']?['achievements'] as List<dynamic>? ?? [];
    final academicsAchievements =
        _userProgress['academics']?['achievements'] as List<dynamic>? ?? [];
    final communityAchievements =
        _userProgress['community']?['achievements'] as List<dynamic>? ?? [];
    final profileAchievements =
        _userProgress['profile']?['achievements'] as List<dynamic>? ?? [];

    allAchievements.addAll(fitnessAchievements.cast<String>());
    allAchievements.addAll(academicsAchievements.cast<String>());
    allAchievements.addAll(communityAchievements.cast<String>());
    allAchievements.addAll(profileAchievements.cast<String>());

    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Achievement Gallery'),

          if (allAchievements.isEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(Icons.emoji_events, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No Achievements Yet',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Start using the app to unlock your first achievement!',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            )
          else
            _buildAchievementGallery(allAchievements),

          const SizedBox(height: 24),

          // Achievement progress
          _buildSectionHeader('Achievement Progress by Category'),
          _buildAchievementProgress(),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('Personalized Insights'),
          _buildPersonalizedInsights(),

          const SizedBox(height: 24),

          _buildSectionHeader('Recommendations'),
          _buildRecommendations(),

          const SizedBox(height: 24),

          _buildSectionHeader('Goal Setting'),
          _buildGoalSetting(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 0, 0, 16),
      child: Text(
        title,
        style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildOverviewCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCategoryBreakdown(
    Map<String, dynamic> fitness,
    Map<String, dynamic> academics,
    Map<String, dynamic> community,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildCategoryRow(
              'Fitness',
              fitness['totalWorkouts'] ?? 0,
              Icons.fitness_center,
              Colors.green,
            ),
            const Divider(),
            _buildCategoryRow(
              'Academics',
              academics['totalQuizzesCompleted'] ?? 0,
              Icons.school,
              Colors.blue,
            ),
            const Divider(),
            _buildCategoryRow(
              'Community',
              community['postsCreated'] ?? 0,
              Icons.people,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryRow(
    String category,
    int value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              category,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            value.toString(),
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    // Mock recent activity data
    final activities = [
      {
        'action': 'Completed Quiz',
        'time': '2 hours ago',
        'icon': Icons.quiz,
        'color': Colors.blue,
      },
      {
        'action': 'Finished Workout',
        'time': '1 day ago',
        'icon': Icons.fitness_center,
        'color': Colors.green,
      },
      {
        'action': 'Read Study Material',
        'time': '2 days ago',
        'icon': Icons.book,
        'color': Colors.orange,
      },
      {
        'action': 'Created Post',
        'time': '3 days ago',
        'icon': Icons.post_add,
        'color': Colors.purple,
      },
    ];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (activities.isEmpty)
              const Text(
                'No recent activity. Start using the app to see your activity here!',
                style: TextStyle(color: Colors.grey),
              )
            else
              ...activities.map(
                (activity) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    children: [
                      Icon(
                        activity['icon'] as IconData,
                        color: activity['color'] as Color,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(child: Text(activity['action'] as String)),
                      Text(
                        activity['time'] as String,
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeframeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Time Period',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children:
                  ['7d', '30d', '90d', 'all'].map((timeframe) {
                    final isSelected = _selectedTimeframe == timeframe;
                    return FilterChip(
                      label: Text(_getTimeframeLabel(timeframe)),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          _selectedTimeframe = timeframe;
                        });
                      },
                      selectedColor: AppTheme.primaryColor.withValues(
                        alpha: 0.2,
                      ),
                      checkmarkColor: AppTheme.primaryColor,
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  String _getTimeframeLabel(String timeframe) {
    switch (timeframe) {
      case '7d':
        return 'Last 7 Days';
      case '30d':
        return 'Last 30 Days';
      case '90d':
        return 'Last 90 Days';
      case 'all':
        return 'All Time';
      default:
        return timeframe;
    }
  }

  Widget _buildProgressTrends() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Progress Over Time',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.show_chart, size: 48, color: Colors.grey),
                    SizedBox(height: 8),
                    Text(
                      'Progress Trend Chart',
                      style: TextStyle(
                        color: Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Chart visualization would appear here',
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakAnalysis() {
    final fitnessStreak = _userProgress['fitness']?['streakDays'] ?? 0;
    final academicsStreak = _userProgress['academics']?['currentStreak'] ?? 0;
    final readingStreak = _userProgress['academics']?['readingStreak'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Current Streaks',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStreakCard(
                    'Fitness',
                    fitnessStreak,
                    Icons.fitness_center,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStreakCard(
                    'Quiz',
                    academicsStreak,
                    Icons.quiz,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStreakCard(
                    'Reading',
                    readingStreak,
                    Icons.book,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStreakCard(
    String title,
    int streak,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            streak.toString(),
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildPerformanceComparison() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance vs Previous Period',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildComparisonRow('Workout Frequency', '+15%', true),
            _buildComparisonRow('Quiz Average Score', '+8%', true),
            _buildComparisonRow('Study Time', '+22%', true),
            _buildComparisonRow('Community Engagement', '-3%', false),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonRow(String metric, String change, bool isPositive) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(child: Text(metric)),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color:
                  isPositive
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                  color: isPositive ? Colors.green : Colors.red,
                ),
                Text(
                  change,
                  style: TextStyle(
                    color: isPositive ? Colors.green : Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementGallery(List<String> achievements) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.emoji_events, size: 32, color: Colors.amber),
                const SizedBox(height: 8),
                Text(
                  achievement,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAchievementProgress() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAchievementCategoryProgress(
              'Fitness',
              (_userProgress['fitness']?['achievements'] as List?)?.length ?? 0,
              10,
            ),
            const SizedBox(height: 12),
            _buildAchievementCategoryProgress(
              'Academics',
              (_userProgress['academics']?['achievements'] as List?)?.length ??
                  0,
              8,
            ),
            const SizedBox(height: 12),
            _buildAchievementCategoryProgress(
              'Community',
              (_userProgress['community']?['achievements'] as List?)?.length ??
                  0,
              6,
            ),
            const SizedBox(height: 12),
            _buildAchievementCategoryProgress(
              'Profile',
              (_userProgress['profile']?['achievements'] as List?)?.length ?? 0,
              4,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAchievementCategoryProgress(
    String category,
    int current,
    int total,
  ) {
    final progress = total > 0 ? current / total : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(category, style: const TextStyle(fontWeight: FontWeight.w500)),
            Text(
              '$current/$total',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[200],
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
      ],
    );
  }

  Widget _buildPersonalizedInsights() {
    final insights = _generatePersonalizedInsights();

    return Column(
      children:
          insights
              .map(
                (insight) => Card(
                  child: ListTile(
                    leading: Icon(
                      insight['icon'] as IconData,
                      color: insight['color'] as Color,
                    ),
                    title: Text(insight['title'] as String),
                    subtitle: Text(insight['description'] as String),
                  ),
                ),
              )
              .toList(),
    );
  }

  List<Map<String, dynamic>> _generatePersonalizedInsights() {
    final insights = <Map<String, dynamic>>[];

    final fitnessProgress = _userProgress['fitness'] ?? {};
    final academicsProgress = _userProgress['academics'] ?? {};
    final communityProgress = _userProgress['community'] ?? {};

    // Fitness insights
    final totalWorkouts = fitnessProgress['totalWorkouts'] ?? 0;
    if (totalWorkouts == 0) {
      insights.add({
        'icon': Icons.fitness_center,
        'color': Colors.green,
        'title': 'Start Your Fitness Journey',
        'description':
            'Begin with your first workout to track your fitness progress!',
      });
    } else if (totalWorkouts < 10) {
      insights.add({
        'icon': Icons.trending_up,
        'color': Colors.blue,
        'title': 'Building Momentum',
        'description': 'You\'re off to a great start! Keep up the consistency.',
      });
    }

    // Academic insights
    final averageScore = academicsProgress['averageQuizScore'] ?? 0.0;
    if (averageScore < 60) {
      insights.add({
        'icon': Icons.school,
        'color': Colors.orange,
        'title': 'Focus on Weak Areas',
        'description': 'Consider reviewing topics where you scored lower.',
      });
    } else if (averageScore > 80) {
      insights.add({
        'icon': Icons.star,
        'color': Colors.amber,
        'title': 'Excellent Performance',
        'description': 'Your quiz scores are outstanding! Keep it up!',
      });
    }

    // Community insights
    final postsCreated = communityProgress['postsCreated'] ?? 0;
    if (postsCreated == 0) {
      insights.add({
        'icon': Icons.people,
        'color': Colors.purple,
        'title': 'Join the Community',
        'description': 'Share your experience by creating your first post!',
      });
    }

    return insights;
  }

  Widget _buildRecommendations() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recommended Actions',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildRecommendationItem(
              'Complete Daily Workout',
              'Maintain your fitness streak',
              Icons.fitness_center,
              Colors.green,
            ),
            _buildRecommendationItem(
              'Take a Practice Quiz',
              'Test your knowledge in weak areas',
              Icons.quiz,
              Colors.blue,
            ),
            _buildRecommendationItem(
              'Read Study Material',
              'Spend 30 minutes reading today',
              Icons.book,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Icon(Icons.chevron_right, color: Colors.grey[400]),
        ],
      ),
    );
  }

  Widget _buildGoalSetting() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Set Your Goals',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Set weekly goals to stay motivated and track your progress.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Show goal setting dialog
                _showGoalSettingDialog();
              },
              icon: const Icon(Icons.flag),
              label: const Text('Set Weekly Goals'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showGoalSettingDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Set Weekly Goals'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Workouts per week',
                    hintText: 'e.g., 3',
                  ),
                  keyboardType: TextInputType.number,
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Study hours per week',
                    hintText: 'e.g., 10',
                  ),
                  keyboardType: TextInputType.number,
                ),
                SizedBox(height: 16),
                TextField(
                  decoration: InputDecoration(
                    labelText: 'Quizzes per week',
                    hintText: 'e.g., 5',
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Goals saved successfully!')),
                  );
                },
                child: const Text('Save Goals'),
              ),
            ],
          ),
    );
  }
}
