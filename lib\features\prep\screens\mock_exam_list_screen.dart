import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/prep/models/mock_exam_model.dart';
import 'package:fit_4_force/features/prep/services/mock_exam_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/premium_badge.dart';

class MockExamListScreen extends StatefulWidget {
  final UserModel user;

  const MockExamListScreen({super.key, required this.user});

  @override
  State<MockExamListScreen> createState() => _MockExamListScreenState();
}

class _MockExamListScreenState extends State<MockExamListScreen> {
  final MockExamService _examService = MockExamService();
  late List<MockExamModel> _allExams;
  late List<MockExamModel> _filteredExams;
  String _selectedCategory = 'All';
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _allExams = _examService.getAllExams();
    _filteredExams = _allExams;

    _searchController.addListener(() {
      _filterExams(_searchController.text);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterExams(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredExams =
            _selectedCategory == 'All'
                ? _allExams
                : _examService.getExamsByCategory(_selectedCategory);
      } else {
        final baseList =
            _selectedCategory == 'All'
                ? _allExams
                : _examService.getExamsByCategory(_selectedCategory);

        _filteredExams =
            baseList.where((exam) {
              return exam.title.toLowerCase().contains(query.toLowerCase()) ||
                  exam.description.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  exam.category.toLowerCase().contains(query.toLowerCase());
            }).toList();
      }
    });
  }

  void _selectCategory(String category) {
    setState(() {
      _selectedCategory = category;
      _filterExams(_searchController.text);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            _isSearching
                ? TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search exams...',
                    border: InputBorder.none,
                  ),
                  autofocus: true,
                )
                : const Text(
                  'Mock Exams',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
        actions: [
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [_buildCategoryFilter(), Expanded(child: _buildExamsList())],
      ),
    );
  }

  Widget _buildCategoryFilter() {
    // Get unique categories
    final categories = _allExams.map((exam) => exam.category).toSet().toList();

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        children: [
          // All category
          _buildCategoryChip('All', null),
          // Other categories
          ...categories.map((category) {
            return _buildCategoryChip(category, _getCategoryColor(category));
          }),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String name, Color? color) {
    final isSelected = _selectedCategory == name;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        selected: isSelected,
        label: Text(name),
        labelStyle: TextStyle(
          color: isSelected ? Colors.white : Colors.black87,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
        backgroundColor: Colors.grey.shade200,
        selectedColor: color ?? AppTheme.primaryColor,
        onSelected: (selected) {
          _selectCategory(name);
        },
      ),
    );
  }

  Widget _buildExamsList() {
    if (_filteredExams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.quiz_outlined, size: 80, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              'No exams found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try a different search or category',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredExams.length,
      itemBuilder: (context, index) {
        return _buildExamCard(_filteredExams[index]);
      },
    );
  }

  Widget _buildExamCard(MockExamModel exam) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: exam.color.withValues(alpha: 0.3 * 255),
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: () {
          _showExamDetailDialog(exam);
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    exam.color.withValues(alpha: 0.2 * 255),
                    exam.color.withValues(alpha: 0.1 * 255),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: exam.color.withValues(alpha: 0.3 * 255),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Icon(
                      _getCategoryIcon(exam.category),
                      color: exam.color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                exam.title,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                            if (exam.isPremium) const PremiumBadge(),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          exam.category,
                          style: TextStyle(
                            color: exam.color,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exam.description,
                    style: const TextStyle(fontSize: 14, color: Colors.black87),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildExamStat(
                        Icons.timer_outlined,
                        '${exam.timeLimit} min',
                        'Time Limit',
                      ),
                      _buildExamStat(
                        Icons.quiz_outlined,
                        '${exam.questions.length}',
                        'Questions',
                      ),
                      _buildExamStat(
                        Icons.people_outline,
                        '${exam.totalAttempts}',
                        'Attempts',
                      ),
                      _buildExamStat(
                        Icons.analytics_outlined,
                        '${exam.averageScore.toStringAsFixed(1)}%',
                        'Avg. Score',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExamStat(IconData icon, String value, String label) {
    return Column(
      children: [
        Icon(icon, size: 20, color: Colors.grey.shade600),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  void _showExamDetailDialog(MockExamModel exam) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            children: [
              Expanded(
                child: Text(exam.title, style: TextStyle(color: exam.color)),
              ),
              if (exam.isPremium) const PremiumBadge(),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(exam.description, style: const TextStyle(fontSize: 14)),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                _buildExamDetailItem(
                  'Category',
                  exam.category,
                  _getCategoryIcon(exam.category),
                ),
                _buildExamDetailItem(
                  'Time Limit',
                  '${exam.timeLimit} minutes',
                  Icons.timer_outlined,
                ),
                _buildExamDetailItem(
                  'Questions',
                  '${exam.questions.length} questions',
                  Icons.quiz_outlined,
                ),
                _buildExamDetailItem(
                  'Total Points',
                  '${exam.totalPoints} points',
                  Icons.star_outline,
                ),
                _buildExamDetailItem(
                  'Average Score',
                  '${exam.averageScore.toStringAsFixed(1)}%',
                  Icons.analytics_outlined,
                ),
                _buildExamDetailItem(
                  'Total Attempts',
                  '${exam.totalAttempts} attempts',
                  Icons.people_outline,
                ),
                const SizedBox(height: 16),
                const Divider(),
                const SizedBox(height: 16),
                const Text(
                  'Question Types:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: 8),
                _buildQuestionTypesList(exam),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CLOSE'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (exam.isPremium && !widget.user.isPremium) {
                  _showPremiumRequiredDialog();
                } else {
                  _startExam(exam);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: exam.color,
                foregroundColor: Colors.white,
              ),
              child: const Text('START EXAM'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildExamDetailItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey.shade600),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionTypesList(MockExamModel exam) {
    // Count question types
    int multipleChoice = 0;
    int trueFalse = 0;
    int shortAnswer = 0;
    int essay = 0;

    for (var question in exam.questions) {
      switch (question.type) {
        case QuestionType.multipleChoice:
          multipleChoice++;
          break;
        case QuestionType.trueFalse:
          trueFalse++;
          break;
        case QuestionType.shortAnswer:
          shortAnswer++;
          break;
        case QuestionType.essay:
          essay++;
          break;
      }
    }

    return Column(
      children: [
        if (multipleChoice > 0)
          _buildQuestionTypeItem('Multiple Choice', multipleChoice),
        if (trueFalse > 0) _buildQuestionTypeItem('True/False', trueFalse),
        if (shortAnswer > 0)
          _buildQuestionTypeItem('Short Answer', shortAnswer),
        if (essay > 0) _buildQuestionTypeItem('Essay', essay),
      ],
    );
  }

  Widget _buildQuestionTypeItem(String type, int count) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Text('$type: $count', style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  void _showPremiumRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Row(
            children: [
              const Text('Premium Required'),
              const SizedBox(width: 8),
              const PremiumBadge(),
            ],
          ),
          content: const Text(
            'This exam is only available to premium users. Upgrade to premium to access all exams and features.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('CANCEL'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // Navigate to premium subscription screen
                // NavigationService().navigateTo(AppRoutes.premium);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('UPGRADE'),
            ),
          ],
        );
      },
    );
  }

  void _startExam(MockExamModel exam) {
    // This will be implemented in the next part
    // For now, just show a message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting exam: ${exam.title}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      // Core Exams
      case 'General Knowledge':
        return Colors.blue;
      case 'Mathematics':
        return Colors.green;
      case 'English':
        return Colors.purple;
      case 'Current Affairs':
        return Colors.orange;

      // Military Agencies
      case 'Nigerian Army':
        return const Color(0xFF2E7D32); // Army Green
      case 'Nigerian Navy':
        return const Color(0xFF1565C0); // Navy Blue
      case 'Nigerian Air Force':
        return const Color(0xFF0277BD); // Sky Blue
      case 'NDA':
        return const Color(0xFF6A1B9A); // Purple for academy
      case 'DSSC':
        return const Color(0xFF8E24AA); // Deep Purple

      // Paramilitary Agencies
      case 'POLAC':
        return const Color(0xFF424242); // Police Gray
      case 'Fire Service':
        return const Color(0xFFD32F2F); // Fire Red
      case 'NSCDC':
        return const Color(0xFF388E3C); // Defence Green
      case 'Customs':
        return const Color(0xFF7B1FA2); // Customs Purple
      case 'Immigration':
        return const Color(0xFF303F9F); // Immigration Blue
      case 'FRSC':
        return const Color(0xFFFF6F00); // Road Safety Orange

      case 'Agency Specific':
        return AppTheme.primaryColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      // Core Exams
      case 'General Knowledge':
        return Icons.lightbulb_outline;
      case 'Mathematics':
        return Icons.calculate;
      case 'English':
        return Icons.menu_book;
      case 'Current Affairs':
        return Icons.public;

      // Military Agencies
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Nigerian Navy':
        return Icons.sailing;
      case 'Nigerian Air Force':
        return Icons.flight;
      case 'NDA':
        return Icons.school;
      case 'DSSC':
        return Icons.psychology;

      // Paramilitary Agencies
      case 'POLAC':
        return Icons.local_police;
      case 'Fire Service':
        return Icons.local_fire_department;
      case 'NSCDC':
        return Icons.security;
      case 'Customs':
        return Icons.inventory;
      case 'Immigration':
        return Icons.card_membership;
      case 'FRSC':
        return Icons.traffic;

      case 'Agency Specific':
        return Icons.shield;
      default:
        return Icons.help_outline;
    }
  }
}
