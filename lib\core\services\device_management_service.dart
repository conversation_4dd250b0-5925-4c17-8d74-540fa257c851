import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';
import '../../shared/models/user_device_model.dart';
import '../config/supabase_config.dart';
import 'device_id_service.dart';

/// Service for managing user devices and enforcing device limits
class DeviceManagementService {
  static final SupabaseClient _client = SupabaseConfig.client;
  static final Logger _logger = Logger();
  static const String _tableName = 'user_devices';
  static const int _maxDevicesPerUser = 2;

  /// Register or update the current device for a user
  static Future<DeviceRegistrationResponse> registerCurrentDevice(
    String userId,
  ) async {
    try {
      _logger.i('📱 Registering device for user: $userId');

      // Get current device information
      final deviceId = await DeviceIdService.getDeviceId();
      final deviceInfo = await DeviceIdService.getDeviceInfo();
      final appVersion = await DeviceIdService.getAppVersion();

      // Call the database function to register the device
      final response = await _client.rpc(
        'register_device',
        params: {
          'p_user_id': userId,
          'p_device_id': deviceId,
          'p_device_name': deviceInfo['deviceName'] ?? 'Unknown Device',
          'p_device_type': deviceInfo['deviceType'] ?? 'mobile',
          'p_platform': deviceInfo['platform'] ?? 'Unknown',
          'p_device_model': deviceInfo['model'],
          'p_os_version': deviceInfo['osVersion'],
          'p_app_version': appVersion,
        },
      );

      final result = DeviceRegistrationResponse.fromJson(
        response as Map<String, dynamic>,
      );

      if (result.success) {
        _logger.i('✅ Device registered successfully: ${result.action}');
      } else {
        _logger.w('⚠️ Device registration failed: ${result.message}');
      }

      return result;
    } catch (e) {
      _logger.e('❌ Error registering device: $e');
      return DeviceRegistrationResponse(
        success: false,
        action: 'error',
        error: 'registration_failed',
        message: 'Failed to register device: $e',
      );
    }
  }

  /// Get all devices for a user
  static Future<List<UserDeviceModel>> getUserDevices(String userId) async {
    try {
      _logger.d('📱 Fetching devices for user: $userId');

      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .order('last_login', ascending: false);

      final devices =
          (response as List)
              .map(
                (json) =>
                    UserDeviceModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();

      _logger.d('📱 Found ${devices.length} devices for user');
      return devices;
    } catch (e) {
      _logger.e('❌ Error fetching user devices: $e');
      return [];
    }
  }

  /// Get only active devices for a user
  static Future<List<UserDeviceModel>> getActiveUserDevices(
    String userId,
  ) async {
    try {
      _logger.d('📱 Fetching active devices for user: $userId');

      final response = await _client
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('last_login', ascending: false);

      final devices =
          (response as List)
              .map(
                (json) =>
                    UserDeviceModel.fromJson(json as Map<String, dynamic>),
              )
              .toList();

      _logger.d('📱 Found ${devices.length} active devices for user');
      return devices;
    } catch (e) {
      _logger.e('❌ Error fetching active user devices: $e');
      return [];
    }
  }

  /// Deactivate a specific device
  static Future<bool> deactivateDevice(String deviceId) async {
    try {
      _logger.i('📱 Deactivating device: $deviceId');

      await _client
          .from(_tableName)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', deviceId);

      _logger.i('✅ Device deactivated successfully');
      return true;
    } catch (e) {
      _logger.e('❌ Error deactivating device: $e');
      return false;
    }
  }

  /// Activate a specific device (if within limits)
  static Future<bool> activateDevice(String deviceId, String userId) async {
    try {
      _logger.i('📱 Activating device: $deviceId');

      // Check current active device count
      final activeDevices = await getActiveUserDevices(userId);
      if (activeDevices.length >= _maxDevicesPerUser) {
        _logger.w('⚠️ Cannot activate device: limit exceeded');
        return false;
      }

      await _client
          .from(_tableName)
          .update({
            'is_active': true,
            'last_login': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', deviceId);

      _logger.i('✅ Device activated successfully');
      return true;
    } catch (e) {
      _logger.e('❌ Error activating device: $e');
      return false;
    }
  }

  /// Update device name
  static Future<bool> updateDeviceName(String deviceId, String newName) async {
    try {
      _logger.i('📱 Updating device name: $deviceId -> $newName');

      await _client
          .from(_tableName)
          .update({
            'device_name': newName,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', deviceId);

      _logger.i('✅ Device name updated successfully');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating device name: $e');
      return false;
    }
  }

  /// Delete a device record
  static Future<bool> deleteDevice(String deviceId) async {
    try {
      _logger.i('📱 Deleting device: $deviceId');

      await _client.from(_tableName).delete().eq('id', deviceId);

      _logger.i('✅ Device deleted successfully');
      return true;
    } catch (e) {
      _logger.e('❌ Error deleting device: $e');
      return false;
    }
  }

  /// Update last login time for current device
  static Future<bool> updateLastLogin(String userId) async {
    try {
      final deviceId = await DeviceIdService.getDeviceId();

      await _client
          .from(_tableName)
          .update({
            'last_login': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('device_id', deviceId);

      _logger.d('📱 Updated last login for current device');
      return true;
    } catch (e) {
      _logger.e('❌ Error updating last login: $e');
      return false;
    }
  }

  /// Check if current device is registered and active
  static Future<bool> isCurrentDeviceActive(String userId) async {
    try {
      final deviceId = await DeviceIdService.getDeviceId();

      final response =
          await _client
              .from(_tableName)
              .select('is_active')
              .eq('user_id', userId)
              .eq('device_id', deviceId)
              .maybeSingle();

      if (response == null) {
        return false;
      }

      return response['is_active'] as bool;
    } catch (e) {
      _logger.e('❌ Error checking device status: $e');
      return false;
    }
  }

  /// Get device count for a user
  static Future<int> getActiveDeviceCount(String userId) async {
    try {
      final response = await _client.rpc(
        'get_active_device_count',
        params: {'user_uuid': userId},
      );

      return response as int;
    } catch (e) {
      _logger.e('❌ Error getting device count: $e');
      return 0;
    }
  }

  /// Check if user can add another device
  static Future<bool> canAddDevice(String userId) async {
    try {
      final count = await getActiveDeviceCount(userId);
      return count < _maxDevicesPerUser;
    } catch (e) {
      _logger.e('❌ Error checking device limit: $e');
      return false;
    }
  }

  /// Deactivate current device (for logout)
  static Future<bool> deactivateCurrentDevice(String userId) async {
    try {
      final deviceId = await DeviceIdService.getDeviceId();

      await _client
          .from(_tableName)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('device_id', deviceId);

      _logger.i('📱 Current device deactivated');
      return true;
    } catch (e) {
      _logger.e('❌ Error deactivating current device: $e');
      return false;
    }
  }

  /// Get current device ID
  static Future<String> getCurrentDeviceId() async {
    return await DeviceIdService.getDeviceId();
  }

  /// Remove a device completely
  static Future<bool> removeDevice(String userId, String deviceId) async {
    try {
      _logger.i('📱 Removing device: $deviceId for user: $userId');

      await _client
          .from(_tableName)
          .delete()
          .eq('user_id', userId)
          .eq('device_id', deviceId);

      _logger.i('✅ Device removed successfully');
      return true;
    } catch (e) {
      _logger.e('❌ Error removing device: $e');
      return false;
    }
  }

  /// Clean up old inactive devices (called periodically)
  static Future<int> cleanupOldDevices() async {
    try {
      _logger.i('📱 Running device cleanup...');

      final response = await _client.rpc('cleanup_inactive_devices');
      final affectedRows = response as int;

      _logger.i('✅ Device cleanup completed: $affectedRows devices affected');
      return affectedRows;
    } catch (e) {
      _logger.e('❌ Error during device cleanup: $e');
      return 0;
    }
  }

  /// Get maximum allowed devices per user
  static int get maxDevicesPerUser => _maxDevicesPerUser;
}
