import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/workout_session_screen.dart';
import 'package:fit_4_force/features/fitness/screens/exercise_detail_screen.dart';

class WorkoutDayScreen extends StatefulWidget {
  final String workoutId;
  final String dayTitle;

  const WorkoutDayScreen({
    super.key,
    required this.workoutId,
    required this.dayTitle,
  });

  @override
  State<WorkoutDayScreen> createState() => _WorkoutDayScreenState();
}

class _WorkoutDayScreenState extends State<WorkoutDayScreen> {
  late WorkoutModel _workout;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWorkout();
  }

  Future<void> _loadWorkout() async {
    // In a real app, you would fetch this from your service
    // For now, we'll create a mock workout
    await Future.delayed(const Duration(milliseconds: 500));

    // Different exercises based on workout ID
    List<ExerciseModel> exercises = [];
    String name = '';
    String description = '';
    String imageUrl = '';
    String category = '';
    int duration = 45;
    int calories = 350;
    IconData icon = Icons.fitness_center;
    Color color = Colors.blue;

    switch (widget.workoutId) {
      case 'hiit_workout':
        name = 'Fat Loss / HIIT Workout';
        description =
            'High-intensity interval training for maximum calorie burn';
        imageUrl = 'assets/images/workouts/hiit.jpg';
        category = 'HIIT';
        duration = 30;
        calories = 400;
        icon = Icons.local_fire_department;
        color = Colors.red;
        exercises = [
          ExerciseModel(
            id: 'hiit1',
            name: 'Jumping Jacks',
            description: 'A classic cardio exercise to warm up',
            imageUrl: 'assets/images/exercises/jumping_jacks.png',
            videoUrl: 'https://example.com/videos/jumping_jacks.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit2',
            name: 'Mountain Climbers',
            description: 'Dynamic exercise that works multiple muscle groups',
            imageUrl: 'assets/images/exercises/mountain_climbers.png',
            videoUrl: 'https://example.com/videos/mountain_climbers.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit3',
            name: 'Burpees',
            description:
                'Full-body exercise that builds strength and endurance',
            imageUrl: 'assets/images/exercises/burpees.png',
            videoUrl: 'https://example.com/videos/burpees.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit4',
            name: 'High Knees',
            description:
                'Running in place with high knees to elevate heart rate',
            imageUrl: 'assets/images/exercises/high_knees.png',
            videoUrl: 'https://example.com/videos/high_knees.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit5',
            name: 'Jump Squats',
            description: 'Explosive lower body exercise that builds power',
            imageUrl: 'assets/images/exercises/jump_squats.png',
            videoUrl: 'https://example.com/videos/jump_squats.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit6',
            name: 'Skater Jumps',
            description: 'Lateral jumps that mimic a speed skater\'s movement',
            imageUrl: 'assets/images/exercises/skater_jumps.png',
            videoUrl: 'https://example.com/videos/skater_jumps.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit7',
            name: 'Plank Jacks',
            description:
                'Combination of plank and jumping jacks for core and cardio',
            imageUrl: 'assets/images/exercises/plank_jacks.png',
            videoUrl: 'https://example.com/videos/plank_jacks.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit8',
            name: 'Tuck Jumps',
            description: 'Explosive jumps bringing knees to chest',
            imageUrl: 'assets/images/exercises/tuck_jumps.png',
            videoUrl: 'https://example.com/videos/tuck_jumps.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit9',
            name: 'Speed Rope Jumping',
            description: 'Fast-paced jump rope for cardiovascular conditioning',
            imageUrl: 'assets/images/exercises/speed_rope.png',
            videoUrl: 'https://example.com/videos/speed_rope.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit10',
            name: 'Box Jumps',
            description: 'Explosive jumps onto an elevated platform',
            imageUrl: 'assets/images/exercises/box_jumps.png',
            videoUrl: 'https://example.com/videos/box_jumps.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit11',
            name: 'Kettlebell Swings',
            description:
                'Dynamic exercise using a kettlebell for power and conditioning',
            imageUrl: 'assets/images/exercises/kettlebell_swings.png',
            videoUrl: 'https://example.com/videos/kettlebell_swings.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit12',
            name: 'Battle Ropes',
            description:
                'High-intensity rope exercise for upper body and cardio',
            imageUrl: 'assets/images/exercises/battle_ropes.png',
            videoUrl: 'https://example.com/videos/battle_ropes.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit13',
            name: 'Lateral Bounds',
            description: 'Side-to-side jumps to improve agility and power',
            imageUrl: 'assets/images/exercises/lateral_bounds.png',
            videoUrl: 'https://example.com/videos/lateral_bounds.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit14',
            name: 'Sprinter Starts',
            description: 'Explosive sprinting from a crouched position',
            imageUrl: 'assets/images/exercises/sprinter_starts.png',
            videoUrl: 'https://example.com/videos/sprinter_starts.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'hiit15',
            name: 'Medicine Ball Slams',
            description:
                'Explosive exercise using a medicine ball for full-body power',
            imageUrl: 'assets/images/exercises/medicine_ball_slams.png',
            videoUrl: 'https://example.com/videos/medicine_ball_slams.mp4',
            duration: 30,
            sets: 1,
            reps: 0,
            restTime: 15,
          ),
        ];
        break;

      case 'strength_workout':
        name = 'Strength Building Workout';
        description = 'Build muscle and increase strength with these exercises';
        imageUrl = 'assets/images/workouts/strength.jpg';
        category = 'Strength';
        duration = 50;
        calories = 320;
        icon = Icons.fitness_center;
        color = Colors.green;
        exercises = [
          ExerciseModel(
            id: 'str1',
            name: 'Push-Ups',
            description:
                'Classic upper body exercise for chest, shoulders, and triceps',
            imageUrl: 'assets/images/exercises/pushups.png',
            videoUrl: 'https://example.com/videos/pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str2',
            name: 'Squats',
            description:
                'Lower body exercise targeting quads, hamstrings, and glutes',
            imageUrl: 'assets/images/exercises/squats.png',
            videoUrl: 'https://example.com/videos/squats.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str3',
            name: 'Lunges',
            description: 'Unilateral exercise for leg strength and balance',
            imageUrl: 'assets/images/exercises/lunges.png',
            videoUrl: 'https://example.com/videos/lunges.mp4',
            duration: 0,
            sets: 3,
            reps: 10,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str4',
            name: 'Dips',
            description: 'Upper body exercise focusing on triceps and chest',
            imageUrl: 'assets/images/exercises/dips.png',
            videoUrl: 'https://example.com/videos/dips.mp4',
            duration: 0,
            sets: 3,
            reps: 8,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str5',
            name: 'Pull-Ups',
            description: 'Upper body exercise for back and biceps',
            imageUrl: 'assets/images/exercises/pullups.png',
            videoUrl: 'https://example.com/videos/pullups.mp4',
            duration: 0,
            sets: 3,
            reps: 6,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str6',
            name: 'Bench Press',
            description: 'Compound exercise for chest, shoulders, and triceps',
            imageUrl: 'assets/images/exercises/bench_press.png',
            videoUrl: 'https://example.com/videos/bench_press.mp4',
            duration: 0,
            sets: 3,
            reps: 8,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str7',
            name: 'Deadlifts',
            description:
                'Compound exercise for posterior chain and overall strength',
            imageUrl: 'assets/images/exercises/deadlifts.png',
            videoUrl: 'https://example.com/videos/deadlifts.mp4',
            duration: 0,
            sets: 3,
            reps: 8,
            restTime: 90,
          ),
          ExerciseModel(
            id: 'str8',
            name: 'Shoulder Press',
            description: 'Vertical pressing movement for shoulder development',
            imageUrl: 'assets/images/exercises/shoulder_press.png',
            videoUrl: 'https://example.com/videos/shoulder_press.mp4',
            duration: 0,
            sets: 3,
            reps: 10,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str9',
            name: 'Bent-Over Rows',
            description: 'Horizontal pulling movement for back development',
            imageUrl: 'assets/images/exercises/bent_over_rows.png',
            videoUrl: 'https://example.com/videos/bent_over_rows.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str10',
            name: 'Bicep Curls',
            description: 'Isolation exercise for bicep development',
            imageUrl: 'assets/images/exercises/bicep_curls.png',
            videoUrl: 'https://example.com/videos/bicep_curls.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'str11',
            name: 'Tricep Extensions',
            description: 'Isolation exercise for tricep development',
            imageUrl: 'assets/images/exercises/tricep_extensions.png',
            videoUrl: 'https://example.com/videos/tricep_extensions.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'str12',
            name: 'Calf Raises',
            description: 'Isolation exercise for calf development',
            imageUrl: 'assets/images/exercises/calf_raises.png',
            videoUrl: 'https://example.com/videos/calf_raises.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'str13',
            name: 'Lat Pulldowns',
            description: 'Machine exercise for back development',
            imageUrl: 'assets/images/exercises/lat_pulldowns.png',
            videoUrl: 'https://example.com/videos/lat_pulldowns.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str14',
            name: 'Leg Press',
            description: 'Machine exercise for lower body development',
            imageUrl: 'assets/images/exercises/leg_press.png',
            videoUrl: 'https://example.com/videos/leg_press.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'str15',
            name: 'Face Pulls',
            description: 'Cable exercise for rear deltoids and upper back',
            imageUrl: 'assets/images/exercises/face_pulls.png',
            videoUrl: 'https://example.com/videos/face_pulls.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 45,
          ),
        ];
        break;

      case 'military_workout':
        name = 'Military Fitness Workout';
        description =
            'Training specifically designed for military fitness tests';
        imageUrl = 'assets/images/workouts/military.jpg';
        category = 'Military';
        duration = 45;
        calories = 380;
        icon = Icons.military_tech;
        color = Colors.blue;
        exercises = [
          ExerciseModel(
            id: 'mil1',
            name: 'Military Push-Ups',
            description: 'Standard push-ups with proper military form',
            imageUrl: 'assets/images/exercises/pushups.png',
            videoUrl: 'https://example.com/videos/military_pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 20,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'mil2',
            name: 'Sit-Ups',
            description: 'Core exercise required in military fitness tests',
            imageUrl: 'assets/images/exercises/situps.png',
            videoUrl: 'https://example.com/videos/situps.mp4',
            duration: 0,
            sets: 3,
            reps: 25,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'mil3',
            name: 'Bear Crawls',
            description:
                'Full-body exercise that builds strength and coordination',
            imageUrl: 'assets/images/exercises/bear_crawls.png',
            videoUrl: 'https://example.com/videos/bear_crawls.mp4',
            duration: 30,
            sets: 3,
            reps: 0,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'mil4',
            name: 'Shuttle Runs',
            description: 'Agility and speed exercise',
            imageUrl: 'assets/images/exercises/shuttle_runs.png',
            videoUrl: 'https://example.com/videos/shuttle_runs.mp4',
            duration: 60,
            sets: 3,
            reps: 0,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'mil5',
            name: 'Fireman Carries',
            description: 'Partner exercise to build strength and endurance',
            imageUrl: 'assets/images/exercises/fireman_carries.png',
            videoUrl: 'https://example.com/videos/fireman_carries.mp4',
            duration: 30,
            sets: 2,
            reps: 0,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'mil6',
            name: 'Rope Climbs',
            description: 'Upper body and grip strength exercise',
            imageUrl: 'assets/images/exercises/rope_climbs.png',
            videoUrl: 'https://example.com/videos/rope_climbs.mp4',
            duration: 0,
            sets: 3,
            reps: 3,
            restTime: 90,
          ),
          ExerciseModel(
            id: 'mil7',
            name: 'Sandbag Carries',
            description: 'Functional strength and endurance exercise',
            imageUrl: 'assets/images/exercises/sandbag_carries.png',
            videoUrl: 'https://example.com/videos/sandbag_carries.mp4',
            duration: 60,
            sets: 3,
            reps: 0,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'mil8',
            name: 'Ruck Marches',
            description: 'Weighted walking for endurance and mental toughness',
            imageUrl: 'assets/images/exercises/ruck_marches.png',
            videoUrl: 'https://example.com/videos/ruck_marches.mp4',
            duration: 300,
            sets: 1,
            reps: 0,
            restTime: 120,
          ),
          ExerciseModel(
            id: 'mil9',
            name: 'Pull-Up Pyramids',
            description: 'Progressive pull-up sets for upper body strength',
            imageUrl: 'assets/images/exercises/pullup_pyramids.png',
            videoUrl: 'https://example.com/videos/pullup_pyramids.mp4',
            duration: 0,
            sets: 5,
            reps: 5,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'mil10',
            name: 'Buddy Drags',
            description: 'Partner rescue exercise for strength and endurance',
            imageUrl: 'assets/images/exercises/buddy_drags.png',
            videoUrl: 'https://example.com/videos/buddy_drags.mp4',
            duration: 30,
            sets: 2,
            reps: 0,
            restTime: 60,
          ),
          ExerciseModel(
            id: 'mil11',
            name: 'Ammo Can Lifts',
            description: 'Military-specific strength exercise',
            imageUrl: 'assets/images/exercises/ammo_can_lifts.png',
            videoUrl: 'https://example.com/videos/ammo_can_lifts.mp4',
            duration: 0,
            sets: 3,
            reps: 20,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'mil12',
            name: 'Obstacle Course',
            description: 'Multi-station agility and strength challenge',
            imageUrl: 'assets/images/exercises/obstacle_course.png',
            videoUrl: 'https://example.com/videos/obstacle_course.mp4',
            duration: 180,
            sets: 1,
            reps: 0,
            restTime: 120,
          ),
          ExerciseModel(
            id: 'mil13',
            name: 'Combat Fitness Test',
            description: 'Military-specific fitness assessment',
            imageUrl: 'assets/images/exercises/combat_fitness_test.png',
            videoUrl: 'https://example.com/videos/combat_fitness_test.mp4',
            duration: 300,
            sets: 1,
            reps: 0,
            restTime: 0,
          ),
          ExerciseModel(
            id: 'mil14',
            name: 'Tactical Lunges',
            description: 'Weighted lunges with tactical movement patterns',
            imageUrl: 'assets/images/exercises/tactical_lunges.png',
            videoUrl: 'https://example.com/videos/tactical_lunges.mp4',
            duration: 0,
            sets: 3,
            reps: 20,
            restTime: 45,
          ),
          ExerciseModel(
            id: 'mil15',
            name: 'Battle Buddy Squats',
            description: 'Partner squats for strength and teamwork',
            imageUrl: 'assets/images/exercises/battle_buddy_squats.png',
            videoUrl: 'https://example.com/videos/battle_buddy_squats.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 60,
          ),
        ];
        break;

      case 'core_flexibility_workout':
        name = 'Core & Flexibility Workout';
        description = 'Improve core strength and overall flexibility';
        imageUrl = 'assets/images/workouts/core.jpg';
        category = 'Core & Flexibility';
        duration = 40;
        calories = 250;
        icon = Icons.accessibility_new;
        color = Colors.purple;
        exercises = [
          ExerciseModel(
            id: 'core1',
            name: 'Plank',
            description: 'Isometric core exercise that builds stability',
            imageUrl: 'assets/images/exercises/plank.png',
            videoUrl: 'https://example.com/videos/plank.mp4',
            duration: 30,
            sets: 3,
            reps: 0,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core2',
            name: 'Russian Twists',
            description: 'Rotational exercise for obliques and core',
            imageUrl: 'assets/images/exercises/russian_twists.png',
            videoUrl: 'https://example.com/videos/russian_twists.mp4',
            duration: 0,
            sets: 3,
            reps: 20,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core3',
            name: 'Leg Raises',
            description: 'Lower abdominal exercise',
            imageUrl: 'assets/images/exercises/leg_raises.png',
            videoUrl: 'https://example.com/videos/leg_raises.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core4',
            name: 'Hip Flexor Stretch',
            description: 'Stretch to improve hip mobility',
            imageUrl: 'assets/images/exercises/hip_flexor_stretch.png',
            videoUrl: 'https://example.com/videos/hip_flexor_stretch.mp4',
            duration: 30,
            sets: 2,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'core5',
            name: 'Cobra Stretch',
            description: 'Back extension to improve spine mobility',
            imageUrl: 'assets/images/exercises/cobra_stretch.png',
            videoUrl: 'https://example.com/videos/cobra_stretch.mp4',
            duration: 30,
            sets: 2,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'core6',
            name: 'Bird Dog',
            description: 'Core stability exercise for back health',
            imageUrl: 'assets/images/exercises/bird_dog.png',
            videoUrl: 'https://example.com/videos/bird_dog.mp4',
            duration: 0,
            sets: 3,
            reps: 10,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core7',
            name: 'Superman',
            description: 'Back extension exercise for posterior chain',
            imageUrl: 'assets/images/exercises/superman.png',
            videoUrl: 'https://example.com/videos/superman.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core8',
            name: 'Glute Bridges',
            description: 'Hip extension exercise for glute activation',
            imageUrl: 'assets/images/exercises/glute_bridges.png',
            videoUrl: 'https://example.com/videos/glute_bridges.mp4',
            duration: 0,
            sets: 3,
            reps: 15,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core9',
            name: 'Side Plank',
            description: 'Lateral core exercise for obliques',
            imageUrl: 'assets/images/exercises/side_plank.png',
            videoUrl: 'https://example.com/videos/side_plank.mp4',
            duration: 30,
            sets: 2,
            reps: 0,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core10',
            name: 'Pigeon Pose',
            description: 'Hip opener for improved mobility',
            imageUrl: 'assets/images/exercises/pigeon_pose.png',
            videoUrl: 'https://example.com/videos/pigeon_pose.mp4',
            duration: 60,
            sets: 2,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'core11',
            name: 'Cat-Cow Stretch',
            description: 'Dynamic spine mobility exercise',
            imageUrl: 'assets/images/exercises/cat_cow.png',
            videoUrl: 'https://example.com/videos/cat_cow.mp4',
            duration: 60,
            sets: 1,
            reps: 0,
            restTime: 0,
          ),
          ExerciseModel(
            id: 'core12',
            name: 'Hollow Body Hold',
            description: 'Advanced core stability exercise',
            imageUrl: 'assets/images/exercises/hollow_body.png',
            videoUrl: 'https://example.com/videos/hollow_body.mp4',
            duration: 30,
            sets: 3,
            reps: 0,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core13',
            name: 'Seated Forward Fold',
            description: 'Hamstring and lower back stretch',
            imageUrl: 'assets/images/exercises/seated_forward_fold.png',
            videoUrl: 'https://example.com/videos/seated_forward_fold.mp4',
            duration: 45,
            sets: 2,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'core14',
            name: 'Butterfly Stretch',
            description: 'Inner thigh and hip opener',
            imageUrl: 'assets/images/exercises/butterfly_stretch.png',
            videoUrl: 'https://example.com/videos/butterfly_stretch.mp4',
            duration: 45,
            sets: 2,
            reps: 0,
            restTime: 15,
          ),
          ExerciseModel(
            id: 'core15',
            name: 'Windshield Wipers',
            description: 'Rotational core exercise for obliques',
            imageUrl: 'assets/images/exercises/windshield_wipers.png',
            videoUrl: 'https://example.com/videos/windshield_wipers.mp4',
            duration: 0,
            sets: 3,
            reps: 10,
            restTime: 30,
          ),
        ];
        break;

      default:
        // Default workout if ID doesn't match
        name = 'Full Body Workout';
        description = 'A complete workout targeting all major muscle groups';
        imageUrl = 'assets/images/workouts/full_body.jpg';
        category = 'Strength';
        duration = 45;
        calories = 350;
        icon = Icons.fitness_center;
        color = Colors.blue;
        exercises = [
          ExerciseModel(
            id: '1',
            name: 'Jumping Jacks',
            description: 'A classic cardio exercise to warm up',
            imageUrl: 'assets/images/exercises/jumping_jacks.png',
            videoUrl: 'https://example.com/videos/jumping_jacks.mp4',
            duration: 20,
            sets: 1,
            reps: 0,
            restTime: 10,
          ),
          ExerciseModel(
            id: '2',
            name: 'Incline Push-Ups',
            description: 'Push-ups with hands elevated on a bench or step',
            imageUrl: 'assets/images/exercises/incline_pushups.png',
            videoUrl: 'https://example.com/videos/incline_pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 16,
            restTime: 30,
          ),
          ExerciseModel(
            id: '3',
            name: 'Knee Push-Ups',
            description: 'Modified push-ups with knees on the ground',
            imageUrl: 'assets/images/exercises/knee_pushups.png',
            videoUrl: 'https://example.com/videos/knee_pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 10,
            restTime: 30,
          ),
          ExerciseModel(
            id: '4',
            name: 'Push-Ups',
            description: 'Standard push-ups',
            imageUrl: 'assets/images/exercises/pushups.png',
            videoUrl: 'https://example.com/videos/pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 8,
            restTime: 45,
          ),
          ExerciseModel(
            id: '5',
            name: 'Wide Arm Push-Ups',
            description: 'Push-ups with arms placed wider than shoulders',
            imageUrl: 'assets/images/exercises/wide_pushups.png',
            videoUrl: 'https://example.com/videos/wide_pushups.mp4',
            duration: 0,
            sets: 3,
            reps: 6,
            restTime: 60,
          ),
        ];
    }

    _workout = WorkoutModel(
      id: widget.workoutId,
      name: name,
      description: description,
      imageUrl: imageUrl,
      category: category,
      duration: duration,
      calories: calories,
      exercises: exercises,
      icon: icon,
      color: color,
    );

    setState(() {
      _isLoading = false;
    });
  }

  void _startWorkout() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WorkoutSessionScreen(workoutId: widget.workoutId),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.dayTitle),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () {
              // Show options menu
            },
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildWorkoutContent(),
    );
  }

  Widget _buildWorkoutContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Exercises',
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
        ),
        Expanded(
          child: ListView.separated(
            itemCount: _workout.exercises.length,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              final exercise = _workout.exercises[index];
              return _buildExerciseItem(exercise);
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _startWorkout,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              child: const Text(
                'Start',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExerciseItem(ExerciseModel exercise) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ExerciseDetailScreen(
                  exercise:
                      exercise is Map<String, dynamic>
                          ? exercise
                          : (exercise as dynamic).toJson(),
                ),
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        child: Row(
          children: [
            // Exercise image
            Hero(
              tag: 'exercise_image_${exercise.id}',
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child:
                    exercise.imageUrl.startsWith('http')
                        ? Image.network(
                          exercise.imageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey.shade200,
                              child: const Icon(
                                Icons.fitness_center,
                                color: Colors.grey,
                              ),
                            );
                          },
                        )
                        : Image.asset(
                          exercise.imageUrl,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 80,
                              height: 80,
                              color: Colors.grey.shade200,
                              child: const Icon(
                                Icons.fitness_center,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
              ),
            ),
            const SizedBox(width: 16),

            // Exercise details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    exercise.duration > 0
                        ? '00:${exercise.duration.toString().padLeft(2, '0')}'
                        : 'x${exercise.reps}',
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                  ),
                ],
              ),
            ),

            // Navigation arrow
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey.shade400,
            ),
          ],
        ),
      ),
    );
  }
}
