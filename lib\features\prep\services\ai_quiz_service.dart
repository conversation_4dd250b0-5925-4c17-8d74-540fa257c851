import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/quiz_question_model.dart';

/// AI-powered quiz service using DeepSeek R1 model for premium features
class AIQuizService {
  static const String _apiKey = '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';
  static const String _baseUrl = 'https://api.deepseek.com/v1/chat/completions';
  
  /// Generate additional quiz questions using AI
  Future<List<QuizQuestionModel>> generateQuestions({
    required String category,
    required String? userAgency,
    required int count,
    String difficulty = 'intermediate',
  }) async {
    try {
      final prompt = _buildQuestionGenerationPrompt(
        category: category,
        userAgency: userAgency,
        count: count,
        difficulty: difficulty,
      );

      final response = await _makeAIRequest(prompt);
      return _parseGeneratedQuestions(response, category, userAgency, difficulty);
    } catch (e) {
      print('❌ Error generating AI questions: $e');
      return []; 
    }
  }

  /// Generate detailed AI explanation for a question
  Future<String> generateDetailedExplanation({
    required QuizQuestionModel question,
    required String? userAgency,
  }) async {
    try {
      final prompt = _buildExplanationPrompt(question, userAgency);
      final response = await _makeAIRequest(prompt);
      return _parseExplanationResponse(response);
    } catch (e) {
      print('❌ Error generating AI explanation: $e');
      return question.explanation; // Fallback to static explanation
    }
  }

  /// Make request to DeepSeek R1 API
  Future<String> _makeAIRequest(String prompt) async {
    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_apiKey',
    };

    final body = jsonEncode({
      'model': 'deepseek-chat',
      'messages': [
        {
          'role': 'system',
          'content': 'You are an expert in Nigerian military recruitment exam preparation. Generate high-quality educational content for JSS2-SS3 level students preparing for Nigerian military entrance exams.',
        },
        {
          'role': 'user',
          'content': prompt,
        },
      ],
      'max_tokens': 2000,
      'temperature': 0.7,
      'stream': false,
    });

    final response = await http.post(
      Uri.parse(_baseUrl),
      headers: headers,
      body: body,
    ).timeout(const Duration(seconds: 30));

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return data['choices'][0]['message']['content'];
    } else {
      throw HttpException('AI API request failed: ${response.statusCode}');
    }
  }

  /// Build prompt for question generation
  String _buildQuestionGenerationPrompt({
    required String category,
    required String? userAgency,
    required int count,
    required String difficulty,
  }) {
    final agencyContext = userAgency != null 
        ? 'The user is preparing for $userAgency recruitment.'
        : 'The user is preparing for Nigerian military recruitment.';

    return '''
Generate $count multiple-choice quiz questions for Nigerian military recruitment exam preparation.

Category: $category
Difficulty Level: $difficulty (JSS2-SS3 level)
Context: $agencyContext

Requirements:
1. Each question must have exactly 4 options (A, B, C, D)
2. Questions must be appropriate for JSS2-SS3 educational level
3. Content must be relevant to Nigerian military recruitment
4. Include proper explanations for correct answers
5. For agency history questions, focus on $userAgency if specified

Format each question as JSON:
{
  "question": "Question text here",
  "options": ["Option A", "Option B", "Option C", "Option D"],
  "correctAnswerIndex": 0,
  "explanation": "Detailed explanation here",
  "subcategory": "Specific subcategory"
}

Category Guidelines:
- English Language: Grammar, vocabulary, comprehension, military terminology
- Mathematics: Algebra, geometry, percentages, word problems with military context
- Military History: Agency establishment dates, mottos, key figures, organizational structure

Return only a JSON array of questions, no additional text.
''';
  }

  /// Build prompt for detailed explanation
  String _buildExplanationPrompt(QuizQuestionModel question, String? userAgency) {
    final agencyContext = userAgency != null 
        ? 'The user is preparing for $userAgency recruitment.'
        : 'The user is preparing for Nigerian military recruitment.';

    return '''
Provide a detailed explanation for this quiz question:

Question: ${question.question}
Options: ${question.options.join(', ')}
Correct Answer: ${question.correctAnswer}
Current Explanation: ${question.explanation}

Context: $agencyContext
Category: ${question.category}
Difficulty: ${question.difficulty}

Please provide a comprehensive explanation that includes:
1. Why the correct answer is right (with detailed reasoning)
2. Why each incorrect option is wrong
3. Additional context and background information
4. Study tips related to this topic
5. How this relates to Nigerian military recruitment preparation

Make the explanation educational and helpful for JSS2-SS3 level students.
Keep it concise but informative (maximum 300 words).
''';
  }

  /// Parse generated questions from AI response
  List<QuizQuestionModel> _parseGeneratedQuestions(
    String response,
    String category,
    String? userAgency,
    String difficulty,
  ) {
    try {
      // Clean the response to extract JSON
      String cleanResponse = response.trim();
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }

      final List<dynamic> questionsJson = jsonDecode(cleanResponse);
      final List<QuizQuestionModel> questions = [];

      for (int i = 0; i < questionsJson.length; i++) {
        final questionData = questionsJson[i];
        
        final question = QuizQuestionModel(
          id: 'ai_${DateTime.now().millisecondsSinceEpoch}_$i',
          question: questionData['question'] ?? '',
          options: List<String>.from(questionData['options'] ?? []),
          correctAnswerIndex: questionData['correctAnswerIndex'] ?? 0,
          category: category,
          subcategory: questionData['subcategory'] ?? category,
          difficulty: difficulty,
          explanation: questionData['explanation'] ?? '',
          agency: userAgency,
          tags: ['ai-generated', category.toLowerCase().replaceAll(' ', '-')],
          timeLimit: 60,
          points: 1.0,
        );

        // Validate question has 4 options and valid correct answer index
        if (question.options.length == 4 && 
            question.correctAnswerIndex >= 0 && 
            question.correctAnswerIndex < 4 &&
            question.question.isNotEmpty) {
          questions.add(question);
        }
      }

      return questions;
    } catch (e) {
      print('❌ Error parsing AI generated questions: $e');
      return [];
    }
  }

  /// Parse explanation response from AI
  String _parseExplanationResponse(String response) {
    // Clean up the response and return
    return response.trim();
  }

  /// Check if AI service is available
  Future<bool> isServiceAvailable() async {
    try {
      final response = await http.get(
        Uri.parse('https://api.deepseek.com/v1/models'),
        headers: {'Authorization': 'Bearer $_apiKey'},
      ).timeout(const Duration(seconds: 10));
      
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  /// Generate study tips based on quiz performance
  Future<String> generateStudyTips({
    required List<QuizQuestionModel> incorrectQuestions,
    required String? userAgency,
  }) async {
    if (incorrectQuestions.isEmpty) {
      return 'Excellent work! You answered all questions correctly. Keep practicing to maintain your performance.';
    }

    try {
      final categories = incorrectQuestions.map((q) => q.category).toSet().join(', ');
      final agencyContext = userAgency != null 
          ? 'preparing for $userAgency recruitment'
          : 'preparing for Nigerian military recruitment';

      final prompt = '''
Based on the quiz performance, provide personalized study tips for a student $agencyContext.

Areas needing improvement: $categories
Number of incorrect questions: ${incorrectQuestions.length}

Provide 3-5 specific, actionable study tips that will help improve performance in these areas.
Focus on Nigerian military recruitment exam preparation at JSS2-SS3 level.
Keep tips practical and motivating.
''';

      final response = await _makeAIRequest(prompt);
      return response.trim();
    } catch (e) {
      print('❌ Error generating study tips: $e');
      return 'Focus on reviewing the topics you missed. Practice regularly and don\'t hesitate to seek help when needed.';
    }
  }
}
