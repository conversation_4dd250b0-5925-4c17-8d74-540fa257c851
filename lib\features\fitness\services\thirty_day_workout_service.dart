import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

// Difficulty levels
enum WorkoutLevel { beginner, intermediate, advanced }

// Workout durations in minutes
enum WorkoutDuration {
  quick(15),
  medium(30),
  standard(45),
  extended(60);

  const WorkoutDuration(this.minutes);
  final int minutes;
}

// Achievement types
enum AchievementType {
  firstWorkout,
  weekCompleted,
  twoWeeksCompleted,
  halfwayPoint,
  threeWeeksCompleted,
  programCompleted,
  perfectWeek,
  streakFive,
  streakTen,
  caloriesBurned500,
  caloriesBurned1000,
  caloriesBurned2000,
}

// Achievement model
class Achievement {
  final AchievementType type;
  final String title;
  final String description;
  final String icon;
  final DateTime? unlockedAt;
  final bool isUnlocked;

  Achievement({
    required this.type,
    required this.title,
    required this.description,
    required this.icon,
    this.unlockedAt,
    this.isUnlocked = false,
  });

  Achievement copyWith({DateTime? unlockedAt, bool? isUnlocked}) {
    return Achievement(
      type: type,
      title: title,
      description: description,
      icon: icon,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      isUnlocked: isUnlocked ?? this.isUnlocked,
    );
  }
}

// Workout analytics model
class WorkoutAnalytics {
  final int totalWorkouts;
  final int totalCaloriesBurned;
  final int currentStreak;
  final int longestStreak;
  final Map<String, int> exerciseFrequency;
  final Map<String, int> categoryBreakdown;
  final double averageWorkoutDuration;
  final List<String> favoriteExercises;

  WorkoutAnalytics({
    required this.totalWorkouts,
    required this.totalCaloriesBurned,
    required this.currentStreak,
    required this.longestStreak,
    required this.exerciseFrequency,
    required this.categoryBreakdown,
    required this.averageWorkoutDuration,
    required this.favoriteExercises,
  });
}

class ThirtyDayWorkoutService {
  static const String _currentDayKey = 'thirty_day_current_day';
  static const String _startDateKey = 'thirty_day_start_date';
  static const String _selectedLevelKey = 'thirty_day_selected_level';
  static const String _selectedDurationKey = 'thirty_day_selected_duration';
  static const String _completedDaysKey = 'thirty_day_completed_days';
  static const String _totalCaloriesBurnedKey = 'thirty_day_total_calories';
  static const String _currentStreakKey = 'thirty_day_current_streak';
  static const String _longestStreakKey = 'thirty_day_longest_streak';
  static const String _achievementsKey = 'thirty_day_achievements';
  static const String _lastWorkoutDateKey = 'thirty_day_last_workout_date';

  // Achievement definitions
  final Map<AchievementType, Achievement> _achievements = {
    AchievementType.firstWorkout: Achievement(
      type: AchievementType.firstWorkout,
      title: 'First Steps',
      description: 'Complete your first workout!',
      icon: '🎯',
    ),
    AchievementType.weekCompleted: Achievement(
      type: AchievementType.weekCompleted,
      title: 'Week Warrior',
      description: 'Complete your first week!',
      icon: '💪',
    ),
    AchievementType.twoWeeksCompleted: Achievement(
      type: AchievementType.twoWeeksCompleted,
      title: 'Two Week Champion',
      description: 'Complete 14 days of workouts!',
      icon: '🏆',
    ),
    AchievementType.halfwayPoint: Achievement(
      type: AchievementType.halfwayPoint,
      title: 'Halfway Hero',
      description: 'Reach the halfway point!',
      icon: '🔥',
    ),
    AchievementType.threeWeeksCompleted: Achievement(
      type: AchievementType.threeWeeksCompleted,
      title: 'Three Week Legend',
      description: 'Complete 21 days of workouts!',
      icon: '⭐',
    ),
    AchievementType.programCompleted: Achievement(
      type: AchievementType.programCompleted,
      title: '30-Day Champion',
      description: 'Complete the entire 30-day program!',
      icon: '👑',
    ),
    AchievementType.perfectWeek: Achievement(
      type: AchievementType.perfectWeek,
      title: 'Perfect Week',
      description: 'Complete 7 consecutive workouts!',
      icon: '✨',
    ),
    AchievementType.streakFive: Achievement(
      type: AchievementType.streakFive,
      title: 'Streak Master',
      description: '5-day workout streak!',
      icon: '🔥',
    ),
    AchievementType.streakTen: Achievement(
      type: AchievementType.streakTen,
      title: 'Unstoppable',
      description: '10-day workout streak!',
      icon: '⚡',
    ),
    AchievementType.caloriesBurned500: Achievement(
      type: AchievementType.caloriesBurned500,
      title: 'Calorie Crusher',
      description: 'Burn 500 total calories!',
      icon: '🔥',
    ),
    AchievementType.caloriesBurned1000: Achievement(
      type: AchievementType.caloriesBurned1000,
      title: 'Calorie Incinerator',
      description: 'Burn 1000 total calories!',
      icon: '💥',
    ),
    AchievementType.caloriesBurned2000: Achievement(
      type: AchievementType.caloriesBurned2000,
      title: 'Calorie Destroyer',
      description: 'Burn 2000 total calories!',
      icon: '🌟',
    ),
  };

  // Comprehensive exercise database with different difficulty levels
  // Comprehensive exercise database with enhanced instructions
  final Map<WorkoutLevel, List<Map<String, dynamic>>> _exerciseDatabase = {
    WorkoutLevel.beginner: [
      {
        'name': 'Jumping Jacks',
        'duration': 30,
        'reps': 20,
        'sets': 3,
        'description': 'Classic cardio exercise to warm up and burn calories',
        'instructions':
            'Stand with feet together, jump while spreading legs and raising arms overhead',
        'detailedInstructions': [
          '1. Start with feet together, arms at your sides',
          '2. Jump up while spreading legs shoulder-width apart',
          '3. Simultaneously raise arms overhead in a smooth arc',
          '4. Jump again to return to starting position',
          '5. Land softly on the balls of your feet',
        ],
        'breathingTips':
            'Breathe steadily throughout - inhale as you jump out, exhale as you jump in',
        'commonMistakes': [
          'Landing too hard on heels',
          'Moving arms too stiffly',
          'Not engaging core muscles',
          'Going too fast and losing form',
        ],
        'modifications': {
          'easier':
              'Step side to side instead of jumping, raise one arm at a time',
          'harder': 'Add a squat when feet are apart, increase tempo',
        },
        'restTime': 30,
        'focusArea': 'Full body cardio, legs, coordination',
        'imageUrl': 'assets/images/jumping_jacks.png',
        'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        'category': 'cardio',
        'difficulty': 'beginner',
        'caloriesPerMinute': 8,
      },
      {
        'name': 'Wall Push-ups',
        'duration': 0,
        'reps': 15,
        'sets': 3,
        'description': 'Modified push-ups against a wall for beginners',
        'instructions':
            'Stand arm\'s length from wall, place hands on wall, push in and out',
        'detailedInstructions': [
          '1. Stand arm\'s length from a wall',
          '2. Place palms flat against wall at shoulder height',
          '3. Keep body straight, lean toward wall',
          '4. Push back to starting position',
          '5. Keep core engaged throughout movement',
        ],
        'breathingTips':
            'Inhale as you lean toward wall, exhale as you push away',
        'commonMistakes': [
          'Placing hands too high or too low',
          'Not keeping body straight',
          'Pushing off with fingertips instead of full palms',
          'Moving too quickly',
        ],
        'modifications': {
          'easier':
              'Stand closer to wall, use fingertips for lighter resistance',
          'harder': 'Step further from wall, add one-arm variations',
        },
        'restTime': 45,
        'focusArea': 'Chest, shoulders, triceps',
        'imageUrl': 'assets/images/wall_pushups.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'beginner',
        'caloriesPerMinute': 5,
      },
      {
        'name': 'Chair Squats',
        'duration': 0,
        'reps': 15,
        'sets': 3,
        'description': 'Squats using a chair for support and proper form',
        'instructions':
            'Stand in front of chair, squat down until you touch the seat, stand back up',
        'detailedInstructions': [
          '1. Stand with feet hip-width apart in front of chair',
          '2. Extend arms forward for balance',
          '3. Lower down as if sitting, barely touching chair seat',
          '4. Drive through heels to stand back up',
          '5. Keep chest up and knees behind toes',
        ],
        'breathingTips': 'Inhale as you lower down, exhale as you stand up',
        'commonMistakes': [
          'Fully sitting down on chair',
          'Knees caving inward',
          'Leaning too far forward',
          'Not going low enough',
        ],
        'modifications': {
          'easier': 'Use chair arms for support, sit fully if needed',
          'harder': 'Hover above chair without touching, add pulse at bottom',
        },
        'restTime': 45,
        'focusArea': 'Quadriceps, glutes, hamstrings',
        'imageUrl': 'assets/images/chair_squats.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'beginner',
        'caloriesPerMinute': 6,
      },
      {
        'name': 'Modified Plank',
        'duration': 20,
        'reps': 0,
        'sets': 3,
        'description': 'Plank on knees to build core strength',
        'instructions':
            'Start on knees and forearms, keep body straight, hold position',
        'detailedInstructions': [
          '1. Start on knees and forearms on the floor',
          '2. Keep forearms parallel, elbows under shoulders',
          '3. Maintain straight line from head to knees',
          '4. Engage core muscles and breathe normally',
          '5. Hold position for specified time',
        ],
        'breathingTips':
            'Breathe normally - don\'t hold your breath during the hold',
        'commonMistakes': [
          'Dropping hips too low',
          'Raising hips too high',
          'Holding breath',
          'Looking up instead of down',
        ],
        'modifications': {
          'easier': 'Reduce hold time, take breaks if needed',
          'harder': 'Lift one arm or leg, increase hold time',
        },
        'restTime': 60,
        'focusArea': 'Core, shoulders, back',
        'imageUrl': 'assets/images/modified_plank.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'core',
        'difficulty': 'beginner',
        'caloriesPerMinute': 4,
      },
      {
        'name': 'Marching in Place',
        'duration': 60,
        'reps': 0,
        'sets': 2,
        'description': 'Simple cardio exercise lifting knees while stationary',
        'instructions':
            'March in place, lifting knees to hip level alternately',
        'detailedInstructions': [
          '1. Stand tall with feet hip-width apart',
          '2. Lift right knee toward chest',
          '3. Lower right foot and lift left knee',
          '4. Pump arms naturally opposite to legs',
          '5. Maintain steady rhythm throughout',
        ],
        'breathingTips': 'Breathe naturally with the rhythm of your movement',
        'commonMistakes': [
          'Not lifting knees high enough',
          'Moving arms too much',
          'Leaning backward',
          'Going too fast initially',
        ],
        'modifications': {
          'easier': 'Hold onto something for balance, reduce knee height',
          'harder': 'Add arm movements, increase knee height, add speed',
        },
        'restTime': 30,
        'focusArea': 'Cardio, legs, coordination',
        'imageUrl': 'assets/images/marching.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'cardio',
        'difficulty': 'beginner',
        'caloriesPerMinute': 6,
      },
      {
        'name': 'Step-ups',
        'duration': 0,
        'reps': 12,
        'sets': 3,
        'description': 'Step up and down using a sturdy surface',
        'instructions':
            'Step up onto platform with one foot, bring other foot up, step back down',
        'detailedInstructions': [
          '1. Stand facing a sturdy platform or step',
          '2. Step up with your right foot completely on the platform',
          '3. Bring your left foot up to meet your right foot',
          '4. Step down with your right foot first',
          '5. Bring your left foot down to complete one rep',
        ],
        'breathingTips':
            'Breathe naturally, exhale on the effort (stepping up)',
        'commonMistakes': [
          'Using momentum instead of muscle control',
          'Not placing entire foot on platform',
          'Leaning too far forward',
          'Going too fast',
        ],
        'modifications': {
          'easier': 'Use a lower step, hold onto railing for balance',
          'harder': 'Use higher step, add knee drive at top, hold weights',
        },
        'restTime': 45,
        'focusArea': 'Legs, glutes, cardio',
        'imageUrl': 'assets/images/step_ups.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'cardio',
        'difficulty': 'beginner',
        'caloriesPerMinute': 7,
      },
    ],

    WorkoutLevel.intermediate: [
      {
        'name': 'Standard Push-ups',
        'duration': 0,
        'reps': 15,
        'sets': 3,
        'description': 'Full push-ups for upper body strength',
        'instructions':
            'Start in plank position, lower chest to ground, push back up',
        'detailedInstructions': [
          '1. Start in a plank position with hands slightly wider than shoulders',
          '2. Keep your body in a straight line from head to heels',
          '3. Lower your chest to the ground by bending your elbows',
          '4. Push back up to the starting position with control',
          '5. Keep core engaged throughout the movement',
        ],
        'modifications': {
          'easier': 'Do push-ups on your knees or against a wall',
          'harder': 'Elevate your feet on a step or add a clap at the top',
        },
        'breathingTips': 'Inhale as you lower down, exhale as you push up',
        'commonMistakes': [
          'Sagging hips',
          'Flaring elbows too wide',
          'Not going full range of motion',
          'Rushing the movement',
        ],
        'restTime': 60,
        'focusArea': 'Chest, shoulders, triceps, core',
        'imageUrl': 'assets/images/pushups.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 7,
      },
      {
        'name': 'Squats',
        'duration': 0,
        'reps': 20,
        'sets': 3,
        'description': 'Bodyweight squats for lower body strength',
        'instructions':
            'Stand with feet shoulder-width apart, lower into squat, return to standing',
        'detailedInstructions': [
          '1. Stand with feet shoulder-width apart, toes slightly turned out',
          '2. Lower your body by bending at the hips and knees',
          '3. Keep your chest up and weight on your heels',
          '4. Go down until your thighs are parallel to the floor',
          '5. Push through your heels to return to standing',
        ],
        'modifications': {
          'easier': 'Use a chair for support or do partial squats',
          'harder': 'Add jump squats or hold weights',
        },
        'breathingTips': 'Inhale as you lower down, exhale as you stand up',
        'commonMistakes': [
          'Knees caving inward',
          'Rounding the back',
          'Not going deep enough',
          'Weight on toes instead of heels',
        ],
        'restTime': 45,
        'focusArea': 'Quadriceps, glutes, hamstrings, core',
        'imageUrl': 'assets/images/squats.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 8,
      },
      {
        'name': 'Plank',
        'duration': 45,
        'reps': 0,
        'sets': 3,
        'description': 'Core strengthening isometric exercise',
        'instructions': 'Hold body straight from head to heels, engage core',
        'detailedInstructions': [
          '1. Start in a push-up position, then lower onto your forearms',
          '2. Keep your body in a straight line from head to heels',
          '3. Engage your core and squeeze your glutes',
          '4. Keep elbows directly under shoulders',
          '5. Breathe steadily throughout the hold',
        ],
        'modifications': {
          'easier': 'Do plank on knees or against a wall',
          'harder': 'Lift one leg or add arm movements',
        },
        'breathingTips': 'Breathe steadily and deeply, don\'t hold your breath',
        'commonMistakes': [
          'Sagging hips',
          'Lifting hips too high',
          'Holding breath',
          'Looking up instead of maintaining neutral neck',
        ],
        'restTime': 60,
        'focusArea': 'Core, shoulders, back, glutes',
        'imageUrl': 'assets/images/plank.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'core',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 5,
      },
      {
        'name': 'Mountain Climbers',
        'duration': 45,
        'reps': 0,
        'sets': 3,
        'description': 'High-intensity cardio and core exercise',
        'instructions':
            'Start in plank, alternately bring knees toward chest rapidly',
        'detailedInstructions': [
          '1. Start in a plank position with hands directly under shoulders',
          '2. Rapidly alternate bringing each knee toward your chest',
          '3. Keep your core engaged and hips level',
          '4. Maintain plank position throughout',
          '5. Drive knees up with control, don\'t just bounce',
        ],
        'modifications': {
          'easier': 'Slow down the pace or step instead of running',
          'harder': 'Add cross-body mountain climbers or increase speed',
        },
        'breathingTips':
            'Breathe rapidly but controlled, matching your movement rhythm',
        'commonMistakes': [
          'Lifting hips too high',
          'Not engaging core',
          'Going too fast and losing form',
          'Hands too far forward',
        ],
        'restTime': 60,
        'focusArea': 'Core, cardio, shoulders, legs',
        'imageUrl': 'assets/images/mountain_climbers.png',
        'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        'category': 'cardio',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 10,
      },
      {
        'name': 'Lunges',
        'duration': 0,
        'reps': 12,
        'sets': 3,
        'description': 'Alternating forward lunges for leg strength',
        'instructions':
            'Step forward into lunge position, return to standing, alternate legs',
        'detailedInstructions': [
          '1. Stand tall with feet hip-width apart',
          '2. Step forward with one leg, lowering your hips',
          '3. Lower until both knees are bent at 90 degrees',
          '4. Push off the front foot to return to starting position',
          '5. Alternate legs with each rep',
        ],
        'modifications': {
          'easier': 'Hold onto a wall for balance or do stationary lunges',
          'harder': 'Add weights or do jumping lunges',
        },
        'breathingTips':
            'Inhale as you step forward, exhale as you return to standing',
        'commonMistakes': [
          'Knee extending past toes',
          'Leaning forward too much',
          'Not stepping far enough',
          'Landing hard on front foot',
        ],
        'restTime': 45,
        'focusArea': 'Quadriceps, glutes, hamstrings, calves',
        'imageUrl': 'assets/images/lunges.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 7,
      },
      {
        'name': 'High Knees',
        'duration': 30,
        'reps': 0,
        'sets': 3,
        'description': 'Cardio exercise running in place with high knees',
        'instructions': 'Run in place bringing knees up towards chest',
        'detailedInstructions': [
          '1. Stand with feet hip-width apart',
          '2. Run in place while lifting your knees as high as possible',
          '3. Aim to bring knees to hip level',
          '4. Pump your arms naturally as you would when running',
          '5. Land on the balls of your feet',
        ],
        'modifications': {
          'easier': 'March in place with high knees at slower pace',
          'harder': 'Increase speed or add arm movements',
        },
        'breathingTips':
            'Breathe rhythmically, matching your breathing to your movement pattern',
        'commonMistakes': [
          'Not lifting knees high enough',
          'Landing too hard',
          'Poor posture',
          'Moving arms too wildly',
        ],
        'restTime': 45,
        'focusArea': 'Cardio, legs, core',
        'imageUrl': 'assets/images/high_knees.png',
        'videoUrl': 'https://youtu.be/OAJ_J3EZkdY',
        'category': 'cardio',
        'difficulty': 'intermediate',
        'caloriesPerMinute': 9,
      },
      {
        'name': 'Side Plank',
        'duration': 30,
        'reps': 0,
        'sets': 2,
        'description': 'Lateral core strengthening exercise',
        'instructions': 'Hold body sideways supported by one forearm',
        'detailedInstructions':
            'Lie on your side with legs extended. Support your upper body on your forearm, keeping your elbow directly under your shoulder. Lift your hips to create a straight line from head to feet.',
        'modifications':
            'Easier: Do on knees or against a wall. Harder: Lift top leg or add hip dips.',
        'breathingTips': 'Breathe steadily throughout the hold.',
        'commonMistakes':
            'Sagging hips, rolling forward or backward, not stacking shoulders.',
        'focusArea': 'Obliques, core, shoulders',
        'imageUrl': 'assets/images/side_plank.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'core',
        'difficulty': 'intermediate',
      },
      {
        'name': 'Tricep Dips',
        'duration': 0,
        'reps': 12,
        'sets': 3,
        'description': 'Tricep strengthening using chair or bench',
        'instructions': 'Use chair or bench to perform dipping motion',
        'detailedInstructions':
            'Sit on the edge of a chair or bench with hands beside your hips. Slide off the edge, supporting your weight with your arms. Lower your body by bending your elbows, then push back up.',
        'modifications':
            'Easier: Keep feet closer to body or use higher surface. Harder: Extend legs farther or add weight.',
        'breathingTips': 'Inhale as you lower, exhale as you push up.',
        'commonMistakes':
            'Going too low, flaring elbows out, using legs too much.',
        'focusArea': 'Triceps, shoulders, chest',
        'imageUrl': 'assets/images/tricep_dips.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'intermediate',
      },
    ],

    WorkoutLevel.advanced: [
      {
        'name': 'Burpees',
        'duration': 0,
        'reps': 10,
        'sets': 4,
        'description': 'Full-body high-intensity exercise',
        'instructions':
            'Squat down, jump back to plank, do push-up, jump feet forward, jump up',
        'detailedInstructions': [
          '1. Start standing with feet shoulder-width apart',
          '2. Squat down and place hands on the ground',
          '3. Jump feet back into plank position',
          '4. Perform one push-up',
          '5. Jump feet forward to squat position',
          '6. Jump up with arms overhead',
        ],
        'breathingTips':
            'Breathe out during the explosive movements, maintain rhythm',
        'commonMistakes': [
          'Skipping the push-up',
          'Not jumping feet all the way forward',
          'Landing too hard',
          'Rushing through movements',
        ],
        'modifications': {
          'easier': 'Step back instead of jumping, skip the push-up',
          'harder': 'Add a tuck jump at the top, increase speed',
        },
        'restTime': 90,
        'focusArea': 'Full body, cardio, strength',
        'imageUrl': 'assets/images/burpees.png',
        'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        'category': 'hiit',
        'difficulty': 'advanced',
        'caloriesPerMinute': 15,
      },
      {
        'name': 'Jump Squats',
        'duration': 0,
        'reps': 15,
        'sets': 4,
        'description': 'Explosive squat jumps for power',
        'instructions': 'Perform squat, then jump explosively up, land softly',
        'detailedInstructions': [
          '1. Start in a squat position with feet shoulder-width apart',
          '2. Lower into a deep squat position',
          '3. Drive through heels and jump explosively upward',
          '4. Land softly on balls of feet',
          '5. Immediately lower into next squat',
        ],
        'breathingTips':
            'Exhale explosively on the jump, inhale on the landing',
        'commonMistakes': [
          'Not squatting deep enough',
          'Landing too hard',
          'Not using arms for momentum',
          'Pausing between reps',
        ],
        'modifications': {
          'easier': 'Do regular squats or reduce jump height',
          'harder': 'Add 180-degree turns or weighted vest',
        },
        'restTime': 60,
        'focusArea': 'Legs, glutes, power, cardio',
        'imageUrl': 'assets/images/jump_squats.png',
        'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        'category': 'hiit',
        'difficulty': 'advanced',
        'caloriesPerMinute': 12,
      },
      {
        'name': 'Diamond Push-ups',
        'duration': 0,
        'reps': 12,
        'sets': 3,
        'description': 'Advanced push-ups targeting triceps',
        'instructions': 'Form diamond shape with hands, perform push-ups',
        'focusArea': 'Triceps, chest, shoulders, core',
        'imageUrl': 'assets/images/diamond_pushups.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'advanced',
      },
      {
        'name': 'Pike Push-ups',
        'duration': 0,
        'reps': 10,
        'sets': 3,
        'description': 'Shoulder-focused push-up variation',
        'instructions':
            'Start in downward dog position, lower head toward ground, push back up',
        'focusArea': 'Shoulders, triceps, core',
        'imageUrl': 'assets/images/pike_pushups.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'advanced',
      },
      {
        'name': 'Single-leg Squats',
        'duration': 0,
        'reps': 8,
        'sets': 3,
        'description': 'Pistol squats for advanced leg strength',
        'instructions':
            'Balance on one leg, squat down as far as possible, return to standing',
        'focusArea': 'Legs, balance, core, glutes',
        'imageUrl': 'assets/images/single_leg_squats.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'strength',
        'difficulty': 'advanced',
      },
      {
        'name': 'Plank to Push-up',
        'duration': 0,
        'reps': 10,
        'sets': 3,
        'description': 'Dynamic plank variation',
        'instructions':
            'Start in forearm plank, push up to full plank, return to forearms',
        'focusArea': 'Core, shoulders, triceps',
        'imageUrl': 'assets/images/plank_pushup.png',
        'videoUrl': 'https://youtu.be/example',
        'category': 'core',
        'difficulty': 'advanced',
      },
    ],
  };

  // Get current day of the program
  Future<int> getCurrentDay() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_currentDayKey) ?? 1;
  }

  // Start or restart the 30-day program
  Future<void> startProgram({
    required WorkoutLevel level,
    required WorkoutDuration duration,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_currentDayKey, 1);
    await prefs.setString(_startDateKey, DateTime.now().toIso8601String());
    await prefs.setString(_selectedLevelKey, level.name);
    await prefs.setString(_selectedDurationKey, duration.name);
    await prefs.setStringList(_completedDaysKey, []);
  }

  // Get program settings
  Future<Map<String, dynamic>> getProgramSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final levelName =
        prefs.getString(_selectedLevelKey) ?? WorkoutLevel.beginner.name;
    final durationName =
        prefs.getString(_selectedDurationKey) ?? WorkoutDuration.medium.name;
    final startDateStr = prefs.getString(_startDateKey);

    return {
      'level': WorkoutLevel.values.firstWhere((e) => e.name == levelName),
      'duration': WorkoutDuration.values.firstWhere(
        (e) => e.name == durationName,
      ),
      'startDate': startDateStr != null ? DateTime.parse(startDateStr) : null,
      'currentDay': await getCurrentDay(),
    };
  }

  // Generate today's workout based on level, duration, and day
  Future<List<Map<String, dynamic>>> generateTodaysWorkout() async {
    final settings = await getProgramSettings();
    final level = settings['level'] as WorkoutLevel;
    final duration = settings['duration'] as WorkoutDuration;
    final currentDay = settings['currentDay'] as int;

    return _generateWorkoutForDay(level, duration, currentDay);
  }

  // Generate workout for specific day
  List<Map<String, dynamic>> _generateWorkoutForDay(
    WorkoutLevel level,
    WorkoutDuration duration,
    int day,
  ) {
    final availableExercises = _exerciseDatabase[level] ?? [];
    if (availableExercises.isEmpty) return [];

    // Seed random with day to ensure same workout for same day
    final dayRandom = Random(day);

    // Calculate number of exercises based on duration
    int exerciseCount = 6; // Default value
    switch (duration) {
      case WorkoutDuration.quick:
        exerciseCount = 4;
        break;
      case WorkoutDuration.medium:
        exerciseCount = 6;
        break;
      case WorkoutDuration.standard:
        exerciseCount = 8;
        break;
      case WorkoutDuration.extended:
        exerciseCount = 10;
        break;
    }

    // Shuffle and select exercises
    final shuffledExercises = List<Map<String, dynamic>>.from(
      availableExercises,
    );
    shuffledExercises.shuffle(dayRandom);

    final selectedExercises = shuffledExercises.take(exerciseCount).toList();

    // Adjust exercise intensity based on day and duration
    return selectedExercises.map((exercise) {
      final adjustedExercise = Map<String, dynamic>.from(exercise);

      // Progressive difficulty: increase reps/duration as days progress
      final progressMultiplier = 1.0 + (day / 30.0) * 0.3; // Up to 30% increase

      if (adjustedExercise['reps'] > 0) {
        adjustedExercise['reps'] =
            (adjustedExercise['reps'] * progressMultiplier).round();
      }

      if (adjustedExercise['duration'] > 0) {
        adjustedExercise['duration'] =
            (adjustedExercise['duration'] * progressMultiplier).round();
      }

      // Add unique ID for tracking
      adjustedExercise['id'] =
          '${day}_${exercise['name'].replaceAll(' ', '_').toLowerCase()}';

      return adjustedExercise;
    }).toList();
  }

  // Mark day as completed
  Future<void> completeDayWorkout(int day) async {
    final prefs = await SharedPreferences.getInstance();
    final completedDays = prefs.getStringList(_completedDaysKey) ?? [];

    if (!completedDays.contains(day.toString())) {
      completedDays.add(day.toString());
      await prefs.setStringList(_completedDaysKey, completedDays);
    }

    // Move to next day if current day is completed
    final currentDay = await getCurrentDay();
    if (day == currentDay && currentDay < 30) {
      await prefs.setInt(_currentDayKey, currentDay + 1);
    }
  }

  // Get completed days
  Future<List<int>> getCompletedDays() async {
    final prefs = await SharedPreferences.getInstance();
    final completedDays = prefs.getStringList(_completedDaysKey) ?? [];
    return completedDays.map((day) => int.parse(day)).toList();
  }

  // Get progress percentage
  Future<double> getProgressPercentage() async {
    final completedDays = await getCompletedDays();
    return completedDays.length / 30.0;
  }

  // Check if program is active
  Future<bool> isProgramActive() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_startDateKey) != null;
  }

  // Get workout for any specific day (for preview)
  List<Map<String, dynamic>> getWorkoutForDay(
    WorkoutLevel level,
    WorkoutDuration duration,
    int day,
  ) {
    return _generateWorkoutForDay(level, duration, day);
  }

  // Get total estimated calories for workout
  int getEstimatedCalories(List<Map<String, dynamic>> exercises) {
    int totalCalories = 0;

    for (final exercise in exercises) {
      final category = exercise['category'] as String;
      final duration = exercise['duration'] as int;
      final reps = exercise['reps'] as int;
      final sets = exercise['sets'] as int;

      // Rough calorie estimation based on exercise type
      int caloriesPerMinute;
      switch (category) {
        case 'hiit':
          caloriesPerMinute = 12;
          break;
        case 'cardio':
          caloriesPerMinute = 8;
          break;
        case 'strength':
          caloriesPerMinute = 6;
          break;
        case 'core':
          caloriesPerMinute = 5;
          break;
        default:
          caloriesPerMinute = 7;
      }

      if (duration > 0) {
        totalCalories += (duration / 60.0 * caloriesPerMinute * sets).round();
      } else {
        // Estimate time for rep-based exercises (assuming 2 seconds per rep)
        final estimatedMinutes = (reps * sets * 2) / 60.0;
        totalCalories += (estimatedMinutes * caloriesPerMinute).round();
      }
    }

    return totalCalories;
  }

  // Reset program
  Future<void> resetProgram() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentDayKey);
    await prefs.remove(_startDateKey);
    await prefs.remove(_selectedLevelKey);
    await prefs.remove(_selectedDurationKey);
    await prefs.remove(_completedDaysKey);
  }

  // Check if a specific day is a rest day
  bool isRestDay(int day) {
    // Every 7th day is a rest day (days 7, 14, 21, 28)
    return day % 7 == 0 && day <= 30;
  }

  // Get rest days for the entire program
  List<int> getRestDays() {
    return [7, 14, 21, 28];
  }

  // Check and unlock achievements
  Future<List<Achievement>> checkAndUnlockAchievements() async {
    final completedDays = await getCompletedDays();
    final totalCalories = await getTotalCaloriesBurned();
    final currentStreak = await getCurrentStreak();
    final unlockedAchievements = <Achievement>[];

    // Check each achievement
    for (final achievement in _achievements.values) {
      if (achievement.isUnlocked) continue;

      bool shouldUnlock = false;
      switch (achievement.type) {
        case AchievementType.firstWorkout:
          shouldUnlock = completedDays.isNotEmpty;
          break;
        case AchievementType.weekCompleted:
          shouldUnlock = completedDays.length >= 7;
          break;
        case AchievementType.twoWeeksCompleted:
          shouldUnlock = completedDays.length >= 14;
          break;
        case AchievementType.halfwayPoint:
          shouldUnlock = completedDays.length >= 15;
          break;
        case AchievementType.threeWeeksCompleted:
          shouldUnlock = completedDays.length >= 21;
          break;
        case AchievementType.programCompleted:
          shouldUnlock = completedDays.length >= 30;
          break;
        case AchievementType.perfectWeek:
          shouldUnlock = _hasConsecutiveDays(completedDays, 7);
          break;
        case AchievementType.streakFive:
          shouldUnlock = currentStreak >= 5;
          break;
        case AchievementType.streakTen:
          shouldUnlock = currentStreak >= 10;
          break;
        case AchievementType.caloriesBurned500:
          shouldUnlock = totalCalories >= 500;
          break;
        case AchievementType.caloriesBurned1000:
          shouldUnlock = totalCalories >= 1000;
          break;
        case AchievementType.caloriesBurned2000:
          shouldUnlock = totalCalories >= 2000;
          break;
      }

      if (shouldUnlock) {
        final unlockedAchievement = achievement.copyWith(
          isUnlocked: true,
          unlockedAt: DateTime.now(),
        );
        unlockedAchievements.add(unlockedAchievement);
        await _saveAchievement(unlockedAchievement);
      }
    }

    return unlockedAchievements;
  }

  // Helper method to check for consecutive days
  bool _hasConsecutiveDays(List<int> completedDays, int consecutive) {
    if (completedDays.length < consecutive) return false;

    completedDays.sort();
    int currentConsecutive = 1;

    for (int i = 1; i < completedDays.length; i++) {
      if (completedDays[i] == completedDays[i - 1] + 1) {
        currentConsecutive++;
        if (currentConsecutive >= consecutive) return true;
      } else {
        currentConsecutive = 1;
      }
    }

    return false;
  }

  // Get total calories burned
  Future<int> getTotalCaloriesBurned() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_totalCaloriesBurnedKey) ?? 0;
  }

  // Add calories to total
  Future<void> addCaloriesBurned(int calories) async {
    final prefs = await SharedPreferences.getInstance();
    final currentTotal = await getTotalCaloriesBurned();
    await prefs.setInt(_totalCaloriesBurnedKey, currentTotal + calories);
  }

  // Get current workout streak
  Future<int> getCurrentStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_currentStreakKey) ?? 0;
  }

  // Update workout streak
  Future<void> updateWorkoutStreak() async {
    final prefs = await SharedPreferences.getInstance();
    final lastWorkoutDate = prefs.getString(_lastWorkoutDateKey);
    final today = DateTime.now();
    final todayStr = '${today.year}-${today.month}-${today.day}';

    if (lastWorkoutDate == null) {
      // First workout
      await prefs.setInt(_currentStreakKey, 1);
      await prefs.setString(_lastWorkoutDateKey, todayStr);
    } else {
      final lastDate = DateTime.parse(lastWorkoutDate);
      final daysDifference = today.difference(lastDate).inDays;

      if (daysDifference == 1) {
        // Consecutive day
        final currentStreak = await getCurrentStreak();
        await prefs.setInt(_currentStreakKey, currentStreak + 1);

        // Update longest streak if needed
        final longestStreak = prefs.getInt(_longestStreakKey) ?? 0;
        if (currentStreak + 1 > longestStreak) {
          await prefs.setInt(_longestStreakKey, currentStreak + 1);
        }
      } else if (daysDifference > 1) {
        // Streak broken
        await prefs.setInt(_currentStreakKey, 1);
      }
      // If daysDifference == 0, it's the same day, don't update streak

      await prefs.setString(_lastWorkoutDateKey, todayStr);
    }
  }

  // Save achievement
  Future<void> _saveAchievement(Achievement achievement) async {
    final prefs = await SharedPreferences.getInstance();
    final achievementsJson = prefs.getStringList(_achievementsKey) ?? [];
    // Simple JSON-like storage for achievements
    achievementsJson.add(
      '${achievement.type.name}|${achievement.unlockedAt?.toIso8601String()}',
    );
    await prefs.setStringList(_achievementsKey, achievementsJson);
  }

  // Get unlocked achievements
  Future<List<Achievement>> getUnlockedAchievements() async {
    final prefs = await SharedPreferences.getInstance();
    final achievementsJson = prefs.getStringList(_achievementsKey) ?? [];
    final unlockedAchievements = <Achievement>[];

    for (final achievementStr in achievementsJson) {
      final parts = achievementStr.split('|');
      if (parts.length == 2) {
        final type = AchievementType.values.firstWhere(
          (e) => e.name == parts[0],
          orElse: () => AchievementType.firstWorkout,
        );
        final unlockedAt = DateTime.parse(parts[1]);
        final achievement = _achievements[type];
        if (achievement != null) {
          unlockedAchievements.add(
            achievement.copyWith(isUnlocked: true, unlockedAt: unlockedAt),
          );
        }
      }
    }

    return unlockedAchievements;
  }

  // Get workout analytics
  Future<WorkoutAnalytics> getWorkoutAnalytics() async {
    final completedDays = await getCompletedDays();
    final totalCalories = await getTotalCaloriesBurned();
    final currentStreak = await getCurrentStreak();
    final longestStreak = await _getLongestStreak();

    return WorkoutAnalytics(
      totalWorkouts: completedDays.length,
      totalCaloriesBurned: totalCalories,
      currentStreak: currentStreak,
      longestStreak: longestStreak,
      exerciseFrequency: {}, // TODO: Implement exercise frequency tracking
      categoryBreakdown: {}, // TODO: Implement category breakdown
      averageWorkoutDuration: 30.0, // TODO: Calculate from actual data
      favoriteExercises: [], // TODO: Implement favorite exercises tracking
    );
  }

  // Get longest streak
  Future<int> _getLongestStreak() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_longestStreakKey) ?? 0;
  }

  // Complete workout with analytics and achievements
  Future<List<Achievement>> completeWorkout(int day, int caloriesBurned) async {
    // Mark day as completed
    await completeDayWorkout(day);

    // Add calories burned
    await addCaloriesBurned(caloriesBurned);

    // Update streak
    await updateWorkoutStreak();

    // Check for new achievements
    return await checkAndUnlockAchievements();
  }
}
