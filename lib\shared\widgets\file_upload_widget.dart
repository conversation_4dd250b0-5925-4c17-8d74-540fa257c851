import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:fit_4_force/core/services/storage_service.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

enum FileUploadType {
  profileImage,
  workoutImage,
  postImage,
  exerciseVideo,
  studyMaterial,
}

class FileUploadWidget extends StatefulWidget {
  final FileUploadType uploadType;
  final String? entityId; // userId, workoutId, postId, etc.
  final Function(String? url)? onUploadComplete;
  final Function(String error)? onUploadError;
  final Widget? child;
  final bool showProgress;
  final String? buttonText;

  const FileUploadWidget({
    super.key,
    required this.uploadType,
    this.entityId,
    this.onUploadComplete,
    this.onUploadError,
    this.child,
    this.showProgress = true,
    this.buttonText,
  });

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  final StorageService _storageService = StorageService();
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String? _uploadedUrl;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Upload button or custom child
        widget.child ?? _buildDefaultUploadButton(),
        
        // Progress indicator
        if (_isUploading && widget.showProgress) ...[
          const SizedBox(height: 16),
          _buildProgressIndicator(),
        ],
        
        // Success indicator
        if (_uploadedUrl != null) ...[
          const SizedBox(height: 16),
          _buildSuccessIndicator(),
        ],
      ],
    );
  }

  Widget _buildDefaultUploadButton() {
    return ElevatedButton.icon(
      onPressed: _isUploading ? null : _pickAndUploadFile,
      icon: _isUploading 
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(_getUploadIcon()),
      label: Text(widget.buttonText ?? _getDefaultButtonText()),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.cloud_upload, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Uploading ${_getFileTypeName()}...',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.blue,
                  ),
                ),
              ),
              Text(
                '${(_uploadProgress * 100).toInt()}%',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _uploadProgress,
            backgroundColor: Colors.blue.shade100,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${_getFileTypeName()} uploaded successfully!',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.green,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.copy, size: 20),
            onPressed: () => _copyUrlToClipboard(),
            tooltip: 'Copy URL',
          ),
        ],
      ),
    );
  }

  Future<void> _pickAndUploadFile() async {
    try {
      setState(() {
        _isUploading = true;
        _uploadProgress = 0.0;
        _uploadedUrl = null;
      });

      // Pick file based on type
      FilePickerResult? result;
      
      switch (widget.uploadType) {
        case FileUploadType.profileImage:
        case FileUploadType.workoutImage:
        case FileUploadType.postImage:
          result = await FilePicker.platform.pickFiles(
            type: FileType.image,
            allowMultiple: false,
          );
          break;
        case FileUploadType.exerciseVideo:
          result = await FilePicker.platform.pickFiles(
            type: FileType.video,
            allowMultiple: false,
          );
          break;
        case FileUploadType.studyMaterial:
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: ['pdf', 'doc', 'docx', 'txt'],
            allowMultiple: false,
          );
          break;
      }

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        final fileData = file.bytes;
        final fileName = file.name;

        if (fileData != null) {
          // Simulate progress updates
          _simulateProgress();
          
          // Upload file
          final uploadedUrl = await _uploadFile(fileData, fileName);
          
          setState(() {
            _isUploading = false;
            _uploadProgress = 1.0;
            _uploadedUrl = uploadedUrl;
          });

          if (uploadedUrl != null) {
            widget.onUploadComplete?.call(uploadedUrl);
          } else {
            widget.onUploadError?.call('Upload failed');
          }
        }
      } else {
        setState(() {
          _isUploading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
      });
      widget.onUploadError?.call(e.toString());
    }
  }

  Future<String?> _uploadFile(Uint8List fileData, String fileName) async {
    final entityId = widget.entityId ?? 'default';
    
    switch (widget.uploadType) {
      case FileUploadType.profileImage:
        return await _storageService.uploadProfileImage(entityId, fileData, fileName);
      case FileUploadType.workoutImage:
        return await _storageService.uploadWorkoutImage(entityId, fileData, fileName);
      case FileUploadType.postImage:
        return await _storageService.uploadPostImage(entityId, fileData, fileName);
      case FileUploadType.exerciseVideo:
        return await _storageService.uploadExerciseVideo(entityId, fileData, fileName);
      case FileUploadType.studyMaterial:
        return await _storageService.uploadStudyMaterial(entityId, fileData, fileName);
    }
    return null;
  }

  void _simulateProgress() {
    // Simulate upload progress for better UX
    const duration = Duration(milliseconds: 100);
    const steps = 20;
    
    for (int i = 1; i <= steps; i++) {
      Future.delayed(duration * i, () {
        if (mounted && _isUploading) {
          setState(() {
            _uploadProgress = (i / steps) * 0.9; // Go up to 90%, final 10% for actual upload
          });
        }
      });
    }
  }

  void _copyUrlToClipboard() {
    if (_uploadedUrl != null) {
      // Copy URL to clipboard
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('URL copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  IconData _getUploadIcon() {
    switch (widget.uploadType) {
      case FileUploadType.profileImage:
      case FileUploadType.workoutImage:
      case FileUploadType.postImage:
        return Icons.image;
      case FileUploadType.exerciseVideo:
        return Icons.video_library;
      case FileUploadType.studyMaterial:
        return Icons.description;
    }
  }

  String _getDefaultButtonText() {
    switch (widget.uploadType) {
      case FileUploadType.profileImage:
        return 'Upload Profile Image';
      case FileUploadType.workoutImage:
        return 'Upload Workout Image';
      case FileUploadType.postImage:
        return 'Upload Image';
      case FileUploadType.exerciseVideo:
        return 'Upload Video';
      case FileUploadType.studyMaterial:
        return 'Upload Document';
    }
  }

  String _getFileTypeName() {
    switch (widget.uploadType) {
      case FileUploadType.profileImage:
        return 'Profile image';
      case FileUploadType.workoutImage:
        return 'Workout image';
      case FileUploadType.postImage:
        return 'Image';
      case FileUploadType.exerciseVideo:
        return 'Video';
      case FileUploadType.studyMaterial:
        return 'Document';
    }
  }
}
