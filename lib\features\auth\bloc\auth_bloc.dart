import 'package:fit_4_force/shared/bloc/base_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/core/services/supabase_auth_service.dart';
import 'package:fit_4_force/core/services/persistent_auth_service.dart';
import 'package:fit_4_force/core/services/device_management_service.dart';
import 'package:fit_4_force/shared/models/user_device_model.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

// Events
abstract class AuthEvent extends BaseEvent {}

class AuthenticatedEvent extends AuthEvent {
  final UserModel user;

  AuthenticatedEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class SignUpEvent extends AuthEvent {
  final String email;
  final String password;
  final String fullName;
  final int age;
  final String gender;
  final double height;
  final double weight;
  final String targetAgency;
  final String fitnessGoal;
  final Map<String, bool> notificationPreferences;

  SignUpEvent({
    required this.email,
    required this.password,
    required this.fullName,
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.targetAgency,
    required this.fitnessGoal,
    required this.notificationPreferences,
  });

  @override
  List<Object?> get props => [
    email,
    password,
    fullName,
    age,
    gender,
    height,
    weight,
    targetAgency,
    fitnessGoal,
    notificationPreferences,
  ];
}

class SignInEvent extends AuthEvent {
  final String email;
  final String password;

  SignInEvent({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}

class CheckAuthStatusEvent extends AuthEvent {
  @override
  List<Object?> get props => [];
}

class RestoreAuthEvent extends AuthEvent {
  final UserModel user;

  RestoreAuthEvent(this.user);

  @override
  List<Object?> get props => [user];
}

class SignOutEvent extends AuthEvent {}

class ResetPasswordEvent extends AuthEvent {
  final String email;

  ResetPasswordEvent({required this.email});

  @override
  List<Object?> get props => [email];
}

class UpdateProfileImageEvent extends AuthEvent {
  final String? imageUrl;

  UpdateProfileImageEvent(this.imageUrl);

  @override
  List<Object?> get props => [imageUrl];
}

class UpdateEmailEvent extends AuthEvent {
  final String newEmail;
  final String password;

  UpdateEmailEvent({required this.newEmail, required this.password});

  @override
  List<Object?> get props => [newEmail, password];
}

class UpdateNotificationPreferencesEvent extends AuthEvent {
  final Map<String, bool> notificationPreferences;

  UpdateNotificationPreferencesEvent({required this.notificationPreferences});

  @override
  List<Object?> get props => [notificationPreferences];
}

class DeviceLimitExceededEvent extends AuthEvent {
  final List<UserDeviceModel> activeDevices;

  DeviceLimitExceededEvent({required this.activeDevices});

  @override
  List<Object?> get props => [activeDevices];
}

class UpdatePasswordEvent extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  UpdatePasswordEvent({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object?> get props => [currentPassword, newPassword];
}

class DeleteAccountEvent extends AuthEvent {
  final String password;

  DeleteAccountEvent({required this.password});

  @override
  List<Object?> get props => [password];
}

// States
abstract class AuthState extends BaseState {}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class Authenticated extends AuthState {
  final UserModel user;

  Authenticated(this.user);

  @override
  List<Object?> get props => [user];
}

class Unauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;

  AuthError(this.message);

  @override
  List<Object?> get props => [message];
}

class DeviceLimitExceeded extends AuthState {
  final List<UserDeviceModel> activeDevices;
  final String message;

  DeviceLimitExceeded({required this.activeDevices, required this.message});

  @override
  List<Object?> get props => [activeDevices, message];
}

// Bloc
class AuthBloc extends BaseBloc<AuthEvent, AuthState> {
  final PersistentAuthService _persistentAuth = PersistentAuthService();

  AuthBloc() : super(AuthInitial()) {
    on<AuthenticatedEvent>(_handleAuthenticated);
    on<SignUpEvent>(_handleSignUp);
    on<SignInEvent>(_handleSignIn);
    on<CheckAuthStatusEvent>(_handleCheckAuthStatus);
    on<RestoreAuthEvent>(_handleRestoreAuth);
    on<SignOutEvent>(_handleSignOut);
    on<ResetPasswordEvent>(_handleResetPassword);
    on<UpdateProfileImageEvent>(_handleUpdateProfileImage);
    on<UpdateEmailEvent>(_handleUpdateEmail);
    on<UpdateNotificationPreferencesEvent>(
      _handleUpdateNotificationPreferences,
    );
    on<UpdatePasswordEvent>(_handleUpdatePassword);
    on<DeleteAccountEvent>(_handleDeleteAccount);
    on<DeviceLimitExceededEvent>(_handleDeviceLimitExceeded);
  }

  @override
  Future<void> handleEvent(AuthEvent event, Emitter<AuthState> emit) async {
    if (event is AuthenticatedEvent) {
      await _handleAuthenticated(event, emit);
    } else if (event is SignUpEvent) {
      await _handleSignUp(event, emit);
    } else if (event is SignInEvent) {
      await _handleSignIn(event, emit);
    } else if (event is CheckAuthStatusEvent) {
      await _handleCheckAuthStatus(event, emit);
    } else if (event is RestoreAuthEvent) {
      await _handleRestoreAuth(event, emit);
    } else if (event is SignOutEvent) {
      await _handleSignOut(event, emit);
    } else if (event is ResetPasswordEvent) {
      await _handleResetPassword(event, emit);
    } else if (event is UpdateProfileImageEvent) {
      await _handleUpdateProfileImage(event, emit);
    } else if (event is UpdateEmailEvent) {
      await _handleUpdateEmail(event, emit);
    } else if (event is UpdateNotificationPreferencesEvent) {
      await _handleUpdateNotificationPreferences(event, emit);
    } else if (event is UpdatePasswordEvent) {
      await _handleUpdatePassword(event, emit);
    } else if (event is DeleteAccountEvent) {
      await _handleDeleteAccount(event, emit);
    }
  }

  Future<void> _handleAuthenticated(
    AuthenticatedEvent event,
    Emitter<AuthState> emit,
  ) async {
    // Save user session for persistence
    await _persistentAuth.saveUserSession(event.user);
    emit(Authenticated(event.user));
  }

  Future<void> _handleSignUp(SignUpEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());

      // Sign up with Supabase
      final authResponse = await SupabaseAuthService.signUp(
        email: event.email,
        password: event.password,
        fullName: event.fullName,
        targetAgency: event.targetAgency,
        additionalData: {
          'age': event.age,
          'gender': event.gender,
          'height': event.height,
          'weight': event.weight,
          'fitness_goal': event.fitnessGoal,
          'notification_preferences': event.notificationPreferences,
        },
      );

      if (authResponse.user != null) {
        // Get the user profile from our custom table
        final userProfile = await SupabaseAuthService.getUserProfile(
          userId: authResponse.user!.id,
        );

        if (userProfile != null) {
          // Register the first device for new user
          final deviceRegistration =
              await DeviceManagementService.registerCurrentDevice(
                userProfile.id,
              );

          if (deviceRegistration.success) {
            // Device registration successful, complete authentication
            await _persistentAuth.saveUserSession(userProfile);
            emit(Authenticated(userProfile));
          } else {
            // Device registration failed (shouldn't happen for new users)
            emit(
              AuthError(
                deviceRegistration.message ?? 'Device registration failed',
              ),
            );
          }
        } else {
          emit(AuthError('Failed to create user profile'));
        }
      } else {
        emit(AuthError('Failed to create user account'));
      }
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleSignIn(SignInEvent event, Emitter<AuthState> emit) async {
    try {
      emit(AuthLoading());

      // Sign in with Supabase
      final authResponse = await SupabaseAuthService.signIn(
        email: event.email,
        password: event.password,
      );

      if (authResponse.user != null) {
        // Get the user profile from our custom table
        final userProfile = await SupabaseAuthService.getUserProfile(
          userId: authResponse.user!.id,
        );

        if (userProfile != null) {
          // Register/check device before completing authentication
          final deviceRegistration =
              await DeviceManagementService.registerCurrentDevice(
                userProfile.id,
              );

          if (deviceRegistration.success) {
            // Device registration successful, complete authentication
            await _persistentAuth.saveUserSession(userProfile);
            emit(Authenticated(userProfile));
          } else if (deviceRegistration.isDeviceLimitExceeded) {
            // Device limit exceeded, show device management dialog
            emit(
              DeviceLimitExceeded(
                activeDevices: deviceRegistration.activeDevices ?? [],
                message:
                    deviceRegistration.message ??
                    'Maximum device limit reached',
              ),
            );
          } else {
            // Other device registration error
            emit(
              AuthError(
                deviceRegistration.message ?? 'Device registration failed',
              ),
            );
          }
        } else {
          emit(AuthError('Failed to load user profile'));
        }
      } else {
        emit(AuthError('Failed to sign in'));
      }
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleCheckAuthStatus(
    CheckAuthStatusEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // First check for stored user session (persistent login)
      final storedUser = await _persistentAuth.getStoredUser();

      if (storedUser != null) {
        // User has a valid stored session
        emit(Authenticated(storedUser));
        return;
      }

      // Check current Supabase session
      final currentUser = SupabaseAuthService.currentUser;

      if (currentUser != null) {
        // Session exists, fetch user profile
        final userProfile = await SupabaseAuthService.getUserProfile(
          userId: currentUser.id,
        );

        if (userProfile != null) {
          // Save the session for persistence
          await _persistentAuth.saveUserSession(userProfile);
          emit(Authenticated(userProfile));
        } else {
          emit(Unauthenticated());
        }
      } else {
        emit(Unauthenticated());
      }
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleRestoreAuth(
    RestoreAuthEvent event,
    Emitter<AuthState> emit,
  ) async {
    // Directly restore the user session without additional API calls
    await _persistentAuth.saveUserSession(event.user);
    emit(Authenticated(event.user));
  }

  Future<void> _handleSignOut(
    SignOutEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      // Deactivate current device if user is authenticated
      if (state is Authenticated) {
        final user = (state as Authenticated).user;
        await DeviceManagementService.deactivateCurrentDevice(user.id);
      }

      // Clear persistent session
      await _persistentAuth.clearUserSession();

      // Sign out from Supabase
      await SupabaseAuthService.signOut();

      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleResetPassword(
    ResetPasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());
      await SupabaseAuthService.resetPassword(email: event.email);
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateProfileImage(
    UpdateProfileImageEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state is! Authenticated) {
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update user model with new profile image URL
      final updatedUser = currentUser.copyWith(
        profileImageUrl: event.imageUrl,
        updatedAt: DateTime.now(),
      );

      // Update user in Supabase
      await SupabaseAuthService.updateUserProfile(
        userId: updatedUser.id,
        updates: {
          'profile_image_url': event.imageUrl,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateEmail(
    UpdateEmailEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update email in Supabase Auth and Database
      await SupabaseAuthService.updateEmail(newEmail: event.newEmail);

      // Update user model with new email
      final updatedUser = currentUser.copyWith(
        email: event.newEmail,
        updatedAt: DateTime.now(),
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdateNotificationPreferences(
    UpdateNotificationPreferencesEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      final currentUser = (state as Authenticated).user;

      // Update user model with new notification preferences
      final updatedUser = currentUser.copyWith(
        notificationPreferences: event.notificationPreferences,
        updatedAt: DateTime.now(),
      );

      // Update user in Supabase
      await SupabaseAuthService.updateUserProfile(
        userId: updatedUser.id,
        updates: {
          'notification_preferences': event.notificationPreferences,
          'updated_at': DateTime.now().toIso8601String(),
        },
      );

      // Emit authenticated state with updated user
      emit(Authenticated(updatedUser));
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleUpdatePassword(
    UpdatePasswordEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      // Update password in Supabase Auth
      await SupabaseAuthService.updatePassword(newPassword: event.newPassword);

      // Password updated successfully, keep the current user state
      emit(state);
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleDeleteAccount(
    DeleteAccountEvent event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthLoading());

      if (state is! Authenticated) {
        emit(AuthError('User not authenticated'));
        return;
      }

      // Delete account in Supabase
      await SupabaseAuthService.deleteAccount();

      // Account deleted successfully, sign out the user
      emit(Unauthenticated());
    } catch (e) {
      emit(AuthError(SupabaseAuthService.getErrorMessage(e)));
    }
  }

  Future<void> _handleDeviceLimitExceeded(
    DeviceLimitExceededEvent event,
    Emitter<AuthState> emit,
  ) async {
    emit(
      DeviceLimitExceeded(
        activeDevices: event.activeDevices,
        message:
            'Maximum device limit reached. Please remove a device to continue.',
      ),
    );
  }
}
