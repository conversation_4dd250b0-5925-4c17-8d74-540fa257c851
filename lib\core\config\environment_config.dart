import 'package:flutter/foundation.dart';

/// Environment configuration for Fit4Force app
/// This class manages environment-specific settings and sensitive keys
class EnvironmentConfig {
  // Private constructor to prevent instantiation
  EnvironmentConfig._();

  /// Environment types
  static const String development = 'development';
  static const String staging = 'staging';
  static const String production = 'production';

  /// Current environment (defaults to development in debug mode)
  static String get currentEnvironment {
    if (kDebugMode) {
      return const String.fromEnvironment(
        'ENVIRONMENT',
        defaultValue: development,
      );
    }
    return production;
  }

  /// Check if running in development
  static bool get isDevelopment => currentEnvironment == development;

  /// Check if running in staging
  static bool get isStaging => currentEnvironment == staging;

  /// Check if running in production
  static bool get isProduction => currentEnvironment == production;

  /// Supabase Service Role Key (NEVER expose this in client code)
  /// This should only be used for server-side operations or admin functions
  /// In a real app, this would be stored on your backend server
  static const String _supabaseServiceRoleKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnYXhka2R2Znh3dXVqbnpmbGx1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODE1OTE5NiwiZXhwIjoyMDYzNzM1MTk2fQ.2rLtPEPzZ_j0HGMduBAxGr1jvcb-pQRTw08zWnnMKrU';

  /// Get service role key (only for admin operations)
  /// WARNING: This should NEVER be used in production client code
  /// This is only for development/testing purposes
  static String? get supabaseServiceRoleKey {
    if (kDebugMode && isDevelopment) {
      // Only return the service role key in development mode
      return _supabaseServiceRoleKey;
    }

    // In production, this should return null or throw an error
    if (kDebugMode) {
      print(
        '⚠️ WARNING: Service role key requested in non-development environment',
      );
    }
    return null;
  }

  /// App configuration based on environment
  static Map<String, dynamic> get appConfig {
    switch (currentEnvironment) {
      case development:
        return {
          'app_name': 'Fit4Force (Dev)',
          'api_timeout': 30000, // 30 seconds
          'enable_logging': true,
          'enable_analytics': false,
          'enable_crash_reporting': false,
          'show_debug_info': true,
        };
      case staging:
        return {
          'app_name': 'Fit4Force (Staging)',
          'api_timeout': 20000, // 20 seconds
          'enable_logging': true,
          'enable_analytics': true,
          'enable_crash_reporting': true,
          'show_debug_info': false,
        };
      case production:
        return {
          'app_name': 'Fit4Force',
          'api_timeout': 15000, // 15 seconds
          'enable_logging': false,
          'enable_analytics': true,
          'enable_crash_reporting': true,
          'show_debug_info': false,
        };
      default:
        return {
          'app_name': 'Fit4Force',
          'api_timeout': 15000,
          'enable_logging': false,
          'enable_analytics': false,
          'enable_crash_reporting': false,
          'show_debug_info': false,
        };
    }
  }

  /// Get app name based on environment
  static String get appName => appConfig['app_name'] as String;

  /// Get API timeout based on environment
  static int get apiTimeout => appConfig['api_timeout'] as int;

  /// Check if logging is enabled
  static bool get isLoggingEnabled => appConfig['enable_logging'] as bool;

  /// Check if analytics is enabled
  static bool get isAnalyticsEnabled => appConfig['enable_analytics'] as bool;

  /// Check if crash reporting is enabled
  static bool get isCrashReportingEnabled =>
      appConfig['enable_crash_reporting'] as bool;

  /// Check if debug info should be shown
  static bool get showDebugInfo => appConfig['show_debug_info'] as bool;

  /// Deep link configuration
  static Map<String, String> get deepLinks {
    switch (currentEnvironment) {
      case development:
        return {
          'base_url': 'https://dev.fit4force.com',
          'reset_password': 'https://dev.fit4force.com/reset-password',
          'email_confirmation': 'https://dev.fit4force.com/confirm-email',
        };
      case staging:
        return {
          'base_url': 'https://staging.fit4force.com',
          'reset_password': 'https://staging.fit4force.com/reset-password',
          'email_confirmation': 'https://staging.fit4force.com/confirm-email',
        };
      case production:
        return {
          'base_url': 'https://fit4force.com',
          'reset_password': 'https://fit4force.com/reset-password',
          'email_confirmation': 'https://fit4force.com/confirm-email',
        };
      default:
        return {
          'base_url': 'https://fit4force.com',
          'reset_password': 'https://fit4force.com/reset-password',
          'email_confirmation': 'https://fit4force.com/confirm-email',
        };
    }
  }

  /// Print environment information (debug only)
  static void printEnvironmentInfo() {
    if (kDebugMode) {
      print('🌍 Environment: $currentEnvironment');
      print('📱 App Name: $appName');
      print('⏱️ API Timeout: ${apiTimeout}ms');
      print('📝 Logging: ${isLoggingEnabled ? 'Enabled' : 'Disabled'}');
      print('📊 Analytics: ${isAnalyticsEnabled ? 'Enabled' : 'Disabled'}');
      print(
        '💥 Crash Reporting: ${isCrashReportingEnabled ? 'Enabled' : 'Disabled'}',
      );
      print('🐛 Debug Info: ${showDebugInfo ? 'Enabled' : 'Disabled'}');
    }
  }

  /// Validate environment configuration
  static bool validateConfiguration() {
    try {
      // Check if all required configurations are present
      final config = appConfig;
      final requiredKeys = ['app_name', 'api_timeout', 'enable_logging'];

      for (final key in requiredKeys) {
        if (!config.containsKey(key)) {
          if (kDebugMode) {
            print('❌ Missing required configuration: $key');
          }
          return false;
        }
      }

      if (kDebugMode) {
        print('✅ Environment configuration validated successfully');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Environment configuration validation failed: $e');
      }
      return false;
    }
  }
}
