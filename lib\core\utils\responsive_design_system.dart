import 'package:flutter/material.dart';

/// Comprehensive responsive design system for real device compatibility
class ResponsiveDesignSystem {
  static const double _baseWidth = 375.0; // iPhone 6/7/8 base width
  static const double _baseHeight = 667.0; // iPhone 6/7/8 base height
  
  // Device breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 1024.0;
  static const double desktopBreakpoint = 1440.0;
  
  // Minimum touch target size (accessibility)
  static const double minTouchTarget = 44.0;
  
  /// Get screen dimensions with proper handling
  static Size getScreenSize(BuildContext context) {
    return MediaQuery.of(context).size;
  }
  
  /// Get safe screen dimensions (excluding system UI)
  static Size getSafeScreenSize(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final padding = mediaQuery.padding;
    return Size(
      mediaQuery.size.width,
      mediaQuery.size.height - padding.top - padding.bottom,
    );
  }
  
  /// Responsive width calculation
  static double width(BuildContext context, double designWidth) {
    final screenWidth = getScreenSize(context).width;
    return (designWidth / _baseWidth) * screenWidth;
  }
  
  /// Responsive height calculation
  static double height(BuildContext context, double designHeight) {
    final screenHeight = getScreenSize(context).height;
    return (designHeight / _baseHeight) * screenHeight;
  }
  
  /// Responsive font size with accessibility support
  static double fontSize(BuildContext context, double designFontSize) {
    final screenWidth = getScreenSize(context).width;
    final scaleFactor = screenWidth / _baseWidth;
    
    // Clamp scale factor to prevent extreme sizes
    final clampedScale = scaleFactor.clamp(0.8, 1.3);
    
    return designFontSize * clampedScale;
  }
  
  /// Responsive spacing system
  static double spacing(BuildContext context, SpacingSize size) {
    final screenWidth = getScreenSize(context).width;
    final scaleFactor = screenWidth / _baseWidth;
    
    double baseSpacing;
    switch (size) {
      case SpacingSize.xs:
        baseSpacing = 4.0;
        break;
      case SpacingSize.sm:
        baseSpacing = 8.0;
        break;
      case SpacingSize.md:
        baseSpacing = 16.0;
        break;
      case SpacingSize.lg:
        baseSpacing = 24.0;
        break;
      case SpacingSize.xl:
        baseSpacing = 32.0;
        break;
      case SpacingSize.xxl:
        baseSpacing = 48.0;
        break;
    }
    
    return baseSpacing * scaleFactor.clamp(0.8, 1.2);
  }
  
  /// Get responsive padding
  static EdgeInsets padding(BuildContext context, {
    double? all,
    double? horizontal,
    double? vertical,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    if (all != null) {
      return EdgeInsets.all(width(context, all));
    }
    
    return EdgeInsets.only(
      top: top != null ? height(context, top) : (vertical != null ? height(context, vertical) : 0),
      bottom: bottom != null ? height(context, bottom) : (vertical != null ? height(context, vertical) : 0),
      left: left != null ? width(context, left) : (horizontal != null ? width(context, horizontal) : 0),
      right: right != null ? width(context, right) : (horizontal != null ? width(context, horizontal) : 0),
    );
  }
  
  /// Device type detection
  static DeviceType getDeviceType(BuildContext context) {
    final screenWidth = getScreenSize(context).width;
    
    if (screenWidth < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (screenWidth < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }
  
  /// Check if device is small mobile (like Infinix Smart 8)
  static bool isSmallMobile(BuildContext context) {
    final screenWidth = getScreenSize(context).width;
    return screenWidth <= 400;
  }
  
  /// Get grid columns based on screen size
  static int getGridColumns(BuildContext context, {
    int? mobileColumns,
    int? tabletColumns,
    int? desktopColumns,
  }) {
    final deviceType = getDeviceType(context);
    final isSmall = isSmallMobile(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        if (isSmall && mobileColumns != null && mobileColumns > 2) {
          return 2; // Force max 2 columns on small devices
        }
        return mobileColumns ?? 2;
      case DeviceType.tablet:
        return tabletColumns ?? 3;
      case DeviceType.desktop:
        return desktopColumns ?? 4;
    }
  }
  
  /// Get responsive border radius
  static double borderRadius(BuildContext context, double designRadius) {
    return width(context, designRadius);
  }
  
  /// Get responsive icon size
  static double iconSize(BuildContext context, IconSizeType type) {
    double baseSize;
    switch (type) {
      case IconSizeType.small:
        baseSize = 16.0;
        break;
      case IconSizeType.medium:
        baseSize = 24.0;
        break;
      case IconSizeType.large:
        baseSize = 32.0;
        break;
      case IconSizeType.extraLarge:
        baseSize = 48.0;
        break;
    }
    
    return width(context, baseSize).clamp(minTouchTarget * 0.5, minTouchTarget * 1.5);
  }
  
  /// Normalize text scale factor to prevent layout breaks
  static MediaQueryData normalizeTextScale(MediaQueryData mediaQuery) {
    return mediaQuery.copyWith(
      textScaler: TextScaler.linear(
        mediaQuery.textScaler.scale(1.0).clamp(0.8, 1.2),
      ),
    );
  }
  
  /// Create responsive container constraints
  static BoxConstraints responsiveConstraints(BuildContext context, {
    double? minWidth,
    double? maxWidth,
    double? minHeight,
    double? maxHeight,
  }) {
    return BoxConstraints(
      minWidth: minWidth != null ? width(context, minWidth) : 0,
      maxWidth: maxWidth != null ? width(context, maxWidth) : double.infinity,
      minHeight: minHeight != null ? height(context, minHeight) : 0,
      maxHeight: maxHeight != null ? height(context, maxHeight) : double.infinity,
    );
  }
}

/// Spacing size enumeration
enum SpacingSize { xs, sm, md, lg, xl, xxl }

/// Device type enumeration
enum DeviceType { mobile, tablet, desktop }

/// Icon size type enumeration
enum IconSizeType { small, medium, large, extraLarge }

/// Responsive wrapper widget
class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final bool normalizeTextScale;
  final bool useSafeArea;
  
  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.normalizeTextScale = true,
    this.useSafeArea = true,
  });
  
  @override
  Widget build(BuildContext context) {
    Widget wrappedChild = child;
    
    // Apply SafeArea if requested
    if (useSafeArea) {
      wrappedChild = SafeArea(child: wrappedChild);
    }
    
    // Normalize text scale if requested
    if (normalizeTextScale) {
      return MediaQuery(
        data: ResponsiveDesignSystem.normalizeTextScale(MediaQuery.of(context)),
        child: wrappedChild,
      );
    }
    
    return wrappedChild;
  }
}

/// Responsive text widget with overflow protection
class ResponsiveText extends StatelessWidget {
  final String text;
  final double fontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final bool autoResize;
  
  const ResponsiveText(
    this.text, {
    super.key,
    required this.fontSize,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.autoResize = true,
  });
  
  @override
  Widget build(BuildContext context) {
    final responsiveFontSize = autoResize 
        ? ResponsiveDesignSystem.fontSize(context, fontSize)
        : fontSize;
    
    Widget textWidget = Text(
      text,
      style: TextStyle(
        fontSize: responsiveFontSize,
        fontWeight: fontWeight,
        color: color,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.ellipsis,
    );
    
    // Use FittedBox for auto-resizing if needed
    if (autoResize && maxLines == 1) {
      textWidget = FittedBox(
        fit: BoxFit.scaleDown,
        child: textWidget,
      );
    }
    
    return textWidget;
  }
}
