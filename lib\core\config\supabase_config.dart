import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';

class SupabaseConfig {
  // Fit4Force Supabase Project Credentials
  static const String supabaseUrl = 'https://cgaxdkdvfxwuujnzfllu.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNnYXhka2R2Znh3dXVqbnpmbGx1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgxNTkxOTYsImV4cCI6MjA2MzczNTE5Nn0.QbitrTrR7WETP9XVcau7ik6TXBg53RnSyUYS5fun9Tw';

  // For development, you can use environment variables or a separate config file
  static String get _supabaseUrl {
    // Temporarily disabled environment variable override to fix login issues
    // if (kDebugMode) {
    //   // You can use different URLs for development/staging
    //   return const String.fromEnvironment(
    //     'SUPABASE_URL',
    //     defaultValue: supabaseUrl,
    //   );
    // }
    return supabaseUrl;
  }

  static String get _supabaseAnonKey {
    // Temporarily disabled environment variable override to fix login issues
    // if (kDebugMode) {
    //   return const String.fromEnvironment(
    //     'SUPABASE_ANON_KEY',
    //     defaultValue: supabaseAnonKey,
    //   );
    // }
    return supabaseAnonKey;
  }

  static SupabaseClient get client => Supabase.instance.client;

  static Future<void> initialize() async {
    try {
      // Handle SSL certificate issues in development
      if (kDebugMode) {
        HttpOverrides.global = _DevHttpOverrides();
      }

      await Supabase.initialize(
        url: _supabaseUrl,
        anonKey: _supabaseAnonKey,
        debug: kDebugMode,
        authOptions: const FlutterAuthClientOptions(
          authFlowType: AuthFlowType.pkce,
        ),
        realtimeClientOptions: const RealtimeClientOptions(
          logLevel: RealtimeLogLevel.info,
        ),
      );

      if (kDebugMode) {
        print('✅ Supabase initialized successfully');
        print('🔗 URL: $_supabaseUrl');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Supabase initialization failed: $e');
      }
      rethrow;
    }
  }

  // Helper methods for common operations
  static bool get isInitialized {
    try {
      // Check if Supabase has been initialized by accessing the client
      Supabase.instance.client;
      return true;
    } catch (e) {
      return false;
    }
  }

  static User? get currentUser => client.auth.currentUser;

  static Session? get currentSession => client.auth.currentSession;

  static bool get isAuthenticated => currentUser != null;

  // Database table names - centralized for consistency
  static const String usersTable = 'users';
  static const String workoutsTable = 'workouts';
  static const String exercisesTable = 'exercises';
  static const String userWorkoutsTable = 'user_workouts';
  static const String progressTable = 'progress';
  static const String postsTable = 'posts';
  static const String commentsTable = 'comments';
  static const String likesTable = 'likes';
  static const String subscriptionsTable = 'subscriptions';
  static const String notificationsTable = 'notifications';
  static const String studyGroupsTable = 'study_groups';
  static const String studyGroupMembersTable = 'study_group_members';
  static const String badgesTable = 'badges';
  static const String userBadgesTable = 'user_badges';
  static const String quizzesTable = 'quizzes';
  static const String quizQuestionsTable = 'quiz_questions';
  static const String userQuizAttemptsTable = 'user_quiz_attempts';
  static const String nutritionPlansTable = 'nutrition_plans';
  static const String mealPlansTable = 'meal_plans';
  static const String challengesTable = 'challenges';
  static const String userChallengesTable = 'user_challenges';
  static const String userDevicesTable = 'user_devices';

  // Storage bucket names
  static const String profileImagesBucket = 'profile-images';
  static const String workoutImagesBucket = 'workout-images';
  static const String postImagesBucket = 'post-images';
  static const String exerciseVideosBucket = 'exercise-videos';
  static const String documentsBucket = 'documents';

  // Paystack public key for payment integration
  static const String paystackPublicKey =
      'pk_live_7c08b7b3f540f90e1b5729f88ae963cabc6079b1';
}

// Extension to add convenience methods to SupabaseClient
extension SupabaseClientExtension on SupabaseClient {
  // Quick access to commonly used services
  GoTrueClient get authClient => auth;
  SupabaseQueryBuilder get usersQuery => from(SupabaseConfig.usersTable);
  SupabaseQueryBuilder get workoutsQuery => from(SupabaseConfig.workoutsTable);
  SupabaseQueryBuilder get postsQuery => from(SupabaseConfig.postsTable);
  SupabaseQueryBuilder get progressQuery => from(SupabaseConfig.progressTable);
  SupabaseStorageClient get storageClient => storage;
}

// Custom HttpOverrides to handle SSL certificate issues in development
class _DevHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // In development, allow all certificates for Supabase domains
        if (kDebugMode &&
            (host.contains('supabase.co') ||
                host.contains('cgaxdkdvfxwuujnzfllu'))) {
          return true;
        }
        return false;
      };
  }
}
