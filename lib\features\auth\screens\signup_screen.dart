import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';
import 'package:fit_4_force/features/onboarding/services/onboarding_service.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _ageController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  final bool _obscurePassword = true;
  String _selectedGender = 'Male';
  String? _selectedAgency;
  String _selectedAgencyCode = '';
  String _selectedFitnessGoal = 'Build Strength';

  List<Map<String, dynamic>> _availableAgencies = [];
  bool _isLoadingAgencies = true;

  final List<String> _fitnessGoals = [
    'Build Strength',
    'Improve Endurance',
    'Lose Weight',
    'Maintain Fitness',
    'Prepare for Selection',
  ];

  @override
  void dispose() {
    _fullNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _ageController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadAvailableAgencies();
  }

  Future<void> _loadAvailableAgencies() async {
    try {
      // For now, use the static list but in a format that matches our database structure
      // In a real implementation, you would load this from Supabase
      final agencies = [
        {'code': 'army', 'name': 'Nigerian Army', 'full_name': 'Nigerian Army'},
        {'code': 'navy', 'name': 'Nigerian Navy', 'full_name': 'Nigerian Navy'},
        {
          'code': 'airforce',
          'name': 'Nigerian Air Force',
          'full_name': 'Nigerian Air Force',
        },
        {'code': 'nda', 'name': 'NDA', 'full_name': 'Nigerian Defence Academy'},
        {
          'code': 'dssc',
          'name': 'DSSC/SSC',
          'full_name': 'Direct Short Service Commission',
        },
        {'code': 'polac', 'name': 'POLAC', 'full_name': 'Police Academy'},
        {
          'code': 'fire',
          'name': 'Fire Service',
          'full_name': 'Federal Fire Service',
        },
        {
          'code': 'nscdc',
          'name': 'NSCDC',
          'full_name': 'Nigeria Security and Civil Defence Corps',
        },
        {
          'code': 'customs',
          'name': 'Customs Service',
          'full_name': 'Nigeria Customs Service',
        },
        {
          'code': 'immigration',
          'name': 'Immigration',
          'full_name': 'Nigeria Immigration Service',
        },
        {
          'code': 'frsc',
          'name': 'FRSC',
          'full_name': 'Federal Road Safety Corps',
        },
      ];

      setState(() {
        _availableAgencies = agencies;
        _isLoadingAgencies = false;
        if (agencies.isNotEmpty) {
          _selectedAgency = agencies.first['name'] as String;
          _selectedAgencyCode = agencies.first['code'] as String;
        }
      });
    } catch (e) {
      setState(() {
        _isLoadingAgencies = false;
        // Fallback to static list
        _availableAgencies =
            AppConfig.supportedAgencies
                .map(
                  (agency) => {
                    'code': agency.toLowerCase().replaceAll(' ', '_'),
                    'name': agency,
                    'full_name': agency,
                  },
                )
                .toList();
        if (_availableAgencies.isNotEmpty) {
          _selectedAgency = _availableAgencies.first['name'] as String;
          _selectedAgencyCode = _availableAgencies.first['code'] as String;
        }
      });
    }
  }

  void _handleSignUp() {
    if (_formKey.currentState?.validate() ?? false) {
      setState(() {
        _isLoading = true;
      });
      context.read<AuthBloc>().add(
        SignUpEvent(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          fullName: _fullNameController.text.trim(),
          age: int.parse(_ageController.text),
          gender: _selectedGender,
          height: double.parse(_heightController.text),
          weight: double.parse(_weightController.text),
          targetAgency: _selectedAgencyCode,
          fitnessGoal: _selectedFitnessGoal,
          notificationPreferences: {
            'workout_reminders': true,
            'study_reminders': true,
            'community_updates': true,
          },
        ),
      );
      // Reset loading state after signup attempt
      Future.delayed(const Duration(milliseconds: 800), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Account')),
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          } else if (state is Authenticated) {
            // Mark onboarding as completed for new users
            OnboardingService.completeOnboarding();

            // Automatically navigate to home after successful registration
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/home');
            }
          }
        },
        builder: (context, state) {
          final isDesktop = ResponsiveUtils.isDesktop(context);
          final isTablet = ResponsiveUtils.isTablet(context);
          final padding = ResponsiveUtils.getResponsivePadding(context);
          final spacing = ResponsiveUtils.getResponsiveSpacing(context);

          return SafeArea(
            child: SingleChildScrollView(
              padding: padding,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth:
                        isDesktop ? 500 : (isTablet ? 600 : double.infinity),
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Fit4Force Logo
                        Center(
                          child: Container(
                            margin: EdgeInsets.only(bottom: spacing),
                            child: Image.asset(
                              'assets/images/fit4force_main_logo.png',
                              width: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                mobile: 80,
                                tablet: 90,
                                desktop: 100,
                              ),
                              height: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                mobile: 80,
                                tablet: 90,
                                desktop: 100,
                              ),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                // Fallback to simple icon if logo not found
                                return Icon(
                                  Icons.fitness_center,
                                  size: ResponsiveUtils.getResponsiveFontSize(
                                    context,
                                    mobile: 40,
                                    tablet: 45,
                                    desktop: 50,
                                  ),
                                  color: AppTheme.primaryColor,
                                );
                              },
                            ),
                          ),
                        ),

                        // Welcome text
                        Center(
                          child: Column(
                            children: [
                              Text(
                                'Join Fit4Force',
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveUtils.getResponsiveFontSize(
                                        context,
                                        mobile: 24,
                                        tablet: 28,
                                        desktop: 32,
                                      ),
                                  fontWeight: FontWeight.bold,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                              SizedBox(height: spacing * 0.5),
                              Text(
                                'Start your military preparation journey',
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveUtils.getResponsiveFontSize(
                                        context,
                                        mobile: 14,
                                        tablet: 16,
                                        desktop: 18,
                                      ),
                                  color: Colors.grey[600],
                                ),
                                textAlign: TextAlign.center,
                              ),
                              SizedBox(height: spacing * 1.5),
                            ],
                          ),
                        ),

                        BaseTextField(
                          label: 'Full Name',
                          controller: _fullNameController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your full name';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: spacing),
                        BaseTextField(
                          label: 'Email',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!value.contains('@')) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        BaseTextField(
                          label: 'Password',
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        BaseTextField(
                          label: 'Age',
                          controller: _ageController,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your age';
                            }
                            final age = int.tryParse(value);
                            if (age == null || age < 18 || age > 35) {
                              return 'Age must be between 18 and 35';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: _selectedGender,
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                          decoration: const InputDecoration(
                            labelText: 'Gender',
                            fillColor: Colors.white,
                            filled: true,
                          ),
                          items:
                              ['Male', 'Female']
                                  .map(
                                    (gender) => DropdownMenuItem(
                                      value: gender,
                                      child: Text(
                                        gender,
                                        style: const TextStyle(
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedGender = value;
                              });
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        BaseTextField(
                          label: 'Height (cm)',
                          controller: _heightController,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your height';
                            }
                            final height = double.tryParse(value);
                            if (height == null ||
                                height < 150 ||
                                height > 220) {
                              return 'Height must be between 150 and 220 cm';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        BaseTextField(
                          label: 'Weight (kg)',
                          controller: _weightController,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your weight';
                            }
                            final weight = double.tryParse(value);
                            if (weight == null || weight < 45 || weight > 120) {
                              return 'Weight must be between 45 and 120 kg';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),
                        _isLoadingAgencies
                            ? const Center(child: CircularProgressIndicator())
                            : DropdownButtonFormField<String>(
                              value: _selectedAgency,
                              style: const TextStyle(
                                color: Colors.black87,
                                fontSize: 16,
                              ),
                              decoration: const InputDecoration(
                                labelText: 'Target Agency',
                                prefixIcon: Icon(Icons.military_tech),
                                fillColor: Colors.white,
                                filled: true,
                              ),
                              items:
                                  _availableAgencies
                                      .map(
                                        (agency) => DropdownMenuItem(
                                          value: agency['name'] as String,
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Text(
                                                agency['name'] as String,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                  color: Colors.black87,
                                                ),
                                              ),
                                              Text(
                                                agency['full_name'] as String,
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  color: Colors.grey[600],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                      .toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  final selectedAgencyData = _availableAgencies
                                      .firstWhere(
                                        (agency) => agency['name'] == value,
                                      );
                                  setState(() {
                                    _selectedAgency = value;
                                    _selectedAgencyCode =
                                        selectedAgencyData['code'] as String;
                                  });
                                }
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select your target agency';
                                }
                                return null;
                              },
                            ),
                        const SizedBox(height: 16),
                        DropdownButtonFormField<String>(
                          value: _selectedFitnessGoal,
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                          decoration: const InputDecoration(
                            labelText: 'Fitness Goal',
                            fillColor: Colors.white,
                            filled: true,
                          ),
                          items:
                              _fitnessGoals
                                  .map(
                                    (goal) => DropdownMenuItem(
                                      value: goal,
                                      child: Text(
                                        goal,
                                        style: const TextStyle(
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _selectedFitnessGoal = value;
                              });
                            }
                          },
                        ),
                        SizedBox(height: spacing * 1.5),
                        ResponsiveButton(
                          text: 'Create Account',
                          backgroundColor: Theme.of(context).primaryColor,
                          textColor: Colors.white,
                          onPressed:
                              (_isLoading || state is AuthLoading)
                                  ? null
                                  : _handleSignUp,
                          mobileHeight: 48.0,
                          tabletHeight: 52.0,
                          desktopHeight: 56.0,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
