import 'package:fit_4_force/features/news/models/agency_news_model.dart';

/// Simplified Agency News Service for testing
/// This is a minimal version to get the app running
class AgencyNewsService {
  static final AgencyNewsService _instance = AgencyNewsService._internal();
  factory AgencyNewsService() => _instance;
  AgencyNewsService._internal();

  // Mock news data
  final List<AgencyNewsModel> _mockNews = [
    AgencyNewsModel(
      id: '1',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      title: 'Nigerian Army Recruitment 2025',
      content: 'New recruitment portal opens for aspiring soldiers.',
      agency: 'Nigerian Army',
      category: 'Recruitment',
      source: 'Nigerian Army HQ',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      isBreaking: true,
      isPinned: true,
      priority: 'high',
      imageUrl: '',
      tags: ['recruitment', 'army'],
      applicationDeadline: DateTime.now().add(const Duration(days: 30)),
    ),
    AgencyNewsModel(
      id: '2',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      title: 'Police Force Training Program',
      content: 'New training curriculum announced for 2025.',
      agency: 'Nigeria Police Force',
      category: 'Training',
      source: 'Police Training Command',
      publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      isBreaking: false,
      isPinned: false,
      priority: 'medium',
      imageUrl: '',
      tags: ['training', 'police'],
    ),
  ];

  /// Get all news
  Future<List<AgencyNewsModel>> getAllNews() async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay
    return List.from(_mockNews);
  }

  /// Get personalized news
  Future<List<AgencyNewsModel>> getPersonalizedNews(
    String userId, {
    List<String>? preferredAgencies,
    List<String>? interests,
  }) async {
    return getAllNews();
  }

  /// Get breaking news
  Future<List<AgencyNewsModel>> getBreakingNews() async {
    final allNews = await getAllNews();
    return allNews.where((news) => news.isBreaking).toList();
  }

  /// Get news with deadlines
  Future<List<AgencyNewsModel>> getNewsWithDeadlines() async {
    final allNews = await getAllNews();
    return allNews.where((news) => news.applicationDeadline != null).toList();
  }

  /// Get categories
  Future<List<String>> getCategories() async {
    return ['Recruitment', 'Training', 'News', 'Updates'];
  }

  /// Mark news as read
  Future<void> markAsRead(String newsId, String userId) async {
    // Simulate marking as read
    await Future.delayed(const Duration(milliseconds: 100));
  }

  /// Get news by agency
  Future<List<AgencyNewsModel>> getNewsByAgency(String agency) async {
    final allNews = await getAllNews();
    return allNews.where((news) => news.agency == agency).toList();
  }

  /// Search news
  Future<List<AgencyNewsModel>> searchNews(String query) async {
    if (query.isEmpty) return getAllNews();

    final allNews = await getAllNews();
    return allNews
        .where(
          (news) =>
              news.title.toLowerCase().contains(query.toLowerCase()) ||
              news.content.toLowerCase().contains(query.toLowerCase()),
        )
        .toList();
  }

  /// Refresh agency news
  Future<void> refreshAgencyNews(String agency) async {
    // Simulate refresh
    await Future.delayed(const Duration(seconds: 1));
  }

  /// Get news status
  Map<String, dynamic> getNewsStatus() {
    return {
      'total_news': _mockNews.length,
      'breaking_news': _mockNews.where((n) => n.isBreaking).length,
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}
