import 'dart:io';

void main() async {
  print('🔧 Starting deprecation fixes...');

  // Get all Dart files in lib directory
  final libDir = Directory('lib');
  final dartFiles =
      await libDir
          .list(recursive: true)
          .where((entity) => entity is File && entity.path.endsWith('.dart'))
          .cast<File>()
          .toList();

  int totalReplacements = 0;

  for (final file in dartFiles) {
    final content = await file.readAsString();
    String updatedContent = content;
    int replacements = 0;

    // Fix withOpacity deprecation
    final withOpacityRegex = RegExp(r'\.withOpacity\(([^)]+)\)');
    updatedContent = updatedContent.replaceAllMapped(withOpacityRegex, (match) {
      final alphaValue = match.group(1);
      replacements++;
      return '.withValues(alpha: $alphaValue)';
    });

    // Fix unnecessary string interpolation braces
    final stringInterpRegex = RegExp(r'\$\{([a-zA-Z_][a-zA-Z0-9_]*)\}');
    updatedContent = updatedContent.replaceAllMapped(stringInterpRegex, (
      match,
    ) {
      final variable = match.group(1);
      // Only replace if it's a simple variable (no dots, brackets, etc.)
      if (variable != null &&
          !variable.contains('.') &&
          !variable.contains('[')) {
        replacements++;
        return '\$$variable';
      }
      return match.group(0)!;
    });

    if (replacements > 0) {
      await file.writeAsString(updatedContent);
      print('✅ Fixed $replacements issues in ${file.path}');
      totalReplacements += replacements;
    }
  }

  print('🎉 Completed! Fixed $totalReplacements deprecation warnings.');
  print('📋 Next steps:');
  print('  1. Run "flutter pub get" to ensure dependencies are up to date');
  print('  2. Run "dart analyze" to check remaining issues');
  print('  3. Review and remove any remaining unused variables manually');
}
