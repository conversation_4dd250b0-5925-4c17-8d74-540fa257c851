import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_ui.dart';
import '../models/study_tip_model.dart';
import '../data/study_tips_database.dart';
import 'study_tip_detail_screen.dart';

class StudyTipsScreen extends StatefulWidget {
  const StudyTipsScreen({super.key});

  @override
  State<StudyTipsScreen> createState() => _StudyTipsScreenState();
}

class _StudyTipsScreenState extends State<StudyTipsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String _searchQuery = '';
  StudyTipCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: const Text(
          'Study Tips & Strategies',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Featured'),
            Tab(text: 'Categories'),
            Tab(text: 'All Tips'),
            Tab(text: 'Bookmarks'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFeaturedTab(),
          _buildCategoriesTab(),
          _buildAllTipsTab(),
          _buildBookmarksTab(),
        ],
      ),
    );
  }

  Widget _buildFeaturedTab() {
    final featuredTips = StudyTipsDatabase.getFeaturedTips();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeCard(),
          const SizedBox(height: 24),
          Text(
            '🌟 Featured Study Strategies',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryDark,
            ),
          ),
          const SizedBox(height: 16),
          ...featuredTips.map((tip) => _buildFeaturedTipCard(tip)),
        ],
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8 * 255),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppUI.universalShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2 * 255),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.military_tech,
                  color: Colors.white,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Master Your Mission',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Elite study strategies for military success',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9 * 255),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1 * 255),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '25+',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Expert Tips',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8 * 255),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.white.withValues(alpha: 0.3 * 255),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '10',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Categories',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8 * 255),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.white.withValues(alpha: 0.3 * 255),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '100%',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Success Rate',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white.withValues(alpha: 0.8 * 255),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedTipCard(StudyTipModel tip) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppUI.universalShadow,
      ),
      child: InkWell(
        onTap: () => _navigateToTipDetail(tip),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: tip.color.withValues(alpha: 0.1 * 255),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(tip.icon, color: tip.color, size: 24),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tip.title,
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textPrimaryDark,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          tip.subtitle,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.textSecondaryLight),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: tip.difficultyColor.withValues(alpha: 0.1 * 255),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      tip.difficulty,
                      style: TextStyle(
                        color: tip.difficultyColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    tip.keyPoints
                        .take(3)
                        .map(
                          (point) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.backgroundLight,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              point,
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondaryLight,
                                fontSize: 11,
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppTheme.textSecondaryLight,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${tip.readingTimeMinutes} min read',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryLight,
                    ),
                  ),
                  const Spacer(),
                  Icon(Icons.arrow_forward_ios, size: 16, color: tip.color),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoriesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📚 Study Categories',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryDark,
            ),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
            ),
            itemCount: StudyTipCategory.values.length,
            itemBuilder: (context, index) {
              final category = StudyTipCategory.values[index];
              return _buildCategoryCard(category);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(StudyTipCategory category) {
    final tips = StudyTipsDatabase.getTipsByCategory(category);
    final categoryInfo = _getCategoryInfo(category);

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppUI.universalShadow,
      ),
      child: InkWell(
        onTap: () => _showCategoryTips(category),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: categoryInfo['color'].withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  categoryInfo['icon'],
                  color: categoryInfo['color'],
                  size: 32,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                categoryInfo['name'],
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryDark,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${tips.length} tips',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryLight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAllTipsTab() {
    final allTips = StudyTipsDatabase.allStudyTips;
    final filteredTips =
        _searchQuery.isEmpty
            ? allTips
            : StudyTipsDatabase.searchTips(_searchQuery);

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredTips.length,
      itemBuilder: (context, index) {
        final tip = filteredTips[index];
        return _buildCompactTipCard(tip);
      },
    );
  }

  Widget _buildBookmarksTab() {
    // In a real app, this would fetch bookmarked tips from user preferences
    final bookmarkedTips = StudyTipsDatabase.allStudyTips.take(2).toList();

    if (bookmarkedTips.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_border,
              size: 64,
              color: AppTheme.textSecondaryLight,
            ),
            const SizedBox(height: 16),
            Text(
              'No Bookmarked Tips',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textSecondaryLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Bookmark your favorite study tips for quick access',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryLight,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: bookmarkedTips.length,
      itemBuilder: (context, index) {
        final tip = bookmarkedTips[index];
        return _buildCompactTipCard(tip);
      },
    );
  }

  Widget _buildCompactTipCard(StudyTipModel tip) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppUI.universalShadow,
      ),
      child: InkWell(
        onTap: () => _navigateToTipDetail(tip),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: tip.color.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(tip.icon, color: tip.color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tip.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryDark,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      tip.subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryLight,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: tip.difficultyColor.withValues(alpha: 0.1 * 255),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      tip.difficulty,
                      style: TextStyle(
                        color: tip.difficultyColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${tip.readingTimeMinutes} min',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryLight,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Map<String, dynamic> _getCategoryInfo(StudyTipCategory category) {
    switch (category) {
      case StudyTipCategory.timeManagement:
        return {
          'name': 'Time Management',
          'icon': Icons.schedule,
          'color': const Color(0xFF1976D2),
        };
      case StudyTipCategory.memoryTechniques:
        return {
          'name': 'Memory Techniques',
          'icon': Icons.psychology,
          'color': const Color(0xFF7B1FA2),
        };
      case StudyTipCategory.examStrategy:
        return {
          'name': 'Exam Strategy',
          'icon': Icons.psychology_alt,
          'color': const Color(0xFFE65100),
        };
      case StudyTipCategory.stressManagement:
        return {
          'name': 'Stress Management',
          'icon': Icons.self_improvement,
          'color': const Color(0xFF2E7D32),
        };
      case StudyTipCategory.motivation:
        return {
          'name': 'Motivation',
          'icon': Icons.emoji_events,
          'color': const Color(0xFFD32F2F),
        };
      case StudyTipCategory.healthWellness:
        return {
          'name': 'Health & Wellness',
          'icon': Icons.favorite,
          'color': const Color(0xFFE91E63),
        };
      case StudyTipCategory.studyEnvironment:
        return {
          'name': 'Study Environment',
          'icon': Icons.home,
          'color': const Color(0xFF388E3C),
        };
      case StudyTipCategory.noteTaking:
        return {
          'name': 'Note Taking',
          'icon': Icons.edit_note,
          'color': const Color(0xFF5E35B1),
        };
      case StudyTipCategory.practiceTests:
        return {
          'name': 'Practice Tests',
          'icon': Icons.quiz,
          'color': const Color(0xFF00796B),
        };
      case StudyTipCategory.lastMinutePrep:
        return {
          'name': 'Last Minute Prep',
          'icon': Icons.flash_on,
          'color': const Color(0xFFFF6F00),
        };
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Study Tips'),
            content: TextField(
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'Enter keywords...',
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _tabController.animateTo(2); // Switch to All Tips tab
                },
                child: const Text('Search'),
              ),
            ],
          ),
    );
  }

  void _showCategoryTips(StudyTipCategory category) {
    final tips = StudyTipsDatabase.getTipsByCategory(category);
    final categoryInfo = _getCategoryInfo(category);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => Scaffold(
              appBar: AppBar(
                title: Text(categoryInfo['name']),
                backgroundColor: categoryInfo['color'],
                foregroundColor: Colors.white,
              ),
              body: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: tips.length,
                itemBuilder: (context, index) {
                  return _buildCompactTipCard(tips[index]);
                },
              ),
            ),
      ),
    );
  }

  void _navigateToTipDetail(StudyTipModel tip) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => StudyTipDetailScreen(tip: tip)),
    );
  }
}
