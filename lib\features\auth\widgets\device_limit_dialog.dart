import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/shared/models/user_device_model.dart';
import 'package:fit_4_force/core/services/device_management_service.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

class DeviceLimitDialog extends StatefulWidget {
  final List<UserDeviceModel> activeDevices;
  final String message;

  const DeviceLimitDialog({
    super.key,
    required this.activeDevices,
    required this.message,
  });

  @override
  State<DeviceLimitDialog> createState() => _DeviceLimitDialogState();
}

class _DeviceLimitDialogState extends State<DeviceLimitDialog> {
  bool _isRemoving = false;
  String? _currentDeviceId;

  @override
  void initState() {
    super.initState();
    _loadCurrentDeviceId();
  }

  Future<void> _loadCurrentDeviceId() async {
    _currentDeviceId = await DeviceManagementService.getCurrentDeviceId();
    if (mounted) setState(() {});
  }

  Future<void> _removeDeviceAndRetry(UserDeviceModel device) async {
    setState(() => _isRemoving = true);

    try {
      final authState = context.read<AuthBloc>().state;
      if (authState is! Authenticated) {
        Navigator.of(context).pop();
        return;
      }

      // Remove the selected device
      final success = await DeviceManagementService.removeDevice(
        authState.user.id,
        device.deviceId,
      );

      if (success) {
        // Try to register current device again
        final deviceRegistration = await DeviceManagementService.registerCurrentDevice(
          authState.user.id,
        );

        if (deviceRegistration.success) {
          // Success! Close dialog and complete authentication
          Navigator.of(context).pop();
          context.read<AuthBloc>().add(AuthenticatedEvent(authState.user));
        } else {
          // Still failed, show error
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(deviceRegistration.message ?? 'Failed to register device'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to remove device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isRemoving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Colors.orange,
            size: 28,
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              'Device Limit Reached',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.message,
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          const Text(
            'Select a device to remove:',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.activeDevices.map((device) {
            final isCurrentDevice = device.deviceId == _currentDeviceId;
            
            return Container(
              margin: const EdgeInsets.only(bottom: 8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.grey.shade300,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                leading: Icon(
                  _getDeviceIcon(device.deviceType),
                  color: AppTheme.primaryColor,
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        device.deviceName,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    if (isCurrentDevice)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Text(
                          'This Device',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                  ],
                ),
                subtitle: Text(
                  '${device.platform} • Last active: ${device.getFormattedLastLogin()}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
                trailing: isCurrentDevice
                    ? Icon(
                        Icons.block,
                        color: Colors.grey.shade400,
                        size: 20,
                      )
                    : IconButton(
                        onPressed: _isRemoving
                            ? null
                            : () => _removeDeviceAndRetry(device),
                        icon: _isRemoving
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(
                                Icons.delete_outline,
                                color: Colors.red,
                                size: 20,
                              ),
                        tooltip: 'Remove this device',
                      ),
              ),
            );
          }),
          if (widget.activeDevices.every((d) => d.deviceId == _currentDeviceId))
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.only(top: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.blue.shade600,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'You cannot remove the current device. Please use another device to manage your device list.',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isRemoving ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            Navigator.of(context).pushNamed('/device-management');
          },
          child: const Text('Manage Devices'),
        ),
      ],
    );
  }

  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'phone':
      case 'mobile':
        return Icons.smartphone;
      case 'tablet':
        return Icons.tablet;
      case 'desktop':
      case 'computer':
        return Icons.computer;
      case 'laptop':
        return Icons.laptop;
      default:
        return Icons.device_unknown;
    }
  }
}
