import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/mock_exam_model.dart';
import 'package:fit_4_force/features/prep/data/comprehensive_mock_exam_database.dart';

class MockExamService {
  // Singleton pattern
  static final MockExamService _instance = MockExamService._internal();

  factory MockExamService() {
    return _instance;
  }

  MockExamService._internal();

  // Comprehensive mock exams with 100 questions each for all agencies
  List<MockExamModel> get _mockExams => [
    MockExamModel(
      id: '1',
      title: 'General Knowledge Test (Premium)',
      description:
          'Comprehensive test covering Nigerian history, geography, culture, and government. 100 questions for thorough preparation.',
      category: 'General Knowledge',
      timeLimit: 120, // 2 hours for 100 questions
      questions: ComprehensiveMockExamDatabase.generalKnowledgeQuestions,
      isPremium: true, // Now premium feature
      color: Colors.blue,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      totalAttempts: 245,
      averageScore: 72.5,
    ),

    MockExamModel(
      id: '2',
      title: 'Mathematics Aptitude Test (Premium)',
      description:
          'Comprehensive mathematical assessment covering arithmetic, algebra, geometry, and problem-solving. 100 questions for complete preparation.',
      category: 'Mathematics',
      timeLimit: 150, // 2.5 hours for 100 questions
      questions: ComprehensiveMockExamDatabase.mathematicsAptitudeQuestions,
      isPremium: true, // Now premium feature
      color: Colors.green,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      totalAttempts: 189,
      averageScore: 68.2,
    ),

    MockExamModel(
      id: '3',
      title: 'Current Affairs Test (Premium)',
      description:
          'Comprehensive current affairs assessment covering Nigerian and international developments, politics, economy, and recent events. 100 questions for up-to-date knowledge.',
      category: 'Current Affairs',
      timeLimit: 120, // 2 hours for 100 questions
      questions: ComprehensiveMockExamDatabase.currentAffairsQuestions,
      isPremium: true,
      color: Colors.orange,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      totalAttempts: 132,
      averageScore: 75.8,
    ),

    // Military Agency Exams
    MockExamModel(
      id: '4',
      title: 'Nigerian Army Exam (Premium)',
      description:
          'Specialized exam for Nigerian Army recruitment covering military history, traditions, operations, and army-specific knowledge. 100 comprehensive questions.',
      category: 'Nigerian Army',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.nigerianArmyQuestions,
      isPremium: true,
      color: const Color(0xFF2E7D32), // Army Green
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      totalAttempts: 156,
      averageScore: 78.3,
    ),

    MockExamModel(
      id: '5',
      title: 'Nigerian Navy Exam (Premium)',
      description:
          'Specialized exam for Nigerian Navy recruitment covering maritime operations, naval history, seamanship, and navy-specific knowledge. 100 comprehensive questions.',
      category: 'Nigerian Navy',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.nigerianNavyQuestions,
      isPremium: true,
      color: const Color(0xFF1565C0), // Navy Blue
      createdAt: DateTime.now().subtract(const Duration(days: 8)),
      totalAttempts: 134,
      averageScore: 76.8,
    ),

    MockExamModel(
      id: '6',
      title: 'Nigerian Air Force Exam (Premium)',
      description:
          'Specialized exam for Nigerian Air Force recruitment covering aviation, air operations, aerospace knowledge, and air force-specific topics. 100 comprehensive questions.',
      category: 'Nigerian Air Force',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.nigerianAirForceQuestions,
      isPremium: true,
      color: const Color(0xFF0277BD), // Sky Blue
      createdAt: DateTime.now().subtract(const Duration(days: 6)),
      totalAttempts: 98,
      averageScore: 79.1,
    ),

    MockExamModel(
      id: '7',
      title: 'NDA Exam (Premium)',
      description:
          'Nigerian Defence Academy entrance exam covering leadership, military science, academic excellence, and officer training preparation. 100 comprehensive questions.',
      category: 'NDA',
      timeLimit: 150, // Longer for academy entrance
      questions: ComprehensiveMockExamDatabase.ndaQuestions,
      isPremium: true,
      color: const Color(0xFF6A1B9A), // Purple for academy
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      totalAttempts: 87,
      averageScore: 81.2,
    ),

    MockExamModel(
      id: '8',
      title: 'DSSC/SSC Exam (Premium)',
      description:
          'Defence Services Staff College exam for senior military officers covering strategic studies, command, and advanced military education. 100 comprehensive questions.',
      category: 'DSSC',
      timeLimit: 150,
      questions: ComprehensiveMockExamDatabase.dsscQuestions,
      isPremium: true,
      color: const Color(0xFF8E24AA), // Deep Purple
      createdAt: DateTime.now().subtract(const Duration(days: 4)),
      totalAttempts: 45,
      averageScore: 83.5,
    ),

    // Paramilitary Agency Exams
    MockExamModel(
      id: '9',
      title: 'POLAC Exam (Premium)',
      description:
          'Nigeria Police Academy exam covering law enforcement, criminal justice, police procedures, and academy-specific knowledge. 100 comprehensive questions.',
      category: 'POLAC',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.polacQuestions,
      isPremium: true,
      color: const Color(0xFF424242), // Police Gray
      createdAt: DateTime.now().subtract(const Duration(days: 3)),
      totalAttempts: 167,
      averageScore: 74.6,
    ),

    MockExamModel(
      id: '10',
      title: 'Fire Service Exam (Premium)',
      description:
          'Federal Fire Service exam covering fire safety, emergency response, rescue operations, and fire service protocols. 100 comprehensive questions.',
      category: 'Fire Service',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.fireServiceQuestions,
      isPremium: true,
      color: const Color(0xFFD32F2F), // Fire Red
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      totalAttempts: 89,
      averageScore: 77.2,
    ),

    MockExamModel(
      id: '11',
      title: 'NSCDC Exam (Premium)',
      description:
          'National Security and Civil Defence Corps exam covering infrastructure protection, civil defence, security operations, and emergency management. 100 comprehensive questions.',
      category: 'NSCDC',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.nscdcQuestions,
      isPremium: true,
      color: const Color(0xFF388E3C), // Defence Green
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      totalAttempts: 123,
      averageScore: 76.8,
    ),

    MockExamModel(
      id: '12',
      title: 'Customs Exam (Premium)',
      description:
          'Nigerian Customs Service exam covering customs procedures, trade regulations, border security, and revenue collection. 100 comprehensive questions.',
      category: 'Customs',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.customsQuestions,
      isPremium: true,
      color: const Color(0xFF7B1FA2), // Customs Purple
      createdAt: DateTime.now(),
      totalAttempts: 145,
      averageScore: 75.4,
    ),

    MockExamModel(
      id: '13',
      title: 'Immigration Exam (Premium)',
      description:
          'Nigerian Immigration Service exam covering immigration laws, border control, passport procedures, and visa regulations. 100 comprehensive questions.',
      category: 'Immigration',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.immigrationQuestions,
      isPremium: true,
      color: const Color(0xFF303F9F), // Immigration Blue
      createdAt: DateTime.now().add(const Duration(days: 1)),
      totalAttempts: 112,
      averageScore: 78.1,
    ),

    MockExamModel(
      id: '14',
      title: 'FRSC Exam (Premium)',
      description:
          'Federal Road Safety Corps exam covering traffic laws, road safety regulations, accident prevention, and vehicle inspection procedures. 100 comprehensive questions.',
      category: 'FRSC',
      timeLimit: 120,
      questions: ComprehensiveMockExamDatabase.frscQuestions,
      isPremium: true,
      color: const Color(0xFFFF6F00), // Road Safety Orange
      createdAt: DateTime.now().add(const Duration(days: 2)),
      totalAttempts: 178,
      averageScore: 73.9,
    ),
  ];

  // Sample exam attempts
  final List<ExamAttemptModel> _examAttempts = [];

  // Get all mock exams
  List<MockExamModel> getAllExams() {
    return _mockExams;
  }

  // Get mock exams by category
  List<MockExamModel> getExamsByCategory(String category) {
    return _mockExams.where((exam) => exam.category == category).toList();
  }

  // Get a specific exam by ID
  MockExamModel? getExamById(String id) {
    try {
      return _mockExams.firstWhere((exam) => exam.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get all attempts for a user
  List<ExamAttemptModel> getUserAttempts(String userId) {
    return _examAttempts.where((attempt) => attempt.userId == userId).toList();
  }

  // Get attempts for a specific exam by a user
  List<ExamAttemptModel> getUserExamAttempts(String userId, String examId) {
    return _examAttempts
        .where(
          (attempt) => attempt.userId == userId && attempt.examId == examId,
        )
        .toList();
  }

  // Start a new exam attempt
  ExamAttemptModel startExam(String examId, String userId) {
    final attempt = ExamAttemptModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      examId: examId,
      userId: userId,
      startTime: DateTime.now(),
      answers: {},
    );

    _examAttempts.add(attempt);
    return attempt;
  }

  // Save an answer during an exam
  void saveAnswer(String attemptId, String questionId, dynamic answer) {
    final index = _examAttempts.indexWhere(
      (attempt) => attempt.id == attemptId,
    );
    if (index != -1) {
      final attempt = _examAttempts[index];
      final answers = Map<String, dynamic>.from(attempt.answers);
      answers[questionId] = answer;

      _examAttempts[index] = ExamAttemptModel(
        id: attempt.id,
        examId: attempt.examId,
        userId: attempt.userId,
        startTime: attempt.startTime,
        endTime: attempt.endTime,
        answers: answers,
        score: attempt.score,
        totalPossible: attempt.totalPossible,
        isCompleted: attempt.isCompleted,
      );
    }
  }

  // Complete an exam and calculate score
  ExamResultModel completeExam(String attemptId) {
    final index = _examAttempts.indexWhere(
      (attempt) => attempt.id == attemptId,
    );
    if (index == -1) {
      throw Exception('Attempt not found');
    }

    final attempt = _examAttempts[index];
    final exam = getExamById(attempt.examId);

    if (exam == null) {
      throw Exception('Exam not found');
    }

    // Calculate score
    int score = 0;
    int totalPossible = exam.totalPoints;
    Map<String, bool> questionResults = {};
    Map<String, String> feedback = {};

    for (var question in exam.questions) {
      final answer = attempt.answers[question.id];
      bool isCorrect = false;

      if (answer != null) {
        switch (question.type) {
          case QuestionType.multipleChoice:
            isCorrect = question.isMultipleChoiceAnswerCorrect(
              answer as List<int>,
            );
            break;
          case QuestionType.trueFalse:
            isCorrect = question.isTrueFalseAnswerCorrect(answer as bool);
            break;
          case QuestionType.shortAnswer:
            isCorrect = question.isShortAnswerCorrect(answer as String);
            break;
          case QuestionType.essay:
            // Essay questions need manual grading
            isCorrect = false;
            break;
        }

        if (isCorrect) {
          score += question.points;
        }
      }

      questionResults[question.id] = isCorrect;
      feedback[question.id] = question.explanation ?? '';
    }

    // Update attempt
    final now = DateTime.now();
    _examAttempts[index] = ExamAttemptModel(
      id: attempt.id,
      examId: attempt.examId,
      userId: attempt.userId,
      startTime: attempt.startTime,
      endTime: now,
      answers: attempt.answers,
      score: score,
      totalPossible: totalPossible,
      isCompleted: true,
    );

    // Create and return result
    return ExamResultModel(
      attemptId: attempt.id,
      examId: exam.id,
      examTitle: exam.title,
      score: score,
      totalPossible: totalPossible,
      percentage: totalPossible > 0 ? (score / totalPossible) * 100 : 0,
      timeTakenMinutes: now.difference(attempt.startTime).inMinutes,
      completedAt: now,
      questionResults: questionResults,
      feedback: feedback,
    );
  }
}
