import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';

/// Demo showcasing the clean Material Design implementation
class CleanMaterialDesignDemo extends StatelessWidget {
  const CleanMaterialDesignDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Clean Material Design'),
        backgroundColor: AppTheme.backgroundSurface,
        foregroundColor: AppTheme.textPrimaryLight,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'CLEAN MATERIAL DESIGN REFACTOR',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Professional, accessible, and visually clean interface',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryLight,
              ),
            ),
            const SizedBox(height: 32),

            // Before/After Comparison
            _buildBeforeAfterSection(),
            const SizedBox(height: 32),

            // Clean Cards Demo
            _buildCleanCardsDemo(),
            const SizedBox(height: 32),

            // Button Styles Demo
            _buildButtonStylesDemo(),
            const SizedBox(height: 32),

            // Accessibility Features
            _buildAccessibilitySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildBeforeAfterSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppUI.materialCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BEFORE vs AFTER',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'BEFORE (Removed)',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.red[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem('❌ Colorful outer glows', Colors.red[100]!),
                    _buildFeatureItem('❌ Neon-style shadows', Colors.red[100]!),
                    _buildFeatureItem('❌ Excessive blur radius', Colors.red[100]!),
                    _buildFeatureItem('❌ Bright saturated colors', Colors.red[100]!),
                    _buildFeatureItem('❌ Visual noise', Colors.red[100]!),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AFTER (Implemented)',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildFeatureItem('✅ Soft gray shadows', Colors.green[100]!),
                    _buildFeatureItem('✅ Minimal elevation', Colors.green[100]!),
                    _buildFeatureItem('✅ Clean flat design', Colors.green[100]!),
                    _buildFeatureItem('✅ Neutral colors', Colors.green[100]!),
                    _buildFeatureItem('✅ Professional look', Colors.green[100]!),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String text, Color backgroundColor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: AppTheme.textPrimaryLight,
        ),
      ),
    );
  }

  Widget _buildCleanCardsDemo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'CLEAN CARD DESIGNS',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildCleanCard('Basic Card', 'materialCard', AppUI.materialCard),
            _buildCleanCard('Elevated Card', 'materialElevatedCard', AppUI.materialElevatedCard),
            _buildCleanCard('Bordered Card', 'materialBorderedCard', AppUI.materialBorderedCard),
            _buildCleanCard('Card Shadow', 'materialCardShadow', AppUI.materialCard),
          ],
        ),
      ],
    );
  }

  Widget _buildCleanCard(String title, String type, BoxDecoration decoration) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: decoration,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: AppTheme.primaryColor,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            type,
            style: TextStyle(
              fontSize: 10,
              color: AppTheme.textSecondaryLight,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildButtonStylesDemo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppUI.materialCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'CLEAN BUTTON STYLES',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: [
              ElevatedButton(
                onPressed: () {},
                style: AppUI.buttonStyle(),
                child: const Text('Primary Button'),
              ),
              OutlinedButton(
                onPressed: () {},
                style: AppUI.outlinedButtonStyle(),
                child: const Text('Outlined Button'),
              ),
              TextButton(
                onPressed: () {},
                style: AppUI.textButtonStyle(),
                child: const Text('Text Button'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAccessibilitySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppUI.materialCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ACCESSIBILITY & READABILITY',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          _buildAccessibilityItem('✅ WCAG contrast ratios met'),
          _buildAccessibilityItem('✅ Minimum 44px touch targets'),
          _buildAccessibilityItem('✅ Consistent 8-16px spacing'),
          _buildAccessibilityItem('✅ Reduced eye strain'),
          _buildAccessibilityItem('✅ Professional appearance'),
          _buildAccessibilityItem('✅ Clean visual hierarchy'),
        ],
      ),
    );
  }

  Widget _buildAccessibilityItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryLight,
          height: 1.4,
        ),
      ),
    );
  }
}
