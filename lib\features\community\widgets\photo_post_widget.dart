import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/post_model.dart';
import 'package:fit_4_force/features/community/models/comment_model.dart';
import 'package:fit_4_force/features/community/widgets/facebook_style_comment.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

/// Enhanced photo post widget with Facebook-style interactions
class PhotoPostWidget extends StatefulWidget {
  final PostModel post;
  final UserModel currentUser;
  final VoidCallback? onLike;
  final VoidCallback? onComment;
  final VoidCallback? onShare;
  final Function(String reaction)? onReaction;
  final List<CommentModel>? comments;

  const PhotoPostWidget({
    super.key,
    required this.post,
    required this.currentUser,
    this.onLike,
    this.onComment,
    this.onShare,
    this.onReaction,
    this.comments,
  });

  @override
  State<PhotoPostWidget> createState() => _PhotoPostWidgetState();
}

class _PhotoPostWidgetState extends State<PhotoPostWidget>
    with TickerProviderStateMixin {
  late PageController _imagePageController;
  int _currentImageIndex = 0;
  bool _showComments = false;
  bool _showReactionPicker = false;
  late AnimationController _reactionAnimationController;
  late Animation<double> _reactionAnimation;

  // Facebook-style reactions
  final List<Map<String, dynamic>> _reactions = [
    {'emoji': '👍', 'name': 'Like', 'color': Colors.blue},
    {'emoji': '❤️', 'name': 'Love', 'color': Colors.red},
    {'emoji': '😂', 'name': 'Haha', 'color': Colors.orange},
    {'emoji': '😮', 'name': 'Wow', 'color': Colors.orange},
    {'emoji': '😢', 'name': 'Sad', 'color': Colors.orange},
    {'emoji': '😡', 'name': 'Angry', 'color': Colors.red},
  ];

  @override
  void initState() {
    super.initState();
    _imagePageController = PageController();
    _reactionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _reactionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _reactionAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _imagePageController.dispose();
    _reactionAnimationController.dispose();
    super.dispose();
  }

  void _toggleReactionPicker() {
    setState(() => _showReactionPicker = !_showReactionPicker);
    if (_showReactionPicker) {
      _reactionAnimationController.forward();
    } else {
      _reactionAnimationController.reverse();
    }
  }

  void _addReaction(String reaction) {
    widget.onReaction?.call(reaction);
    setState(() => _showReactionPicker = false);
    _reactionAnimationController.reverse();
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  Color _getAgencyColor(String agency) {
    switch (agency.toLowerCase()) {
      case 'nigerian army':
        return Colors.green;
      case 'navy':
      case 'nigerian navy':
        return Colors.blue;
      case 'air force':
      case 'nigerian air force':
        return Colors.cyan;
      case 'police':
      case 'nigerian police':
        return Colors.indigo;
      case 'nscdc':
        return Colors.orange;
      case 'efcc':
        return Colors.purple;
      default:
        return AppTheme.primaryColor;
    }
  }

  Widget _buildImageCarousel() {
    if (widget.post.imageUrls.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 400,
      margin: const EdgeInsets.symmetric(vertical: 12),
      child: Stack(
        children: [
          PageView.builder(
            controller: _imagePageController,
            onPageChanged: (index) {
              setState(() => _currentImageIndex = index);
            },
            itemCount: widget.post.imageUrls.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  // Open image viewer
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => _ImageViewer(
                        images: widget.post.imageUrls,
                        initialIndex: index,
                      ),
                    ),
                  );
                },
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    image: DecorationImage(
                      image: FileImage(File(widget.post.imageUrls[index])),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Image indicator
          if (widget.post.imageUrls.length > 1)
            Positioned(
              top: 12,
              right: 12,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.6),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_currentImageIndex + 1}/${widget.post.imageUrls.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          
          // Navigation arrows for desktop/web
          if (widget.post.imageUrls.length > 1) ...[
            Positioned(
              left: 8,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_currentImageIndex > 0) {
                      _imagePageController.previousPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.chevron_left,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              right: 8,
              top: 0,
              bottom: 0,
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    if (_currentImageIndex < widget.post.imageUrls.length - 1) {
                      _imagePageController.nextPage(
                        duration: const Duration(milliseconds: 300),
                        curve: Curves.easeInOut,
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.chevron_right,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Like button with long press for reactions
          GestureDetector(
            onTap: widget.onLike,
            onLongPress: _toggleReactionPicker,
            child: Row(
              children: [
                Icon(
                  widget.post.isLikedByCurrentUser 
                      ? Icons.thumb_up 
                      : Icons.thumb_up_outlined,
                  color: widget.post.isLikedByCurrentUser 
                      ? Colors.blue 
                      : Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  'Like',
                  style: TextStyle(
                    color: widget.post.isLikedByCurrentUser 
                        ? Colors.blue 
                        : Colors.grey[600],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 24),
          
          // Comment button
          GestureDetector(
            onTap: () {
              setState(() => _showComments = !_showComments);
              widget.onComment?.call();
            },
            child: Row(
              children: [
                Icon(
                  Icons.comment_outlined,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  'Comment',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 24),
          
          // Share button
          GestureDetector(
            onTap: widget.onShare,
            child: Row(
              children: [
                Icon(
                  Icons.share_outlined,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  'Share',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          const Spacer(),
          
          // Premium badge for premium posts
          if (widget.post.isPremiumContent)
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 6,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.purple, Colors.blue],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'PREMIUM',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey[300],
                  backgroundImage: widget.post.userProfileImageUrl != null
                      ? NetworkImage(widget.post.userProfileImageUrl!)
                      : null,
                  child: widget.post.userProfileImageUrl == null
                      ? Text(
                          widget.post.userName[0].toUpperCase(),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            widget.post.userName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 15,
                            ),
                          ),
                          if (widget.post.isPremiumContent) ...[
                            const SizedBox(width: 4),
                            const Icon(
                              Icons.verified,
                              color: Colors.blue,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getAgencyColor(widget.post.agency).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              widget.post.agency,
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: _getAgencyColor(widget.post.agency),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatTimeAgo(widget.post.createdAt),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Show post options
                  },
                  icon: Icon(Icons.more_horiz, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Post content
          if (widget.post.title.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                widget.post.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          
          if (widget.post.content.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Text(
                widget.post.content,
                style: const TextStyle(fontSize: 14, height: 1.4),
              ),
            ),

          // Image carousel
          _buildImageCarousel(),

          // Like and comment counts
          if (widget.post.likesCount > 0 || widget.post.commentsCount > 0)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  if (widget.post.likesCount > 0) ...[
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.blue,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.thumb_up,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${widget.post.likesCount}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 13,
                          ),
                        ),
                      ],
                    ),
                  ],
                  const Spacer(),
                  if (widget.post.commentsCount > 0)
                    GestureDetector(
                      onTap: () => setState(() => _showComments = !_showComments),
                      child: Text(
                        '${widget.post.commentsCount} comment${widget.post.commentsCount > 1 ? 's' : ''}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 13,
                        ),
                      ),
                    ),
                ],
              ),
            ),

          const Divider(height: 1),

          // Action bar
          _buildActionBar(),

          // Reaction picker
          if (_showReactionPicker)
            AnimatedBuilder(
              animation: _reactionAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _reactionAnimation.value,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: _reactions.map((reaction) {
                        return GestureDetector(
                          onTap: () => _addReaction(reaction['name']),
                          child: Container(
                            padding: const EdgeInsets.all(8),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  reaction['emoji'],
                                  style: const TextStyle(fontSize: 24),
                                ),
                                Text(
                                  reaction['name'],
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: reaction['color'],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                );
              },
            ),

          // Comments section
          if (_showComments && widget.comments != null) ...[
            const Divider(height: 1),
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: widget.comments!.map((comment) {
                  return FacebookStyleComment(
                    comment: comment,
                    currentUser: widget.currentUser,
                    onLike: () {
                      // Handle comment like
                    },
                    onReaction: (reaction) {
                      // Handle comment reaction
                    },
                    onReplySubmit: (content, imagePaths) {
                      // Handle reply submission
                    },
                  );
                }).toList(),
              ),
            ),
          ],

          // Tags
          if (widget.post.tags.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Wrap(
                spacing: 6,
                runSpacing: 6,
                children: widget.post.tags.map((tag) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '#$tag',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }
}

/// Full screen image viewer
class _ImageViewer extends StatefulWidget {
  final List<String> images;
  final int initialIndex;

  const _ImageViewer({
    required this.images,
    required this.initialIndex,
  });

  @override
  State<_ImageViewer> createState() => _ImageViewerState();
}

class _ImageViewerState extends State<_ImageViewer> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        foregroundColor: Colors.white,
        title: Text('${_currentIndex + 1} of ${widget.images.length}'),
      ),
      body: PageView.builder(
        controller: _pageController,
        onPageChanged: (index) {
          setState(() => _currentIndex = index);
        },
        itemCount: widget.images.length,
        itemBuilder: (context, index) {
          return InteractiveViewer(
            child: Center(
              child: Image.file(
                File(widget.images[index]),
                fit: BoxFit.contain,
              ),
            ),
          );
        },
      ),
    );
  }
}
