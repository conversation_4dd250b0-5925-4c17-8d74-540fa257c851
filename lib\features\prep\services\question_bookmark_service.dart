import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/quiz_question_model.dart';

/// Service to manage bookmarked questions for later review
class QuestionBookmarkService {
  static const String _bookmarksKey = 'bookmarked_questions';
  static const String _bookmarkNotesKey = 'bookmark_notes';
  
  static final QuestionBookmarkService _instance = QuestionBookmarkService._internal();
  factory QuestionBookmarkService() => _instance;
  QuestionBookmarkService._internal();

  final List<VoidCallback> _listeners = [];
  Set<String> _bookmarkedQuestions = {};
  Map<String, String> _bookmarkNotes = {};

  /// Initialize the service and load bookmarks
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Load bookmarked question IDs
      final bookmarksJson = prefs.getString(_bookmarksKey);
      if (bookmarksJson != null) {
        final List<dynamic> bookmarksList = jsonDecode(bookmarksJson);
        _bookmarkedQuestions = Set<String>.from(bookmarksList);
      }
      
      // Load bookmark notes
      final notesJson = prefs.getString(_bookmarkNotesKey);
      if (notesJson != null) {
        final Map<String, dynamic> notesMap = jsonDecode(notesJson);
        _bookmarkNotes = Map<String, String>.from(notesMap);
      }
      
      debugPrint('✅ Bookmark service initialized with ${_bookmarkedQuestions.length} bookmarks');
    } catch (e) {
      debugPrint('❌ Error initializing bookmark service: $e');
    }
  }

  /// Check if a question is bookmarked
  bool isBookmarked(String questionId) {
    return _bookmarkedQuestions.contains(questionId);
  }

  /// Toggle bookmark status for a question
  Future<bool> toggleBookmark(String questionId, {String? note}) async {
    try {
      if (_bookmarkedQuestions.contains(questionId)) {
        // Remove bookmark
        _bookmarkedQuestions.remove(questionId);
        _bookmarkNotes.remove(questionId);
      } else {
        // Add bookmark
        _bookmarkedQuestions.add(questionId);
        if (note != null && note.isNotEmpty) {
          _bookmarkNotes[questionId] = note;
        }
      }
      
      await _saveBookmarks();
      _notifyListeners();
      return _bookmarkedQuestions.contains(questionId);
    } catch (e) {
      debugPrint('❌ Error toggling bookmark: $e');
      return false;
    }
  }

  /// Add a note to a bookmarked question
  Future<void> addNote(String questionId, String note) async {
    if (_bookmarkedQuestions.contains(questionId)) {
      _bookmarkNotes[questionId] = note;
      await _saveBookmarks();
      _notifyListeners();
    }
  }

  /// Get note for a bookmarked question
  String? getNote(String questionId) {
    return _bookmarkNotes[questionId];
  }

  /// Get all bookmarked question IDs
  Set<String> getBookmarkedQuestionIds() {
    return Set<String>.from(_bookmarkedQuestions);
  }

  /// Get count of bookmarked questions
  int get bookmarkCount => _bookmarkedQuestions.length;

  /// Clear all bookmarks
  Future<void> clearAllBookmarks() async {
    _bookmarkedQuestions.clear();
    _bookmarkNotes.clear();
    await _saveBookmarks();
    _notifyListeners();
  }

  /// Save bookmarks to persistent storage
  Future<void> _saveBookmarks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Save bookmarked question IDs
      final bookmarksJson = jsonEncode(_bookmarkedQuestions.toList());
      await prefs.setString(_bookmarksKey, bookmarksJson);
      
      // Save bookmark notes
      final notesJson = jsonEncode(_bookmarkNotes);
      await prefs.setString(_bookmarkNotesKey, notesJson);
    } catch (e) {
      debugPrint('❌ Error saving bookmarks: $e');
    }
  }

  /// Add listener for bookmark changes
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// Remove listener
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// Notify all listeners of changes
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// Get bookmarked questions with their details
  Future<List<BookmarkedQuestion>> getBookmarkedQuestionsWithDetails(
    List<QuizQuestionModel> allQuestions,
  ) async {
    final bookmarkedQuestions = <BookmarkedQuestion>[];
    
    for (final questionId in _bookmarkedQuestions) {
      final question = allQuestions.firstWhere(
        (q) => q.id == questionId,
        orElse: () => throw Exception('Question not found'),
      );
      
      bookmarkedQuestions.add(
        BookmarkedQuestion(
          question: question,
          note: _bookmarkNotes[questionId],
          bookmarkedAt: DateTime.now(), // In production, store actual timestamp
        ),
      );
    }
    
    // Sort by bookmark date (most recent first)
    bookmarkedQuestions.sort((a, b) => b.bookmarkedAt.compareTo(a.bookmarkedAt));
    
    return bookmarkedQuestions;
  }

  /// Export bookmarks as JSON for backup
  Future<String> exportBookmarks() async {
    return jsonEncode({
      'bookmarks': _bookmarkedQuestions.toList(),
      'notes': _bookmarkNotes,
      'exportedAt': DateTime.now().toIso8601String(),
    });
  }

  /// Import bookmarks from JSON backup
  Future<bool> importBookmarks(String jsonData) async {
    try {
      final data = jsonDecode(jsonData);
      _bookmarkedQuestions = Set<String>.from(data['bookmarks'] ?? []);
      _bookmarkNotes = Map<String, String>.from(data['notes'] ?? {});
      
      await _saveBookmarks();
      _notifyListeners();
      return true;
    } catch (e) {
      debugPrint('❌ Error importing bookmarks: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _listeners.clear();
  }
}

/// Model for bookmarked question with additional metadata
class BookmarkedQuestion {
  final QuizQuestionModel question;
  final String? note;
  final DateTime bookmarkedAt;

  const BookmarkedQuestion({
    required this.question,
    this.note,
    required this.bookmarkedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'question': question.toMap(),
      'note': note,
      'bookmarkedAt': bookmarkedAt.toIso8601String(),
    };
  }

  factory BookmarkedQuestion.fromMap(Map<String, dynamic> map) {
    return BookmarkedQuestion(
      question: QuizQuestionModel.fromMap(map['question']),
      note: map['note'],
      bookmarkedAt: DateTime.parse(map['bookmarkedAt']),
    );
  }
}
