import 'package:flutter/material.dart';

/// Study Tip Category Enum
enum StudyTipCategory {
  timeManagement,
  memoryTechniques,
  examStrategy,
  stressManagement,
  motivation,
  healthWellness,
  studyEnvironment,
  noteTaking,
  practiceTests,
  lastMinutePrep,
}

/// Study Tip Model
class StudyTipModel {
  final String id;
  final String title;
  final String subtitle;
  final String content;
  final StudyTipCategory category;
  final IconData icon;
  final Color color;
  final List<String> keyPoints;
  final List<StudyTipAction> actions;
  final int readingTimeMinutes;
  final String difficulty;
  final List<String> tags;
  final DateTime createdAt;
  final bool isPremium;

  StudyTipModel({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.content,
    required this.category,
    required this.icon,
    required this.color,
    required this.keyPoints,
    required this.actions,
    required this.readingTimeMinutes,
    required this.difficulty,
    required this.tags,
    required this.createdAt,
    this.isPremium = false,
  });

  /// Get category display name
  String get categoryName {
    switch (category) {
      case StudyTipCategory.timeManagement:
        return 'Time Management';
      case StudyTipCategory.memoryTechniques:
        return 'Memory Techniques';
      case StudyTipCategory.examStrategy:
        return 'Exam Strategy';
      case StudyTipCategory.stressManagement:
        return 'Stress Management';
      case StudyTipCategory.motivation:
        return 'Motivation';
      case StudyTipCategory.healthWellness:
        return 'Health & Wellness';
      case StudyTipCategory.studyEnvironment:
        return 'Study Environment';
      case StudyTipCategory.noteTaking:
        return 'Note Taking';
      case StudyTipCategory.practiceTests:
        return 'Practice Tests';
      case StudyTipCategory.lastMinutePrep:
        return 'Last Minute Prep';
    }
  }

  /// Get difficulty color
  Color get difficultyColor {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// Study Tip Action Model
class StudyTipAction {
  final String title;
  final String description;
  final IconData icon;
  final String actionType; // 'timer', 'checklist', 'link', 'practice'

  StudyTipAction({
    required this.title,
    required this.description,
    required this.icon,
    required this.actionType,
  });
}

/// Study Progress Model
class StudyProgressModel {
  final String userId;
  final String tipId;
  final bool isRead;
  final bool isBookmarked;
  final bool isCompleted;
  final DateTime? readAt;
  final DateTime? completedAt;
  final int timeSpentMinutes;
  final Map<String, bool> actionProgress;

  StudyProgressModel({
    required this.userId,
    required this.tipId,
    this.isRead = false,
    this.isBookmarked = false,
    this.isCompleted = false,
    this.readAt,
    this.completedAt,
    this.timeSpentMinutes = 0,
    this.actionProgress = const {},
  });
}
