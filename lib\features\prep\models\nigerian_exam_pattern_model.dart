
/// Nigerian Military Recruitment Exam Pattern Model
/// Mirrors actual recruitment exam formats used by Nigerian military agencies
class NigerianExamPatternModel {
  final String agencyCode;
  final String examName;
  final int totalQuestions;
  final int timeLimit; // in minutes
  final int passingScore;
  final List<ExamSection> sections;
  final ExamFormat format;
  final List<String> instructions;
  final Map<String, dynamic> scoringPattern;

  const NigerianExamPatternModel({
    required this.agencyCode,
    required this.examName,
    required this.totalQuestions,
    required this.timeLimit,
    required this.passingScore,
    required this.sections,
    required this.format,
    required this.instructions,
    required this.scoringPattern,
  });

  factory NigerianExamPatternModel.fromJson(Map<String, dynamic> json) {
    return NigerianExamPatternModel(
      agencyCode: json['agency_code'],
      examName: json['exam_name'],
      totalQuestions: json['total_questions'],
      timeLimit: json['time_limit'],
      passingScore: json['passing_score'],
      sections: (json['sections'] as List)
          .map((section) => ExamSection.fromJson(section))
          .toList(),
      format: ExamFormat.values.firstWhere(
        (format) => format.name == json['format'],
        orElse: () => ExamFormat.multipleChoice,
      ),
      instructions: List<String>.from(json['instructions']),
      scoringPattern: json['scoring_pattern'],
    );
  }
}

/// Exam section representing different subjects in Nigerian military exams
class ExamSection {
  final String name;
  final int questionCount;
  final int timeAllocation; // in minutes
  final double weightage; // percentage of total score
  final List<String> topics;
  final DifficultyLevel difficulty;

  const ExamSection({
    required this.name,
    required this.questionCount,
    required this.timeAllocation,
    required this.weightage,
    required this.topics,
    required this.difficulty,
  });

  factory ExamSection.fromJson(Map<String, dynamic> json) {
    return ExamSection(
      name: json['name'],
      questionCount: json['question_count'],
      timeAllocation: json['time_allocation'],
      weightage: json['weightage'].toDouble(),
      topics: List<String>.from(json['topics']),
      difficulty: DifficultyLevel.values.firstWhere(
        (level) => level.name == json['difficulty'],
        orElse: () => DifficultyLevel.intermediate,
      ),
    );
  }
}

/// Nigerian-specific question model with local context
class NigerianQuestionModel {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswer;
  final String explanation;
  final String category;
  final DifficultyLevel difficulty;
  final List<String> tags;
  final String agencyCode;
  final NigerianContext context;
  final double timeToAnswer; // in seconds
  final Map<String, dynamic> analytics;

  const NigerianQuestionModel({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswer,
    required this.explanation,
    required this.category,
    required this.difficulty,
    required this.tags,
    required this.agencyCode,
    required this.context,
    required this.timeToAnswer,
    required this.analytics,
  });

  factory NigerianQuestionModel.fromJson(Map<String, dynamic> json) {
    return NigerianQuestionModel(
      id: json['id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswer: json['correct_answer'],
      explanation: json['explanation'],
      category: json['category'],
      difficulty: DifficultyLevel.values.firstWhere(
        (level) => level.name == json['difficulty'],
        orElse: () => DifficultyLevel.intermediate,
      ),
      tags: List<String>.from(json['tags']),
      agencyCode: json['agency_code'],
      context: NigerianContext.fromJson(json['context']),
      timeToAnswer: json['time_to_answer'].toDouble(),
      analytics: json['analytics'] ?? {},
    );
  }
}

/// Nigerian context for questions (local examples, case studies, regional content)
class NigerianContext {
  final String region; // North, South, East, West, Central
  final List<String> states; // Relevant Nigerian states
  final String caseStudyType; // historical, current, geographical, etc.
  final Map<String, String> localReferences; // Nigerian-specific references
  final bool isRegionalSpecific;

  const NigerianContext({
    required this.region,
    required this.states,
    required this.caseStudyType,
    required this.localReferences,
    required this.isRegionalSpecific,
  });

  factory NigerianContext.fromJson(Map<String, dynamic> json) {
    return NigerianContext(
      region: json['region'],
      states: List<String>.from(json['states']),
      caseStudyType: json['case_study_type'],
      localReferences: Map<String, String>.from(json['local_references']),
      isRegionalSpecific: json['is_regional_specific'],
    );
  }
}

/// Exam formats used by Nigerian military agencies
enum ExamFormat {
  multipleChoice,
  computerBasedTest,
  paperBased,
  hybrid,
  oral,
  practical,
}

/// Difficulty levels for questions
enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
  expert,
}

/// Quiz modes for different practice types
enum QuizMode {
  practice, // Untimed, with immediate feedback
  timed, // Simulates actual exam conditions
  adaptive, // Adjusts difficulty based on performance
  review, // Review previously answered questions
  challenge, // Competitive mode
}

/// Performance analytics for topic-based tracking
class TopicPerformanceModel {
  final String topicName;
  final String category;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final double averageTime;
  final double masteryLevel; // 0.0 to 1.0
  final List<String> weakAreas;
  final List<String> strongAreas;
  final DateTime lastPracticed;
  final int practiceCount;

  const TopicPerformanceModel({
    required this.topicName,
    required this.category,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.averageTime,
    required this.masteryLevel,
    required this.weakAreas,
    required this.strongAreas,
    required this.lastPracticed,
    required this.practiceCount,
  });

  double get accuracy => totalQuestions > 0 ? correctAnswers / totalQuestions : 0.0;
  
  bool get needsReview => masteryLevel < 0.7 || 
    DateTime.now().difference(lastPracticed).inDays > 7;
}

/// Adaptive questioning system
class AdaptiveQuestioningModel {
  final String userId;
  final String agencyCode;
  final Map<String, double> topicMastery;
  final Map<DifficultyLevel, double> difficultyPerformance;
  final int consecutiveCorrect;
  final int consecutiveIncorrect;
  final DifficultyLevel currentLevel;
  final List<String> recentTopics;

  const AdaptiveQuestioningModel({
    required this.userId,
    required this.agencyCode,
    required this.topicMastery,
    required this.difficultyPerformance,
    required this.consecutiveCorrect,
    required this.consecutiveIncorrect,
    required this.currentLevel,
    required this.recentTopics,
  });

  /// Determine next question difficulty based on performance
  DifficultyLevel getNextDifficulty() {
    if (consecutiveCorrect >= 3 && currentLevel != DifficultyLevel.expert) {
      // Increase difficulty
      switch (currentLevel) {
        case DifficultyLevel.beginner:
          return DifficultyLevel.intermediate;
        case DifficultyLevel.intermediate:
          return DifficultyLevel.advanced;
        case DifficultyLevel.advanced:
          return DifficultyLevel.expert;
        case DifficultyLevel.expert:
          return DifficultyLevel.expert;
      }
    } else if (consecutiveIncorrect >= 2 && currentLevel != DifficultyLevel.beginner) {
      // Decrease difficulty
      switch (currentLevel) {
        case DifficultyLevel.expert:
          return DifficultyLevel.advanced;
        case DifficultyLevel.advanced:
          return DifficultyLevel.intermediate;
        case DifficultyLevel.intermediate:
          return DifficultyLevel.beginner;
        case DifficultyLevel.beginner:
          return DifficultyLevel.beginner;
      }
    }
    return currentLevel;
  }

  /// Get topics that need more practice
  List<String> getWeakTopics() {
    return topicMastery.entries
        .where((entry) => entry.value < 0.7)
        .map((entry) => entry.key)
        .toList();
  }
}
