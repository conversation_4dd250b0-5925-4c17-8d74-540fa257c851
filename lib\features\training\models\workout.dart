class Exercise {
  final String id;
  final String name;
  final String description;
  final int sets;
  final int reps; // Can be time in seconds for timed exercises
  final bool isTimeBased;
  final String? imageUrl;
  final String? videoUrl;
  final String difficulty; // 'Beginner', 'Intermediate', 'Advanced'
  final List<String> targetMuscles;
  final String? instructions;

  Exercise({
    required this.id,
    required this.name,
    required this.description,
    required this.sets,
    required this.reps,
    this.isTimeBased = false,
    this.imageUrl,
    this.videoUrl,
    required this.difficulty,
    required this.targetMuscles,
    this.instructions,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'sets': sets,
    'reps': reps,
    'isTimeBased': isTimeBased,
    'imageUrl': imageUrl,
    'videoUrl': videoUrl,
    'difficulty': difficulty,
    'targetMuscles': targetMuscles,
    'instructions': instructions,
  };

  // Create from JSON
  factory Exercise.fromJson(Map<String, dynamic> json) => Exercise(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    sets: json['sets'],
    reps: json['reps'],
    isTimeBased: json['isTimeBased'] ?? false,
    imageUrl: json['imageUrl'],
    videoUrl: json['videoUrl'],
    difficulty: json['difficulty'],
    targetMuscles: List<String>.from(json['targetMuscles']),
    instructions: json['instructions'],
  );
}

class WorkoutDay {
  final String id;
  final String title;
  final String description;
  final List<Exercise> exercises;
  final String focusArea; // 'Cardio', 'Strength', 'Endurance', etc.
  final String difficulty; // 'Beginner', 'Intermediate', 'Advanced'
  final int estimatedDurationMinutes;
  final String? imageUrl;
  final String? videoUrl;
  final bool requiresEquipment;

  WorkoutDay({
    required this.id,
    required this.title,
    required this.description,
    required this.exercises,
    required this.focusArea,
    required this.difficulty,
    required this.estimatedDurationMinutes,
    this.imageUrl,
    this.videoUrl,
    this.requiresEquipment = false,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'exercises': exercises.map((e) => e.toJson()).toList(),
    'focusArea': focusArea,
    'difficulty': difficulty,
    'estimatedDurationMinutes': estimatedDurationMinutes,
    'imageUrl': imageUrl,
    'videoUrl': videoUrl,
    'requiresEquipment': requiresEquipment,
  };

  // Create from JSON
  factory WorkoutDay.fromJson(Map<String, dynamic> json) => WorkoutDay(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    exercises:
        (json['exercises'] as List).map((e) => Exercise.fromJson(e)).toList(),
    focusArea: json['focusArea'],
    difficulty: json['difficulty'],
    estimatedDurationMinutes: json['estimatedDurationMinutes'],
    imageUrl: json['imageUrl'],
    videoUrl: json['videoUrl'],
    requiresEquipment: json['requiresEquipment'] ?? false,
  );
}

class TrainingPlan {
  final String id;
  final String userId;
  final String title;
  final String description;
  final List<WorkoutDay> workouts;
  final DateTime createdAt;
  final DateTime startDate;
  final DateTime endDate;
  final String difficulty;
  final String militaryAgency;
  final bool isCompleted;
  final Map<String, bool> completedWorkouts; // workoutId -> completed

  TrainingPlan({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.workouts,
    required this.createdAt,
    required this.startDate,
    required this.endDate,
    required this.difficulty,
    required this.militaryAgency,
    this.isCompleted = false,
    required this.completedWorkouts,
  });

  // Get completion percentage
  double getCompletionPercentage() {
    if (workouts.isEmpty) return 0;
    final completed = completedWorkouts.values.where((v) => v).length;
    return (completed / workouts.length) * 100;
  }

  // Convert to JSON for storage
  Map<String, dynamic> toJson() => {
    'id': id,
    'userId': userId,
    'title': title,
    'description': description,
    'workouts': workouts.map((w) => w.toJson()).toList(),
    'createdAt': createdAt.toIso8601String(),
    'startDate': startDate.toIso8601String(),
    'endDate': endDate.toIso8601String(),
    'difficulty': difficulty,
    'militaryAgency': militaryAgency,
    'isCompleted': isCompleted,
    'completedWorkouts': completedWorkouts,
  };

  // Create from JSON
  factory TrainingPlan.fromJson(Map<String, dynamic> json) => TrainingPlan(
    id: json['id'],
    userId: json['userId'],
    title: json['title'],
    description: json['description'],
    workouts:
        (json['workouts'] as List).map((w) => WorkoutDay.fromJson(w)).toList(),
    createdAt: DateTime.parse(json['createdAt']),
    startDate: DateTime.parse(json['startDate']),
    endDate: DateTime.parse(json['endDate']),
    difficulty: json['difficulty'],
    militaryAgency: json['militaryAgency'],
    isCompleted: json['isCompleted'] ?? false,
    completedWorkouts: Map<String, bool>.from(json['completedWorkouts']),
  );
}
