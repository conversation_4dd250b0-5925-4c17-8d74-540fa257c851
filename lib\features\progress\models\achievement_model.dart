class Achievement {
  final String id;
  final String title;
  final String description;
  final String category;
  final String iconUrl;
  final int points;
  final DateTime earnedDate;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.iconUrl,
    required this.points,
    required this.earnedDate,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'category': category,
    'iconUrl': iconUrl,
    'points': points,
    'earnedDate': earnedDate.toIso8601String(),
  };

  factory Achievement.fromJson(Map<String, dynamic> json) => Achievement(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    category: json['category'],
    iconUrl: json['iconUrl'],
    points: json['points'],
    earnedDate: DateTime.parse(json['earnedDate']),
  );
}
