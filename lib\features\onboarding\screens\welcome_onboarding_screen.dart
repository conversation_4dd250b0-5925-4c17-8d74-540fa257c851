import 'package:flutter/material.dart';
import '../../../core/utils/responsive_utils.dart';
import '../widgets/onboarding_page.dart';
import '../models/onboarding_page_model.dart';
import '../services/onboarding_service.dart';

class WelcomeOnboardingScreen extends StatefulWidget {
  const WelcomeOnboardingScreen({super.key});

  @override
  State<WelcomeOnboardingScreen> createState() =>
      _WelcomeOnboardingScreenState();
}

class _WelcomeOnboardingScreenState extends State<WelcomeOnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _logoController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _logoAnimation;

  int _currentPage = 0;

  final List<OnboardingPageModel> _pages = [
    OnboardingPageModel(
      title: "🎯 Welcome to Fit4Force",
      subtitle: "Your Journey to Service Starts Here!",
      description:
          "Whether you're preparing for the military, paramilitary, or simply passionate about fitness and discipline — <PERSON>t4<PERSON><PERSON><PERSON> is your all-in-one companion to get ahead.",
      imagePath: "",
      backgroundColor: const Color(0xFF1E3A8A), // Deep blue
      accentColor: const Color(0xFFFFFFFF), // White
    ),
    OnboardingPageModel(
      title: "📚 All-In-One Prep Center",
      subtitle: "Train Smarter. Learn Faster.",
      description:
          "Access mock tests, quizzes, study materials, and up-to-date recruitment tips — tailored for the Army, Navy, Police, Civil Defence, Immigration, and more.",
      imagePath: "assets/images/military_training.png",
      backgroundColor: const Color(0xFF2563EB), // Bright blue
      accentColor: const Color(0xFFFFFFFF), // White
      bulletPoints: [
        "📝 Mock Tests & Quizzes",
        "📖 Study Materials",
        "💡 Recruitment Tips",
        "🎯 Agency-Specific Content",
      ],
    ),
    OnboardingPageModel(
      title: "💪 Fitness That Follows Orders",
      subtitle: "Military-Style Workouts At Home!",
      description:
          "Train like a recruit with guided home workouts, routines, challenges, and tracking tools to help you stay strong, fit, and test-ready.",
      imagePath: "assets/images/ai_learning.png",
      backgroundColor: const Color(0xFF1D4ED8), // Medium blue
      accentColor: const Color(0xFFFFFFFF), // White
      bulletPoints: [
        "🏋️ Guided Home Workouts",
        "📈 Progress Tracking",
        "🏃‍♂️ Military-Style Routines",
        "💯 Fitness Challenges",
      ],
    ),
    OnboardingPageModel(
      title: "🫡 Join the Community, Stay Ahead",
      subtitle: "You're Never Alone in This Mission.",
      description:
          "Connect with fellow aspirants, ask questions, share progress, and stay motivated with our community forum — built just for people like you.",
      imagePath: "assets/images/success_badge.png",
      backgroundColor: const Color(0xFF3B82F6), // Light blue
      accentColor: const Color(0xFFFFFFFF), // White
      bulletPoints: [
        "👥 Connect with Aspirants",
        "❓ Ask Questions & Get Help",
        "📊 Share Your Progress",
        "💪 Stay Motivated Together",
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();

    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000), // Smooth animations
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200), // Enhanced slide
      vsync: this,
    );

    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500), // Logo emphasis
      vsync: this,
    );

    // Initialize animations with enhanced curves
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOutCubic),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutQuart),
    );

    _logoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    // Start initial animations
    _startPageAnimations();
  }

  void _startPageAnimations() {
    _fadeController.forward();
    _slideController.forward();
    if (_currentPage == 0) {
      _logoController.forward();
    }
  }

  void _resetAnimations() {
    _fadeController.reset();
    _slideController.reset();
    if (_currentPage == 0) {
      _logoController.reset();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _logoController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400), // Smooth transition
        curve: Curves.easeInOutCubic,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() async {
    await OnboardingService.skipOnboarding();
    _navigateToSignup();
  }

  Future<void> _completeOnboarding() async {
    await OnboardingService.completeOnboarding();
    _navigateToSignup();
  }

  void _navigateToSignup() {
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/signup');
    }
  }

  void _onPageChanged(int page) {
    setState(() {
      _currentPage = page;
    });

    // Reset and restart animations for new page
    _resetAnimations();
    Future.delayed(const Duration(milliseconds: 100), () {
      _startPageAnimations();
    });
  }

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _pages[_currentPage].backgroundColor,
              _pages[_currentPage].backgroundColor.withOpacity(0.8),
              _pages[_currentPage].accentColor.withOpacity(0.6),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Top section with page indicators and skip button
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(width: 60), // Spacer
                    // Enhanced page indicators
                    Row(
                      children: List.generate(
                        _pages.length,
                        (index) => AnimatedContainer(
                          duration: const Duration(milliseconds: 400),
                          margin: const EdgeInsets.symmetric(horizontal: 4),
                          width: _currentPage == index ? 32 : 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color:
                                _currentPage == index
                                    ? Colors.white
                                    : Colors.white.withOpacity(0.4),
                            borderRadius: BorderRadius.circular(6),
                            boxShadow:
                                _currentPage == index
                                    ? [
                                      BoxShadow(
                                        color: Colors.white.withOpacity(0.3),
                                        blurRadius: 8,
                                        spreadRadius: 2,
                                      ),
                                    ]
                                    : null,
                          ),
                        ),
                      ),
                    ),
                    // Skip button with enhanced styling
                    TextButton(
                      onPressed: _skipOnboarding,
                      style: TextButton.styleFrom(
                        backgroundColor: Colors.white.withOpacity(0.2),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      child: const Text(
                        'Skip',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // PageView content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  itemCount: _pages.length,
                  itemBuilder: (context, index) {
                    return OnboardingPage(
                      page: _pages[index],
                      fadeAnimation: _fadeAnimation,
                      slideAnimation: _slideAnimation,
                      logoAnimation: _logoAnimation,
                      isFirstPage: index == 0,
                      isSmallScreen: isSmallScreen,
                    );
                  },
                ),
              ),

              // Bottom navigation with enhanced styling
              Padding(
                padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Previous button (invisible on first page)
                    SizedBox(
                      width: 80,
                      child:
                          _currentPage > 0
                              ? TextButton(
                                onPressed: () {
                                  _pageController.previousPage(
                                    duration: const Duration(milliseconds: 400),
                                    curve: Curves.easeInOutCubic,
                                  );
                                },
                                style: TextButton.styleFrom(
                                  backgroundColor: Colors.white.withOpacity(
                                    0.2,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                child: const Text(
                                  'Back',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              )
                              : null,
                    ),

                    // Next/Get Started button with enhanced design
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 400),
                      child: ElevatedButton(
                        onPressed: _nextPage,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: _pages[_currentPage].backgroundColor,
                          padding: EdgeInsets.symmetric(
                            horizontal: isSmallScreen ? 32 : 40,
                            vertical: isSmallScreen ? 16 : 20,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                          elevation: 12,
                          shadowColor: Colors.black.withOpacity(0.3),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _currentPage == _pages.length - 1
                                  ? 'Get Started'
                                  : 'Next',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 18 : 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Icon(
                              _currentPage == _pages.length - 1
                                  ? Icons.rocket_launch
                                  : Icons.arrow_forward,
                              size: isSmallScreen ? 20 : 22,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
