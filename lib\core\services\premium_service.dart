import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:logger/logger.dart';

/// Centralized service for managing premium access across the app
/// Ensures consistent premium feature enforcement
class PremiumService {
  static final PremiumService _instance = PremiumService._internal();
  factory PremiumService() => _instance;
  PremiumService._internal();

  final Logger _logger = Logger();

  /// Check if user has access to premium features
  /// Includes both premium users and test/admin users
  bool hasAccessToPremiumFeatures(UserModel user) {
    final hasAccess = user.hasAccessToPremiumFeatures;
    _logger.d(
      'Premium access check for ${user.fullName}: $hasAccess (isPremium: ${user.isPremium}, isTestUser: ${user.isTestUser})',
    );
    return hasAccess;
  }

  /// Check if user can access fitness features
  bool canAccessFitness(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access 30-day challenge
  bool canAccess30DayChallenge(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access advanced analytics
  bool canAccessAdvancedAnalytics(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access unlimited quiz questions
  bool canAccessUnlimitedQuizzes(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access AI-powered explanations
  bool canAccessAIExplanations(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access premium study materials
  bool canAccessPremiumStudyMaterials(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can create posts in community
  bool canCreateCommunityPosts(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can create photo posts
  bool canCreatePhotoPosts(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can upload multiple images
  bool canUploadMultipleImages(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Get maximum images allowed per post
  int getMaxImagesPerPost(UserModel user) {
    return hasAccessToPremiumFeatures(user) ? 10 : 1;
  }

  /// Check if user can access nutrition plans
  bool canAccessNutritionPlans(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access recovery tools
  bool canAccessRecoveryTools(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access offline content
  bool canAccessOfflineContent(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Check if user can access priority support
  bool canAccessPrioritySupport(UserModel user) {
    return hasAccessToPremiumFeatures(user);
  }

  /// Get the maximum number of quiz questions for user
  int getMaxQuizQuestions(UserModel user) {
    return hasAccessToPremiumFeatures(user) ? -1 : 5; // -1 = unlimited
  }

  /// Get the maximum workout session duration for user (in minutes)
  int getMaxWorkoutDuration(UserModel user) {
    return hasAccessToPremiumFeatures(user) ? 90 : 30;
  }

  /// Get the number of study materials user can access per day
  int getDailyStudyMaterialLimit(UserModel user) {
    return hasAccessToPremiumFeatures(user) ? -1 : 3; // -1 = unlimited
  }

  /// Check if content requires premium access
  bool contentRequiresPremium(Map<String, dynamic>? contentMeta) {
    if (contentMeta == null) return false;
    return contentMeta['isPremium'] == true ||
        contentMeta['isPremiumOnly'] == true ||
        contentMeta['requiresPremium'] == true;
  }

  /// Get premium feature names that user doesn't have access to
  List<String> getMissingPremiumFeatures(UserModel user) {
    if (hasAccessToPremiumFeatures(user)) return [];

    return [
      'Complete Fitness Suite',
      '30-Day Workout Challenge',
      'Personalized Nutrition Plans',
      'Recovery & Wellness Tools',
      'Advanced Analytics',
      'Unlimited Study Materials',
      'AI-Powered Explanations',
      'Priority Community Support',
      'Offline Access',
      'Unlimited Quiz Questions',
    ];
  }

  /// Get premium status display text
  String getPremiumStatusText(UserModel user) {
    if (user.isTestUser) return 'Test User (Full Access)';
    if (user.isPremium) return 'Premium Member';
    return 'Free User';
  }

  /// Get premium status color
  static const premiumColor = Color(0xFFFFD700); // Gold color
  static const freeColor = Color(0xFF9E9E9E); // Grey color
  static const testUserColor = Color(0xFF4CAF50); // Green color

  Color getPremiumStatusColor(UserModel user) {
    if (user.isTestUser) return testUserColor;
    if (user.isPremium) return premiumColor;
    return freeColor;
  }

  /// Log premium access attempt for analytics
  void logPremiumAccessAttempt(
    UserModel user,
    String featureName,
    bool granted,
  ) {
    _logger.i(
      'Premium access attempt: Feature="$featureName", User="${user.fullName}", Granted=$granted, UserType="${getPremiumStatusText(user)}"',
    );
  }
}
