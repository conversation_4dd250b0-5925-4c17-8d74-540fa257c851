/// Configuration for email services
class EmailConfig {
  // EmailJS configuration (free email service for development/testing)
  static const String emailJsServiceId =
      'service_5k8r3xb'; // Your EmailJS service ID
  static const String emailJsPublicKey =
      's17mZY4KMOXnCqHYF'; // Your EmailJS public key

  // Email templates IDs (create these templates in EmailJS dashboard)
  static const String welcomeTemplateId = 'template_welcome';
  static const String premiumUpgradeTemplateId = 'template_premium_upgrade';
  static const String notificationTemplateId = 'template_notification';

  // Email addresses
  static const String supportEmail = '<EMAIL>';
  static const String premiumSupportEmail = '<EMAIL>';
  static const String noReplyEmail = '<EMAIL>';

  // App information
  static const String appName = 'Fit4Force';
  static const String appUrl = 'https://fit4force.com';
  static const String loginUrl = 'https://fit4force.com/login';

  // Email features toggle (useful for development)
  static const bool enableWelcomeEmail = true;
  static const bool enablePremiumUpgradeEmail = true;
  static const bool enableNotificationEmails = true;

  // For production, you might want to use environment variables
  static String get apiBaseUrl => const String.fromEnvironment(
    'EMAIL_API_URL',
    defaultValue: 'https://api.emailjs.com/api/v1.0/email/send',
  );
}

/// Email service setup instructions
class EmailServiceSetup {
  static const String setupInstructions = '''
  Email Service Setup Instructions:
  
  1. Create an EmailJS account at https://emailjs.com
  2. Create a new service and get your service ID
  3. Create email templates with the following IDs:
     - template_welcome: For welcome emails
     - template_premium_upgrade: For premium upgrade confirmations
     - template_notification: For general notifications
  
  4. Update the configuration in EmailConfig:
     - emailJsServiceId: Your EmailJS service ID
     - emailJsPublicKey: Your EmailJS public key
  
  5. Alternative email services for production:
     - SendGrid: https://sendgrid.com
     - AWS SES: https://aws.amazon.com/ses/
     - Mailgun: https://www.mailgun.com
     - Postmark: https://postmarkapp.com
  
  Email Templates Variables:
  
  Welcome Email Template Variables:
  - {{user_name}}: User's full name
  - {{user_email}}: User's email address
  - {{app_name}}: Application name
  - {{welcome_message}}: Welcome message text
  - {{features_list}}: List of basic features
  - {{premium_benefits}}: Premium upgrade benefits
  
  Premium Upgrade Email Template Variables:
  - {{user_name}}: User's full name
  - {{user_email}}: User's email address
  - {{app_name}}: Application name
  - {{upgrade_message}}: Upgrade confirmation message
  - {{subscription_type}}: Monthly or Yearly Premium
  - {{expiry_date}}: Subscription expiry date
  - {{transaction_reference}}: Payment transaction reference
  - {{premium_features}}: List of premium features
  - {{support_email}}: Support email address
  - {{login_link}}: Login URL
  ''';
}
