import 'package:get_it/get_it.dart';
import 'package:fit_4_force/core/services/supabase_auth_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart'
    as shared;
import 'package:fit_4_force/core/services/user_storage_service.dart';
import 'package:fit_4_force/core/services/notification_service.dart';
import 'package:fit_4_force/core/services/realtime_service.dart';
import 'package:fit_4_force/core/services/error_handling_service.dart';
import 'package:fit_4_force/core/services/offline_service.dart';
import 'package:fit_4_force/core/auth/enhanced_auth_service.dart';
import 'package:fit_4_force/core/services/file_upload_service.dart';
import 'package:fit_4_force/core/services/user_rating_service.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';
import 'package:fit_4_force/features/settings/services/settings_service.dart';

/// Service locator for dependency injection
final GetIt getIt = GetIt.instance;

/// Setup basic services for fast startup (critical services only)
Future<void> setupBasicServiceLocator() async {
  // Only register the most essential services for startup
  if (!getIt.isRegistered<ErrorHandlingService>()) {
    getIt.registerLazySingleton<ErrorHandlingService>(
      () => ErrorHandlingService(),
    );
  }

  if (!getIt.isRegistered<SupabaseAuthService>()) {
    getIt.registerLazySingleton<SupabaseAuthService>(
      () => SupabaseAuthService(),
    );
  }

  if (!getIt.isRegistered<shared.SupabaseAuthService>()) {
    getIt.registerLazySingleton<shared.SupabaseAuthService>(
      () => shared.SupabaseAuthService(),
    );
  }

  // Initialize settings service
  await SettingsService.instance.initialize();
}

/// Setup all services for dependency injection (called after app startup)
Future<void> setupServiceLocator() async {
  // First ensure basic services are setup
  await setupBasicServiceLocator();

  // Register additional services (non-critical for startup)
  if (!getIt.isRegistered<OfflineService>()) {
    getIt.registerLazySingleton<OfflineService>(() => OfflineService());
  }

  if (!getIt.isRegistered<EnhancedAuthService>()) {
    getIt.registerLazySingleton<EnhancedAuthService>(
      () => EnhancedAuthService(),
    );
  }

  // Storage and data services
  if (!getIt.isRegistered<UserStorageService>()) {
    getIt.registerLazySingleton<UserStorageService>(() => UserStorageService());
  }
  if (!getIt.isRegistered<FileUploadService>()) {
    getIt.registerLazySingleton<FileUploadService>(() => FileUploadService());
  }
  if (!getIt.isRegistered<UserRatingService>()) {
    getIt.registerLazySingleton<UserRatingService>(() => UserRatingService());
  }

  // Feature services
  if (!getIt.isRegistered<EnhancedStudyMaterialService>()) {
    getIt.registerLazySingleton<EnhancedStudyMaterialService>(
      () => EnhancedStudyMaterialService(),
    );
  }

  // Communication services
  if (!getIt.isRegistered<NotificationService>()) {
    getIt.registerLazySingleton<NotificationService>(
      () => NotificationService(),
    );
  }
  if (!getIt.isRegistered<RealtimeService>()) {
    getIt.registerLazySingleton<RealtimeService>(() => RealtimeService());
  }

  // Initialize services that need setup (only if offline service was just registered)
  try {
    if (getIt.isRegistered<OfflineService>()) {
      await getIt<OfflineService>().initialize();
    }
  } catch (e) {
    // Don't crash if offline service fails
    print('Warning: OfflineService initialization failed: $e');
  }
}

/// Reset service locator (useful for testing)
Future<void> resetServiceLocator() async {
  await getIt.reset();
}

/// Get service instance
T getService<T extends Object>() => getIt<T>();
