import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../shared/models/user_model.dart';
import '../services/premium_service.dart';

/// Service to manage premium access for AI features
class AIPremiumAccessService {
  static final AIPremiumAccessService _instance =
      AIPremiumAccessService._internal();
  factory AIPremiumAccessService() => _instance;
  AIPremiumAccessService._internal();

  final Logger _logger = Logger();
  final PremiumService _premiumService = PremiumService();

  /// Check if user has premium access for AI features
  Future<bool> hasAIAccess(UserModel? user) async {
    if (user == null) {
      _logger.w('🚫 No user provided for AI access check');
      return false;
    }

    try {
      // Use unified premium service for consistent access control
      final hasPremium = _premiumService.hasAccessToPremiumFeatures(user);

      if (hasPremium) {
        _logger.i(
          '✅ User ${user.id} has premium AI access (isPremium: ${user.isPremium}, isTestUser: ${user.isTestUser})',
        );
        return true;
      } else {
        _logger.i(
          '❌ User ${user.id} does not have premium AI access (isPremium: ${user.isPremium}, isTestUser: ${user.isTestUser})',
        );
        return false;
      }
    } catch (e) {
      _logger.e('❌ Error checking AI access for user ${user.id}: $e');
      return false;
    }
  }

  /// Show premium upgrade dialog for AI features
  void showAIUpgradeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.smart_toy,
                  color: Theme.of(context).primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text('AI Features - Premium Only'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Unlock powerful AI features to supercharge your military preparation:',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 16),
                _buildFeatureItem('🧠 Personalized Study Assistant'),
                _buildFeatureItem('📝 AI-Generated Practice Questions'),
                _buildFeatureItem('💪 Smart Fitness Coaching'),
                _buildFeatureItem('📊 Advanced Performance Analysis'),
                _buildFeatureItem('🎯 Career Guidance & Tips'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.star, color: Colors.orange.shade600, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'Powered by DeepSeek R1 - Advanced AI for Nigerian Military Preparation',
                          style: TextStyle(
                            color: Colors.orange.shade800,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Maybe Later'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamed('/premium');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Upgrade to Premium'),
              ),
            ],
          ),
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green.shade600, size: 16),
          const SizedBox(width: 8),
          Expanded(child: Text(feature, style: const TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  /// Show AI feature locked snackbar
  void showAILockedSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.lock, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Expanded(
              child: Text('AI features are available for Premium members only'),
            ),
          ],
        ),
        backgroundColor: Colors.orange.shade600,
        action: SnackBarAction(
          label: 'Upgrade',
          textColor: Colors.white,
          onPressed: () {
            Navigator.of(context).pushNamed('/premium');
          },
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// Check AI access and show upgrade dialog if needed
  Future<bool> checkAIAccessWithDialog(
    BuildContext context,
    UserModel? user,
  ) async {
    final hasAccess = await hasAIAccess(user);

    if (!hasAccess) {
      showAIUpgradeDialog(context);
      return false;
    }

    return true;
  }

  /// Get AI usage limits for different subscription tiers
  Map<String, int> getAIUsageLimits(UserModel? user) {
    if (user != null && _premiumService.hasAccessToPremiumFeatures(user)) {
      return {
        'daily_queries': 100,
        'monthly_queries': 3000,
        'quiz_generations': 50,
        'study_plans': 10,
        'performance_analyses': 20,
      };
    } else {
      return {
        'daily_queries': 0,
        'monthly_queries': 0,
        'quiz_generations': 0,
        'study_plans': 0,
        'performance_analyses': 0,
      };
    }
  }

  /// Track AI usage for premium users
  Future<void> trackAIUsage(String userId, String featureType) async {
    try {
      // In a real implementation, this would track usage in the database
      _logger.i('📊 AI usage tracked: User $userId used $featureType');

      // TODO: Implement actual usage tracking with Supabase
      // This could include:
      // - Daily/monthly usage counters
      // - Feature-specific usage tracking
      // - Usage analytics for business insights
    } catch (e) {
      _logger.e('❌ Error tracking AI usage: $e');
    }
  }

  /// Get remaining AI usage for user
  Future<Map<String, int>> getRemainingUsage(String userId) async {
    try {
      // In a real implementation, this would fetch from database
      // For now, return mock data
      return {
        'daily_queries': 85,
        'monthly_queries': 2750,
        'quiz_generations': 45,
        'study_plans': 8,
        'performance_analyses': 15,
      };
    } catch (e) {
      _logger.e('❌ Error getting remaining AI usage: $e');
      return {};
    }
  }

  /// Check if user has reached usage limit for specific feature
  Future<bool> hasReachedUsageLimit(String userId, String featureType) async {
    try {
      final remaining = await getRemainingUsage(userId);
      final limit = remaining[featureType] ?? 0;

      return limit <= 0;
    } catch (e) {
      _logger.e('❌ Error checking usage limit: $e');
      return true; // Err on the side of caution
    }
  }

  /// Show usage limit reached dialog
  void showUsageLimitDialog(BuildContext context, String featureType) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  Icons.hourglass_empty,
                  color: Colors.orange.shade600,
                  size: 28,
                ),
                const SizedBox(width: 12),
                const Text('Usage Limit Reached'),
              ],
            ),
            content: Text(
              'You\'ve reached your monthly limit for $featureType. Your usage will reset next month, or you can upgrade to get higher limits.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamed('/premium');
                },
                child: const Text('Upgrade Plan'),
              ),
            ],
          ),
    );
  }
}
