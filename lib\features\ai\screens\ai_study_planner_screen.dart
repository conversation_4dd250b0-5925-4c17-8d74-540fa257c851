import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../core/services/deepseek_ai_service.dart';
import '../../../core/services/ai_premium_access_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';

/// AI-powered Study Planner for Fit4Force
/// Creates personalized study plans based on user goals and timeline
class AIStudyPlannerScreen extends StatefulWidget {
  final String targetAgency;

  const AIStudyPlannerScreen({super.key, required this.targetAgency});

  @override
  State<AIStudyPlannerScreen> createState() => _AIStudyPlannerScreenState();
}

class _AIStudyPlannerScreenState extends State<AIStudyPlannerScreen> {
  final Logger _logger = Logger();
  final DeepSeekAIService _aiService = DeepSeekAIService();
  final AIPremiumAccessService _accessService = AIPremiumAccessService();
  final AuthService _authService = AuthService();

  UserModel? _currentUser;
  bool _hasAIAccess = false;
  bool _isLoading = false;
  bool _isGenerating = false;

  // Study plan configuration
  String _examDate = '';
  String _studyHoursPerDay = '2-3 hours';
  String _currentLevel = 'Beginner';
  final List<String> _weakAreas = [];
  final List<String> _strongAreas = [];
  Map<String, dynamic>? _generatedPlan;

  final List<String> _studyHours = ['1-2 hours', '2-3 hours', '3-4 hours', '4+ hours'];
  final List<String> _levels = ['Beginner', 'Intermediate', 'Advanced'];
  final List<String> _subjects = [
    'General Knowledge',
    'Current Affairs',
    'Mathematics',
    'English Language',
    'Military History',
    'Physical Fitness',
    'Leadership',
    'Nigerian Constitution',
  ];

  @override
  void initState() {
    super.initState();
    _initializeStudyPlanner();
  }

  Future<void> _initializeStudyPlanner() async {
    setState(() => _isLoading = true);

    try {
      _currentUser = await _authService.getCurrentUser();
      _hasAIAccess = await _accessService.hasAIAccess(_currentUser);
      
      _logger.i('📚 AI Study Planner initialized. Access: $_hasAIAccess');
    } catch (e) {
      _logger.e('❌ Error initializing study planner: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateStudyPlan() async {
    if (!await _accessService.checkAIAccessWithDialog(context, _currentUser)) {
      return;
    }

    if (_examDate.isEmpty) {
      _showErrorMessage('Please select your exam date');
      return;
    }

    setState(() => _isGenerating = true);

    try {
      await _accessService.trackAIUsage(_currentUser!.id, 'study_planning');

      final prompt = '''
Create a comprehensive study plan for ${widget.targetAgency} recruitment preparation.

User Details:
- Target Agency: ${widget.targetAgency}
- Exam Date: $_examDate
- Available Study Time: $_studyHoursPerDay per day
- Current Level: $_currentLevel
- Weak Areas: ${_weakAreas.join(', ')}
- Strong Areas: ${_strongAreas.join(', ')}

Requirements:
1. Create a week-by-week breakdown until exam date
2. Allocate time based on weak areas (more focus)
3. Include revision periods
4. Add practice test schedules
5. Include fitness preparation for military requirements
6. Provide daily study goals
7. Include motivational milestones

Format the response with clear sections and actionable daily tasks.
''';

      final response = await _aiService.generateResponse(
        prompt: prompt,
        context: AIContext.studyAssistant,
        maxTokens: 2000,
        temperature: 0.7,
      );

      if (response.success) {
        _parseGeneratedPlan(response.content);
      } else {
        _showErrorMessage('Failed to generate study plan. Please try again.');
      }
    } catch (e) {
      _logger.e('❌ Error generating study plan: $e');
      _showErrorMessage('An error occurred while generating the study plan.');
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  void _parseGeneratedPlan(String content) {
    // For demo purposes, create a structured plan
    setState(() {
      _generatedPlan = {
        'title': 'Personalized Study Plan for ${widget.targetAgency}',
        'duration': _calculateDaysUntilExam(),
        'content': content,
        'weeks': _generateWeeklyBreakdown(),
        'dailyGoals': _generateDailyGoals(),
        'milestones': _generateMilestones(),
      };
    });
  }

  int _calculateDaysUntilExam() {
    if (_examDate.isEmpty) return 0;
    try {
      final examDateTime = DateTime.parse(_examDate);
      final now = DateTime.now();
      return examDateTime.difference(now).inDays;
    } catch (e) {
      return 0;
    }
  }

  List<Map<String, dynamic>> _generateWeeklyBreakdown() {
    final weeks = <Map<String, dynamic>>[];
    final totalWeeks = (_calculateDaysUntilExam() / 7).ceil();
    
    for (int i = 1; i <= totalWeeks && i <= 12; i++) {
      weeks.add({
        'week': i,
        'focus': i <= totalWeeks * 0.7 ? 'Learning & Practice' : 'Revision & Mock Tests',
        'subjects': _getWeeklySubjects(i, totalWeeks),
        'goals': _getWeeklyGoals(i, totalWeeks),
      });
    }
    
    return weeks;
  }

  List<String> _getWeeklySubjects(int week, int totalWeeks) {
    if (week <= totalWeeks * 0.3) {
      return _weakAreas.isNotEmpty ? _weakAreas : ['General Knowledge', 'Current Affairs'];
    } else if (week <= totalWeeks * 0.7) {
      return ['Mathematics', 'English Language', 'Military History'];
    } else {
      return ['Mock Tests', 'Revision', 'Physical Fitness'];
    }
  }

  List<String> _getWeeklyGoals(int week, int totalWeeks) {
    if (week <= totalWeeks * 0.3) {
      return ['Master basic concepts', 'Complete 2 practice tests', 'Review weak areas'];
    } else if (week <= totalWeeks * 0.7) {
      return ['Advanced problem solving', 'Speed improvement', 'Mock test analysis'];
    } else {
      return ['Final revision', 'Full mock tests', 'Confidence building'];
    }
  }

  List<String> _generateDailyGoals() {
    return [
      'Study for ${_studyHoursPerDay.split(' ')[0]} focused on weak areas',
      'Complete 20 practice questions',
      'Review previous day\'s mistakes',
      '30 minutes physical fitness',
      'Read current affairs for 15 minutes',
    ];
  }

  List<Map<String, dynamic>> _generateMilestones() {
    return [
      {'week': 2, 'milestone': 'Complete baseline assessment', 'reward': 'Treat yourself to favorite meal'},
      {'week': 4, 'milestone': 'Improve weak area scores by 20%', 'reward': 'Watch a movie'},
      {'week': 6, 'milestone': 'Score 70%+ in mock test', 'reward': 'Day off from studies'},
      {'week': 8, 'milestone': 'Master all core concepts', 'reward': 'Buy something you want'},
    ];
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.schedule, color: Colors.white),
            SizedBox(width: 8),
            Text('AI Study Planner'),
            if (_hasAIAccess) ...[
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'AI POWERED',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: _hasAIAccess ? _buildPlannerInterface() : _buildUpgradePrompt(),
    );
  }

  Widget _buildPlannerInterface() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_generatedPlan == null) {
      return _buildPlanConfiguration();
    }

    return _buildGeneratedPlan();
  }

  Widget _buildPlanConfiguration() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Create Your AI Study Plan',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 24),
          
          _buildExamDateSection(),
          _buildStudyHoursSection(),
          _buildLevelSection(),
          _buildWeakAreasSection(),
          _buildStrongAreasSection(),
          
          SizedBox(height: 32),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isGenerating ? null : _generateStudyPlan,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isGenerating
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Generating AI Study Plan...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.auto_awesome),
                        SizedBox(width: 8),
                        Text('Generate AI Study Plan'),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExamDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Exam Date', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: DateTime.now().add(Duration(days: 30)),
              firstDate: DateTime.now(),
              lastDate: DateTime.now().add(Duration(days: 365)),
            );
            if (date != null) {
              setState(() {
                _examDate = date.toIso8601String().split('T')[0];
              });
            }
          },
          child: Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.grey.shade600),
                SizedBox(width: 12),
                Text(_examDate.isEmpty ? 'Select exam date' : _examDate),
              ],
            ),
          ),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildStudyHoursSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Daily Study Time', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _studyHoursPerDay,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _studyHours.map((hours) {
            return DropdownMenuItem(value: hours, child: Text(hours));
          }).toList(),
          onChanged: (value) => setState(() => _studyHoursPerDay = value!),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildLevelSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Current Level', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _currentLevel,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _levels.map((level) {
            return DropdownMenuItem(value: level, child: Text(level));
          }).toList(),
          onChanged: (value) => setState(() => _currentLevel = value!),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildWeakAreasSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Weak Areas (Select up to 3)', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _subjects.map((subject) {
            final isSelected = _weakAreas.contains(subject);
            return FilterChip(
              label: Text(subject),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected && _weakAreas.length < 3) {
                    _weakAreas.add(subject);
                  } else {
                    _weakAreas.remove(subject);
                  }
                });
              },
              selectedColor: Colors.red.shade100,
              checkmarkColor: Colors.red.shade600,
            );
          }).toList(),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildStrongAreasSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Strong Areas (Select up to 3)', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _subjects.map((subject) {
            final isSelected = _strongAreas.contains(subject);
            return FilterChip(
              label: Text(subject),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected && _strongAreas.length < 3) {
                    _strongAreas.add(subject);
                  } else {
                    _strongAreas.remove(subject);
                  }
                });
              },
              selectedColor: Colors.green.shade100,
              checkmarkColor: Colors.green.shade600,
            );
          }).toList(),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildGeneratedPlan() {
    final plan = _generatedPlan!;
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.auto_awesome, color: Colors.orange.shade600),
                    SizedBox(width: 8),
                    Text(
                      'AI-Generated Study Plan',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8),
                Text(
                  plan['title'],
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
                Text(
                  '${plan['duration']} days until exam',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 24),
          
          _buildPlanSection('Weekly Breakdown', plan['weeks']),
          _buildPlanSection('Daily Goals', plan['dailyGoals']),
          _buildPlanSection('Milestones & Rewards', plan['milestones']),
          
          SizedBox(height: 32),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() => _generatedPlan = null);
                  },
                  child: Text('Create New Plan'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    // TODO: Save plan functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Study plan saved!')),
                    );
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
                  child: Text('Save Plan'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlanSection(String title, dynamic content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        SizedBox(height: 12),
        
        if (content is List<Map<String, dynamic>> && title.contains('Weekly'))
          ...content.map((week) => _buildWeekCard(week))
        else if (content is List<String>)
          ...content.map((item) => _buildListItem(item))
        else if (content is List<Map<String, dynamic>> && title.contains('Milestones'))
          ...content.map((milestone) => _buildMilestoneCard(milestone)),
        
        SizedBox(height: 24),
      ],
    );
  }

  Widget _buildWeekCard(Map<String, dynamic> week) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Week ${week['week']} - ${week['focus']}',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Subjects: ${(week['subjects'] as List).join(', ')}'),
            SizedBox(height: 4),
            Text('Goals: ${(week['goals'] as List).join(', ')}'),
          ],
        ),
      ),
    );
  }

  Widget _buildListItem(String item) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.check_circle, size: 16, color: AppTheme.primaryColor),
          SizedBox(width: 8),
          Expanded(child: Text(item)),
        ],
      ),
    );
  }

  Widget _buildMilestoneCard(Map<String, dynamic> milestone) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      color: Colors.green.shade50,
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Week ${milestone['week']}',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.green.shade700),
            ),
            SizedBox(height: 4),
            Text(milestone['milestone']),
            SizedBox(height: 4),
            Text(
              'Reward: ${milestone['reward']}',
              style: TextStyle(fontStyle: FontStyle.italic, color: Colors.green.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpgradePrompt() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.schedule, size: 80, color: Colors.grey.shade400),
            SizedBox(height: 24),
            Text(
              'AI Study Planner',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.grey.shade700),
            ),
            SizedBox(height: 16),
            Text(
              'Create personalized study plans with AI that adapt to your goals, timeline, and learning style.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _accessService.showAIUpgradeDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text('Upgrade to Premium'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _aiService.dispose();
    super.dispose();
  }
}
