import 'package:fit_4_force/shared/models/base_model.dart';

/// Model representing a comment on a post with Facebook-style nested replies
class CommentModel extends BaseModel {
  final String userId;
  final String userName;
  final String? userProfileImageUrl;
  final String content;
  final String? imageUrl; // Support for comment images
  final List<String> imageUrls; // Multiple images in comments
  final int likesCount;
  final bool isLikedByCurrentUser;
  final String? parentCommentId; // For nested replies
  final List<CommentModel> replies; // Nested replies
  final int repliesCount;
  final List<String> reactions; // Different types of reactions
  final Map<String, int> reactionCounts; // Count of each reaction type
  final Map<String, String> userReactions; // Which users reacted how
  final bool isEdited;
  final String? editedAt;
  final bool isPinned; // For moderator pinned comments
  final String? mentionedUsers; // @username mentions

  const CommentModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.userName,
    this.userProfileImageUrl,
    required this.content,
    this.imageUrl,
    this.imageUrls = const [],
    required this.likesCount,
    required this.isLikedByCurrentUser,
    this.parentCommentId,
    this.replies = const [],
    this.repliesCount = 0,
    this.reactions = const [],
    this.reactionCounts = const {},
    this.userReactions = const {},
    this.isEdited = false,
    this.editedAt,
    this.isPinned = false,
    this.mentionedUsers,
  });

  @override
  List<Object?> get props => [
    ...super.props,
    userId,
    userName,
    userProfileImageUrl,
    content,
    imageUrl,
    imageUrls,
    likesCount,
    isLikedByCurrentUser,
    parentCommentId,
    replies,
    repliesCount,
    reactions,
    reactionCounts,
    userReactions,
    isEdited,
    editedAt,
    isPinned,
    mentionedUsers,
  ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'userId': userId,
      'userName': userName,
      'userProfileImageUrl': userProfileImageUrl,
      'content': content,
      'imageUrl': imageUrl,
      'imageUrls': imageUrls,
      'likesCount': likesCount,
      'isLikedByCurrentUser': isLikedByCurrentUser,
      'parentCommentId': parentCommentId,
      'replies': replies.map((reply) => reply.toJson()).toList(),
      'repliesCount': repliesCount,
      'reactions': reactions,
      'reactionCounts': reactionCounts,
      'userReactions': userReactions,
      'isEdited': isEdited,
      'editedAt': editedAt,
      'isPinned': isPinned,
      'mentionedUsers': mentionedUsers,
    };
  }

  factory CommentModel.fromJson(Map<String, dynamic> json) {
    return CommentModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
      userId: json['userId'] as String,
      userName: json['userName'] as String,
      userProfileImageUrl: json['userProfileImageUrl'] as String?,
      content: json['content'] as String,
      imageUrl: json['imageUrl'] as String?,
      imageUrls: json['imageUrls'] != null 
          ? List<String>.from(json['imageUrls'] as List)
          : const [],
      likesCount: json['likesCount'] as int,
      isLikedByCurrentUser: json['isLikedByCurrentUser'] as bool,
      parentCommentId: json['parentCommentId'] as String?,
      replies:
          json['replies'] != null
              ? List<CommentModel>.from(
                (json['replies'] as List).map(
                  (reply) => CommentModel.fromJson(reply),
                ),
              )
              : const [],
      repliesCount: json['repliesCount'] as int? ?? 0,
      reactions:
          json['reactions'] != null
              ? List<String>.from(json['reactions'] as List)
              : const [],
      reactionCounts:
          json['reactionCounts'] != null
              ? Map<String, int>.from(json['reactionCounts'] as Map)
              : const {},
      userReactions:
          json['userReactions'] != null
              ? Map<String, String>.from(json['userReactions'] as Map)
              : const {},
      isEdited: json['isEdited'] as bool? ?? false,
      editedAt: json['editedAt'] as String?,
      isPinned: json['isPinned'] as bool? ?? false,
      mentionedUsers: json['mentionedUsers'] as String?,
    );
  }

  @override
  CommentModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? userName,
    String? userProfileImageUrl,
    String? content,
    String? imageUrl,
    List<String>? imageUrls,
    int? likesCount,
    bool? isLikedByCurrentUser,
    String? parentCommentId,
    List<CommentModel>? replies,
    int? repliesCount,
    List<String>? reactions,
    Map<String, int>? reactionCounts,
    Map<String, String>? userReactions,
    bool? isEdited,
    String? editedAt,
    bool? isPinned,
    String? mentionedUsers,
  }) {
    return CommentModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userProfileImageUrl: userProfileImageUrl ?? this.userProfileImageUrl,
      content: content ?? this.content,
      imageUrl: imageUrl ?? this.imageUrl,
      imageUrls: imageUrls ?? this.imageUrls,
      likesCount: likesCount ?? this.likesCount,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
      parentCommentId: parentCommentId ?? this.parentCommentId,
      replies: replies ?? this.replies,
      repliesCount: repliesCount ?? this.repliesCount,
      reactions: reactions ?? this.reactions,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      userReactions: userReactions ?? this.userReactions,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      isPinned: isPinned ?? this.isPinned,
      mentionedUsers: mentionedUsers ?? this.mentionedUsers,
    );
  }
}
