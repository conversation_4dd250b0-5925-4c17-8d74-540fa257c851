import 'package:flutter/material.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/shared/widgets/app_drawer.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

class ResponsiveDrawer extends StatelessWidget {
  final UserModel user;
  final Function(int)? onTabChange;

  const ResponsiveDrawer({super.key, required this.user, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final screenHeight = mediaQuery.size.height;
    final padding = mediaQuery.padding;

    // Normalize text scale factor to prevent layout issues
    final textScaler = MediaQuery.textScalerOf(context);
    final double scale = textScaler.scale(1.0);
    final normalizedTextScaler = TextScaler.linear(scale.clamp(0.8, 1.2));

    // Calculate responsive drawer width
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    final drawerWidth =
        isTablet
            ? screenWidth * 0.4
            : (isLandscape ? screenWidth * 0.35 : screenWidth * 0.85);

    // Calculate responsive padding
    final horizontalPadding = screenWidth * 0.04;
    final verticalPadding = screenHeight * 0.02;

    return MediaQuery(
      data: mediaQuery.copyWith(
        textScaler: normalizedTextScaler,
        padding: padding.copyWith(
          left: padding.left + horizontalPadding,
          right: padding.right + horizontalPadding,
          top: padding.top + verticalPadding,
          bottom: padding.bottom + verticalPadding,
        ),
      ),
      child: SafeArea(
        child: Drawer(
          width: drawerWidth,
          child: AppDrawer(user: user, onTabChange: onTabChange),
        ),
      ),
    );
  }
}
