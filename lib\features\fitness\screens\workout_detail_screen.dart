import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';

class WorkoutDetailScreen extends StatefulWidget {
  final String workoutId;
  final UserModel? user;

  const WorkoutDetailScreen({super.key, required this.workoutId, this.user});

  @override
  State<WorkoutDetailScreen> createState() => _WorkoutDetailScreenState();
}

class _WorkoutDetailScreenState extends State<WorkoutDetailScreen> {
  final WorkoutService _workoutService = WorkoutService();
  late WorkoutModel _workout;
  bool _isLoading = true;
  bool _isPremiumUser = false;
  int? _playingIndex; // Track which exercise is playing
  ChewieController? _chewieController;
  VideoPlayerController? _videoPlayerController;

  @override
  void initState() {
    super.initState();
    _loadWorkoutData();
    _isPremiumUser = widget.user?.isPremium ?? false;
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
    super.dispose();
  }

  Future<void> _loadWorkoutData() async {
    // In a real app, this would be an async call to a service
    final workout = _workoutService.getWorkoutById(widget.workoutId);

    if (workout != null) {
      setState(() {
        _workout = workout;
        _isLoading = false;
      });
    } else {
      // Handle workout not found
      NavigationService().goBack();
    }
  }

  void _startWorkout() {
    if (_workout.isPremium && !_isPremiumUser) {
      _showPremiumDialog();
      return;
    }

    // In a real app, navigate to workout session screen
    NavigationService().navigateTo('/workout-session', arguments: _workout.id);
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.workspace_premium, color: AppTheme.premiumColor),
                const SizedBox(width: 8),
                const Text('Premium Feature'),
              ],
            ),
            content: const Text(
              'This workout is only available to premium users. Upgrade to access all premium content.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().navigateTo('/premium');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.premiumColor,
                ),
                child: const Text('UPGRADE'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Workout')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWorkoutStats(),
                  const SizedBox(height: 24),
                  _buildSectionTitle('Description'),
                  const SizedBox(height: 8),
                  Text(
                    _workout.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryLight,
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildSectionTitle(
                    'Exercises (${_workout.exercises.length})',
                  ),
                ],
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final exercise = _workout.exercises[index];
              return _buildExerciseItem(exercise, index);
            }, childCount: _workout.exercises.length),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(height: 100), // Space for the button
          ),
        ],
      ),
      floatingActionButton: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: ElevatedButton(
          onPressed: _startWorkout,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _workout.isPremium && !_isPremiumUser
                    ? Icons.workspace_premium
                    : Icons.play_arrow,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _workout.isPremium && !_isPremiumUser
                    ? 'UPGRADE TO START'
                    : 'START WORKOUT',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _workout.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black54,
                blurRadius: 2,
                offset: Offset(1, 1),
              ),
            ],
          ),
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              color: _workout.color,
              child: Icon(
                _workout.icon,
                size: 100,
                color: Colors.white.withValues(
                  alpha: (0.2 * 255).round().toDouble(),
                ),
              ),
            ),
            // Gradient overlay for better text visibility
            const DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, Colors.black54],
                ),
              ),
            ),
            if (_workout.isPremium)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.premiumColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      Icon(
                        Icons.workspace_premium,
                        size: 16,
                        color: Colors.white,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'PREMIUM',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkoutStats() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatCard(Icons.timer_outlined, '${_workout.duration}', 'Minutes'),
        _buildStatCard(
          Icons.local_fire_department_outlined,
          '${_workout.calories}',
          'Calories',
        ),
        _buildStatCard(
          Icons.fitness_center_outlined,
          '${_workout.exercises.length}',
          'Exercises',
        ),
      ],
    );
  }

  Widget _buildStatCard(IconData icon, String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: (0.1 * 255).round().toDouble(),
            ),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, size: 24, color: _workout.color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildExerciseItem(ExerciseModel exercise, int index) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.07),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          margin: const EdgeInsets.symmetric(horizontal: 0),
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          child: Row(
            children: [
              // Exercise image
              Container(
                width: 54,
                height: 54,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(12),
                ),
                child:
                    exercise.imageUrl.isNotEmpty
                        ? ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.asset(
                            exercise.imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder:
                                (context, error, stackTrace) => Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey[400],
                                  size: 32,
                                ),
                          ),
                        )
                        : Icon(Icons.image, color: Colors.grey[300], size: 32),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      exercise.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      exercise.duration > 0
                          ? _formatDuration(exercise.duration)
                          : (exercise.reps > 0 ? 'x${exercise.reps}' : ''),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  _playingIndex == index ? Icons.close : Icons.play_circle_fill,
                  color: AppTheme.primaryColor,
                  size: 32,
                ),
                tooltip: _playingIndex == index ? 'Close Video' : 'Play Demo',
                onPressed: () async {
                  if (_playingIndex == index) {
                    setState(() {
                      _playingIndex = null;
                      _chewieController?.dispose();
                      _videoPlayerController?.dispose();
                      _chewieController = null;
                      _videoPlayerController = null;
                    });
                  } else {
                    _chewieController?.dispose();
                    _videoPlayerController?.dispose();
                    final videoUrl = exercise.videoUrl;
                    final videoController = VideoPlayerController.network(
                      videoUrl,
                    );
                    await videoController.initialize();
                    final chewieController = ChewieController(
                      videoPlayerController: videoController,
                      autoPlay: true,
                      looping: false,
                    );
                    setState(() {
                      _playingIndex = index;
                      _videoPlayerController = videoController;
                      _chewieController = chewieController;
                    });
                  }
                },
              ),
            ],
          ),
        ),
        if (_playingIndex == index && _chewieController != null)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
            child: AspectRatio(
              aspectRatio: _videoPlayerController?.value.aspectRatio ?? 16 / 9,
              child: Chewie(controller: _chewieController!),
            ),
          ),
        const SizedBox(height: 8),
        if (index != _workout.exercises.length - 1)
          Divider(height: 1, thickness: 1, color: Colors.grey[200]),
        const SizedBox(height: 8),
      ],
    );
  }

  String _formatDuration(int seconds) {
    if (seconds < 60) {
      return '00:${seconds.toString().padLeft(2, '0')}';
    } else {
      final min = (seconds ~/ 60).toString().padLeft(2, '0');
      final sec = (seconds % 60).toString().padLeft(2, '0');
      return '$min:$sec';
    }
  }
}
