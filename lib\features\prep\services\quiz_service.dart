import 'package:flutter/material.dart';
import 'package:fit_4_force/features/prep/models/quiz_model.dart';
import 'package:fit_4_force/features/prep/data/scenario_quiz_database.dart';
import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Service for handling quizzes
class QuizService {
  final Logger _logger = Logger();
  final Uuid _uuid = const Uuid();

  // Mock quizzes
  final List<QuizModel> _quizzes = [
    QuizModel(
      id: 'q001',
      title: 'Nigerian Military History Quiz',
      description: 'Test your knowledge of Nigerian military history.',
      agency: 'Nigerian Army',
      category: 'History',
      difficulty: 'medium',
      questions: [
        QuizQuestion(
          id: 'q001_q1',
          question: 'In what year was the Nigerian Army established?',
          options: ['1956', '1960', '1963', '1966'],
          correctOptionIndex: 0,
          explanation:
              'The Nigerian Army was established in 1956, before Nigeria gained independence in 1960.',
        ),
        QuizQuestion(
          id: 'q001_q2',
          question: 'Who was the first Nigerian to command the Nigerian Army?',
          options: [
            '<PERSON>',
            '<PERSON><PERSON><PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON><PERSON>',
          ],
          correctOptionIndex: 0,
          explanation:
              'Major General <PERSON> Aguiyi-Ironsi was the first Nigerian to command the Nigerian Army in 1965.',
        ),
        QuizQuestion(
          id: 'q001_q3',
          question:
              'Which of these was NOT a major operation by the Nigerian Army?',
          options: [
            'Operation Python Dance',
            'Operation Lafiya Dole',
            'Operation Eagle Strike',
            'Operation Crocodile Smile',
          ],
          correctOptionIndex: 2,
          explanation:
              'Operation Eagle Strike is not a major operation by the Nigerian Army. The others are real operations.',
        ),
      ],
      timeLimit: 10,
      isPremium: true,
      publishedDate: DateTime(2023, 1, 20),
      icon: Icons.history_edu,
      color: Colors.brown,
      passingScore: 70,
    ),
    QuizModel(
      id: 'q002',
      title: 'Basic Mathematics Quiz',
      description:
          'Test your basic mathematics skills required for military exams.',
      agency: 'All',
      category: 'Mathematics',
      difficulty: 'easy',
      questions: [
        QuizQuestion(
          id: 'q002_q1',
          question:
              'If a soldier marches 5 km north, then 3 km east, and finally 2 km south, how far is the soldier from the starting point?',
          options: ['4 km', '5 km', '6 km', '10 km'],
          correctOptionIndex: 1,
          explanation:
              'Using the Pythagorean theorem: distance = √[(5-2)² + 3²] = √[9 + 9] = √18 ≈ 4.24 km, which rounds to 4 km.',
        ),
        QuizQuestion(
          id: 'q002_q2',
          question:
              'A military truck travels at 60 km/h. How long will it take to travel 150 km?',
          options: ['1.5 hours', '2 hours', '2.5 hours', '3 hours'],
          correctOptionIndex: 2,
          explanation:
              'Time = Distance ÷ Speed = 150 km ÷ 60 km/h = 2.5 hours.',
        ),
        QuizQuestion(
          id: 'q002_q3',
          question:
              'If a military unit has 120 soldiers and 25% are on leave, how many soldiers are available for duty?',
          options: ['30', '60', '90', '95'],
          correctOptionIndex: 2,
          explanation:
              'Available soldiers = Total - Soldiers on leave = 120 - (120 × 25%) = 120 - 30 = 90 soldiers.',
        ),
      ],
      timeLimit: 10,
      isPremium: false,
      publishedDate: DateTime(2023, 2, 15),
      icon: Icons.calculate,
      color: Colors.blue,
      passingScore: 70,
    ),
    QuizModel(
      id: 'q003',
      title: 'English Comprehension Quiz',
      description:
          'Test your English comprehension skills for military entrance exams.',
      agency: 'All',
      category: 'English',
      difficulty: 'medium',
      questions: [
        QuizQuestion(
          id: 'q003_q1',
          question:
              'Choose the word that is closest in meaning to "Discipline":',
          options: ['Freedom', 'Control', 'Punishment', 'Training'],
          correctOptionIndex: 1,
          explanation:
              'Discipline refers to control of behavior or actions, especially in a military context.',
        ),
        QuizQuestion(
          id: 'q003_q2',
          question:
              'Which of the following sentences is grammatically correct?',
          options: [
            'The soldiers was marching in formation.',
            'Each of the cadets have their own rifle.',
            'Neither the captain nor the lieutenants were present.',
            'The commander, along with his officers, is planning the operation.',
          ],
          correctOptionIndex: 3,
          explanation:
              'The subject is "commander" (singular), so the verb should be "is" (singular).',
        ),
      ],
      timeLimit: 8,
      isPremium: false,
      publishedDate: DateTime(2023, 3, 10),
      icon: Icons.spellcheck,
      color: Colors.green,
      passingScore: 70,
    ),
  ];

  // Mock quiz results
  final List<QuizResult> _quizResults = [];

  /// Get all quizzes
  List<QuizModel> getAllQuizzes() {
    _logger.i('Getting all quizzes');
    return _quizzes;
  }

  /// Get quizzes by category
  List<QuizModel> getQuizzesByCategory(String category) {
    _logger.i('Getting quizzes for category: $category');
    return _quizzes
        .where((quiz) => quiz.category.toLowerCase() == category.toLowerCase())
        .toList();
  }

  /// Get quizzes by agency
  List<QuizModel> getQuizzesByAgency(String agency) {
    _logger.i('Getting quizzes for agency: $agency');
    return _quizzes
        .where((quiz) => quiz.agency == agency || quiz.agency == 'All')
        .toList();
  }

  /// Get quiz by ID
  QuizModel? getQuizById(String id) {
    _logger.i('Getting quiz with ID: $id');
    try {
      return _quizzes.firstWhere((quiz) => quiz.id == id);
    } catch (e) {
      _logger.e('Error getting quiz: $e');
      return null;
    }
  }

  /// Get featured quizzes
  List<QuizModel> getFeaturedQuizzes() {
    _logger.i('Getting featured quizzes');
    // Return the 3 most recent quizzes
    final sortedQuizzes = List<QuizModel>.from(_quizzes)
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
    return sortedQuizzes.take(3).toList();
  }

  /// Create scenario quiz for specific agency
  QuizModel createScenarioQuiz(String agency, {bool isPremiumUser = false}) {
    _logger.i(
      'Creating scenario quiz for agency: $agency (Premium: $isPremiumUser)',
    );

    final totalQuestions = ScenarioQuizDatabase.getScenarioQuestionCount(
      agency,
    );
    final freeQuestions = ScenarioQuizDatabase.getFreeScenarioQuestionCount(
      agency,
    );
    final premiumQuestions =
        ScenarioQuizDatabase.getPremiumScenarioQuestionCount(agency);

    final questionCount = isPremiumUser ? totalQuestions : freeQuestions;
    final timeLimit = questionCount * 2; // 2 minutes per scenario

    return QuizModel(
      id: 'scenario_${agency.toLowerCase()}_${_uuid.v4()}',
      title:
          isPremiumUser
              ? 'Complete Scenario Quiz - $agency'
              : 'Scenario Quiz (Free) - $agency',
      description:
          isPremiumUser
              ? 'Complete collection of real-world scenarios and tactical decision-making challenges for $agency personnel'
              : 'Sample scenarios and tactical decision-making challenges for $agency personnel. Upgrade to premium for $premiumQuestions more scenarios!',
      agency: agency,
      category: 'scenario-based',
      difficulty: 'advanced',
      questions: [], // Questions loaded dynamically
      timeLimit: timeLimit,
      isPremium:
          false, // Both free and premium users can access, but content differs
      publishedDate: DateTime.now(),
      icon: Icons.psychology, // Brain icon for scenario-based thinking
      color: Colors.deepPurple, // Distinctive color for scenario quizzes
      passingScore: 70,
    );
  }

  /// Get scenario quiz info for display
  Map<String, dynamic> getScenarioQuizInfo(String agency, bool isPremiumUser) {
    final totalQuestions = ScenarioQuizDatabase.getScenarioQuestionCount(
      agency,
    );
    final freeQuestions = ScenarioQuizDatabase.getFreeScenarioQuestionCount(
      agency,
    );
    final premiumQuestions =
        ScenarioQuizDatabase.getPremiumScenarioQuestionCount(agency);

    return {
      'totalQuestions': totalQuestions,
      'freeQuestions': freeQuestions,
      'premiumQuestions': premiumQuestions,
      'userQuestions': isPremiumUser ? totalQuestions : freeQuestions,
      'timeLimit': (isPremiumUser ? totalQuestions : freeQuestions) * 2,
      'hasMoreContent': premiumQuestions > 0,
    };
  }

  /// Get free quizzes
  List<QuizModel> getFreeQuizzes() {
    _logger.i('Getting free quizzes');
    return _quizzes.where((quiz) => !quiz.isPremium).toList();
  }

  /// Submit quiz answers
  QuizResult submitQuizAnswers(
    String quizId,
    String userId,
    List<int> userAnswers,
    int timeSpent,
  ) {
    _logger.i('Submitting answers for quiz: $quizId');

    // Get the quiz
    final quiz = getQuizById(quizId);
    if (quiz == null) {
      throw Exception('Quiz not found');
    }

    // Calculate score
    int correctAnswers = 0;
    for (int i = 0; i < userAnswers.length; i++) {
      if (i < quiz.questions.length &&
          userAnswers[i] == quiz.questions[i].correctOptionIndex) {
        correctAnswers++;
      }
    }

    final score = (correctAnswers / quiz.questions.length * 100).round();
    final passed = score >= quiz.passingScore;

    // Create result
    final result = QuizResult(
      id: _uuid.v4(),
      quizId: quizId,
      userId: userId,
      score: score,
      timeSpent: timeSpent,
      completedAt: DateTime.now(),
      userAnswers: userAnswers,
      passed: passed,
    );

    // Save result
    _quizResults.add(result);

    return result;
  }

  /// Get quiz results for user
  List<QuizResult> getQuizResultsForUser(String userId) {
    _logger.i('Getting quiz results for user: $userId');
    return _quizResults.where((result) => result.userId == userId).toList();
  }

  /// Get quiz result by ID
  QuizResult? getQuizResultById(String id) {
    _logger.i('Getting quiz result with ID: $id');
    try {
      return _quizResults.firstWhere((result) => result.id == id);
    } catch (e) {
      _logger.e('Error getting quiz result: $e');
      return null;
    }
  }
}
