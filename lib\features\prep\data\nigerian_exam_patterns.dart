import '../models/nigerian_exam_pattern_model.dart';

/// Nigerian Military Recruitment Exam Patterns
/// Based on actual recruitment exam formats used by Nigerian agencies
class NigerianExamPatterns {
  
  /// Get exam pattern for specific agency
  static NigerianExamPatternModel getExamPattern(String agencyCode) {
    switch (agencyCode.toLowerCase()) {
      case 'army':
        return _getNigerianArmyPattern();
      case 'navy':
        return _getNigerianNavyPattern();
      case 'airforce':
        return _getNigerianAirForcePattern();
      case 'nda':
        return _getNDAPattern();
      case 'nscdc':
        return _getNSCDCPattern();
      case 'polac':
        return _getPOLACPattern();
      case 'frsc':
        return _getFRSCPattern();
      case 'immigration':
        return _getImmigrationPattern();
      case 'customs':
        return _getCustomsPattern();
      case 'fire':
        return _getFireServicePattern();
      case 'dss':
        return _getDSSPattern();
      default:
        return _getDefaultPattern();
    }
  }

  /// Nigerian Army Recruitment Exam Pattern
  static NigerianExamPatternModel _getNigerianArmyPattern() {
    return NigerianExamPatternModel(
      agencyCode: 'army',
      examName: 'Nigerian Army Recruitment Examination',
      totalQuestions: 100,
      timeLimit: 90, // 1.5 hours
      passingScore: 50,
      format: ExamFormat.computerBasedTest,
      sections: [
        ExamSection(
          name: 'General Knowledge',
          questionCount: 25,
          timeAllocation: 20,
          weightage: 25.0,
          topics: [
            'Nigerian History',
            'Current Affairs',
            'Geography of Nigeria',
            'Government and Politics',
            'Nigerian Constitution',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Mathematics',
          questionCount: 25,
          timeAllocation: 25,
          weightage: 25.0,
          topics: [
            'Arithmetic',
            'Algebra',
            'Geometry',
            'Statistics',
            'Number Theory',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'English Language',
          questionCount: 25,
          timeAllocation: 20,
          weightage: 25.0,
          topics: [
            'Grammar',
            'Vocabulary',
            'Reading Comprehension',
            'Sentence Construction',
            'Error Detection',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Aptitude Test',
          questionCount: 25,
          timeAllocation: 25,
          weightage: 25.0,
          topics: [
            'Logical Reasoning',
            'Numerical Reasoning',
            'Verbal Reasoning',
            'Abstract Reasoning',
            'Problem Solving',
          ],
          difficulty: DifficultyLevel.advanced,
        ),
      ],
      instructions: [
        'This is a Computer-Based Test (CBT) examination',
        'Total time allowed: 90 minutes',
        'Answer ALL questions',
        'Each question carries equal marks',
        'There is no negative marking',
        'Click NEXT to move to the next question',
        'You can review and change answers before submission',
        'Submit your answers before time expires',
      ],
      scoringPattern: {
        'total_marks': 100,
        'pass_mark': 50,
        'negative_marking': false,
        'equal_weightage': true,
        'bonus_marks': false,
      },
    );
  }

  /// Nigerian Navy Recruitment Exam Pattern
  static NigerianExamPatternModel _getNigerianNavyPattern() {
    return NigerianExamPatternModel(
      agencyCode: 'navy',
      examName: 'Nigerian Navy Recruitment Examination',
      totalQuestions: 80,
      timeLimit: 75, // 1 hour 15 minutes
      passingScore: 45,
      format: ExamFormat.computerBasedTest,
      sections: [
        ExamSection(
          name: 'General Knowledge',
          questionCount: 20,
          timeAllocation: 18,
          weightage: 25.0,
          topics: [
            'Maritime History',
            'Naval Operations',
            'Nigerian Geography',
            'Current Affairs',
            'International Relations',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Mathematics',
          questionCount: 20,
          timeAllocation: 20,
          weightage: 25.0,
          topics: [
            'Navigation Mathematics',
            'Trigonometry',
            'Statistics',
            'Basic Calculus',
            'Coordinate Geometry',
          ],
          difficulty: DifficultyLevel.advanced,
        ),
        ExamSection(
          name: 'English Language',
          questionCount: 20,
          timeAllocation: 17,
          weightage: 25.0,
          topics: [
            'Maritime Vocabulary',
            'Technical Writing',
            'Reading Comprehension',
            'Grammar and Usage',
            'Communication Skills',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Naval Knowledge',
          questionCount: 20,
          timeAllocation: 20,
          weightage: 25.0,
          topics: [
            'Ship Types and Functions',
            'Naval Terminology',
            'Maritime Law',
            'Ocean Geography',
            'Naval History',
          ],
          difficulty: DifficultyLevel.advanced,
        ),
      ],
      instructions: [
        'Computer-Based Test for Nigerian Navy recruitment',
        'Total duration: 75 minutes',
        'Answer all 80 questions',
        'Focus on maritime and naval knowledge',
        'No negative marking applied',
        'Review answers before final submission',
      ],
      scoringPattern: {
        'total_marks': 80,
        'pass_mark': 45,
        'negative_marking': false,
        'equal_weightage': true,
        'bonus_marks': false,
      },
    );
  }

  /// Nigerian Air Force Recruitment Exam Pattern
  static NigerianExamPatternModel _getNigerianAirForcePattern() {
    return NigerianExamPatternModel(
      agencyCode: 'airforce',
      examName: 'Nigerian Air Force Recruitment Examination',
      totalQuestions: 120,
      timeLimit: 100, // 1 hour 40 minutes
      passingScore: 60,
      format: ExamFormat.computerBasedTest,
      sections: [
        ExamSection(
          name: 'General Knowledge',
          questionCount: 30,
          timeAllocation: 25,
          weightage: 25.0,
          topics: [
            'Aviation History',
            'Current Affairs',
            'Nigerian Geography',
            'Science and Technology',
            'International Aviation',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Mathematics',
          questionCount: 30,
          timeAllocation: 30,
          weightage: 25.0,
          topics: [
            'Advanced Mathematics',
            'Physics Applications',
            'Trigonometry',
            'Calculus',
            'Statistics',
          ],
          difficulty: DifficultyLevel.advanced,
        ),
        ExamSection(
          name: 'English Language',
          questionCount: 30,
          timeAllocation: 20,
          weightage: 25.0,
          topics: [
            'Technical English',
            'Aviation Terminology',
            'Reading Comprehension',
            'Grammar and Syntax',
            'Report Writing',
          ],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Technical Aptitude',
          questionCount: 30,
          timeAllocation: 25,
          weightage: 25.0,
          topics: [
            'Aircraft Knowledge',
            'Mechanical Reasoning',
            'Spatial Ability',
            'Technical Reasoning',
            'Problem Solving',
          ],
          difficulty: DifficultyLevel.expert,
        ),
      ],
      instructions: [
        'Nigerian Air Force CBT Examination',
        'Duration: 100 minutes for 120 questions',
        'Higher passing score required (60%)',
        'Technical aptitude heavily weighted',
        'Focus on aviation and technical knowledge',
        'Precision and accuracy are crucial',
      ],
      scoringPattern: {
        'total_marks': 120,
        'pass_mark': 60,
        'negative_marking': false,
        'equal_weightage': true,
        'bonus_marks': false,
      },
    );
  }

  /// NDA (Nigerian Defence Academy) Exam Pattern
  static NigerianExamPatternModel _getNDAPattern() {
    return NigerianExamPatternModel(
      agencyCode: 'nda',
      examName: 'Nigerian Defence Academy Entrance Examination',
      totalQuestions: 150,
      timeLimit: 180, // 3 hours
      passingScore: 100,
      format: ExamFormat.computerBasedTest,
      sections: [
        ExamSection(
          name: 'Mathematics',
          questionCount: 50,
          timeAllocation: 60,
          weightage: 33.3,
          topics: [
            'Pure Mathematics',
            'Applied Mathematics',
            'Further Mathematics',
            'Statistics',
            'Mechanics',
          ],
          difficulty: DifficultyLevel.expert,
        ),
        ExamSection(
          name: 'English Language',
          questionCount: 50,
          timeAllocation: 60,
          weightage: 33.3,
          topics: [
            'Literature',
            'Grammar',
            'Composition',
            'Reading Comprehension',
            'Critical Analysis',
          ],
          difficulty: DifficultyLevel.advanced,
        ),
        ExamSection(
          name: 'Science Subjects',
          questionCount: 50,
          timeAllocation: 60,
          weightage: 33.4,
          topics: [
            'Physics',
            'Chemistry',
            'Biology',
            'General Science',
            'Scientific Method',
          ],
          difficulty: DifficultyLevel.expert,
        ),
      ],
      instructions: [
        'NDA Entrance Examination - Officer Cadet Selection',
        'Duration: 3 hours for 150 questions',
        'High academic standard required',
        'Covers university-level subjects',
        'Competitive examination with limited slots',
        'Excellence in all subjects required',
      ],
      scoringPattern: {
        'total_marks': 150,
        'pass_mark': 100,
        'negative_marking': false,
        'equal_weightage': true,
        'bonus_marks': false,
      },
    );
  }

  /// Default pattern for other agencies
  static NigerianExamPatternModel _getDefaultPattern() {
    return NigerianExamPatternModel(
      agencyCode: 'default',
      examName: 'Nigerian Security Agency Recruitment Examination',
      totalQuestions: 100,
      timeLimit: 90,
      passingScore: 50,
      format: ExamFormat.computerBasedTest,
      sections: [
        ExamSection(
          name: 'General Knowledge',
          questionCount: 40,
          timeAllocation: 35,
          weightage: 40.0,
          topics: ['Nigerian History', 'Current Affairs', 'Geography'],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'English Language',
          questionCount: 30,
          timeAllocation: 25,
          weightage: 30.0,
          topics: ['Grammar', 'Vocabulary', 'Comprehension'],
          difficulty: DifficultyLevel.intermediate,
        ),
        ExamSection(
          name: 'Mathematics',
          questionCount: 30,
          timeAllocation: 30,
          weightage: 30.0,
          topics: ['Arithmetic', 'Algebra', 'Geometry'],
          difficulty: DifficultyLevel.intermediate,
        ),
      ],
      instructions: [
        'Standard recruitment examination',
        'Answer all questions within time limit',
        'No negative marking',
      ],
      scoringPattern: {
        'total_marks': 100,
        'pass_mark': 50,
        'negative_marking': false,
        'equal_weightage': false,
      },
    );
  }

  // Placeholder methods for other agencies (to be implemented)
  static NigerianExamPatternModel _getNSCDCPattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getPOLACPattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getFRSCPattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getImmigrationPattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getCustomsPattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getFireServicePattern() => _getDefaultPattern();
  static NigerianExamPatternModel _getDSSPattern() => _getDefaultPattern();
}
