import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/community/models/comment_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

/// Facebook-style comment widget with enhanced features
class FacebookStyleComment extends StatefulWidget {
  final CommentModel comment;
  final UserModel currentUser;
  final bool isReply;
  final VoidCallback? onLike;
  final VoidCallback? onReply;
  final Function(String reaction)? onReaction;
  final Function(String content, List<String> imagePaths)? onReplySubmit;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showReplies;

  const FacebookStyleComment({
    super.key,
    required this.comment,
    required this.currentUser,
    this.isReply = false,
    this.onLike,
    this.onReply,
    this.onReaction,
    this.onReplySubmit,
    this.onEdit,
    this.onDelete,
    this.showReplies = true,
  });

  @override
  State<FacebookStyleComment> createState() => _FacebookStyleCommentState();
}

class _FacebookStyleCommentState extends State<FacebookStyleComment>
    with TickerProviderStateMixin {
  bool _showReactionPicker = false;
  bool _showReplyInput = false;
  bool _isEditing = false;
  late TextEditingController _replyController;
  late TextEditingController _editController;
  late AnimationController _reactionAnimationController;
  late Animation<double> _reactionAnimation;
  final ImagePicker _picker = ImagePicker();
  final List<XFile> _replyImages = [];

  // Facebook-style reactions
  final List<Map<String, dynamic>> _reactions = [
    {'emoji': '👍', 'name': 'Like', 'color': Colors.blue},
    {'emoji': '❤️', 'name': 'Love', 'color': Colors.red},
    {'emoji': '😂', 'name': 'Haha', 'color': Colors.orange},
    {'emoji': '😮', 'name': 'Wow', 'color': Colors.orange},
    {'emoji': '😢', 'name': 'Sad', 'color': Colors.orange},
    {'emoji': '😡', 'name': 'Angry', 'color': Colors.red},
  ];

  @override
  void initState() {
    super.initState();
    _replyController = TextEditingController();
    _editController = TextEditingController(text: widget.comment.content);
    _reactionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _reactionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _reactionAnimationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _replyController.dispose();
    _editController.dispose();
    _reactionAnimationController.dispose();
    super.dispose();
  }

  void _toggleReactionPicker() {
    setState(() => _showReactionPicker = !_showReactionPicker);
    if (_showReactionPicker) {
      _reactionAnimationController.forward();
    } else {
      _reactionAnimationController.reverse();
    }
  }

  void _addReaction(String reaction) {
    widget.onReaction?.call(reaction);
    setState(() => _showReactionPicker = false);
    _reactionAnimationController.reverse();
  }

  void _toggleReplyInput() {
    setState(() => _showReplyInput = !_showReplyInput);
  }

  Future<void> _pickReplyImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );
      
      if (image != null) {
        setState(() {
          if (_replyImages.length < 3) { // Limit reply images to 3
            _replyImages.add(image);
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error picking image: $e')),
      );
    }
  }

  void _submitReply() {
    if (_replyController.text.trim().isNotEmpty || _replyImages.isNotEmpty) {
      final imagePaths = _replyImages.map((img) => img.path).toList();
      widget.onReplySubmit?.call(_replyController.text.trim(), imagePaths);
      _replyController.clear();
      setState(() {
        _replyImages.clear();
        _showReplyInput = false;
      });
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  Widget _buildReactionDisplay() {
    if (widget.comment.reactionCounts.isEmpty) return const SizedBox.shrink();

    final topReactions = widget.comment.reactionCounts.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    if (topReactions.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          // Show top 3 reaction emojis
          ...topReactions.take(3).map((entry) {
            final reaction = _reactions.firstWhere(
              (r) => r['name'].toLowerCase() == entry.key.toLowerCase(),
              orElse: () => {'emoji': '👍', 'name': 'Like'},
            );
            return Container(
              margin: const EdgeInsets.only(right: 2),
              child: Text(
                reaction['emoji'],
                style: const TextStyle(fontSize: 12),
              ),
            );
          }),
          const SizedBox(width: 4),
          Text(
            '${topReactions.fold<int>(0, (sum, entry) => sum + entry.value)}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(
        left: widget.isReply ? 40 : 0,
        bottom: 8,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile picture
              CircleAvatar(
                radius: widget.isReply ? 14 : 16,
                backgroundColor: Colors.grey[300],
                backgroundImage: widget.comment.userProfileImageUrl != null
                    ? NetworkImage(widget.comment.userProfileImageUrl!)
                    : null,
                child: widget.comment.userProfileImageUrl == null
                    ? Text(
                        widget.comment.userName[0].toUpperCase(),
                        style: TextStyle(
                          fontSize: widget.isReply ? 10 : 12,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              const SizedBox(width: 8),
              
              // Comment bubble
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(18),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // User name with badges
                          Row(
                            children: [
                              Text(
                                widget.comment.userName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 13,
                                ),
                              ),
                              if (widget.comment.isPinned) ...[
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.push_pin,
                                  size: 12,
                                  color: Colors.orange[600],
                                ),
                              ],
                              if (widget.comment.isEdited) ...[
                                const SizedBox(width: 4),
                                Text(
                                  '• edited',
                                  style: TextStyle(
                                    fontSize: 11,
                                    color: Colors.grey[600],
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 2),
                          
                          // Comment content
                          if (!_isEditing) ...[
                            Text(
                              widget.comment.content,
                              style: const TextStyle(fontSize: 14),
                            ),
                            
                            // Comment images
                            if (widget.comment.imageUrls.isNotEmpty) ...[
                              const SizedBox(height: 8),
                              Wrap(
                                spacing: 4,
                                runSpacing: 4,
                                children: widget.comment.imageUrls.take(3).map((imageUrl) {
                                  return ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.file(
                                      File(imageUrl),
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                    ),
                                  );
                                }).toList(),
                              ),
                            ],
                          ] else ...[
                            // Edit mode
                            TextField(
                              controller: _editController,
                              decoration: const InputDecoration(
                                border: InputBorder.none,
                                isDense: true,
                              ),
                              style: const TextStyle(fontSize: 14),
                              maxLines: null,
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // Reaction display
                    _buildReactionDisplay(),
                    
                    // Action buttons
                    Padding(
                      padding: const EdgeInsets.only(left: 12, top: 4),
                      child: Row(
                        children: [
                          Text(
                            _formatTimeAgo(widget.comment.createdAt),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 16),
                          
                          // Like button with long press for reactions
                          GestureDetector(
                            onTap: widget.onLike,
                            onLongPress: _toggleReactionPicker,
                            child: Text(
                              'Like',
                              style: TextStyle(
                                fontSize: 12,
                                color: widget.comment.isLikedByCurrentUser 
                                    ? Colors.blue 
                                    : Colors.grey[600],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          
                          if (!widget.isReply) ...[
                            const SizedBox(width: 16),
                            GestureDetector(
                              onTap: _toggleReplyInput,
                              child: Text(
                                'Reply',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                          
                          // Edit/Delete for own comments
                          if (widget.comment.userId == widget.currentUser.id) ...[
                            const SizedBox(width: 16),
                            GestureDetector(
                              onTap: () => setState(() => _isEditing = !_isEditing),
                              child: Text(
                                _isEditing ? 'Save' : 'Edit',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    
                    // Reaction picker
                    if (_showReactionPicker)
                      AnimatedBuilder(
                        animation: _reactionAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _reactionAnimation.value,
                            child: Container(
                              margin: const EdgeInsets.only(top: 8, left: 12),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(25),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 8,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: _reactions.map((reaction) {
                                  return GestureDetector(
                                    onTap: () => _addReaction(reaction['name']),
                                    child: Container(
                                      padding: const EdgeInsets.all(6),
                                      child: Text(
                                        reaction['emoji'],
                                        style: const TextStyle(fontSize: 20),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          // Reply input
          if (_showReplyInput && !widget.isReply)
            Container(
              margin: const EdgeInsets.only(left: 40, top: 8),
              child: Column(
                children: [
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 14,
                        backgroundColor: Colors.grey[300],
                        backgroundImage: widget.currentUser.profileImageUrl != null
                            ? NetworkImage(widget.currentUser.profileImageUrl!)
                            : null,
                        child: widget.currentUser.profileImageUrl == null
                            ? Text(
                                widget.currentUser.fullName[0].toUpperCase(),
                                style: const TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: TextField(
                                  controller: _replyController,
                                  decoration: const InputDecoration(
                                    hintText: 'Write a reply...',
                                    border: InputBorder.none,
                                    isDense: true,
                                  ),
                                  style: const TextStyle(fontSize: 14),
                                  maxLines: null,
                                ),
                              ),
                              IconButton(
                                onPressed: _pickReplyImage,
                                icon: const Icon(
                                  Icons.image,
                                  size: 20,
                                  color: Colors.grey,
                                ),
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                              IconButton(
                                onPressed: _submitReply,
                                icon: const Icon(
                                  Icons.send,
                                  size: 20,
                                  color: AppTheme.primaryColor,
                                ),
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  // Reply images preview
                  if (_replyImages.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(left: 30, top: 8),
                      height: 60,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: _replyImages.length,
                        itemBuilder: (context, index) {
                          return Container(
                            margin: const EdgeInsets.only(right: 8),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.file(
                                    File(_replyImages[index].path),
                                    width: 60,
                                    height: 60,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  top: 2,
                                  right: 2,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() => _replyImages.removeAt(index));
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: const BoxDecoration(
                                        color: Colors.red,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 12,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          
          // Show replies
          if (widget.showReplies && widget.comment.replies.isNotEmpty)
            Column(
              children: widget.comment.replies.map((reply) {
                return FacebookStyleComment(
                  comment: reply,
                  currentUser: widget.currentUser,
                  isReply: true,
                  onLike: () {
                    // Handle reply like
                  },
                  onReaction: (reaction) {
                    // Handle reply reaction
                  },
                  showReplies: false, // Don't show nested replies for now
                );
              }).toList(),
            ),
        ],
      ),
    );
  }
}
