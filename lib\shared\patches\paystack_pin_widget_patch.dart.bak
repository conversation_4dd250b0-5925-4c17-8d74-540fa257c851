import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';

/// A custom implementation of the Paystack PIN widget
///
/// This is a replacement for the Paystack PIN input widget to ensure
/// compatibility with the latest Flutter version
class CustomPinWidget extends StatefulWidget {
  final Function(String) onPinComplete;

  const CustomPinWidget({
    super.key,
    required this.onPinComplete,
  });

  @override
  State<CustomPinWidget> createState() => _CustomPinWidgetState();
}

class _CustomPinWidgetState extends State<CustomPinWidget> {
  final TextEditingController _pinController = TextEditingController();
  final FocusNode _pinFocusNode = FocusNode();
  int _pinLength = 0;

  @override
  void initState() {
    super.initState();
    _pinController.addListener(() {
      setState(() {
        _pinLength = _pinController.text.length;
      });
    });
    
    // Auto-focus the field when the dialog opens
    Future.delayed(const Duration(milliseconds: 100), () {
      _pinFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _pinController.dispose();
    _pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Enter Card PIN',
              style: context.textTheme().titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            
            // PIN dots indicator
            PinWidget(count: _pinLength),
            const SizedBox(height: 20),
            
            // Hidden text field for PIN input
            TextField(
              controller: _pinController,
              focusNode: _pinFocusNode,
              keyboardType: TextInputType.number,
              maxLength: 4,
              obscureText: true,
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                counterText: '',
                border: InputBorder.none,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
              ],
              onChanged: (value) {
                // When 4 digits are entered, call the callback
                if (value.length == 4) {
                  widget.onPinComplete(value);
                }
              },
            ),
            
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(color: Colors.red[700]),
                  ),
                ),
                ElevatedButton(
                  onPressed: _pinLength == 4
                      ? () => widget.onPinComplete(_pinController.text)
                      : null,
                  child: const Text('Submit'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
    );
  }
}

/// Factory to create a PinWidget with the correct count
/// This is used to patch the flutter_paystack package
class PinWidgetFactory {
  /// Create a PinWidget with the correct count
  static Widget create() {
    return const PinWidget(count: 0);
  }
}
