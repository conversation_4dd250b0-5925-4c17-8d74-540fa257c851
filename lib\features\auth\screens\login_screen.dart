import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';
import 'package:fit_4_force/features/onboarding/services/onboarding_service.dart';
import 'package:fit_4_force/features/auth/widgets/device_limit_dialog.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _obscurePassword = true;
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      context.read<AuthBloc>().add(
        SignInEvent(
          email: _emailController.text.trim(),
          password: _passwordController.text,
        ),
      );

      // Reset loading state after a delay
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
  }

  void _showForgotPasswordDialog() {
    final resetEmailController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Reset Password'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Enter your email to receive a password reset link.'),
              const SizedBox(height: 16),
              TextField(
                controller: resetEmailController,
                keyboardType: TextInputType.emailAddress,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email_outlined),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                final email = resetEmailController.text.trim();
                if (email.isNotEmpty && email.contains('@')) {
                  context.read<AuthBloc>().add(
                    ResetPasswordEvent(email: email),
                  );
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'If the email exists, a reset link will be sent.',
                      ),
                    ),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a valid email.'),
                    ),
                  );
                }
              },
              child: const Text('Send Reset Link'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is AuthError) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(state.message)));
          } else if (state is Authenticated) {
            // Mark app as used for returning users (but don't complete onboarding)
            OnboardingService.markAppAsUsed();
            if (mounted) {
              Navigator.of(context).pushReplacementNamed('/home');
            }
          } else if (state is DeviceLimitExceeded) {
            // Show device limit dialog
            showDialog(
              context: context,
              barrierDismissible: false,
              builder:
                  (context) => DeviceLimitDialog(
                    activeDevices: state.activeDevices,
                    message: state.message,
                  ),
            );
          } else if (state is Unauthenticated) {
            // Optionally show a message after reset
            // ScaffoldMessenger.of(context).showSnackBar(
            //   const SnackBar(content: Text('Check your email for reset instructions.')),
            // );
          }
        },
        builder: (context, state) {
          final isDesktop = ResponsiveUtils.isDesktop(context);
          final padding = ResponsiveUtils.getResponsivePadding(context);
          final spacing = ResponsiveUtils.getResponsiveSpacing(context);

          return SafeArea(
            child: Center(
              child: SingleChildScrollView(
                padding: padding,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 400 : double.infinity,
                  ),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Fit4Force Logo
                        Center(
                          child: Container(
                            margin: EdgeInsets.only(bottom: spacing),
                            child: Image.asset(
                              'assets/images/fit4force_main_logo.png',
                              width: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                mobile: 100,
                                tablet: 110,
                                desktop: 120,
                              ),
                              height: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                mobile: 100,
                                tablet: 110,
                                desktop: 120,
                              ),
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) {
                                // Fallback to simple icon if logo not found
                                return Icon(
                                  Icons.fitness_center,
                                  size: ResponsiveUtils.getResponsiveFontSize(
                                    context,
                                    mobile: 50,
                                    tablet: 55,
                                    desktop: 60,
                                  ),
                                  color: AppTheme.primaryColor,
                                );
                              },
                            ),
                          ),
                        ),

                        SizedBox(height: spacing),
                        ResponsiveText(
                          'Welcome Back',
                          mobileFontSize: 24.0,
                          tabletFontSize: 28.0,
                          desktopFontSize: 32.0,
                          fontWeight: FontWeight.bold,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: spacing / 3),
                        ResponsiveText(
                          'Sign in to continue your fitness journey',
                          mobileFontSize: 14.0,
                          tabletFontSize: 16.0,
                          desktopFontSize: 18.0,
                          color: Colors.grey[600],
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: spacing * 1.5),

                        // Email Field
                        BaseTextField(
                          label: 'Email',
                          hint: 'Enter your email',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          prefix: const Icon(Icons.email_outlined),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email';
                            }
                            if (!value.contains('@')) {
                              return 'Please enter a valid email';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: spacing),

                        // Password Field
                        BaseTextField(
                          label: 'Password',
                          hint: 'Enter your password',
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          prefix: const Icon(Icons.lock_outline),
                          suffix: IconButton(
                            icon: Icon(
                              _obscurePassword
                                  ? Icons.visibility_off
                                  : Icons.visibility,
                            ),
                            onPressed: () {
                              setState(() {
                                _obscurePassword = !_obscurePassword;
                              });
                            },
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: spacing / 2),

                        // Forgot Password
                        Align(
                          alignment: Alignment.centerRight,
                          child: TextButton(
                            onPressed: _showForgotPasswordDialog,
                            child: ResponsiveText(
                              'Forgot Password?',
                              mobileFontSize: 14.0,
                              tabletFontSize: 15.0,
                              desktopFontSize: 16.0,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                        SizedBox(height: spacing),

                        // Login Button
                        ResponsiveButton(
                          text: 'Sign In',
                          backgroundColor: AppTheme.primaryColor,
                          textColor: AppTheme.textOnPrimary,
                          onPressed:
                              (_isLoading || state is AuthLoading)
                                  ? null
                                  : _handleLogin,
                          mobileHeight: 48.0,
                          tabletHeight: 52.0,
                          desktopHeight: 56.0,
                        ),
                        SizedBox(height: spacing),

                        // Sign Up Link
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            ResponsiveText(
                              "Don't have an account?",
                              mobileFontSize: 14.0,
                              tabletFontSize: 15.0,
                              desktopFontSize: 16.0,
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pushNamed('/signup');
                              },
                              child: ResponsiveText(
                                'Sign Up',
                                mobileFontSize: 14.0,
                                tabletFontSize: 15.0,
                                desktopFontSize: 16.0,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
