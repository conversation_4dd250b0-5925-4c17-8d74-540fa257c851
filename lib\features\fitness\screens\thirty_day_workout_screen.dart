import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/services/thirty_day_workout_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/premium_access_gate.dart';

class ThirtyDayWorkoutScreen extends StatefulWidget {
  final UserModel user;

  const ThirtyDayWorkoutScreen({super.key, required this.user});

  @override
  State<ThirtyDayWorkoutScreen> createState() => _ThirtyDayWorkoutScreenState();
}

class _ThirtyDayWorkoutScreenState extends State<ThirtyDayWorkoutScreen>
    with TickerProviderStateMixin {
  final ThirtyDayWorkoutService _workoutService = ThirtyDayWorkoutService();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isProgramActive = false;
  int _currentDay = 1;
  double _progressPercentage = 0.0;
  List<int> _completedDays = [];
  Map<String, dynamic>? _programSettings;
  List<Map<String, dynamic>> _todaysWorkout = [];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _loadProgramData();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  Future<void> _loadProgramData() async {
    final isProgramActive = await _workoutService.isProgramActive();

    if (isProgramActive) {
      final currentDay = await _workoutService.getCurrentDay();
      final progress = await _workoutService.getProgressPercentage();
      final completedDays = await _workoutService.getCompletedDays();
      final settings = await _workoutService.getProgramSettings();
      final todaysWorkout = await _workoutService.generateTodaysWorkout();

      setState(() {
        _isProgramActive = true;
        _currentDay = currentDay;
        _progressPercentage = progress;
        _completedDays = completedDays;
        _programSettings = settings;
        _todaysWorkout = todaysWorkout;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: PremiumAccessGate(
        user: widget.user,
        featureName: '30-Day Challenge',
        upgradeMessage:
            'The 30-Day Challenge provides personalized daily workouts, progress tracking, and achievement rewards. Upgrade to Premium to unlock this feature!',
        child: CustomScrollView(
          slivers: [
            _buildAppBar(),
            SliverToBoxAdapter(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (!_isProgramActive) ...[
                          _buildWelcomeCard(),
                          const SizedBox(height: 20),
                          _buildProgramSetup(),
                        ] else ...[
                          _buildProgressCard(),
                          const SizedBox(height: 20),
                          _buildTodaysWorkoutCard(),
                          const SizedBox(height: 20),
                          _buildCalendarView(),
                          const SizedBox(height: 20),
                          _buildQuickActions(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      pinned: true,
      backgroundColor: Theme.of(context).primaryColor,
      flexibleSpace: FlexibleSpaceBar(
        title: const Text(
          '30-Day Challenge',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).primaryColor,
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      actions: [
        if (_isProgramActive)
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onSelected: (value) {
              switch (value) {
                case 'reset':
                  _showResetDialog();
                  break;
                case 'settings':
                  _showSettingsDialog();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'settings',
                    child: Text('Program Settings'),
                  ),
                  const PopupMenuItem(
                    value: 'reset',
                    child: Text('Reset Program'),
                  ),
                ],
          ),
      ],
    );
  }

  Widget _buildWelcomeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.9 * 255),
            Theme.of(context).primaryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3 * 255),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.emoji_events, color: Colors.white, size: 32),
              SizedBox(width: 12),
              Text(
                'Transform in 30 Days!',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Start your fitness journey with our personalized 30-day workout program. Choose your level and duration to get daily shuffled workouts tailored just for you.',
            style: TextStyle(color: Colors.white, fontSize: 16, height: 1.5),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildFeatureItem('🎯', 'Personalized'),
              _buildFeatureItem('🔀', 'Daily Shuffle'),
              _buildFeatureItem('📈', 'Progressive'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem(String emoji, String text) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15 * 255),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(emoji, style: const TextStyle(fontSize: 20)),
            const SizedBox(height: 4),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgramSetup() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Setup Your Program',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildLevelSelection(),
        const SizedBox(height: 20),
        _buildDurationSelection(),
        const SizedBox(height: 30),
        _buildStartButton(),
      ],
    );
  }

  Widget _buildLevelSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Fitness Level',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ...WorkoutLevel.values.map((level) {
            return _buildLevelOption(level);
          }),
        ],
      ),
    );
  }

  Widget _buildLevelOption(WorkoutLevel level) {
    final descriptions = {
      WorkoutLevel.beginner: 'Perfect for starting your fitness journey',
      WorkoutLevel.intermediate: 'Ready to challenge yourself more',
      WorkoutLevel.advanced: 'Push your limits with intense workouts',
    };

    final colors = {
      WorkoutLevel.beginner: Colors.green,
      WorkoutLevel.intermediate: Colors.orange,
      WorkoutLevel.advanced: Colors.red,
    };

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => setState(() => _selectedLevel = level),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  _selectedLevel == level ? colors[level]! : Colors.grey[300]!,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(12),
            color:
                _selectedLevel == level
                    ? colors[level]!.withValues(alpha: 0.1 * 255)
                    : Colors.transparent,
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color:
                      _selectedLevel == level
                          ? colors[level]
                          : Colors.transparent,
                  border: Border.all(color: colors[level]!, width: 2),
                ),
                child:
                    _selectedLevel == level
                        ? const Icon(Icons.check, size: 12, color: Colors.white)
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      level.name.toUpperCase(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: colors[level],
                      ),
                    ),
                    Text(
                      descriptions[level]!,
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDurationSelection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Workout Duration',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 2.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: WorkoutDuration.values.length,
            itemBuilder: (context, index) {
              final duration = WorkoutDuration.values[index];
              return _buildDurationOption(duration);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDurationOption(WorkoutDuration duration) {
    final isSelected = _selectedDuration == duration;

    return InkWell(
      onTap: () => setState(() => _selectedDuration = duration),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color:
                isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12),
          color:
              isSelected
                  ? Theme.of(context).primaryColor.withValues(alpha: 0.1 * 255)
                  : Colors.transparent,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${duration.minutes} min',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color:
                    isSelected ? Theme.of(context).primaryColor : Colors.black,
              ),
            ),
            Text(
              duration.name.toUpperCase(),
              style: TextStyle(
                fontSize: 10,
                color:
                    isSelected
                        ? Theme.of(context).primaryColor
                        : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStartButton() {
    final canStart = _selectedLevel != null && _selectedDuration != null;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: canStart ? _startProgram : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 4,
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.rocket_launch, size: 24),
            SizedBox(width: 12),
            Text(
              'Start 30-Day Challenge',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.9 * 255),
            Theme.of(context).primaryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3 * 255),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Day $_currentDay of 30',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${(_progressPercentage * 100).round()}% Complete',
                    style: const TextStyle(color: Colors.white70, fontSize: 16),
                  ),
                ],
              ),
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2 * 255),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    '${_completedDays.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ClipRRect(
            borderRadius: BorderRadius.circular(10),
            child: LinearProgressIndicator(
              value: _progressPercentage,
              minHeight: 8,
              backgroundColor: Colors.white.withValues(alpha: 0.3 * 255),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTodaysWorkoutCard() {
    // Check if today is a rest day
    if (_workoutService.isRestDay(_currentDay)) {
      return _buildRestDayCard();
    }

    if (_todaysWorkout.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05 * 255),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: const Center(child: Text('Loading today\'s workout...')),
      );
    }

    final estimatedCalories = _workoutService.getEstimatedCalories(
      _todaysWorkout,
    );
    final totalDuration = _programSettings!['duration'].minutes;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade400, Colors.orange.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.today, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Today\'s Workout',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${_todaysWorkout.length} exercises • $totalDuration min • ~$estimatedCalories cal',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: IconButton(
                    icon: Icon(
                      Icons.play_arrow,
                      color: Colors.orange.shade600,
                      size: 28,
                    ),
                    onPressed: _startTodaysWorkout,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children:
                  _todaysWorkout.take(3).map((exercise) {
                    return _buildExercisePreview(exercise);
                  }).toList(),
            ),
          ),
          if (_todaysWorkout.length > 3)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: Text(
                '+ ${_todaysWorkout.length - 3} more exercises',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildExercisePreview(Map<String, dynamic> exercise) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              exercise['name'],
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Text(
            exercise['duration'] > 0
                ? '${exercise['duration']}s'
                : '${exercise['reps']} × ${exercise['sets']}',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildCalendarView() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Progress Calendar',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: 30,
            itemBuilder: (context, index) {
              final day = index + 1;
              final isCompleted = _completedDays.contains(day);
              final isCurrent = day == _currentDay;
              final isPast = day < _currentDay;
              final isRestDay = _workoutService.isRestDay(day);

              Color backgroundColor;
              Color textColor;
              IconData? icon;

              if (isCompleted) {
                backgroundColor =
                    isRestDay ? Colors.green.shade300 : Colors.green;
                textColor = Colors.white;
                icon = isRestDay ? Icons.spa : Icons.check;
              } else if (isCurrent) {
                backgroundColor =
                    isRestDay
                        ? Colors.green.shade600
                        : Theme.of(context).primaryColor;
                textColor = Colors.white;
                icon = isRestDay ? Icons.spa : null;
              } else if (isPast) {
                backgroundColor = Colors.grey[300]!;
                textColor = Colors.black54;
              } else {
                backgroundColor =
                    isRestDay ? Colors.green.shade50 : Colors.grey[100]!;
                textColor = isRestDay ? Colors.green.shade700 : Colors.black;
                if (isRestDay) icon = Icons.spa;
              }

              return Container(
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                  border:
                      isRestDay
                          ? Border.all(color: Colors.green.shade200, width: 1)
                          : null,
                ),
                child: Center(
                  child:
                      icon != null
                          ? Icon(icon, color: textColor, size: 16)
                          : Text(
                            '$day',
                            style: TextStyle(
                              color: textColor,
                              fontWeight:
                                  isCurrent
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              fontSize: 12,
                            ),
                          ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildLegendItem(Colors.green, 'Completed'),
              const SizedBox(width: 12),
              _buildLegendItem(Colors.green.shade300, 'Rest Day', Icons.spa),
              const SizedBox(width: 12),
              _buildLegendItem(Theme.of(context).primaryColor, 'Current'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label, [IconData? icon]) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
          child: icon != null ? Icon(icon, color: Colors.white, size: 8) : null,
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 10, color: Colors.grey)),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Quick Actions',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  'View History',
                  Icons.history,
                  Colors.blue,
                  () => _showWorkoutHistory(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildActionButton(
                  'Statistics',
                  Icons.bar_chart,
                  Colors.purple,
                  () => _showStatistics(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withValues(alpha: 0.1 * 255),
        foregroundColor: color,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          children: [
            Icon(icon, size: 24),
            const SizedBox(height: 4),
            Text(label, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  // State variables for selection
  WorkoutLevel? _selectedLevel;
  WorkoutDuration? _selectedDuration;

  void _startProgram() async {
    if (_selectedLevel == null || _selectedDuration == null) return;

    await _workoutService.startProgram(
      level: _selectedLevel!,
      duration: _selectedDuration!,
    );

    await _loadProgramData();
  }

  void _startTodaysWorkout() {
    // Navigate to workout session with today's exercises
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => _WorkoutSessionSheet(
            exercises: _todaysWorkout,
            onCompleted: () {
              _workoutService.completeDayWorkout(_currentDay);
              _loadProgramData();
            },
          ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Reset Program'),
            content: const Text(
              'Are you sure you want to reset your 30-day program? This will delete all your progress.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await _workoutService.resetProgram();
                  setState(() {
                    _isProgramActive = false;
                    _currentDay = 1;
                    _progressPercentage = 0.0;
                    _completedDays = [];
                    _programSettings = null;
                    _todaysWorkout = [];
                  });
                },
                child: const Text('Reset'),
              ),
            ],
          ),
    );
  }

  void _showSettingsDialog() {
    // Show program settings
  }

  void _showWorkoutHistory() {
    // Show completed workouts history
  }

  void _showStatistics() {
    // Show program statistics
  }

  Widget _buildRestDayCard() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05 * 255),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                const Icon(Icons.spa, color: Colors.white, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Rest Day $_currentDay',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Time to recover and recharge',
                        style: TextStyle(color: Colors.white70, fontSize: 14),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.self_improvement,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Recovery Activities',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                _buildRecoveryActivity(
                  '💆‍♀️',
                  'Gentle Stretching',
                  '10-15 minutes',
                ),
                const SizedBox(height: 12),
                _buildRecoveryActivity(
                  '🚶‍♀️',
                  'Light Walking',
                  '20-30 minutes',
                ),
                const SizedBox(height: 12),
                _buildRecoveryActivity('🧘‍♀️', 'Meditation', '5-10 minutes'),
                const SizedBox(height: 12),
                _buildRecoveryActivity('💤', 'Extra Sleep', '8+ hours'),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _markRestDayComplete,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Mark Rest Day Complete',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecoveryActivity(String emoji, String title, String duration) {
    return Row(
      children: [
        Text(emoji, style: const TextStyle(fontSize: 20)),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            title,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
        ),
        Text(duration, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  void _markRestDayComplete() async {
    await _workoutService.completeDayWorkout(_currentDay);
    await _loadProgramData();

    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text(
          'Rest day completed! Great job taking care of your recovery.',
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

// Workout session bottom sheet
class _WorkoutSessionSheet extends StatefulWidget {
  final List<Map<String, dynamic>> exercises;
  final VoidCallback onCompleted;

  const _WorkoutSessionSheet({
    required this.exercises,
    required this.onCompleted,
  });

  @override
  State<_WorkoutSessionSheet> createState() => _WorkoutSessionSheetState();
}

class _WorkoutSessionSheetState extends State<_WorkoutSessionSheet> {
  int _currentExerciseIndex = 0;
  bool _isResting = false;
  int _restTimeRemaining = 30; // 30 seconds rest

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Exercise ${_currentExerciseIndex + 1} of ${widget.exercises.length}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      LinearProgressIndicator(
                        value:
                            (_currentExerciseIndex + 1) /
                            widget.exercises.length,
                        backgroundColor: Colors.grey[200],
                        valueColor: AlwaysStoppedAnimation(
                          Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),

          // Exercise content
          Expanded(
            child: _isResting ? _buildRestScreen() : _buildExerciseScreen(),
          ),

          // Controls
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                if (_currentExerciseIndex > 0)
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _previousExercise,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey[600],
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Previous'),
                    ),
                  ),
                if (_currentExerciseIndex > 0) const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _nextExercise,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(
                      _currentExerciseIndex == widget.exercises.length - 1
                          ? 'Complete Workout'
                          : _isResting
                          ? 'Skip Rest'
                          : 'Next Exercise',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExerciseScreen() {
    final exercise = widget.exercises[_currentExerciseIndex];

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Exercise image/video area
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(16),
            ),
            child:
                exercise['imageUrl'] != null
                    ? Image.asset(
                      exercise['imageUrl'],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return const Center(
                          child: Icon(Icons.fitness_center, size: 64),
                        );
                      },
                    )
                    : const Center(child: Icon(Icons.fitness_center, size: 64)),
          ),

          const SizedBox(height: 20),

          // Exercise name
          Text(
            exercise['name'],
            style: Theme.of(
              context,
            ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // Exercise details
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (exercise['duration'] > 0)
                _buildStatChip('${exercise['duration']}s', 'Duration'),
              if (exercise['reps'] > 0)
                _buildStatChip('${exercise['reps']}', 'Reps'),
              if (exercise['sets'] > 0)
                _buildStatChip('${exercise['sets']}', 'Sets'),
            ],
          ),

          const SizedBox(height: 20),

          // Instructions
          if (exercise['instructions'] != null)
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    exercise['instructions'],
                    style: const TextStyle(fontSize: 16, height: 1.5),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildRestScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.timer, size: 64, color: Colors.orange),
          const SizedBox(height: 20),
          Text(
            'Rest Time',
            style: Theme.of(
              context,
            ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Text(
            '$_restTimeRemaining',
            style: Theme.of(context).textTheme.displayLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          const Text('seconds'),
        ],
      ),
    );
  }

  Widget _buildStatChip(String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  void _nextExercise() {
    if (_isResting) {
      setState(() {
        _isResting = false;
        _currentExerciseIndex++;
      });
    } else if (_currentExerciseIndex == widget.exercises.length - 1) {
      // Complete workout
      widget.onCompleted();
      Navigator.pop(context);
      _showCompletionDialog();
    } else {
      // Start rest period
      setState(() {
        _isResting = true;
      });
      _startRestTimer();
    }
  }

  void _previousExercise() {
    if (_currentExerciseIndex > 0) {
      setState(() {
        _currentExerciseIndex--;
        _isResting = false;
      });
    }
  }

  void _startRestTimer() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted || !_isResting) return false;

      setState(() {
        _restTimeRemaining--;
      });

      if (_restTimeRemaining <= 0) {
        _nextExercise();
        return false;
      }

      return true;
    });
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('🎉 Workout Complete!'),
            content: const Text(
              'Congratulations! You\'ve completed today\'s workout. Keep up the great work!',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Awesome!'),
              ),
            ],
          ),
    );
  }
}
