/// Configuration for official news sources for each agency
/// This file contains the official websites and RSS feeds for fetching real-time news
library;

class NewsSourcesConfig {
  /// Official websites and RSS feeds for each agency
  static const Map<String, AgencyNewsSource> agencySources = {
    'Nigerian Army': AgencyNewsSource(
      officialWebsite: 'https://www.army.mil.ng',
      newsSection: 'https://www.army.mil.ng/news',
      recruitmentPage: 'https://www.army.mil.ng/recruitment',
      rssFeeds: [
        'https://www.army.mil.ng/rss/news',
        'https://www.army.mil.ng/rss/recruitment',
      ],
      socialMedia: {
        'twitter': '@HQNigerianArmy',
        'facebook': 'HQNigerianArmy',
        'instagram': '@hqnigerianarrmy',
      },
      keywordFilters: [
        'recruitment',
        'training',
        'exercise',
        'promotion',
        'course',
        'application',
        'interview',
        'enlistment',
        '84rri',
        'depot',
      ],
    ),
    'Nigerian Navy': AgencyNewsSource(
      officialWebsite: 'https://www.navy.mil.ng',
      newsSection: 'https://www.navy.mil.ng/news',
      recruitmentPage: 'https://www.navy.mil.ng/recruitment',
      rssFeeds: [
        'https://www.navy.mil.ng/rss/news',
        'https://www.navy.mil.ng/rss/recruitment',
      ],
      socialMedia: {
        'twitter': '@NigerianNavy',
        'facebook': 'NigerianNavy',
        'instagram': '@nigeriannavy',
      },
      keywordFilters: [
        'recruitment',
        'maritime',
        'naval',
        'training',
        'exercise',
        'course',
        'application',
        'interview',
        'enlistment',
        'rating',
        'officer',
      ],
    ),
    'Nigerian Air Force': AgencyNewsSource(
      officialWebsite: 'https://www.airforce.mil.ng',
      newsSection: 'https://www.airforce.mil.ng/news',
      recruitmentPage: 'https://www.airforce.mil.ng/recruitment',
      rssFeeds: [
        'https://www.airforce.mil.ng/rss/news',
        'https://www.airforce.mil.ng/rss/recruitment',
      ],
      socialMedia: {
        'twitter': '@NigAirForce',
        'facebook': 'NigerianAirForce',
        'instagram': '@nigerianairforce',
      },
      keywordFilters: [
        'recruitment',
        'aviation',
        'airmen',
        'pilot',
        'training',
        'course',
        'application',
        'interview',
        'enlistment',
        'academy',
      ],
    ),
    'NDA': AgencyNewsSource(
      officialWebsite: 'https://www.nda.edu.ng',
      newsSection: 'https://www.nda.edu.ng/news',
      recruitmentPage: 'https://www.nda.edu.ng/admission',
      rssFeeds: [
        'https://www.nda.edu.ng/rss/news',
        'https://www.nda.edu.ng/rss/admission',
      ],
      socialMedia: {
        'twitter': '@NDAKaduna',
        'facebook': 'NDAKaduna',
        'instagram': '@ndakaduna',
      },
      keywordFilters: [
        'admission',
        'academy',
        'cadet',
        'training',
        'course',
        'application',
        'interview',
        'selection',
        'exercise',
        'graduation',
      ],
    ),
    'DSSC': AgencyNewsSource(
      officialWebsite: 'https://www.dssc.mil.ng',
      newsSection: 'https://www.dssc.mil.ng/news',
      recruitmentPage: 'https://www.dssc.mil.ng/recruitment',
      rssFeeds: [
        'https://www.dssc.mil.ng/rss/news',
        'https://www.dssc.mil.ng/rss/recruitment',
      ],
      socialMedia: {'twitter': '@DSSC_Nigeria', 'facebook': 'DSS.Nigeria'},
      keywordFilters: [
        'recruitment',
        'security',
        'intelligence',
        'training',
        'course',
        'application',
        'interview',
        'selection',
        'academy',
      ],
    ),
    'NSCDC': AgencyNewsSource(
      officialWebsite: 'https://www.nscdc.gov.ng',
      newsSection: 'https://www.nscdc.gov.ng/news',
      recruitmentPage: 'https://www.nscdc.gov.ng/recruitment',
      rssFeeds: [
        'https://www.nscdc.gov.ng/rss/news',
        'https://www.nscdc.gov.ng/rss/recruitment',
      ],
      socialMedia: {
        'twitter': '@nscdc_ng',
        'facebook': 'NSCDC',
        'instagram': '@nscdc_ng',
      },
      keywordFilters: [
        'recruitment',
        'civil defence',
        'security',
        'training',
        'course',
        'application',
        'interview',
        'enlistment',
        'commandant',
      ],
    ),
    'Fire Service': AgencyNewsSource(
      officialWebsite: 'https://www.federalfireservice.gov.ng',
      newsSection: 'https://www.federalfireservice.gov.ng/news',
      recruitmentPage: 'https://www.federalfireservice.gov.ng/recruitment',
      rssFeeds: [
        'https://www.federalfireservice.gov.ng/rss/news',
        'https://www.federalfireservice.gov.ng/rss/recruitment',
      ],
      socialMedia: {
        'twitter': '@FedFireService',
        'facebook': 'FederalFireServiceNigeria',
      },
      keywordFilters: [
        'recruitment',
        'fire service',
        'emergency',
        'training',
        'course',
        'application',
        'interview',
        'firefighter',
        'rescue',
      ],
    ),
  };

  /// Alternative news sources for broader coverage
  static const Map<String, List<String>> alternativeNewsSources = {
    'general_military': [
      'https://punchng.com/topics/nigerian-military/',
      'https://www.vanguardngr.com/tag/nigerian-military/',
      'https://guardian.ng/category/news/military/',
      'https://dailytrust.com/category/military',
      'https://www.premiumtimesng.com/tag/military',
    ],
    'recruitment_specific': [
      'https://www.naijaloaded.com.ng/news/category/recruitment',
      'https://www.legit.ng/tag/recruitment.html',
      'https://naijaquest.com/category/recruitment/',
    ],
    'government_portals': [
      'https://www.gov.ng/news',
      'https://statehouse.gov.ng/news/',
    ],
  };

  /// API keys for news services (to be configured)
  static const Map<String, String> newsApiConfig = {
    'newsapi_key': 'YOUR_NEWS_API_KEY', // Get from newsapi.org
    'google_news_api_key': 'YOUR_GOOGLE_NEWS_API_KEY',
    'bing_news_api_key': 'YOUR_BING_NEWS_API_KEY',
  };

  /// Get news source configuration for a specific agency
  static AgencyNewsSource? getAgencySource(String agency) {
    return agencySources[agency];
  }

  /// Get all supported agencies
  static List<String> getSupportedAgencies() {
    return agencySources.keys.toList();
  }

  /// Check if agency has RSS feed support
  static bool hasRssFeed(String agency) {
    final source = agencySources[agency];
    return source?.rssFeeds.isNotEmpty ?? false;
  }

  /// Get social media handles for an agency
  static Map<String, String>? getSocialMediaHandles(String agency) {
    return agencySources[agency]?.socialMedia;
  }

  /// Get search keywords for agency-specific news filtering
  static List<String> getAgencyKeywords(String agency) {
    return agencySources[agency]?.keywordFilters ?? [];
  }
}

/// Model for agency news source configuration
class AgencyNewsSource {
  final String officialWebsite;
  final String newsSection;
  final String recruitmentPage;
  final List<String> rssFeeds;
  final Map<String, String> socialMedia;
  final List<String> keywordFilters;

  const AgencyNewsSource({
    required this.officialWebsite,
    required this.newsSection,
    required this.recruitmentPage,
    required this.rssFeeds,
    required this.socialMedia,
    required this.keywordFilters,
  });
}

/// News fetching strategies
enum NewsFetchStrategy {
  rssOnly, // Use only RSS feeds
  webScraping, // Scrape official websites
  newsApi, // Use news APIs with keyword filtering
  socialMedia, // Monitor social media accounts
  hybrid, // Combine multiple strategies
}

/// Configuration for news fetching behavior
class NewsFetchConfig {
  static const NewsFetchStrategy defaultStrategy = NewsFetchStrategy.hybrid;
  static const Duration refreshInterval = Duration(hours: 1);
  static const Duration cacheExpiry = Duration(hours: 6);
  static const int maxNewsPerAgency = 50;
  static const int maxNewsAge = 30; // days

  /// Reliability score for each source type
  static const Map<String, double> sourceReliability = {
    'official_website': 1.0,
    'rss_feed': 0.95,
    'news_api': 0.8,
    'social_media': 0.6,
    'alternative_source': 0.4,
  };
}
