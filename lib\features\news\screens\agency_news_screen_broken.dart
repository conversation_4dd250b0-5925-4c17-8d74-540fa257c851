import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/news/models/agency_news_model.dart';
import 'package:fit_4_force/features/news/services/agency_news_service.dart';

/// Minimal Agency News Screen to get the app running
class AgencyNewsScreen extends StatefulWidget {
  final UserModel user;

  const AgencyNewsScreen({super.key, required this.user});

  @override
  State<AgencyNewsScreen> createState() => _AgencyNewsScreenState();
}

class _AgencyNewsScreenState extends State<AgencyNewsScreen> {
  final AgencyNewsService _newsService = AgencyNewsService();
  List<AgencyNewsModel> _allNews = [];
  List<AgencyNewsModel> _filteredNews = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadNews();
  }

  Future<void> _loadNews() async {
    try {
      final news = await _newsService.getAllNews();
      setState(() {
        _allNews = news;
        _filteredNews = news;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Agency News'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _filteredNews.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.newspaper, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'No news available',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _filteredNews.length,
                  itemBuilder: (context, index) {
                    return _buildNewsCard(_filteredNews[index]);
                  },
                ),
    );
  }

  Widget _buildNewsCard(AgencyNewsModel news) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        title: Text(
          news.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(news.content),
            const SizedBox(height: 8),
            Row(
              children: [
                Chip(
                  label: Text(news.agency),
                  backgroundColor: Colors.blue.shade100,
                ),
                const SizedBox(width: 8),
                Chip(
                  label: Text(news.category),
                  backgroundColor: Colors.green.shade100,
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          // Navigate to news detail (simplified)
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Opened: ${news.title}'),
            ),
          );
        },
      ),
    );
  }
}
