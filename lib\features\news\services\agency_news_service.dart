import 'dart:async';
import 'package:fit_4_force/features/news/models/agency_news_model.dart';
import 'package:fit_4_force/features/news/services/live_news_service.dart';

/// Enhanced Agency News Service with Live News Integration
class AgencyNewsService {
  static final AgencyNewsService _instance = AgencyNewsService._internal();
  factory AgencyNewsService() => _instance;
  AgencyNewsService._internal();

  final LiveNewsService _liveNewsService = LiveNewsService();
  Timer? _refreshTimer;
  bool _isLiveMode = true;

  /// Initialize live news service
  Future<void> initializeLiveNews() async {
    _liveNewsService.clearCache(); // Clear old cache
    _startLiveNewsRefresh();
  }

  /// Start live news refresh timer
  void _startLiveNewsRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      const Duration(minutes: 10), // Refresh every 10 minutes
      (_) async {
        if (_isLiveMode) {
          await _refreshLiveNewsForAllAgencies();
        }
      },
    );
  }

  /// Refresh live news for all agencies
  Future<void> _refreshLiveNewsForAllAgencies() async {
    final agencies = [
      'Nigerian Army',
      'Nigerian Navy',
      'Nigerian Air Force',
      'NDA',
      'Nigeria Police',
      'NSCDC',
      'EFCC',
    ];

    for (final agency in agencies) {
      try {
        await _liveNewsService.fetchAgencyNews(agency);
      } catch (e) {
        print('Error fetching live news for $agency: $e');
      }
    }
  }

  /// Toggle between live and mock news
  void toggleLiveMode(bool enabled) {
    _isLiveMode = enabled;
    if (enabled) {
      _startLiveNewsRefresh();
    } else {
      _refreshTimer?.cancel();
    }
  }

  /// Get combined news (live + mock) for better user experience
  Future<List<AgencyNewsModel>> getCombinedNews({String? targetAgency}) async {
    final combinedNews = <AgencyNewsModel>[];

    // Add mock news for immediate display
    if (targetAgency != null) {
      final mockNews =
          _mockNews
              .where(
                (news) => news.agency == targetAgency || targetAgency == 'All',
              )
              .toList();
      combinedNews.addAll(mockNews);
    } else {
      combinedNews.addAll(_mockNews);
    }

    // Add live news if enabled
    if (_isLiveMode && targetAgency != null) {
      try {
        final liveNews = await _liveNewsService.fetchAgencyNews(targetAgency);

        // Mark live news with special indicator
        final markedLiveNews =
            liveNews
                .map(
                  (news) => news.copyWith(
                    source: '🔴 LIVE: ${news.source}',
                    isBreaking: true, // Mark live news as breaking
                  ),
                )
                .toList();

        combinedNews.addAll(markedLiveNews);
      } catch (e) {
        print('Error fetching live news: $e');
        // Continue with mock news only
      }
    }

    // Remove duplicates and sort by date
    final uniqueNews = _removeDuplicates(combinedNews);
    uniqueNews.sort((a, b) => b.publishedDate.compareTo(a.publishedDate));

    return uniqueNews.take(50).toList(); // Limit to 50 most recent
  }

  /// Remove duplicate news based on title similarity
  List<AgencyNewsModel> _removeDuplicates(List<AgencyNewsModel> newsList) {
    final seen = <String>{};
    return newsList.where((news) {
      final normalizedTitle =
          news.title
              .toLowerCase()
              .replaceAll(RegExp(r'[^\w\s]'), '') // Remove punctuation
              .replaceAll(RegExp(r'\s+'), ' ') // Normalize spaces
              .trim();

      // Consider titles similar if they share 80% of words
      final words = normalizedTitle.split(' ');
      final key = words.where((word) => word.length > 3).join(' ');

      if (seen.contains(key)) {
        return false;
      }
      seen.add(key);
      return true;
    }).toList();
  }

  final List<AgencyNewsModel> _mockNews = [
    // Nigerian Army News
    AgencyNewsModel(
      id: 'army_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      title:
          'Nigerian Army 85RRI Recruitment 2025 - Application Portal Now Open',
      content:
          'The Nigerian Army has commenced the recruitment exercise for 2025 Regular Recruits Intake (85RRI). Interested and qualified candidates are to apply online through the official portal. Requirements include: Age 18-22 years, WAEC/NECO with 5 credits including English and Mathematics, Medical fitness certificate, and Height requirement of 1.68m for males and 1.65m for females.',
      agency: 'Nigerian Army',
      category: 'Recruitment',
      source: 'Nigerian Army Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(hours: 2)),
      isBreaking: true,
      isPinned: true,
      priority: 'high',
      tags: ['recruitment', 'army', '85RRI', 'application'],
      applicationDeadline: DateTime.now().add(const Duration(days: 45)),
      viewsCount: 15420,
    ),
    AgencyNewsModel(
      id: 'army_2',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      title: 'Nigerian Army Physical Fitness Test Guidelines Updated',
      content:
          'New guidelines for the physical fitness test have been released. Candidates must complete: 1.6km run in under 10 minutes, 40 push-ups in 2 minutes, 45 sit-ups in 2 minutes, and pass swimming test for relevant corps. Training tips and preparation materials are available on the official website.',
      agency: 'Nigerian Army',
      category: 'Training',
      source: 'Army Training Command',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      isBreaking: false,
      isPinned: true,
      priority: 'high',
      tags: ['training', 'fitness', 'guidelines', 'physical-test'],
      viewsCount: 8934,
    ),

    // Navy News
    AgencyNewsModel(
      id: 'navy_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 4)),
      title: 'Nigerian Navy DSSC Course 30 Application Commences',
      content:
          'Applications for the Direct Short Service Commission (DSSC) Course 30 are now open for graduates. Eligible candidates must possess minimum of Second Class Lower degree from recognized universities. Positions available in Executive, Engineering, Education, Medical, and other branches. Interview dates will be communicated to shortlisted candidates.',
      agency: 'Navy',
      category: 'Recruitment',
      source: 'Naval Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(hours: 4)),
      isBreaking: true,
      isPinned: true,
      priority: 'high',
      tags: ['DSSC', 'navy', 'commission', 'graduates'],
      applicationDeadline: DateTime.now().add(const Duration(days: 60)),
      viewsCount: 12087,
    ),
    AgencyNewsModel(
      id: 'navy_2',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      title: 'Navy Swimming Test Requirements and Preparation Tips',
      content:
          'All Navy recruits must pass mandatory swimming tests. Requirements include: 100m freestyle swimming, 2 minutes treading water, and underwater swimming for 25m. Training facilities are available at Naval Training Command. Candidates are advised to practice regularly before screening dates.',
      agency: 'Navy',
      category: 'Training',
      source: 'Naval Training Command',
      publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      isBreaking: false,
      isPinned: false,
      priority: 'medium',
      tags: ['swimming', 'training', 'requirements', 'preparation'],
      viewsCount: 6543,
    ),

    // Air Force News
    AgencyNewsModel(
      id: 'airforce_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 6)),
      title: 'Nigerian Air Force DSSC 28 Selection Exercise Announced',
      content:
          'The Nigerian Air Force has announced the commencement of selection exercise for Direct Short Service Commission (DSSC) Regular Course 28. Available branches include: Pilot, Air Traffic Control, Engineering, Intelligence, Medical, Education, and Logistics. Candidates must be between 20-25 years with relevant qualifications.',
      agency: 'Air Force',
      category: 'Recruitment',
      source: 'Air Force Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(hours: 6)),
      isBreaking: true,
      isPinned: true,
      priority: 'high',
      tags: ['DSSC', 'airforce', 'selection', 'commission'],
      applicationDeadline: DateTime.now().add(const Duration(days: 50)),
      viewsCount: 9876,
    ),
    AgencyNewsModel(
      id: 'airforce_2',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      title: 'Air Force Aptitude Test Syllabus and Study Materials Released',
      content:
          'Comprehensive syllabus for Air Force aptitude test now available. Topics include: Mathematics (Algebra, Geometry, Statistics), Physics (Mechanics, Waves, Electricity), English Language, General Knowledge, and Current Affairs. Sample questions and study materials can be downloaded from the official portal.',
      agency: 'Air Force',
      category: 'Examination',
      source: 'Air Force Selection Board',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      isBreaking: false,
      isPinned: true,
      priority: 'high',
      tags: ['aptitude-test', 'syllabus', 'study-materials', 'examination'],
      viewsCount: 7234,
    ),

    // DSSC News
    AgencyNewsModel(
      id: 'dssc_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 8)),
      title: 'Defence Space Science College Research Fellowship 2025',
      content:
          'Applications are invited for Research Fellowship positions at the Defence Space Science College. Open to PhD holders in Physics, Engineering, Computer Science, and Mathematics. Fellowship duration is 2 years with possibility of extension. Research areas include satellite technology, space communications, and defense applications.',
      agency: 'DSSC',
      category: 'Research',
      source: 'DSSC Academic Board',
      publishedDate: DateTime.now().subtract(const Duration(hours: 8)),
      isBreaking: false,
      isPinned: true,
      priority: 'high',
      tags: ['research', 'fellowship', 'PhD', 'space-science'],
      applicationDeadline: DateTime.now().add(const Duration(days: 75)),
      viewsCount: 3456,
    ),

    // Police News
    AgencyNewsModel(
      id: 'police_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 12)),
      title: 'Nigeria Police Force Constable Recruitment 2025',
      content:
          'The Nigeria Police Force announces recruitment of 10,000 Police Constables. Requirements: Age 18-25 years, minimum of SSCE with 5 credits, height 1.67m for males and 1.64m for females, medical fitness, and no criminal record. Application is free and strictly online through the official portal.',
      agency: 'Nigeria Police Force',
      category: 'Recruitment',
      source: 'Police Service Commission',
      publishedDate: DateTime.now().subtract(const Duration(hours: 12)),
      isBreaking: true,
      isPinned: true,
      priority: 'high',
      tags: ['police', 'constable', 'recruitment', '10000-positions'],
      applicationDeadline: DateTime.now().add(const Duration(days: 40)),
      viewsCount: 18765,
    ),

    // NSCDC News
    AgencyNewsModel(
      id: 'nscdc_1',
      createdAt: DateTime.now().subtract(const Duration(days: 1)),
      title: 'NSCDC Agro Rangers Training Programme Launched',
      content:
          'The Nigeria Security and Civil Defence Corps has launched the Agro Rangers special training programme to combat farmers-herders conflicts. Training covers conflict resolution, agricultural security, and community policing. Successful candidates will be deployed to agricultural zones across the country.',
      agency: 'NSCDC',
      category: 'Training',
      source: 'NSCDC Training College',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      isBreaking: false,
      isPinned: false,
      priority: 'medium',
      tags: ['agro-rangers', 'training', 'agriculture', 'security'],
      viewsCount: 4321,
    ),

    // EFCC News
    AgencyNewsModel(
      id: 'efcc_1',
      createdAt: DateTime.now().subtract(const Duration(days: 2)),
      title: 'EFCC Detective Cadet Programme 2025 Application Guidelines',
      content:
          'The Economic and Financial Crimes Commission invites applications for the Detective Cadet Programme. Eligible candidates must have degrees in Law, Accounting, Economics, or related fields with minimum of Second Class Upper. Training duration is 18 months covering financial crimes investigation, forensic accounting, and cybercrime detection.',
      agency: 'EFCC',
      category: 'Recruitment',
      source: 'EFCC Academy',
      publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      isBreaking: false,
      isPinned: true,
      priority: 'high',
      tags: ['detective', 'cadet', 'financial-crimes', 'investigation'],
      applicationDeadline: DateTime.now().add(const Duration(days: 55)),
      viewsCount: 5678,
    ),

    // General Security News
    AgencyNewsModel(
      id: 'general_1',
      createdAt: DateTime.now().subtract(const Duration(hours: 16)),
      title: 'Joint Military Exercise "Eagle Shield 2025" Commences',
      content:
          'A joint military exercise involving Army, Navy, and Air Force has commenced in multiple locations. The exercise aims to test inter-service cooperation, joint operations capabilities, and response to security threats. Citizens in exercise areas are advised to remain calm as activities are part of routine training.',
      agency: 'Joint Forces',
      category: 'Operations',
      source: 'Defence Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(hours: 16)),
      isBreaking: true,
      isPinned: false,
      priority: 'medium',
      tags: ['joint-exercise', 'eagle-shield', 'military', 'training'],
      viewsCount: 12345,
    ),
  ];

  /// Get all news
  Future<List<AgencyNewsModel>> getAllNews() async {
    await Future.delayed(
      const Duration(milliseconds: 500),
    ); // Simulate network delay
    return List.from(_mockNews);
  }

  /// Get personalized news based on user's target agency (with live news)
  Future<List<AgencyNewsModel>> getPersonalizedNews({
    required String targetAgency,
    int limit = 10,
  }) async {
    print('🎯 Fetching personalized news for agency: $targetAgency');

    // Prioritize live news over mock data
    List<AgencyNewsModel> agencySpecificNews = [];

    // Strategy 1: Try to get live news first
    if (_isLiveMode) {
      try {
        final liveNews = await _liveNewsService.fetchAgencyNews(targetAgency);

        // Mark live news with special indicator
        final markedLiveNews =
            liveNews
                .map(
                  (news) => news.copyWith(
                    source: '🔴 LIVE: ${news.source}',
                    isBreaking: true, // Mark live news as breaking
                  ),
                )
                .toList();

        agencySpecificNews.addAll(markedLiveNews);
        print('✅ Found ${liveNews.length} live news items for $targetAgency');
      } catch (e) {
        print('⚠️ Live news fetch failed for $targetAgency: $e');
      }
    }

    // Strategy 2: Add relevant mock/fallback news only for the specific agency
    final mockAgencyNews =
        _mockNews.where((news) {
          // Strict agency matching - only exact matches
          return news.agency.toLowerCase().trim() ==
              targetAgency.toLowerCase().trim();
        }).toList();

    agencySpecificNews.addAll(mockAgencyNews);
    print(
      '📋 Added ${mockAgencyNews.length} mock news items for $targetAgency',
    );

    // Remove duplicates based on title similarity
    final uniqueNews = _removeDuplicatesByTitle(agencySpecificNews);

    // Sort by priority and recency (live news first)
    uniqueNews.sort((a, b) {
      // First prioritize live news (marked with 🔴 LIVE)
      final aIsLive = a.source.contains('🔴 LIVE');
      final bIsLive = b.source.contains('🔴 LIVE');
      if (aIsLive && !bIsLive) return -1;
      if (!aIsLive && bIsLive) return 1;

      // Then sort by pinned status
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then by breaking news
      if (a.isBreaking && !b.isBreaking) return -1;
      if (!a.isBreaking && b.isBreaking) return 1;

      // Then by priority
      final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
      final aPriority = priorityOrder[a.priority] ?? 2;
      final bPriority = priorityOrder[b.priority] ?? 2;
      if (aPriority != bPriority) return aPriority.compareTo(bPriority);

      // Finally by date (newest first)
      return b.publishedDate.compareTo(a.publishedDate);
    });

    final result = uniqueNews.take(limit).toList();
    print(
      '🎉 Returning ${result.length} agency-specific news items for $targetAgency',
    );

    return result;
  }

  /// Remove duplicate news items based on title similarity
  List<AgencyNewsModel> _removeDuplicatesByTitle(List<AgencyNewsModel> news) {
    final seen = <String>{};
    return news.where((item) {
      final normalizedTitle = item.title.toLowerCase().trim();
      if (seen.contains(normalizedTitle)) {
        return false;
      }
      seen.add(normalizedTitle);
      return true;
    }).toList();
  }

  /// Get breaking news (prioritizes live news)
  Future<List<AgencyNewsModel>> getBreakingNews() async {
    final allNews = await getCombinedNews();
    return allNews.where((news) => news.isBreaking).toList()
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
  }

  /// Get news with deadlines (enhanced with live news)
  Future<List<AgencyNewsModel>> getNewsWithDeadlines() async {
    final allNews = await getCombinedNews();
    final now = DateTime.now();
    return allNews.where((news) {
        return news.applicationDeadline != null &&
            news.applicationDeadline!.isAfter(now);
      }).toList()
      ..sort(
        (a, b) => a.applicationDeadline!.compareTo(b.applicationDeadline!),
      );
  }

  /// Get categories
  Future<List<String>> getCategories() async {
    await Future.delayed(const Duration(milliseconds: 100));
    final categories = _mockNews.map((news) => news.category).toSet().toList();
    categories.sort();
    return categories;
  }

  /// Search news
  Future<List<AgencyNewsModel>> searchNews(String query) async {
    await Future.delayed(const Duration(milliseconds: 300));
    final lowercaseQuery = query.toLowerCase();

    return _mockNews.where((news) {
        return news.title.toLowerCase().contains(lowercaseQuery) ||
            news.content.toLowerCase().contains(lowercaseQuery) ||
            news.agency.toLowerCase().contains(lowercaseQuery) ||
            news.category.toLowerCase().contains(lowercaseQuery) ||
            news.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery));
      }).toList()
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
  }

  /// Get news by agency
  Future<List<AgencyNewsModel>> getNewsByAgency(String agency) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockNews.where((news) => news.agency == agency).toList()
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
  }

  /// Get news by category
  Future<List<AgencyNewsModel>> getNewsByCategory(String category) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockNews.where((news) => news.category == category).toList()
      ..sort((a, b) => b.publishedDate.compareTo(a.publishedDate));
  }

  /// Mark news as read
  Future<void> markAsRead(String newsId, String userId) async {
    await Future.delayed(const Duration(milliseconds: 100));
    // In a real implementation, this would update the database
    final newsIndex = _mockNews.indexWhere((news) => news.id == newsId);
    if (newsIndex != -1) {
      // Update view count
      final updatedNews = _mockNews[newsIndex].copyWith(
        viewsCount: _mockNews[newsIndex].viewsCount + 1,
      );
      _mockNews[newsIndex] = updatedNews;
    }
  }

  /// Refresh agency news
  Future<void> refreshAgencyNews(String agency) async {
    await Future.delayed(const Duration(milliseconds: 500));
    // In a real implementation, this would fetch fresh data from the server
    // For now, just simulate a refresh delay
  }

  /// Get news status
  Map<String, dynamic> getNewsStatus() {
    final now = DateTime.now();
    final breakingNews = _mockNews.where((news) => news.isBreaking).length;
    final newsWithDeadlines =
        _mockNews.where((news) {
          return news.applicationDeadline != null &&
              news.applicationDeadline!.isAfter(now);
        }).length;

    return {
      'total_news': _mockNews.length,
      'breaking_news': breakingNews,
      'news_with_deadlines': newsWithDeadlines,
      'last_updated': DateTime.now().toIso8601String(),
      'agencies_covered': _mockNews.map((news) => news.agency).toSet().length,
    };
  }

  /// Get trending news (most viewed)
  Future<List<AgencyNewsModel>> getTrendingNews({int limit = 5}) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return _mockNews.toList()
      ..sort((a, b) => b.viewsCount.compareTo(a.viewsCount))
      ..take(limit);
  }

  /// Get urgent deadlines (within next 7 days)
  Future<List<AgencyNewsModel>> getUrgentDeadlines() async {
    await Future.delayed(const Duration(milliseconds: 200));
    final now = DateTime.now();
    final urgentDeadline = now.add(const Duration(days: 7));

    return _mockNews.where((news) {
        return news.applicationDeadline != null &&
            news.applicationDeadline!.isAfter(now) &&
            news.applicationDeadline!.isBefore(urgentDeadline);
      }).toList()
      ..sort(
        (a, b) => a.applicationDeadline!.compareTo(b.applicationDeadline!),
      );
  }

  /// Get live news status and statistics
  Map<String, dynamic> getLiveNewsStatus() {
    final mockStats = getNewsStatus();
    final liveStats = _liveNewsService.getCacheStatus();

    return {
      ...mockStats,
      'live_mode_enabled': _isLiveMode,
      'live_news_cache': liveStats,
      'last_live_refresh': DateTime.now().toIso8601String(),
    };
  }

  /// Force refresh live news
  Future<void> forceRefreshLiveNews() async {
    if (_isLiveMode) {
      await _refreshLiveNewsForAllAgencies();
    }
  }

  /// Dispose resources
  void dispose() {
    _refreshTimer?.cancel();
  }
}
