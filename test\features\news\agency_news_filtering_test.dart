// Test demonstration for Agency-Specific News Filtering
// This file shows how the filtering works for different agencies

import 'package:flutter_test/flutter_test.dart';
import 'package:fit_4_force/features/news/services/agency_news_service.dart';

void main() {
  group('Agency-Specific News Filtering Tests', () {
    late AgencyNewsService newsService;

    setUp(() {
      newsService = AgencyNewsService();
    });

    test('Nigerian Army user should only see Army news', () async {
      // Test filtering for Nigerian Army
      final armyNews = await newsService.getPersonalizedNews(
        targetAgency: 'Nigerian Army',
      );

      // All news should be from Nigerian Army
      expect(armyNews.every((news) => news.agency == 'Nigerian Army'), true);

      // Should contain specific Army news
      expect(
        armyNews.any((news) => news.title.contains('Nigerian Army 84RRI')),
        true,
      );
      expect(
        armyNews.any(
          (news) => news.title.contains('New Training Requirements'),
        ),
        true,
      );

      // Should NOT contain Navy or Air Force news
      expect(armyNews.any((news) => news.agency == 'Nigerian Navy'), false);
      expect(
        armyNews.any((news) => news.agency == 'Nigerian Air Force'),
        false,
      );

      print(
        '✅ Nigerian Army filtering test passed - ${armyNews.length} news items',
      );
    });

    test('Nigerian Navy user should only see Navy news', () async {
      // Test filtering for Nigerian Navy
      final navyNews = await newsService.getPersonalizedNews(
        targetAgency: 'Nigerian Navy',
      );

      // All news should be from Nigerian Navy
      expect(navyNews.every((news) => news.agency == 'Nigerian Navy'), true);

      // Should contain specific Navy news
      expect(
        navyNews.any(
          (news) => news.title.contains('Physical Fitness Requirements'),
        ),
        true,
      );
      expect(
        navyNews.any(
          (news) => news.title.contains('Maritime Security Training'),
        ),
        true,
      );

      // Should NOT contain Army or Air Force news
      expect(navyNews.any((news) => news.agency == 'Nigerian Army'), false);
      expect(
        navyNews.any((news) => news.agency == 'Nigerian Air Force'),
        false,
      );

      print(
        '✅ Nigerian Navy filtering test passed - ${navyNews.length} news items',
      );
    });

    test('Nigerian Air Force user should only see Air Force news', () async {
      // Test filtering for Nigerian Air Force
      final airForceNews = await newsService.getPersonalizedNews(
        targetAgency: 'Nigerian Air Force',
      );

      // All news should be from Nigerian Air Force
      expect(
        airForceNews.every((news) => news.agency == 'Nigerian Air Force'),
        true,
      );

      // Should contain specific Air Force news
      expect(
        airForceNews.any((news) => news.title.contains('Interview Schedule')),
        true,
      );
      expect(
        airForceNews.any(
          (news) => news.title.contains('Aviation Technology Course'),
        ),
        true,
      );

      // Should NOT contain Army or Navy news
      expect(airForceNews.any((news) => news.agency == 'Nigerian Army'), false);
      expect(airForceNews.any((news) => news.agency == 'Nigerian Navy'), false);

      print(
        '✅ Nigerian Air Force filtering test passed - ${airForceNews.length} news items',
      );
    });

    test('NDA user should only see NDA news', () async {
      // Test filtering for NDA
      final ndaNews = await newsService.getPersonalizedNews(
        targetAgency: 'NDA',
      );

      // All news should be from NDA
      expect(ndaNews.every((news) => news.agency == 'NDA'), true);

      // Should contain specific NDA news
      expect(
        ndaNews.any((news) => news.title.contains('Admission Exercise')),
        true,
      );

      // Should NOT contain other agency news
      expect(ndaNews.any((news) => news.agency == 'Nigerian Army'), false);
      expect(ndaNews.any((news) => news.agency == 'Nigerian Navy'), false);
      expect(ndaNews.any((news) => news.agency == 'Nigerian Air Force'), false);

      print('✅ NDA filtering test passed - ${ndaNews.length} news items');
    });

    test('NSCDC user should only see NSCDC news', () async {
      // Test filtering for NSCDC
      final nscdcNews = await newsService.getPersonalizedNews(
        targetAgency: 'NSCDC',
      );

      // All news should be from NSCDC
      expect(nscdcNews.every((news) => news.agency == 'NSCDC'), true);

      // Should contain specific NSCDC news
      expect(
        nscdcNews.any((news) => news.title.contains('Community Policing')),
        true,
      );

      // Should NOT contain other agency news
      expect(nscdcNews.any((news) => news.agency == 'Nigerian Army'), false);
      expect(nscdcNews.any((news) => news.agency == 'NDA'), false);

      print('✅ NSCDC filtering test passed - ${nscdcNews.length} news items');
    });

    test('No cross-agency content leakage', () async {
      // Test all agencies to ensure no cross-contamination
      final agencies = [
        'Nigerian Army',
        'Nigerian Navy',
        'Nigerian Air Force',
        'NDA',
        'DSSC',
        'NSCDC',
      ];

      for (final agency in agencies) {
        final agencyNews = await newsService.getPersonalizedNews(
          targetAgency: agency,
        );

        // Verify all news items belong to the correct agency
        for (final news in agencyNews) {
          expect(
            news.agency,
            equals(agency),
            reason:
                'News "${news.title}" should belong to $agency but belongs to ${news.agency}',
          );
        }

        print(
          '✅ $agency - No content leakage detected (${agencyNews.length} items)',
        );
      }
    });

    test('Breaking news is filtered by agency', () async {
      // Test that breaking news is also filtered by agency
      final armyNews = await newsService.getPersonalizedNews(
        targetAgency: 'Nigerian Army',
      );
      final ndaNews = await newsService.getPersonalizedNews(
        targetAgency: 'NDA',
      );

      final armyBreaking = armyNews.where((news) => news.isBreaking).toList();
      final ndaBreaking = ndaNews.where((news) => news.isBreaking).toList();

      // Army user should see Army breaking news
      expect(
        armyBreaking.every((news) => news.agency == 'Nigerian Army'),
        true,
      );

      // NDA user should see NDA breaking news
      expect(ndaBreaking.every((news) => news.agency == 'NDA'), true);

      print('✅ Breaking news filtering test passed');
    });

    test('Search works within agency-filtered news', () async {
      // Test search functionality within agency-specific news
      final armyNews = await newsService.getPersonalizedNews(
        targetAgency: 'Nigerian Army',
      );

      // Search for "recruitment" within Army news
      final searchResults =
          armyNews
              .where(
                (news) =>
                    news.title.toLowerCase().contains('recruitment') ||
                    news.content.toLowerCase().contains('recruitment'),
              )
              .toList();

      // All search results should still be from Nigerian Army
      expect(
        searchResults.every((news) => news.agency == 'Nigerian Army'),
        true,
      );

      print(
        '✅ Search within agency news test passed - ${searchResults.length} results',
      );
    });
  });
}

// Expected Test Results:
// ✅ Nigerian Army filtering test passed - 2 news items
// ✅ Nigerian Navy filtering test passed - 2 news items
// ✅ Nigerian Air Force filtering test passed - 2 news items
// ✅ NDA filtering test passed - 1 news items
// ✅ NSCDC filtering test passed - 1 news items
// ✅ Nigerian Army - No content leakage detected (2 items)
// ✅ Nigerian Navy - No content leakage detected (2 items)
// ✅ Nigerian Air Force - No content leakage detected (2 items)
// ✅ NDA - No content leakage detected (1 items)
// ✅ DSSC - No content leakage detected (1 items)
// ✅ NSCDC - No content leakage detected (1 items)
// ✅ Breaking news filtering test passed
// ✅ Search within agency news test passed
