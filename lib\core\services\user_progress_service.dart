import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Centralized service for managing user progress across all app features
/// Ensures all user progress, stats, and achievements start at zero for new users
/// and update dynamically as the user interacts with the app
class UserProgressService {
  static final UserProgressService _instance = UserProgressService._internal();
  factory UserProgressService() => _instance;
  UserProgressService._internal();

  // Storage keys
  static const String _userProgressKey = 'user_progress_data';
  static const String _userInitializedKey = 'user_initialized';

  /// Initialize progress for a new user - all values start at zero
  Map<String, dynamic> _getInitialUserProgress() {
    return {
      // Fitness progress
      'fitness': {
        'totalWorkouts': 0,
        'totalCaloriesBurned': 0,
        'totalHours': 0,
        'averageHeartRate': 0,
        'currentWeight': 0.0,
        'currentHeight': 0.0,
        'bodyFatPercentage': 0.0,
        'muscleMass': 0.0,
        'weeklyGoalProgress': 0.0,
        'streakDays': 0,
        'achievements': <String>[],
        'progressPhotos': <String>[],
        'lastWorkoutDate': null,
        'fitnessLevel': 'Beginner',
        'fitnessScore': 0.0,
      },

      // Academic/Prep progress
      'academics': {
        'totalQuizzesCompleted': 0,
        'totalStudyHours': 0.0,
        'averageQuizScore': 0.0,
        'totalCorrectAnswers': 0,
        'totalQuestionsAttempted': 0,
        'currentStreak': 0,
        'longestStreak': 0,
        'materialsRead': 0,
        'readingStreak': 0,
        'lastReadDate': null,
        'subjectScores': <String, double>{},
        'topicProgress': <String, double>{},
        'completedMaterials': <String>[],
        'bookmarkedMaterials': <String>[],
        'studyTime': <String, int>{}, // date -> minutes
        'quizHistory': <Map<String, dynamic>>[],
        'weakAreas': <String>[],
        'strongAreas': <String>[],
        'achievements': <String>[],
        'targetExamDate': null,
        'daysToExam': 0,
        'readiness': 0.0,
      },

      // Training progress
      'training': {
        'completedWorkouts': 0,
        'totalTrainingHours': 0,
        'currentPlan': null,
        'planProgress': 0.0,
        'strengthProgress': <String, double>{},
        'enduranceProgress': <String, double>{},
        'assessmentScores': <String, double>{},
        'personalRecords': <String, dynamic>{},
        'trainingStreak': 0,
        'achievements': <String>[],
        'lastTrainingDate': null,
      },

      // Profile/General progress
      'profile': {
        'profileCompleteness': 0.0,
        'totalLoginsThisWeek': 0,
        'totalTimeInApp': 0, // minutes
        'featuresUnlocked': <String>[],
        'premiumStatus': false,
        'joinDate': DateTime.now().toIso8601String(),
        'lastActiveDate': DateTime.now().toIso8601String(),
        'hasProfileImage': false,
        'lastProfileUpdate': null,
        'achievements': <String>[],
        'badges': <String>[],
      },

      // Community progress
      'community': {
        'postsCreated': 0,
        'commentsPosted': 0,
        'likesReceived': 0,
        'likesGiven': 0,
        'studyGroupsJoined': 0,
        'forumReputation': 0,
        'helpfulAnswers': 0,
        'totalPosts': 0,
        'totalComments': 0,
        'socialStreak': 0,
        'achievements': <String>[],
        'lastPostDate': null,
        'lastCommentDate': null,
      },

      // App-wide statistics
      'overall': {
        'totalPoints': 0,
        'level': 1,
        'experiencePoints': 0,
        'nextLevelProgress': 0.0,
        'totalAchievements': 0,
        'overallProgress': 0.0,
        'dailyGoalsCompleted': 0,
        'weeklyGoalsCompleted': 0,
        'monthlyGoalsCompleted': 0,
      },

      // Metadata
      'metadata': {
        'initialized': true,
        'version': '1.0.0',
        'lastUpdated': DateTime.now().toIso8601String(),
        'dataSource': 'local_storage',
      },
    };
  }

  /// Load user progress from storage or initialize for new user
  Future<Map<String, dynamic>> loadUserProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final bool isInitialized = prefs.getBool(_userInitializedKey) ?? false;

      if (!isInitialized) {
        // New user - initialize with zero values
        final initialProgress = _getInitialUserProgress();
        await saveUserProgress(initialProgress);
        await prefs.setBool(_userInitializedKey, true);
        return initialProgress;
      }

      // Existing user - load from storage
      final String? progressJson = prefs.getString(_userProgressKey);
      if (progressJson != null) {
        final Map<String, dynamic> progress = json.decode(progressJson);

        // Ensure all required fields exist (for backwards compatibility)
        final initialProgress = _getInitialUserProgress();
        final mergedProgress = _mergeProgress(initialProgress, progress);

        return mergedProgress;
      }

      // Fallback to initial progress if storage is corrupted
      return _getInitialUserProgress();
    } catch (e) {
      print('Error loading user progress: $e');
      return _getInitialUserProgress();
    }
  }

  /// Save user progress to storage
  Future<void> saveUserProgress(Map<String, dynamic> progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Update metadata
      progress['metadata'] = {
        ...progress['metadata'] ?? {},
        'lastUpdated': DateTime.now().toIso8601String(),
        'version': '1.0.0',
      };

      final String progressJson = json.encode(progress);
      await prefs.setString(_userProgressKey, progressJson);
    } catch (e) {
      print('Error saving user progress: $e');
      throw Exception('Failed to save user progress');
    }
  }

  /// Update specific progress section
  Future<void> updateProgress(
    String section,
    Map<String, dynamic> updates,
  ) async {
    final currentProgress = await loadUserProgress();

    if (currentProgress[section] != null) {
      currentProgress[section] = {...currentProgress[section], ...updates};

      await saveUserProgress(currentProgress);
    }
  }

  /// Increment a numeric value in progress
  Future<void> incrementValue(
    String section,
    String key, {
    num increment = 1,
  }) async {
    final currentProgress = await loadUserProgress();

    if (currentProgress[section] != null &&
        currentProgress[section][key] != null) {
      final currentValue = currentProgress[section][key] as num;
      currentProgress[section][key] = currentValue + increment;

      await saveUserProgress(currentProgress);
    }
  }

  /// Add to a list in progress
  Future<void> addToList(String section, String key, dynamic item) async {
    final currentProgress = await loadUserProgress();

    if (currentProgress[section] != null &&
        currentProgress[section][key] is List) {
      final List currentList = currentProgress[section][key];
      if (!currentList.contains(item)) {
        currentList.add(item);
        await saveUserProgress(currentProgress);
      }
    }
  }

  /// Add achievement
  Future<void> addAchievement(String section, String achievement) async {
    await addToList(section, 'achievements', achievement);

    // Also update overall achievements count
    await incrementValue('overall', 'totalAchievements');
  }

  /// Update streak
  Future<void> updateStreak(
    String section,
    String streakKey,
    bool isActive,
  ) async {
    final currentProgress = await loadUserProgress();

    if (currentProgress[section] != null) {
      if (isActive) {
        // Increment streak
        final currentStreak =
            (currentProgress[section][streakKey] as int?) ?? 0;
        currentProgress[section][streakKey] = currentStreak + 1;
      } else {
        // Reset streak
        currentProgress[section][streakKey] = 0;
      }

      await saveUserProgress(currentProgress);
    }
  }

  /// Calculate overall progress percentage
  double calculateOverallProgress(Map<String, dynamic> progress) {
    double totalProgress = 0.0;
    int sectionCount = 0;

    // Fitness progress (0-100%)
    final fitness = progress['fitness'] ?? {};
    final fitnessScore = (fitness['fitnessScore'] as num?)?.toDouble() ?? 0.0;
    totalProgress += (fitnessScore / 100.0) * 100;
    sectionCount++;

    // Academic progress (0-100%)
    final academics = progress['academics'] ?? {};
    final readiness = (academics['readiness'] as num?)?.toDouble() ?? 0.0;
    totalProgress += readiness;
    sectionCount++;

    // Training progress (0-100%)
    final training = progress['training'] ?? {};
    final planProgress = (training['planProgress'] as num?)?.toDouble() ?? 0.0;
    totalProgress += planProgress;
    sectionCount++;

    // Profile completeness (0-100%)
    final profile = progress['profile'] ?? {};
    final profileCompleteness =
        (profile['profileCompleteness'] as num?)?.toDouble() ?? 0.0;
    totalProgress += profileCompleteness;
    sectionCount++;

    return sectionCount > 0 ? totalProgress / sectionCount : 0.0;
  }

  /// Reset all progress (for testing or user request)
  Future<void> resetProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userProgressKey);
    await prefs.remove(_userInitializedKey);
  }

  /// Check if user is new (no progress data)
  Future<bool> isNewUser() async {
    final prefs = await SharedPreferences.getInstance();
    return !(prefs.getBool(_userInitializedKey) ?? false);
  }

  /// Merge progress maps, keeping existing values where they exist
  Map<String, dynamic> _mergeProgress(
    Map<String, dynamic> initial,
    Map<String, dynamic> existing,
  ) {
    final merged = Map<String, dynamic>.from(initial);

    for (final key in existing.keys) {
      if (merged[key] is Map && existing[key] is Map) {
        merged[key] = <String, dynamic>{
          ...merged[key] as Map<String, dynamic>,
          ...existing[key] as Map<String, dynamic>,
        };
      } else {
        merged[key] = existing[key];
      }
    }

    return merged;
  }
}
