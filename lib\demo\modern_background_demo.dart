import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';

/// Demo screen showcasing the new modern background color scheme
class ModernBackgroundDemo extends StatelessWidget {
  const ModernBackgroundDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundLight,
      appBar: AppBar(
        title: const Text('Modern Background Demo'),
        backgroundColor: AppTheme.backgroundSurface,
        foregroundColor: AppTheme.textPrimaryLight,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Text(
              'ARCTIC BLUE COLOR SCHEME',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryLight,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Soft blue-tinted backgrounds - Perfect for comfortable viewing',
              style: TextStyle(
                fontSize: 16,
                color: AppTheme.textSecondaryLight,
              ),
            ),
            const SizedBox(height: 24),

            // Color Palette Display
            _buildColorPalette(),
            const SizedBox(height: 32),

            // Sample Cards Grid
            Text(
              'SAMPLE CARDS WITH NEW SHADOWS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryLight,
              ),
            ),
            const SizedBox(height: 16),
            _buildSampleCardsGrid(),
            const SizedBox(height: 32),

            // Benefits Section
            _buildBenefitsSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildColorPalette() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppUI.materialCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'COLOR PALETTE',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildColorSwatch(
                'Primary Background',
                AppTheme.backgroundLight,
                '#F8FAFF',
              ),
              const SizedBox(width: 12),
              _buildColorSwatch(
                'Secondary Background',
                AppTheme.backgroundDark,
                '#F0F4FF',
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildColorSwatch(
                'Card Background',
                AppTheme.backgroundSurface,
                '#FCFDFF',
              ),
              const SizedBox(width: 12),
              _buildColorSwatch(
                'Accent Background',
                AppTheme.backgroundAccent,
                '#E6EFFF',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorSwatch(String label, Color color, String hex) {
    return Expanded(
      child: Column(
        children: [
          Container(
            height: 60,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.backgroundAccent),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryLight,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            hex,
            style: TextStyle(fontSize: 10, color: AppTheme.textSecondaryLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSampleCardsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildSampleCard(
          'Fitness Progress',
          '75% Complete',
          Icons.fitness_center,
          Colors.green,
        ),
        _buildSampleCard(
          'Study Materials',
          '12 Topics',
          Icons.book,
          AppTheme.primaryColor,
        ),
        _buildSampleCard('Daily Quiz', 'Take Quiz', Icons.quiz, Colors.purple),
        _buildSampleCard(
          'Community',
          '1.2k Members',
          Icons.people,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildSampleCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppUI.materialElevatedCard,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1 * 255),
              shape: BoxShape.circle,
              boxShadow: AppUI.shadowSmall,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: AppUI.materialCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'BENEFITS OF ARCTIC BLUE SCHEME',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 16),
          _buildBenefitItem('✅ Softer, more comfortable viewing experience'),
          _buildBenefitItem('✅ Subtle blue tint reduces eye strain'),
          _buildBenefitItem('✅ Professional military aesthetic'),
          _buildBenefitItem('✅ Perfect harmony with existing blue gradients'),
          _buildBenefitItem('✅ Less bright than pure white backgrounds'),
          _buildBenefitItem('✅ Maintains excellent readability'),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          color: AppTheme.textSecondaryLight,
          height: 1.4,
        ),
      ),
    );
  }
}
