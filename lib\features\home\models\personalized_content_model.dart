import 'package:flutter/material.dart';

/// Enum for different content types
enum ContentType {
  guide,
  tips,
  course,
  study,
  reference,
  video,
  quiz,
  article,
}

/// Model for personalized content items
class PersonalizedContentModel {
  final String id;
  final String title;
  final String description;
  final String category;
  final ContentType contentType;
  final String imageUrl;
  final Color color;
  final bool isFeatured;
  final int estimatedReadTime; // in minutes
  final String difficulty;
  final List<String> tags;
  final Function(BuildContext) onTap;
  final DateTime? publishedDate;
  final int viewCount;
  final double rating;
  final bool isPremium;

  PersonalizedContentModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.contentType,
    required this.imageUrl,
    required this.color,
    this.isFeatured = false,
    this.estimatedReadTime = 5,
    this.difficulty = 'Intermediate',
    this.tags = const [],
    required this.onTap,
    this.publishedDate,
    this.viewCount = 0,
    this.rating = 0.0,
    this.isPremium = false,
  });

  /// Get content type display name
  String get contentTypeDisplayName {
    switch (contentType) {
      case ContentType.guide:
        return 'Guide';
      case ContentType.tips:
        return 'Tips';
      case ContentType.course:
        return 'Course';
      case ContentType.study:
        return 'Study Material';
      case ContentType.reference:
        return 'Reference';
      case ContentType.video:
        return 'Video';
      case ContentType.quiz:
        return 'Quiz';
      case ContentType.article:
        return 'Article';
    }
  }

  /// Get content type icon
  IconData get contentTypeIcon {
    switch (contentType) {
      case ContentType.guide:
        return Icons.menu_book;
      case ContentType.tips:
        return Icons.lightbulb;
      case ContentType.course:
        return Icons.school;
      case ContentType.study:
        return Icons.library_books;
      case ContentType.reference:
        return Icons.bookmark;
      case ContentType.video:
        return Icons.play_circle;
      case ContentType.quiz:
        return Icons.quiz;
      case ContentType.article:
        return Icons.article;
    }
  }

  /// Get difficulty color
  Color get difficultyColor {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Get estimated read time display
  String get readTimeDisplay {
    if (estimatedReadTime < 60) {
      return '${estimatedReadTime}m read';
    } else {
      final hours = estimatedReadTime ~/ 60;
      final minutes = estimatedReadTime % 60;
      if (minutes == 0) {
        return '${hours}h read';
      } else {
        return '${hours}h ${minutes}m read';
      }
    }
  }

  /// Check if content is new (published within last 7 days)
  bool get isNew {
    if (publishedDate == null) return false;
    final now = DateTime.now();
    final difference = now.difference(publishedDate!);
    return difference.inDays <= 7;
  }

  /// Get formatted view count
  String get formattedViewCount {
    if (viewCount < 1000) {
      return viewCount.toString();
    } else if (viewCount < 1000000) {
      final k = (viewCount / 1000).toStringAsFixed(1);
      return '${k}K';
    } else {
      final m = (viewCount / 1000000).toStringAsFixed(1);
      return '${m}M';
    }
  }

  /// Get rating display
  String get ratingDisplay {
    if (rating == 0.0) return 'No ratings';
    return rating.toStringAsFixed(1);
  }

  /// Copy with method for updating properties
  PersonalizedContentModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    ContentType? contentType,
    String? imageUrl,
    Color? color,
    bool? isFeatured,
    int? estimatedReadTime,
    String? difficulty,
    List<String>? tags,
    Function(BuildContext)? onTap,
    DateTime? publishedDate,
    int? viewCount,
    double? rating,
    bool? isPremium,
  }) {
    return PersonalizedContentModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      contentType: contentType ?? this.contentType,
      imageUrl: imageUrl ?? this.imageUrl,
      color: color ?? this.color,
      isFeatured: isFeatured ?? this.isFeatured,
      estimatedReadTime: estimatedReadTime ?? this.estimatedReadTime,
      difficulty: difficulty ?? this.difficulty,
      tags: tags ?? this.tags,
      onTap: onTap ?? this.onTap,
      publishedDate: publishedDate ?? this.publishedDate,
      viewCount: viewCount ?? this.viewCount,
      rating: rating ?? this.rating,
      isPremium: isPremium ?? this.isPremium,
    );
  }

  @override
  String toString() {
    return 'PersonalizedContentModel(id: $id, title: $title, category: $category, contentType: $contentType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PersonalizedContentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
