import 'package:flutter/material.dart';
import '../services/agency_news_service.dart';
import '../services/news_sources_config.dart';

/// Screen for configuring news sources and live news settings
class NewsSourcesSettingsScreen extends StatefulWidget {
  const NewsSourcesSettingsScreen({super.key});

  @override
  State<NewsSourcesSettingsScreen> createState() =>
      _NewsSourcesSettingsScreenState();
}

class _NewsSourcesSettingsScreenState extends State<NewsSourcesSettingsScreen> {
  final AgencyNewsService _newsService = AgencyNewsService();
  bool _isLoading = false;
  Map<String, dynamic> _newsStatus = {};
  final Map<String, bool> _agencyPreferences = {};

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);

    _newsStatus = _newsService.getNewsStatus();

    // Initialize agency preferences
    for (final agency in NewsSourcesConfig.getSupportedAgencies()) {
      _agencyPreferences[agency] = true; // Default to enabled
    }

    setState(() => _isLoading = false);
  }

  Future<void> _refreshAgencyNews(String agency) async {
    setState(() => _isLoading = true);

    try {
      await _newsService.refreshAgencyNews(agency);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ Refreshed news for $agency'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Failed to refresh $agency news: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('News Sources Settings'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLiveNewsSection(),
                    const SizedBox(height: 24),
                    _buildAgencySourcesSection(),
                    const SizedBox(height: 24),
                    _buildCacheStatusSection(),
                    const SizedBox(height: 24),
                    _buildConfigurationSection(),
                  ],
                ),
              ),
    );
  }

  Widget _buildLiveNewsSection() {
    final liveNewsEnabled = _newsStatus['live_news_enabled'] ?? false;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  liveNewsEnabled ? Icons.wifi : Icons.wifi_off,
                  color: liveNewsEnabled ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  'Live News Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color:
                    liveNewsEnabled
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: liveNewsEnabled ? Colors.green : Colors.orange,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    liveNewsEnabled ? Icons.check_circle : Icons.warning,
                    color: liveNewsEnabled ? Colors.green : Colors.orange,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      liveNewsEnabled
                          ? 'Live news fetching is enabled. Latest news will be automatically fetched from official sources.'
                          : 'Live news is disabled. Only mock data will be shown.',
                      style: TextStyle(
                        color:
                            liveNewsEnabled
                                ? Colors.green.shade700
                                : Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgencySourcesSection() {
    final supportedAgencies = _newsStatus['supported_agencies'] as List? ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agency News Sources',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            Text(
              'Manage news sources for each agency',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ...supportedAgencies.map(
              (agency) => _buildAgencySourceCard(agency),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgencySourceCard(String agency) {
    final source = NewsSourcesConfig.getAgencySource(agency);
    final hasRss = NewsSourcesConfig.hasRssFeed(agency);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  agency,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              const Spacer(),
              Icon(
                hasRss ? Icons.rss_feed : Icons.language,
                color: hasRss ? Colors.green : Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                hasRss ? 'RSS Available' : 'Web Scraping',
                style: TextStyle(
                  color: hasRss ? Colors.green : Colors.orange,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (source != null) ...[
            Text(
              'Official Website: ${source.officialWebsite}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 4),
            Text(
              'RSS Feeds: ${source.rssFeeds.length}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _refreshAgencyNews(agency),
                    icon: const Icon(Icons.refresh, size: 16),
                    label: const Text('Refresh News'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () => _showAgencyDetails(agency, source),
                  icon: const Icon(Icons.info_outline),
                  tooltip: 'View Details',
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCacheStatusSection() {
    final cacheStatus = _newsStatus['cache_status'] as Map? ?? {};
    final cachedAgencies = cacheStatus['cached_agencies'] as List? ?? [];
    final cacheSizes = cacheStatus['cache_sizes'] as Map? ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.storage, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Cache Status',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Clear all cache
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Cache cleared successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (cachedAgencies.isEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.grey),
                    SizedBox(width: 8),
                    Text('No cached news data'),
                  ],
                ),
              )
            else
              ...cachedAgencies.map((agency) {
                final size = cacheSizes[agency] ?? 0;
                return ListTile(
                  leading: const Icon(Icons.article),
                  title: Text(agency),
                  subtitle: Text('$size news items cached'),
                  trailing: Text(
                    'Updated recently',
                    style: TextStyle(
                      color: Colors.green.shade600,
                      fontSize: 12,
                    ),
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Configuration Guide',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            _buildConfigStep(
              'Step 1',
              'API Keys Setup',
              'Add your NewsAPI.org key to fetch additional news sources',
              Icons.key,
              Colors.orange,
            ),
            _buildConfigStep(
              'Step 2',
              'RSS Monitoring',
              'Official RSS feeds are automatically monitored for updates',
              Icons.rss_feed,
              Colors.blue,
            ),
            _buildConfigStep(
              'Step 3',
              'News Filtering',
              'News is automatically filtered by agency and relevance',
              Icons.filter_alt,
              Colors.green,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'How it works',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• RSS feeds are checked every hour for new content\n'
                    '• Web scraping is used as a fallback for agencies without RSS\n'
                    '• News APIs provide additional coverage with keyword filtering\n'
                    '• All news is filtered by agency and categorized automatically\n'
                    '• Breaking news and deadlines are prioritized',
                    style: TextStyle(color: Colors.blue.shade700),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigStep(
    String step,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: color),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$step: $title',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAgencyDetails(String agency, AgencyNewsSource source) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('$agency News Sources'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailItem('Official Website', source.officialWebsite),
                  _buildDetailItem('News Section', source.newsSection),
                  _buildDetailItem('Recruitment Page', source.recruitmentPage),
                  const SizedBox(height: 16),
                  const Text(
                    'RSS Feeds:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ...source.rssFeeds.map(
                    (feed) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '• $feed',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Social Media:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  ...source.socialMedia.entries.map(
                    (entry) => Padding(
                      padding: const EdgeInsets.only(left: 16, top: 4),
                      child: Text(
                        '${entry.key}: ${entry.value}',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$label:', style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 2),
          Text(value, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }
}
