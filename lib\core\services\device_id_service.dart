import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:logger/logger.dart';

/// Service for generating and managing unique device identifiers
/// Creates a persistent device ID that survives app reinstalls
class DeviceIdService {
  static const String _deviceIdKey = 'fit4force_device_id';
  static const String _deviceInfoKey = 'fit4force_device_info';
  
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  static final Logger _logger = Logger();
  static final Uuid _uuid = const Uuid();
  
  /// Generate or retrieve the unique device ID
  static Future<String> getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if we already have a stored device ID
      String? storedDeviceId = prefs.getString(_deviceIdKey);
      
      if (storedDeviceId != null && storedDeviceId.isNotEmpty) {
        _logger.d('📱 Retrieved existing device ID: ${storedDeviceId.substring(0, 8)}...');
        return storedDeviceId;
      }
      
      // Generate new device ID
      final deviceId = await _generateDeviceId();
      
      // Store the device ID for future use
      await prefs.setString(_deviceIdKey, deviceId);
      
      _logger.i('📱 Generated new device ID: ${deviceId.substring(0, 8)}...');
      return deviceId;
    } catch (e) {
      _logger.e('❌ Error getting device ID: $e');
      // Fallback to a random UUID if all else fails
      return _uuid.v4();
    }
  }
  
  /// Generate a unique device ID based on device characteristics
  static Future<String> _generateDeviceId() async {
    try {
      final deviceInfo = await getDeviceInfo();
      
      // Create a unique string from device characteristics
      final deviceString = '${deviceInfo['model']}_${deviceInfo['platform']}_${deviceInfo['osVersion']}';
      
      // Generate a random UUID for this installation
      final installationId = _uuid.v4();
      
      // Combine device characteristics with installation ID
      final combinedString = '${deviceString}_$installationId';
      
      // Create a hash of the combined string for consistency
      final bytes = utf8.encode(combinedString);
      final digest = sha256.convert(bytes);
      
      return digest.toString();
    } catch (e) {
      _logger.e('❌ Error generating device ID: $e');
      // Fallback to random UUID
      return _uuid.v4();
    }
  }
  
  /// Get comprehensive device information
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if we have cached device info
      final cachedInfo = prefs.getString(_deviceInfoKey);
      if (cachedInfo != null) {
        final decoded = jsonDecode(cachedInfo) as Map<String, dynamic>;
        _logger.d('📱 Retrieved cached device info');
        return decoded;
      }
      
      Map<String, dynamic> deviceData = {};
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceData = {
          'platform': 'Android',
          'model': androidInfo.model,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'osVersion': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'manufacturer': androidInfo.manufacturer,
          'product': androidInfo.product,
          'deviceType': _getDeviceType(androidInfo.model),
          'deviceName': '${androidInfo.brand} ${androidInfo.model}',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData = {
          'platform': 'iOS',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'osVersion': iosInfo.systemVersion,
          'localizedModel': iosInfo.localizedModel,
          'deviceType': _getDeviceType(iosInfo.model),
          'deviceName': '${iosInfo.name} (${iosInfo.model})',
        };
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfo.windowsInfo;
        deviceData = {
          'platform': 'Windows',
          'model': windowsInfo.computerName,
          'osVersion': windowsInfo.displayVersion,
          'deviceType': 'desktop',
          'deviceName': windowsInfo.computerName,
        };
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfo.macOsInfo;
        deviceData = {
          'platform': 'macOS',
          'model': macInfo.model,
          'osVersion': macInfo.osRelease,
          'deviceType': 'desktop',
          'deviceName': macInfo.computerName,
        };
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfo.linuxInfo;
        deviceData = {
          'platform': 'Linux',
          'model': linuxInfo.name,
          'osVersion': linuxInfo.version,
          'deviceType': 'desktop',
          'deviceName': linuxInfo.prettyName,
        };
      } else {
        // Web or unknown platform
        deviceData = {
          'platform': 'Web',
          'model': 'Web Browser',
          'osVersion': 'Unknown',
          'deviceType': 'desktop',
          'deviceName': 'Web Browser',
        };
      }
      
      // Cache the device info
      await prefs.setString(_deviceInfoKey, jsonEncode(deviceData));
      
      _logger.i('📱 Generated device info for ${deviceData['platform']} ${deviceData['model']}');
      return deviceData;
    } catch (e) {
      _logger.e('❌ Error getting device info: $e');
      // Return fallback device info
      return {
        'platform': Platform.operatingSystem,
        'model': 'Unknown',
        'osVersion': 'Unknown',
        'deviceType': 'mobile',
        'deviceName': 'Unknown Device',
      };
    }
  }
  
  /// Determine device type based on model name
  static String _getDeviceType(String model) {
    final modelLower = model.toLowerCase();
    
    // Tablet patterns
    if (modelLower.contains('ipad') ||
        modelLower.contains('tablet') ||
        modelLower.contains('tab ') ||
        modelLower.contains('pad')) {
      return 'tablet';
    }
    
    // Desktop/laptop patterns
    if (modelLower.contains('mac') ||
        modelLower.contains('imac') ||
        modelLower.contains('macbook') ||
        modelLower.contains('desktop') ||
        modelLower.contains('laptop')) {
      return 'desktop';
    }
    
    // Default to mobile for phones and unknown devices
    return 'mobile';
  }
  
  /// Clear stored device ID (useful for testing or reset)
  static Future<void> clearDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_deviceIdKey);
      await prefs.remove(_deviceInfoKey);
      _logger.i('📱 Cleared stored device ID and info');
    } catch (e) {
      _logger.e('❌ Error clearing device ID: $e');
    }
  }
  
  /// Get a human-readable device name for display
  static Future<String> getDeviceName() async {
    try {
      final deviceInfo = await getDeviceInfo();
      return deviceInfo['deviceName'] ?? 'Unknown Device';
    } catch (e) {
      _logger.e('❌ Error getting device name: $e');
      return 'Unknown Device';
    }
  }
  
  /// Check if the current device ID matches a given ID
  static Future<bool> isCurrentDevice(String deviceId) async {
    try {
      final currentDeviceId = await getDeviceId();
      return currentDeviceId == deviceId;
    } catch (e) {
      _logger.e('❌ Error checking device ID: $e');
      return false;
    }
  }
  
  /// Get app version for device registration
  static Future<String> getAppVersion() async {
    try {
      // This would typically come from package_info_plus
      // For now, return a placeholder
      return '1.0.0';
    } catch (e) {
      _logger.e('❌ Error getting app version: $e');
      return 'Unknown';
    }
  }
}
