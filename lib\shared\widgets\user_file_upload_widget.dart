import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:fit_4_force/core/services/user_storage_service.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

enum UserUploadType { profileImage, postImage }

class UserFileUploadWidget extends StatefulWidget {
  final UserUploadType uploadType;
  final String? entityId; // userId, postId, etc.
  final Function(String? url)? onUploadComplete;
  final Function(String error)? onUploadError;
  final Widget? customButton;
  final bool showProgress;
  final String? buttonText;

  const UserFileUploadWidget({
    super.key,
    required this.uploadType,
    this.entityId,
    this.onUploadComplete,
    this.onUploadError,
    this.customButton,
    this.showProgress = true,
    this.buttonText,
  });

  @override
  State<UserFileUploadWidget> createState() => _UserFileUploadWidgetState();
}

class _UserFileUploadWidgetState extends State<UserFileUploadWidget> {
  final UserStorageService _storageService = UserStorageService();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String? _uploadedUrl;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Upload button or custom widget
        widget.customButton ?? _buildDefaultUploadButton(),

        // Progress indicator
        if (_isUploading && widget.showProgress) ...[
          const SizedBox(height: 16),
          _buildProgressIndicator(),
        ],

        // Error indicator
        if (_errorMessage != null) ...[
          const SizedBox(height: 16),
          _buildErrorIndicator(),
        ],

        // Success indicator
        if (_uploadedUrl != null) ...[
          const SizedBox(height: 16),
          _buildSuccessIndicator(),
        ],
      ],
    );
  }

  Widget _buildDefaultUploadButton() {
    return ElevatedButton.icon(
      onPressed: _isUploading ? null : _handleUpload,
      icon:
          _isUploading
              ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
              : const Icon(Icons.image),
      label: Text(widget.buttonText ?? _getDefaultButtonText()),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        children: [
          Row(
            children: [
              const Icon(Icons.cloud_upload, color: Colors.blue),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Uploading ${_getFileTypeName()}...',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.blue,
                  ),
                ),
              ),
              Text(
                '${(_uploadProgress * 100).toInt()}%',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: _uploadProgress,
            backgroundColor: Colors.blue.shade100,
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          const Icon(Icons.error, color: Colors.red),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Upload Failed',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 20),
            onPressed: () => setState(() => _errorMessage = null),
          ),
        ],
      ),
    );
  }

  Widget _buildSuccessIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Row(
        children: [
          const Icon(Icons.check_circle, color: Colors.green),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${_getFileTypeName()} uploaded successfully!',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.green,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.open_in_new, size: 20),
            onPressed: () => _openUploadedFile(),
            tooltip: 'View File',
          ),
        ],
      ),
    );
  }

  Future<void> _handleUpload() async {
    setState(() {
      _isUploading = true;
      _uploadProgress = 0.0;
      _uploadedUrl = null;
      _errorMessage = null;
    });

    try {
      final uploadedUrl = await _uploadImage();

      setState(() {
        _isUploading = false;
        _uploadedUrl = uploadedUrl;
      });

      if (uploadedUrl != null) {
        widget.onUploadComplete?.call(uploadedUrl);
      } else {
        widget.onUploadError?.call('Upload failed');
      }
    } catch (e) {
      setState(() {
        _isUploading = false;
        _errorMessage = e.toString();
      });
      widget.onUploadError?.call(e.toString());
    }
  }

  Future<String?> _uploadImage() async {
    // Show image source selection for mobile
    ImageSource? source;
    if (Theme.of(context).platform == TargetPlatform.android ||
        Theme.of(context).platform == TargetPlatform.iOS) {
      source = await _showImageSourceDialog();
      if (source == null) return null;
    }

    final XFile? image =
        source != null
            ? await _imagePicker.pickImage(
              source: source,
              maxWidth: 1920,
              maxHeight: 1080,
              imageQuality: 85,
            )
            : await _imagePicker.pickImage(source: ImageSource.gallery);

    if (image == null) return null;

    final imageData = await image.readAsBytes();
    final fileName = image.name;

    switch (widget.uploadType) {
      case UserUploadType.profileImage:
        return await _storageService.uploadProfileImage(
          widget.entityId!,
          imageData,
          fileName,
          onProgress: (progress) => setState(() => _uploadProgress = progress),
        );
      case UserUploadType.postImage:
        return await _storageService.uploadPostImage(
          widget.entityId!,
          imageData,
          fileName,
          onProgress: (progress) => setState(() => _uploadProgress = progress),
        );
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      builder:
          (context) => SafeArea(
            child: Wrap(
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Take Photo'),
                  onTap: () => Navigator.pop(context, ImageSource.camera),
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choose from Gallery'),
                  onTap: () => Navigator.pop(context, ImageSource.gallery),
                ),
              ],
            ),
          ),
    );
  }

  void _openUploadedFile() {
    if (_uploadedUrl != null) {
      // Open file in browser or appropriate app
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('File URL: $_uploadedUrl'),
          action: SnackBarAction(
            label: 'Copy',
            onPressed: () {
              // Copy URL to clipboard
            },
          ),
        ),
      );
    }
  }

  String _getDefaultButtonText() {
    switch (widget.uploadType) {
      case UserUploadType.profileImage:
        return 'Upload Profile Image';
      case UserUploadType.postImage:
        return 'Upload Image';
    }
  }

  String _getFileTypeName() {
    switch (widget.uploadType) {
      case UserUploadType.profileImage:
        return 'profile image';
      case UserUploadType.postImage:
        return 'image';
    }
  }
}
