import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/features/community/models/post_model.dart';
import 'package:fit_4_force/features/community/models/comment_model.dart';
import 'package:fit_4_force/features/community/widgets/facebook_style_comment.dart';
import 'package:fit_4_force/features/community/widgets/photo_post_widget.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

class PostDetailScreen extends StatefulWidget {
  final PostModel post;
  final UserModel user;

  const PostDetailScreen({super.key, required this.post, required this.user});

  @override
  State<PostDetailScreen> createState() => _PostDetailScreenState();
}

class _PostDetailScreenState extends State<PostDetailScreen> {
  late PostModel _post;
  final TextEditingController _commentController = TextEditingController();
  final TextEditingController _replyController = TextEditingController();
  final FocusNode _commentFocusNode = FocusNode();
  final UserProgressService _progressService = UserProgressService();

  List<CommentModel> _comments = [];

  @override
  void initState() {
    super.initState();
    _post = widget.post;
    _loadCommentsWithMockData();
  }

  void _loadCommentsWithMockData() {
    // Mock comments with nested replies for demonstration
    _comments = [
      CommentModel(
        id: 'comment_1',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        userId: 'user_1',
        userName: 'Sarah Johnson',
        userProfileImageUrl: null,
        content:
            'This is really helpful! I\'ve been struggling with the same preparation challenges.',
        likesCount: 5,
        isLikedByCurrentUser: false,
        repliesCount: 2,
        reactionCounts: {'like': 3, 'love': 2},
        replies: [
          CommentModel(
            id: 'reply_1_1',
            createdAt: DateTime.now().subtract(const Duration(hours: 1)),
            userId: widget.user.id,
            userName: widget.user.fullName,
            userProfileImageUrl: widget.user.profileImageUrl,
            content:
                'Glad I could help! What specific area are you finding most challenging?',
            likesCount: 2,
            isLikedByCurrentUser: false,
            parentCommentId: 'comment_1',
            reactionCounts: {'like': 2},
          ),
          CommentModel(
            id: 'reply_1_2',
            createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
            userId: 'user_2',
            userName: 'Mike Chen',
            userProfileImageUrl: null,
            content: 'Same here! The physical fitness requirements are tough.',
            likesCount: 1,
            isLikedByCurrentUser: true,
            parentCommentId: 'comment_1',
            reactionCounts: {'like': 1},
          ),
        ],
      ),
      CommentModel(
        id: 'comment_2',
        createdAt: DateTime.now().subtract(const Duration(minutes: 45)),
        userId: 'user_3',
        userName: 'David Wilson',
        userProfileImageUrl: null,
        content:
            'Thanks for sharing your experience! This will definitely help others preparing for ${widget.post.agency}.',
        likesCount: 3,
        isLikedByCurrentUser: true,
        repliesCount: 0,
        reactionCounts: {'like': 2, 'love': 1},
      ),
    ];
  }

  @override
  void dispose() {
    _commentController.dispose();
    _replyController.dispose();
    _commentFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Post Details'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _sharePost(),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert),
            onPressed: () => _showPostOptions(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Post content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildPostContent(),
                  const SizedBox(height: 8),
                  _buildCommentsSection(),
                ],
              ),
            ),
          ),
          // Comment input
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildPostContent() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: _post.color.withOpacity(0.2),
                  backgroundImage:
                      _post.userProfileImageUrl != null
                          ? NetworkImage(_post.userProfileImageUrl!)
                          : null,
                  child:
                      _post.userProfileImageUrl == null
                          ? Text(
                            _post.userName.substring(0, 1).toUpperCase(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _post.color,
                              fontSize: 18,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _post.userName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getAgencyColor(
                                _post.agency,
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              _post.agency,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: _getAgencyColor(_post.agency),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _formatTimeAgo(_post.createdAt),
                            style: TextStyle(
                              fontSize: 13,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Post title and content
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _post.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  _post.content,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),

          // Post images if available (support multiple images)
          if (_post.imageUrls.isNotEmpty)
            PhotoPostWidget(
              post: _post,
              currentUser: widget.user,
              onLike: _toggleLike,
              onComment: () {
                // Scroll to comments section
                _commentFocusNode.requestFocus();
              },
              onReaction: (reaction) {
                // Handle post reactions
              },
              comments: _comments,
            )
          else if (_post.imageUrl != null)
            Container(
              margin: const EdgeInsets.symmetric(vertical: 16),
              height: 250,
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(_post.imageUrl!),
                  fit: BoxFit.cover,
                ),
              ),
            ),

          // Tags
          if (_post.tags.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    _post.tags.map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: Text(
                          '#$tag',
                          style: TextStyle(
                            fontSize: 13,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),

          // Post engagement stats
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Text(
                  '${_post.likesCount} likes',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(width: 16),
                Text(
                  '${_comments.length + _comments.fold(0, (sum, comment) => sum + comment.replies.length)} comments',
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Action buttons
          _buildActionButtons(),

          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon:
                  _post.isLikedByCurrentUser
                      ? Icons.favorite
                      : Icons.favorite_border,
              label: 'Like',
              color:
                  _post.isLikedByCurrentUser ? Colors.red : Colors.grey[600]!,
              onTap: _toggleLike,
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.chat_bubble_outline,
              label: 'Comment',
              color: Colors.grey[600]!,
              onTap: () => _commentFocusNode.requestFocus(),
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.share,
              label: 'Share',
              color: Colors.grey[600]!,
              onTap: _sharePost,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 20, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsSection() {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_comments.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Comments',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _comments.length,
              itemBuilder: (context, index) {
                return FacebookStyleComment(
                  comment: _comments[index],
                  currentUser: widget.user,
                  onLike: () => _toggleCommentLike(_comments[index]),
                  onReaction:
                      (reaction) =>
                          _addCommentReaction(_comments[index], reaction),
                  onReplySubmit:
                      (content, imagePaths) =>
                          _submitReply(_comments[index], content, imagePaths),
                );
              },
            ),
          ] else
            Padding(
              padding: const EdgeInsets.all(32),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No comments yet',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Be the first to share your thoughts!',
                      style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12,
        bottom: MediaQuery.of(context).viewInsets.bottom + 12,
      ),
      child: SafeArea(
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.grey[300],
              backgroundImage:
                  widget.user.profileImageUrl != null
                      ? NetworkImage(widget.user.profileImageUrl!)
                      : null,
              child:
                  widget.user.profileImageUrl == null
                      ? Text(
                        widget.user.fullName.substring(0, 1).toUpperCase(),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      )
                      : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _commentController,
                        focusNode: _commentFocusNode,
                        decoration: const InputDecoration(
                          hintText: 'Write a comment...',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                        maxLines: null,
                        textCapitalization: TextCapitalization.sentences,
                      ),
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.send,
                        color:
                            _commentController.text.isNotEmpty
                                ? AppTheme.primaryColor
                                : Colors.grey[400],
                      ),
                      onPressed: _submitComment,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleLike() {
    setState(() {
      _post = _post.copyWith(
        isLikedByCurrentUser: !_post.isLikedByCurrentUser,
        likesCount:
            _post.isLikedByCurrentUser
                ? _post.likesCount - 1
                : _post.likesCount + 1,
      );
    });
  }

  void _addCommentReaction(CommentModel comment, String reaction) {
    setState(() {
      final index = _comments.indexWhere((c) => c.id == comment.id);
      if (index != -1) {
        final currentReactions = Map<String, int>.from(comment.reactionCounts);
        final userReactions = Map<String, String>.from(comment.userReactions);

        // Remove any existing reaction from this user
        for (var existingReaction in userReactions.values) {
          if (currentReactions.containsKey(existingReaction)) {
            currentReactions[existingReaction] =
                (currentReactions[existingReaction]! - 1)
                    .clamp(0, double.infinity)
                    .toInt();
          }
        }

        // Add new reaction
        userReactions[widget.user.id] = reaction;
        currentReactions[reaction] = (currentReactions[reaction] ?? 0) + 1;

        _comments[index] = comment.copyWith(
          reactionCounts: currentReactions,
          userReactions: userReactions,
        );
      }
    });
  }

  void _submitReply(
    CommentModel parentComment,
    String content,
    List<String> imagePaths,
  ) {
    if (content.trim().isEmpty && imagePaths.isEmpty) return;

    final newReply = CommentModel(
      id: 'reply_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      userId: widget.user.id,
      userName: widget.user.fullName,
      userProfileImageUrl: widget.user.profileImageUrl,
      content: content,
      imageUrls: imagePaths,
      likesCount: 0,
      isLikedByCurrentUser: false,
      parentCommentId: parentComment.id,
    );

    setState(() {
      final index = _comments.indexWhere((c) => c.id == parentComment.id);
      if (index != -1) {
        final updatedReplies = List<CommentModel>.from(parentComment.replies)
          ..add(newReply);

        _comments[index] = parentComment.copyWith(
          replies: updatedReplies,
          repliesCount: updatedReplies.length,
        );
      }
    });
  }

  void _toggleCommentLike(CommentModel comment) {
    setState(() {
      final commentIndex = _comments.indexWhere((c) => c.id == comment.id);
      if (commentIndex != -1) {
        _comments[commentIndex] = comment.copyWith(
          isLikedByCurrentUser: !comment.isLikedByCurrentUser,
          likesCount:
              comment.isLikedByCurrentUser
                  ? comment.likesCount - 1
                  : comment.likesCount + 1,
        );
      } else {
        // Check if it's a reply
        for (int i = 0; i < _comments.length; i++) {
          final replyIndex = _comments[i].replies.indexWhere(
            (r) => r.id == comment.id,
          );
          if (replyIndex != -1) {
            final updatedReplies = List<CommentModel>.from(
              _comments[i].replies,
            );
            updatedReplies[replyIndex] = comment.copyWith(
              isLikedByCurrentUser: !comment.isLikedByCurrentUser,
              likesCount:
                  comment.isLikedByCurrentUser
                      ? comment.likesCount - 1
                      : comment.likesCount + 1,
            );
            _comments[i] = _comments[i].copyWith(replies: updatedReplies);
            break;
          }
        }
      }
    });
  }

  void _submitComment() async {
    if (_commentController.text.trim().isEmpty) return;

    final newComment = CommentModel(
      id: 'comment_${DateTime.now().millisecondsSinceEpoch}',
      createdAt: DateTime.now(),
      userId: widget.user.id,
      userName: widget.user.fullName,
      userProfileImageUrl: widget.user.profileImageUrl,
      content: _commentController.text.trim(),
      likesCount: 0,
      isLikedByCurrentUser: false,
    );

    setState(() {
      _comments.add(newComment);
      _commentController.clear();
    });

    // Update progress
    await _progressService.incrementValue('community', 'commentsPosted');

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Comment posted successfully!')),
      );
    }
  }

  void _sharePost() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('Sharing post: ${_post.title}')));
  }

  void _showPostOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.bookmark_border),
                title: const Text('Save Post'),
                onTap: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post saved successfully!')),
                  );
                },
              ),
              ListTile(
                leading: const Icon(Icons.share),
                title: const Text('Share Post'),
                onTap: () {
                  Navigator.pop(context);
                  _sharePost();
                },
              ),
              if (_post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Edit Post'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement edit functionality
                  },
                ),
              if (_post.userId == widget.user.id)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text(
                    'Delete Post',
                    style: TextStyle(color: Colors.red),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: Implement delete functionality
                  },
                ),
              ListTile(
                leading: const Icon(Icons.flag),
                title: const Text('Report Post'),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Implement report functionality
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.lightBlue;
      case 'DSSC':
        return Colors.purple;
      case 'NDA':
        return Colors.red;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()}y ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()}mo ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
