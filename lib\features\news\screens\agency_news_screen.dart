import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/news/models/agency_news_model.dart';
import 'package:fit_4_force/features/news/services/agency_news_service.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// Enhanced Agency News Screen with personalized content
class AgencyNewsScreen extends StatefulWidget {
  final UserModel user;

  const AgencyNewsScreen({super.key, required this.user});

  @override
  State<AgencyNewsScreen> createState() => _AgencyNewsScreenState();
}

class _AgencyNewsScreenState extends State<AgencyNewsScreen>
    with TickerProviderStateMixin {
  final AgencyNewsService _newsService = AgencyNewsService();
  late TabController _tabController;

  List<AgencyNewsModel> _personalizedNews = [];
  List<AgencyNewsModel> _breakingNews = [];
  List<AgencyNewsModel> _deadlineNews = [];
  List<AgencyNewsModel> _allNews = [];

  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeLiveNews();
  }

  Future<void> _initializeLiveNews() async {
    // Initialize live news service
    await _newsService.initializeLiveNews();
    await _loadAllNews();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _newsService.dispose();
    super.dispose();
  }

  Future<void> _loadAllNews() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final results = await Future.wait([
        _newsService.getPersonalizedNews(
          targetAgency: widget.user.targetAgency,
        ),
        _newsService.getBreakingNews(),
        _newsService.getNewsWithDeadlines(),
        _newsService.getAllNews(),
      ]);

      setState(() {
        _personalizedNews = results[0];
        _breakingNews = results[1];
        _deadlineNews = results[2];
        _allNews = results[3];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load news. Please try again.';
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshNews() async {
    await _newsService.forceRefreshLiveNews();
    await _loadAllNews();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _errorMessage != null
                    ? _buildErrorState()
                    : TabBarView(
                      controller: _tabController,
                      children: [
                        _buildPersonalizedTab(),
                        _buildBreakingNewsTab(),
                        _buildDeadlinesTab(),
                        _buildAllNewsTab(),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Agency News',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Latest updates for ${widget.user.targetAgency}',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.search, color: Colors.white),
                  onPressed: _showSearchDialog,
                ),
                IconButton(
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: _refreshNews,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildNewsStats(),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsStats() {
    final stats = _newsService.getLiveNewsStatus();
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            icon: Icons.article,
            label: 'Total News',
            value: '${stats['total_news']}',
          ),
          _buildStatItem(
            icon: Icons.flash_on,
            label: 'Breaking',
            value: '${stats['breaking_news']}',
          ),
          _buildStatItem(
            icon: Icons.schedule,
            label: 'Deadlines',
            value: '${stats['news_with_deadlines']}',
          ),
          _buildStatItem(
            icon: stats['live_mode_enabled'] == true ? Icons.live_tv : Icons.tv_off,
            label: 'Live Mode',
            value: stats['live_mode_enabled'] == true ? 'ON' : 'OFF',
            color: stats['live_mode_enabled'] == true ? Colors.green : Colors.orange,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    Color? color,
  }) {
    final iconColor = color ?? Colors.white;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: iconColor, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: iconColor,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(color: iconColor.withOpacity(0.7), fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.grey,
        indicatorColor: AppTheme.primaryColor,
        indicatorWeight: 3,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
        tabs: [
          Tab(
            text: 'FOR YOU',
            icon: Badge(
              label: Text('${_personalizedNews.length}'),
              child: const Icon(Icons.person, size: 16),
            ),
          ),
          Tab(
            text: 'BREAKING',
            icon: Badge(
              label: Text('${_breakingNews.length}'),
              child: const Icon(Icons.warning, size: 16),
            ),
          ),
          Tab(
            text: 'DEADLINES',
            icon: Badge(
              label: Text('${_deadlineNews.length}'),
              child: const Icon(Icons.schedule, size: 16),
            ),
          ),
          Tab(
            text: 'ALL NEWS',
            icon: Badge(
              label: Text('${_allNews.length}'),
              child: const Icon(Icons.list, size: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalizedTab() {
    return RefreshIndicator(
      onRefresh: _refreshNews,
      child:
          _personalizedNews.isEmpty
              ? _buildEmptyState(
                icon: Icons.person_outline,
                title: 'No personalized news yet',
                message:
                    'We\'ll show relevant news for ${widget.user.targetAgency} here.',
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _personalizedNews.length,
                itemBuilder: (context, index) {
                  return _buildNewsCard(
                    _personalizedNews[index],
                    isPersonalized: true,
                  );
                },
              ),
    );
  }

  Widget _buildBreakingNewsTab() {
    return RefreshIndicator(
      onRefresh: _refreshNews,
      child:
          _breakingNews.isEmpty
              ? _buildEmptyState(
                icon: Icons.notifications_active,
                title: 'No breaking news',
                message: 'All quiet on the news front!',
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _breakingNews.length,
                itemBuilder: (context, index) {
                  return _buildNewsCard(_breakingNews[index], isBreaking: true);
                },
              ),
    );
  }

  Widget _buildDeadlinesTab() {
    return RefreshIndicator(
      onRefresh: _refreshNews,
      child:
          _deadlineNews.isEmpty
              ? _buildEmptyState(
                icon: Icons.schedule,
                title: 'No upcoming deadlines',
                message: 'No application deadlines at the moment.',
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _deadlineNews.length,
                itemBuilder: (context, index) {
                  return _buildNewsCard(
                    _deadlineNews[index],
                    showDeadline: true,
                  );
                },
              ),
    );
  }

  Widget _buildAllNewsTab() {
    return RefreshIndicator(
      onRefresh: _refreshNews,
      child:
          _allNews.isEmpty
              ? _buildEmptyState(
                icon: Icons.article_outlined,
                title: 'No news available',
                message: 'Check back later for updates.',
              )
              : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _allNews.length,
                itemBuilder: (context, index) {
                  return _buildNewsCard(_allNews[index]);
                },
              ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String message,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 80, color: Colors.grey[300]),
            const SizedBox(height: 24),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshNews,
              icon: const Icon(Icons.refresh),
              label: const Text('Refresh'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 80, color: Colors.red),
            const SizedBox(height: 24),
            const Text(
              'Oops! Something went wrong',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              _errorMessage ?? 'Unknown error occurred',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadAllNews,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsCard(
    AgencyNewsModel news, {
    bool isPersonalized = false,
    bool isBreaking = false,
    bool showDeadline = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
        border:
            isBreaking
                ? Border.all(color: Colors.red, width: 2)
                : Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openNewsDetail(news),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with badges
                Row(
                  children: [
                    Expanded(
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 4,
                        children: [
                          if (news.isPinned)
                            _buildBadge(
                              'PINNED',
                              Colors.orange,
                              Icons.push_pin,
                            ),
                          if (news.isBreaking)
                            _buildBadge('BREAKING', Colors.red, Icons.warning),
                          if (news.source.contains('🔴 LIVE:'))
                            _buildBadge('LIVE', Colors.green, Icons.live_tv),
                          if (isPersonalized)
                            _buildBadge('FOR YOU', Colors.purple, Icons.person),
                          _buildBadge(
                            news.agency,
                            _getAgencyColor(news.agency),
                            null,
                          ),
                          _buildBadge(news.category, Colors.blue, null),
                        ],
                      ),
                    ),
                    Text(
                      _formatTimeAgo(news.publishedDate),
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // Title
                Text(
                  news.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 8),

                // Content preview
                Text(
                  news.content,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                ),

                // Deadline warning if applicable
                if (showDeadline && news.applicationDeadline != null)
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.access_time,
                          color: Colors.orange,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Application deadline: ${_formatDate(news.applicationDeadline!)}',
                            style: const TextStyle(
                              color: Colors.orange,
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 12),

                // Footer with engagement stats
                Row(
                  children: [
                    Icon(Icons.visibility, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${news.viewsCount} views',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const Spacer(),
                    TextButton.icon(
                      onPressed: () => _openNewsDetail(news),
                      icon: const Icon(Icons.read_more, size: 16),
                      label: const Text('Read More'),
                      style: TextButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        textStyle: const TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBadge(String label, Color color, IconData? icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 12, color: color),
            const SizedBox(width: 4),
          ],
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Color _getAgencyColor(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Colors.green;
      case 'Navy':
        return Colors.blue;
      case 'Air Force':
        return Colors.lightBlue;
      case 'DSSC':
        return Colors.purple;
      case 'Nigeria Police Force':
        return Colors.indigo;
      case 'NSCDC':
        return Colors.orange;
      case 'EFCC':
        return Colors.teal;
      case 'Joint Forces':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _openNewsDetail(AgencyNewsModel news) {
    // Mark as read
    _newsService.markAsRead(news.id, widget.user.id);

    // Show detailed news in a dialog or navigate to detail screen
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(news.title),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${news.agency} • ${news.category}',
                    style: TextStyle(
                      color: _getAgencyColor(news.agency),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(news.content),
                  if (news.applicationDeadline != null) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.schedule, color: Colors.orange),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Deadline: ${_formatDate(news.applicationDeadline!)}',
                              style: const TextStyle(
                                color: Colors.orange,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final searchController = TextEditingController();
        return AlertDialog(
          title: const Text('Search News'),
          content: TextField(
            controller: searchController,
            decoration: const InputDecoration(
              hintText: 'Enter keywords...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (searchController.text.isNotEmpty) {
                  Navigator.pop(context);
                  final results = await _newsService.searchNews(
                    searchController.text,
                  );
                  _showSearchResults(results, searchController.text);
                }
              },
              child: const Text('Search'),
            ),
          ],
        );
      },
    );
  }

  void _showSearchResults(List<AgencyNewsModel> results, String query) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Search Results for "$query"'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child:
                  results.isEmpty
                      ? const Center(child: Text('No results found'))
                      : ListView.builder(
                        itemCount: results.length,
                        itemBuilder: (context, index) {
                          final news = results[index];
                          return ListTile(
                            title: Text(news.title),
                            subtitle: Text('${news.agency} • ${news.category}'),
                            onTap: () {
                              Navigator.pop(context);
                              _openNewsDetail(news);
                            },
                          );
                        },
                      ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final difference = DateTime.now().difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
