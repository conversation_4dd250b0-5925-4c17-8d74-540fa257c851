import 'package:fit_4_force/features/content/models/agency_history_model.dart';

/// Service for managing agency-specific historical content
class AgencyHistoryService {
  static final Map<String, AgencyHistoryModel> _agencyHistories = {
    'NDA': AgencyHistoryModel(
      agency: 'Nigerian Defence Academy (NDA)',
      abbreviation: 'NDA',
      establishedYear: 1964,
      location: 'Kaduna, Nigeria',
      motto: 'To Serve with Integrity',
      overview:
          '''The Nigerian Defence Academy (NDA) stands as Nigeria's premier military institution, established on January 5, 1964, to train and educate future military officers for the Nigerian Armed Forces. Located in the historic city of Kaduna, the Academy has been the cornerstone of military excellence and leadership development in Nigeria for over five decades.''',
      foundingStory:
          '''The establishment of NDA was born from Nigeria's post-independence vision to build a strong, indigenous military leadership. Following independence in 1960, there was a critical need to develop Nigerian officers who could lead the nation's armed forces with competence and patriotism.

The Academy was founded through the collaborative efforts of the Nigerian government and British military advisors, drawing inspiration from prestigious institutions like Sandhurst Royal Military Academy. The first commandant, Brigadier <PERSON>, laid the foundation for what would become Nigeria's most respected military institution.''',
      keyMilestones: [
        MilestoneModel(
          year: '1964',
          event: 'Official establishment and commencement of training',
          significance:
              'First batch of cadets began their journey to becoming officers',
        ),
        MilestoneModel(
          year: '1967',
          event: 'First graduation ceremony',
          significance:
              'Produced the first set of Nigerian-trained military officers',
        ),
        MilestoneModel(
          year: '1970',
          event: 'Post-Civil War reconstruction and expansion',
          significance:
              'Academy played crucial role in national reconciliation',
        ),
        MilestoneModel(
          year: '1985',
          event: 'Introduction of degree programs',
          significance: 'Enhanced academic standards and officer development',
        ),
        MilestoneModel(
          year: '1999',
          event: 'Return to democratic governance support',
          significance: 'Trained officers who supported democratic transition',
        ),
        MilestoneModel(
          year: '2010',
          event: 'Modern facility upgrades and technology integration',
          significance: 'Adapted to 21st-century military training needs',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'General Yakubu Gowon',
          role: 'Former Head of State',
          achievement:
              'Led Nigeria through Civil War and post-war reconstruction',
        ),
        NotableFigureModel(
          name: 'General Olusegun Obasanjo',
          role: 'Former Head of State & President',
          achievement:
              'First military leader to voluntarily hand over power to civilians',
        ),
        NotableFigureModel(
          name: 'General Ibrahim Babangida',
          role: 'Former Military President',
          achievement: 'Modernized the Nigerian military structure',
        ),
        NotableFigureModel(
          name: 'General Abdulsalami Abubakar',
          role: 'Former Head of State',
          achievement: 'Facilitated smooth transition to democracy in 1999',
        ),
      ],
      traditions:
          '''NDA's rich traditions include the prestigious passing-out parades, the honor code system, and the strong alumni network that spans across all sectors of Nigerian society. The Academy's core values of integrity, excellence, and service continue to shape military and civilian leaders who contribute significantly to national development.''',
      modernRole:
          '''Today, NDA continues to evolve, incorporating modern military science, technology, and leadership principles while maintaining its foundational values. The Academy now offers various degree programs and has expanded to accommodate the changing needs of contemporary warfare and peacekeeping operations.''',
      funFacts: [
        'NDA is the only military academy in West Africa that offers degree programs',
        'The Academy has produced over 10,000 officers since its establishment',
        'NDA alumni include former heads of state and military leaders across Africa',
        'The Academy\'s library is one of the largest military libraries in Africa',
        'NDA has exchange programs with military academies worldwide',
      ],
      trainingPrograms: [
        'Regular Course (5-year degree program)',
        'Short Service Course (1-year program)',
        'Direct Short Service Course',
        'Postgraduate programs',
        'Professional military education',
      ],
      achievements: [
        'Produced numerous military leaders and heads of state',
        'Established as a center of excellence in military education',
        'International recognition for military training standards',
        'Strong alumni network in military and civilian sectors',
        'Continuous modernization of training facilities and curriculum',
      ],
    ),

    'POLAC': AgencyHistoryModel(
      agency: 'Nigeria Police Academy (POLAC)',
      abbreviation: 'POLAC',
      establishedYear: 1988,
      location: 'Wudil, Kano State',
      motto: 'Knowledge, Integrity, Service',
      overview:
          '''The Nigeria Police Academy (POLAC) represents the pinnacle of law enforcement education in Nigeria. Established in 1988 in Wudil, Kano State, POLAC has been dedicated to producing highly skilled, ethical, and professional police officers who serve as guardians of peace, security, and justice in Nigerian society.''',
      foundingStory:
          '''POLAC was established in response to the growing need for professionally trained police officers who could address the complex security challenges facing Nigeria. The Academy was conceived as part of broader police reforms aimed at transforming the Nigeria Police Force into a modern, efficient, and community-oriented law enforcement agency.

The founding vision emphasized creating officers who would combine academic excellence with practical policing skills, ethical leadership, and a deep understanding of human rights and community policing principles.''',
      keyMilestones: [
        MilestoneModel(
          year: '1988',
          event: 'Official establishment of POLAC',
          significance:
              'Marked the beginning of formal police officer education in Nigeria',
        ),
        MilestoneModel(
          year: '1992',
          event: 'First graduation ceremony',
          significance:
              'Produced the first cohort of degree-holding police officers',
        ),
        MilestoneModel(
          year: '1999',
          event: 'Democratic governance transition support',
          significance: 'Trained officers for democratic policing principles',
        ),
        MilestoneModel(
          year: '2005',
          event: 'Introduction of specialized training programs',
          significance:
              'Enhanced focus on cybercrime, terrorism, and modern policing',
        ),
        MilestoneModel(
          year: '2015',
          event: 'Community policing initiative launch',
          significance:
              'Shifted focus toward community-oriented law enforcement',
        ),
        MilestoneModel(
          year: '2020',
          event: 'Digital transformation and modern curriculum',
          significance:
              'Adapted to contemporary security challenges and technology',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'Inspector-General Sunday Ehindero',
          role: 'Former Inspector-General of Police',
          achievement: 'Modernized police training and community relations',
        ),
        NotableFigureModel(
          name: 'Inspector-General Mike Okiro',
          role: 'Former Inspector-General of Police',
          achievement: 'Championed police professionalism and accountability',
        ),
        NotableFigureModel(
          name: 'Inspector-General Hafiz Ringim',
          role: 'Former Inspector-General of Police',
          achievement:
              'Enhanced police intelligence and counter-terrorism capabilities',
        ),
        NotableFigureModel(
          name: 'Inspector-General Mohammed Adamu',
          role: 'Former Inspector-General of Police',
          achievement:
              'Promoted community policing and police-citizen partnerships',
        ),
      ],
      traditions:
          '''POLAC's traditions center around the principles of integrity, service excellence, and community partnership. The Academy maintains strong ceremonial traditions including passing-out parades, oath-taking ceremonies, and the cultivation of esprit de corps among cadets. The institution emphasizes continuous learning, ethical leadership, and commitment to constitutional policing.''',
      modernRole:
          '''In contemporary Nigeria, POLAC continues to adapt its curriculum to address modern security challenges including cybercrime, terrorism, human trafficking, and financial crimes. The Academy now emphasizes technology-driven policing, human rights protection, gender-sensitive policing, and international cooperation in law enforcement.''',
      funFacts: [
        'POLAC is the first and only police academy in Nigeria',
        'The Academy has trained over 15,000 police officers since establishment',
        'POLAC offers both undergraduate and postgraduate degree programs',
        'The Academy has partnerships with international police training institutions',
        'POLAC graduates serve in various specialized police units across Nigeria',
      ],
      trainingPrograms: [
        'Cadet Assistant Superintendent of Police (CASP) Course',
        'Cadet Inspector Course',
        'Specialized training programs',
        'Continuing professional development',
        'International exchange programs',
      ],
      achievements: [
        'Established modern police training standards in Nigeria',
        'Produced professional police officers for all states',
        'International recognition for police training excellence',
        'Development of specialized law enforcement curricula',
        'Strong alumni network in law enforcement and security sectors',
      ],
    ),

    'NIS': AgencyHistoryModel(
      agency: 'Nigeria Immigration Service (NIS)',
      abbreviation: 'NIS',
      establishedYear: 1958,
      location: 'Various Training Centers Nationwide',
      motto: 'Securing Borders, Protecting Lives',
      overview:
          '''The Nigeria Immigration Service (NIS) has been the guardian of Nigeria's borders since 1958, evolving from a small colonial administrative unit into a sophisticated, technology-driven border security organization. NIS plays a crucial role in national security, economic development, and international relations through effective border management and immigration control.''',
      foundingStory:
          '''Originally established as the Immigration Department under colonial administration, NIS gained prominence during Nigeria's independence transition. The service was restructured to meet the needs of a sovereign nation, with responsibilities for controlling entry and exit of persons, issuing travel documents, and ensuring border security.

The transformation from a colonial administrative function to a modern immigration service reflects Nigeria's journey toward sovereignty and its commitment to controlled, beneficial immigration that supports national development while maintaining security.''',
      keyMilestones: [
        MilestoneModel(
          year: '1958',
          event: 'Establishment as Immigration Department',
          significance: 'Beginning of organized immigration control in Nigeria',
        ),
        MilestoneModel(
          year: '1963',
          event: 'Post-independence restructuring',
          significance:
              'Adapted to serve independent Nigeria\'s immigration needs',
        ),
        MilestoneModel(
          year: '1980',
          event: 'Formation of Nigeria Immigration Service',
          significance: 'Elevated status and expanded responsibilities',
        ),
        MilestoneModel(
          year: '1995',
          event: 'Introduction of machine-readable passports',
          significance: 'Modernized travel document issuance',
        ),
        MilestoneModel(
          year: '2007',
          event: 'Launch of e-passport system',
          significance: 'Enhanced security and international compliance',
        ),
        MilestoneModel(
          year: '2020',
          event: 'Digital border management implementation',
          significance: 'Integrated technology for efficient border control',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'Comptroller-General Martin Abeshi',
          role: 'Former Comptroller-General',
          achievement: 'Modernized immigration processes and border security',
        ),
        NotableFigureModel(
          name: 'Comptroller-General David Parradang',
          role: 'Former Comptroller-General',
          achievement:
              'Enhanced international cooperation and training programs',
        ),
        NotableFigureModel(
          name: 'Comptroller-General Muhammad Babandede',
          role: 'Former Comptroller-General',
          achievement: 'Led digital transformation and modernization efforts',
        ),
        NotableFigureModel(
          name: 'Comptroller-General Isah Jere Idris',
          role: 'Current Comptroller-General',
          achievement:
              'Advancing technology integration and border security enhancement',
        ),
      ],
      traditions:
          '''NIS maintains strong traditions of border vigilance, service excellence, and international cooperation. The service emphasizes continuous professional development, technological advancement, and adherence to international immigration standards. Officers are trained to balance security requirements with facilitation of legitimate travel and trade.''',
      modernRole:
          '''Today's NIS operates sophisticated border management systems, biometric data collection, and intelligence-driven immigration control. The service actively combats human trafficking, irregular migration, and transnational crimes while facilitating legitimate travel and supporting Nigeria's economic development through effective immigration policies.''',
      funFacts: [
        'NIS manages over 84 official border crossing points across Nigeria',
        'The service processes millions of passport applications annually',
        'NIS operates one of Africa\'s most advanced biometric systems',
        'The service has international partnerships with over 50 countries',
        'NIS officers are trained in multiple languages for international cooperation',
      ],
      trainingPrograms: [
        'Immigration Officer Training Course',
        'Border Management Specialist Program',
        'Anti-trafficking and Human Rights Training',
        'Technology and Biometric Systems Training',
        'International cooperation and diplomatic protocols',
      ],
      achievements: [
        'Modernized Nigeria\'s immigration and border control systems',
        'Established comprehensive passport and visa services',
        'Developed advanced anti-trafficking capabilities',
        'International recognition for border security innovations',
        'Strong partnerships with global immigration agencies',
      ],
    ),

    'NSCDC': AgencyHistoryModel(
      agency: 'Nigeria Security and Civil Defence Corps (NSCDC)',
      abbreviation: 'NSCDC',
      establishedYear: 1967,
      location: 'Abuja (Headquarters)',
      motto: 'Peace Through Vigilance',
      overview:
          '''The Nigeria Security and Civil Defence Corps (NSCDC) emerged from Nigeria's need for a civilian-focused security organization during the challenging period of the Nigerian Civil War. Since 1967, NSCDC has evolved into a comprehensive security agency responsible for protecting lives, property, and critical infrastructure while maintaining civil order and disaster management capabilities.''',
      foundingStory:
          '''NSCDC was born out of necessity during the Nigerian Civil War (1967-1970) when there was an urgent need for a security organization that could protect civilian populations and critical infrastructure. Initially conceived as a civil defense mechanism, the corps was tasked with air raid precautions, evacuation procedures, and civilian protection during wartime.

The organization's founding philosophy centered on the protection of civilian life and property, making it distinct from traditional military and police forces. This civilian-centric approach has remained core to NSCDC's identity and operations.''',
      keyMilestones: [
        MilestoneModel(
          year: '1967',
          event: 'Establishment during Nigerian Civil War',
          significance:
              'Created to protect civilian populations during wartime',
        ),
        MilestoneModel(
          year: '1970',
          event: 'Post-war reorganization',
          significance: 'Transitioned from wartime to peacetime civil defense',
        ),
        MilestoneModel(
          year: '1988',
          event: 'Legal backing through Decree No. 2',
          significance: 'Formal recognition and expanded mandate',
        ),
        MilestoneModel(
          year: '2003',
          event: 'Armed status and enhanced powers',
          significance: 'Became a fully armed paramilitary organization',
        ),
        MilestoneModel(
          year: '2007',
          event: 'NSCDC Act establishment',
          significance: 'Comprehensive legal framework and clear mandate',
        ),
        MilestoneModel(
          year: '2020',
          event: 'Agro Rangers and specialized units creation',
          significance:
              'Expanded to address rural security and agricultural protection',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'Commandant-General Abdullahi Gana Muhammadu',
          role: 'Former Commandant-General',
          achievement: 'Modernized NSCDC operations and training programs',
        ),
        NotableFigureModel(
          name: 'Commandant-General Ade Abolurin',
          role: 'Former Commandant-General',
          achievement:
              'Enhanced inter-agency cooperation and intelligence sharing',
        ),
        NotableFigureModel(
          name: 'Commandant-General Ahmed Audi',
          role: 'Current Commandant-General',
          achievement: 'Leading modernization and specialized unit development',
        ),
      ],
      traditions:
          '''NSCDC's traditions emphasize civilian protection, community engagement, and disaster preparedness. The corps maintains a strong culture of vigilance, rapid response, and protection of vulnerable populations. Training emphasizes both security operations and civil defense responsibilities, creating well-rounded officers capable of diverse security challenges.''',
      modernRole:
          '''Modern NSCDC operates as a multi-faceted security organization with responsibilities including critical infrastructure protection, disaster management, agricultural security, counter-terrorism support, and civil defense coordination. The corps has developed specialized units like Agro Rangers, Chemical Biological Radiological Nuclear (CBRN) specialists, and rapid response teams.''',
      funFacts: [
        'NSCDC is the largest paramilitary organization in Nigeria',
        'The corps has over 180,000 personnel across all states',
        'NSCDC operates specialized units including Agro Rangers and CBRN teams',
        'The corps protects critical national infrastructure worth billions of naira',
        'NSCDC has partnerships with international civil defense organizations',
      ],
      trainingPrograms: [
        'Basic Civil Defence Training',
        'Critical Infrastructure Protection Course',
        'Disaster Management and Emergency Response',
        'Agro Rangers Specialized Training',
        'CBRN (Chemical, Biological, Radiological, Nuclear) Training',
      ],
      achievements: [
        'Established comprehensive civil defense capabilities nationwide',
        'Developed specialized agricultural security units',
        'Created advanced disaster response and management systems',
        'International recognition for civil defense innovations',
        'Strong community partnerships for security and safety',
      ],
    ),

    'FRSC': AgencyHistoryModel(
      agency: 'Federal Road Safety Corps (FRSC)',
      abbreviation: 'FRSC',
      establishedYear: 1988,
      location: 'Abuja (National Headquarters)',
      motto: 'Safety is No Accident',
      overview:
          '''The Federal Road Safety Corps (FRSC) was established in 1988 as Nigeria's specialized agency for road traffic administration and safety management. Born from the urgent need to address Nigeria's alarming road traffic accident rates, FRSC has become a model organization in road safety management, accident prevention, and traffic law enforcement across Africa.''',
      foundingStory:
          '''FRSC was created in response to Nigeria's growing road traffic crisis of the 1980s. With increasing vehicle ownership, expanding road networks, and rising accident rates, there was a critical need for a specialized agency dedicated solely to road safety and traffic management.

The founding vision was to create a professional, technology-driven organization that would reduce road traffic crashes, minimize losses from road accidents, and create a safer motoring environment for all Nigerians. Dr. Wole Adeniran, a road safety expert, provided much of the conceptual framework for the organization.''',
      keyMilestones: [
        MilestoneModel(
          year: '1988',
          event: 'Establishment through Decree No. 45',
          significance:
              'Created Nigeria\'s first specialized road safety agency',
        ),
        MilestoneModel(
          year: '1992',
          event: 'National deployment and operational commencement',
          significance: 'Extended operations to all states and major highways',
        ),
        MilestoneModel(
          year: '1999',
          event: 'Introduction of Drivers License production',
          significance: 'Standardized driver certification across Nigeria',
        ),
        MilestoneModel(
          year: '2007',
          event: 'Launch of electronic driving license',
          significance: 'Modernized driver documentation and verification',
        ),
        MilestoneModel(
          year: '2015',
          event: 'Digital number plate introduction',
          significance: 'Enhanced vehicle identification and tracking',
        ),
        MilestoneModel(
          year: '2020',
          event: 'AI-powered traffic management systems',
          significance:
              'Integrated artificial intelligence for traffic monitoring',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'Dr. Wole Adeniran',
          role: 'Founding Conceptual Architect',
          achievement:
              'Developed the framework for Nigeria\'s road safety management',
        ),
        NotableFigureModel(
          name: 'Corps Marshal Osita Chidoka',
          role: 'Former Corps Marshal',
          achievement: 'Modernized FRSC operations and introduced technology',
        ),
        NotableFigureModel(
          name: 'Corps Marshal Boboye Oyeyemi',
          role: 'Former Corps Marshal',
          achievement: 'Enhanced road safety education and enforcement',
        ),
        NotableFigureModel(
          name: 'Corps Marshal Dauda Biu',
          role: 'Current Corps Marshal',
          achievement:
              'Leading digital transformation and smart mobility initiatives',
        ),
      ],
      traditions:
          '''FRSC traditions center around professionalism, innovation, and public service. The corps maintains a culture of continuous improvement, technology adoption, and community engagement. Officers are trained to be educators, enforcers, and emergency responders, embodying the organization's commitment to road safety through diverse approaches.''',
      modernRole:
          '''Today's FRSC operates as a modern, technology-driven organization utilizing artificial intelligence, GPS tracking, biometric systems, and data analytics for road safety management. The corps has expanded beyond enforcement to include road safety research, public education, emergency response, and policy development for sustainable transportation.''',
      funFacts: [
        'FRSC has reduced road traffic fatalities by over 60% since establishment',
        'The corps operates the largest road safety database in West Africa',
        'FRSC has trained over 50,000 driving instructors nationwide',
        'The organization processes millions of driver\'s licenses annually',
        'FRSC has partnerships with road safety organizations in over 30 countries',
      ],
      trainingPrograms: [
        'Road Safety Officer Training Course',
        'Traffic Management and Enforcement',
        'Emergency Response and Rescue Operations',
        'Vehicle Inspection and Technical Training',
        'Public Education and Community Outreach',
      ],
      achievements: [
        'Established comprehensive road safety management system',
        'Significantly reduced road traffic accidents and fatalities',
        'Developed advanced driver training and licensing systems',
        'International recognition for road safety innovations',
        'Strong public-private partnerships for road safety',
      ],
    ),

    'DSS': AgencyHistoryModel(
      agency: 'Department of State Services (DSS)',
      abbreviation: 'DSS',
      establishedYear: 1986,
      location: 'Abuja (National Headquarters)',
      motto: 'Securing the Nation',
      overview:
          '''The Department of State Services (DSS) serves as Nigeria's primary domestic intelligence and security agency. Established in 1986 as part of intelligence sector reforms, DSS has the critical responsibility of detecting, preventing, and investigating threats to national security, maintaining state security, and providing intelligence support to Nigeria's democratic governance.''',
      foundingStory:
          '''DSS was established following the reorganization of Nigeria's intelligence architecture in 1986, when the former National Security Organization (NSO) was restructured into separate agencies. The creation of DSS reflected the need for a specialized domestic intelligence service that could address internal security threats while supporting democratic governance.

The agency was designed to operate within constitutional frameworks, balancing security requirements with democratic principles and civil liberties. This founding philosophy emphasized professional intelligence gathering, threat assessment, and security operations conducted within legal boundaries.''',
      keyMilestones: [
        MilestoneModel(
          year: '1986',
          event: 'Establishment through Decree No. 19',
          significance:
              'Created as Nigeria\'s primary domestic intelligence agency',
        ),
        MilestoneModel(
          year: '1999',
          event: 'Democratic transition adaptation',
          significance:
              'Restructured to support democratic governance and rule of law',
        ),
        MilestoneModel(
          year: '2009',
          event: 'Counter-terrorism capability enhancement',
          significance:
              'Developed specialized counter-terrorism and anti-extremism units',
        ),
        MilestoneModel(
          year: '2015',
          event: 'Regional security cooperation expansion',
          significance:
              'Enhanced collaboration with regional and international partners',
        ),
        MilestoneModel(
          year: '2020',
          event: 'Cyber security and digital intelligence integration',
          significance:
              'Adapted to address cyber threats and digital security challenges',
        ),
      ],
      notableFigures: [
        NotableFigureModel(
          name: 'Lawal Daura',
          role: 'Former Director-General',
          achievement:
              'Enhanced intelligence capabilities and operational effectiveness',
        ),
        NotableFigureModel(
          name: 'Matthew Seiyefa',
          role: 'Former Acting Director-General',
          achievement: 'Maintained stability during leadership transition',
        ),
        NotableFigureModel(
          name: 'Yusuf Bichi',
          role: 'Current Director-General',
          achievement:
              'Leading modernization and democratic intelligence operations',
        ),
      ],
      traditions:
          '''DSS maintains traditions of confidentiality, professionalism, and loyalty to constitutional authority. The service emphasizes intelligence professionalism, analytical rigor, and operational security. Training focuses on intelligence gathering, threat assessment, protective security, and maintaining the delicate balance between security operations and democratic values.''',
      modernRole:
          '''Contemporary DSS operates as a modern intelligence service addressing diverse threats including terrorism, cybercrime, economic sabotage, and threats to democratic institutions. The service employs advanced technology, intelligence analysis, and international cooperation to protect Nigeria's national security interests while respecting constitutional governance and human rights.''',
      funFacts: [
        'DSS is Nigeria\'s primary domestic intelligence and security service',
        'The service operates in all 36 states and the Federal Capital Territory',
        'DSS has specialized units for counter-terrorism and cybersecurity',
        'The service maintains international partnerships with global intelligence agencies',
        'DSS officers undergo rigorous psychological and security screening',
      ],
      trainingPrograms: [
        'Intelligence Officer Basic Course',
        'Counter-terrorism and Security Operations',
        'Cybersecurity and Digital Intelligence',
        'Protective Security and VIP Protection',
        'Intelligence Analysis and Threat Assessment',
      ],
      achievements: [
        'Established comprehensive domestic intelligence capabilities',
        'Developed advanced counter-terrorism and security operations',
        'Created sophisticated threat assessment and analysis systems',
        'International recognition for intelligence professionalism',
        'Strong partnerships with global intelligence and security agencies',
      ],
    ),
  };

  /// Get detailed history for a specific agency
  static AgencyHistoryModel? getAgencyHistory(String agency) {
    return _agencyHistories[agency.toUpperCase()];
  }

  /// Get list of all available agencies
  static List<String> getAvailableAgencies() {
    return _agencyHistories.keys.toList();
  }

  /// Get brief overview for an agency
  static String? getAgencyOverview(String agency) {
    return _agencyHistories[agency.toUpperCase()]?.overview;
  }

  /// Get agency establishment year
  static int? getEstablishmentYear(String agency) {
    return _agencyHistories[agency.toUpperCase()]?.establishedYear;
  }

  /// Get agency motto
  static String? getAgencyMotto(String agency) {
    return _agencyHistories[agency.toUpperCase()]?.motto;
  }

  /// Get agency location
  static String? getAgencyLocation(String agency) {
    return _agencyHistories[agency.toUpperCase()]?.location;
  }

  /// Search for specific information within agency histories
  static List<Map<String, dynamic>> searchAgencyHistory(String query) {
    List<Map<String, dynamic>> results = [];

    _agencyHistories.forEach((key, history) {
      if (history.overview.toLowerCase().contains(query.toLowerCase()) ||
          history.foundingStory.toLowerCase().contains(query.toLowerCase()) ||
          history.traditions.toLowerCase().contains(query.toLowerCase()) ||
          history.modernRole.toLowerCase().contains(query.toLowerCase())) {
        results.add({'agency': key, 'data': history});
      }
    });

    return results;
  }
}
