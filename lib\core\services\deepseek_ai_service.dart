import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';

/// DeepSeek R1 AI Service for Fit4Force
/// Provides AI-powered features for Nigerian military recruitment preparation
class DeepSeekAIService {
  static const String _baseUrl = 'https://api.openrouter.ai/api/v1';
  static const String _apiKey = '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';
  static const String _model = 'deepseek/deepseek-r1-0528:free';
  
  final Logger _logger = Logger();
  final http.Client _client = http.Client();

  /// Generate AI response for military preparation assistance
  Future<AIResponse> generateResponse({
    required String prompt,
    required AIContext context,
    int maxTokens = 1000,
    double temperature = 0.7,
  }) async {
    try {
      _logger.i('🤖 Generating AI response for context: ${context.name}');
      
      final enhancedPrompt = _buildContextualPrompt(prompt, context);
      
      final response = await _client.post(
        Uri.parse('$_baseUrl/chat/completions'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://fit4force.app',
          'X-Title': 'Fit4Force - Nigerian Military Prep',
        },
        body: jsonEncode({
          'model': _model,
          'messages': [
            {
              'role': 'system',
              'content': _getSystemPrompt(context),
            },
            {
              'role': 'user',
              'content': enhancedPrompt,
            },
          ],
          'max_tokens': maxTokens,
          'temperature': temperature,
          'stream': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'] as String;
        
        _logger.i('✅ AI response generated successfully');
        
        return AIResponse(
          content: content,
          context: context,
          tokensUsed: data['usage']['total_tokens'] ?? 0,
          success: true,
        );
      } else {
        _logger.e('❌ AI API Error: ${response.statusCode} - ${response.body}');
        return AIResponse(
          content: 'Sorry, I encountered an error. Please try again.',
          context: context,
          success: false,
          error: 'API Error: ${response.statusCode}',
        );
      }
    } catch (e) {
      _logger.e('❌ AI Service Exception: $e');
      return AIResponse(
        content: 'Sorry, I\'m temporarily unavailable. Please try again later.',
        context: context,
        success: false,
        error: e.toString(),
      );
    }
  }

  /// Build contextual prompt based on AI context
  String _buildContextualPrompt(String userPrompt, AIContext context) {
    switch (context) {
      case AIContext.studyAssistant:
        return '''
Nigerian Military Context: $userPrompt

Please provide detailed, accurate information relevant to Nigerian military recruitment preparation. Include specific examples from Nigerian military history, current affairs, and recruitment requirements where applicable.
''';

      case AIContext.quizGeneration:
        return '''
Generate Nigerian military recruitment exam questions about: $userPrompt

Requirements:
- Questions should reflect actual Nigerian military exam patterns
- Include questions about Nigerian history, geography, current affairs
- Reference Nigerian military agencies (Army, Navy, Air Force, etc.)
- Provide 4 multiple choice options with correct answers
- Include detailed explanations
''';

      case AIContext.fitnessCoaching:
        return '''
Nigerian Military Fitness Context: $userPrompt

Provide fitness guidance specifically for Nigerian military recruitment standards. Consider:
- Nigerian military fitness requirements
- Local climate and training conditions
- Available equipment and resources
- Cultural considerations for Nigerian military aspirants
''';

      case AIContext.performanceAnalysis:
        return '''
Analyze performance data for Nigerian military preparation: $userPrompt

Provide insights on:
- Strengths and weaknesses in preparation
- Specific recommendations for improvement
- Study strategies for Nigerian military exams
- Timeline and goal setting for recruitment preparation
''';

      case AIContext.careerGuidance:
        return '''
Nigerian Military Career Guidance: $userPrompt

Provide guidance about:
- Different Nigerian military agencies and their roles
- Career paths and opportunities
- Requirements and qualifications
- Preparation strategies for specific agencies
''';

      default:
        return userPrompt;
    }
  }

  /// Get system prompt based on context
  String _getSystemPrompt(AIContext context) {
    const basePrompt = '''
You are an AI assistant specialized in helping Nigerian military aspirants prepare for recruitment exams and fitness tests. You have deep knowledge of:

- Nigerian military history and structure
- Nigerian Armed Forces (Army, Navy, Air Force)
- Nigerian Defence Academy (NDA)
- Nigerian military recruitment processes
- Nigerian geography, history, and current affairs
- Military fitness standards and training
- Study strategies and exam preparation

Always provide accurate, helpful, and culturally relevant information for Nigerian military recruitment preparation.
''';

    switch (context) {
      case AIContext.studyAssistant:
        return '$basePrompt\n\nFocus on providing detailed study assistance and educational content.';
      
      case AIContext.quizGeneration:
        return '$basePrompt\n\nGenerate high-quality quiz questions that mirror actual Nigerian military recruitment exams.';
      
      case AIContext.fitnessCoaching:
        return '$basePrompt\n\nProvide expert fitness coaching tailored to Nigerian military standards.';
      
      case AIContext.performanceAnalysis:
        return '$basePrompt\n\nAnalyze performance data and provide actionable insights for improvement.';
      
      case AIContext.careerGuidance:
        return '$basePrompt\n\nProvide comprehensive career guidance for Nigerian military aspirants.';
      
      default:
        return basePrompt;
    }
  }

  /// Generate personalized study plan
  Future<AIResponse> generateStudyPlan({
    required String targetAgency,
    required int preparationTimeWeeks,
    required List<String> weakAreas,
    required String currentLevel,
  }) async {
    final prompt = '''
Create a personalized study plan for Nigerian $targetAgency recruitment preparation:

Preparation Time: $preparationTimeWeeks weeks
Current Level: $currentLevel
Weak Areas: ${weakAreas.join(', ')}

Please provide:
1. Weekly study schedule
2. Subject-wise time allocation
3. Specific topics to focus on
4. Practice test schedule
5. Fitness training integration
6. Milestone checkpoints
''';

    return generateResponse(
      prompt: prompt,
      context: AIContext.studyAssistant,
      maxTokens: 1500,
    );
  }

  /// Generate quiz questions
  Future<AIResponse> generateQuizQuestions({
    required String subject,
    required String targetAgency,
    required int questionCount,
    required String difficultyLevel,
  }) async {
    final prompt = '''
Generate $questionCount multiple-choice questions for Nigerian $targetAgency recruitment exam:

Subject: $subject
Difficulty: $difficultyLevel
Format: Multiple choice with 4 options each

Include:
- Nigerian-specific content and examples
- Current affairs relevant to Nigeria
- Military knowledge specific to $targetAgency
- Detailed explanations for correct answers
''';

    return generateResponse(
      prompt: prompt,
      context: AIContext.quizGeneration,
      maxTokens: 2000,
    );
  }

  /// Analyze performance and provide recommendations
  Future<AIResponse> analyzePerformance({
    required Map<String, dynamic> performanceData,
    required String targetAgency,
  }) async {
    final prompt = '''
Analyze this Nigerian military recruitment preparation performance:

Target Agency: $targetAgency
Performance Data: ${jsonEncode(performanceData)}

Provide:
1. Strengths and weaknesses analysis
2. Specific improvement recommendations
3. Study strategy adjustments
4. Timeline recommendations
5. Focus areas for next phase
''';

    return generateResponse(
      prompt: prompt,
      context: AIContext.performanceAnalysis,
      maxTokens: 1500,
    );
  }

  /// Get fitness coaching advice
  Future<AIResponse> getFitnessCoaching({
    required String fitnessGoal,
    required Map<String, dynamic> currentStats,
    required String targetAgency,
  }) async {
    final prompt = '''
Provide fitness coaching for Nigerian $targetAgency recruitment:

Fitness Goal: $fitnessGoal
Current Stats: ${jsonEncode(currentStats)}

Include:
1. Specific exercise recommendations
2. Training schedule
3. Nutrition advice for Nigerian context
4. Progress tracking methods
5. Military fitness test preparation
''';

    return generateResponse(
      prompt: prompt,
      context: AIContext.fitnessCoaching,
      maxTokens: 1500,
    );
  }

  void dispose() {
    _client.close();
  }
}

/// AI Context for different use cases
enum AIContext {
  studyAssistant,
  quizGeneration,
  fitnessCoaching,
  performanceAnalysis,
  careerGuidance,
  general,
}

extension AIContextExtension on AIContext {
  String get name {
    switch (this) {
      case AIContext.studyAssistant:
        return 'Study Assistant';
      case AIContext.quizGeneration:
        return 'Quiz Generation';
      case AIContext.fitnessCoaching:
        return 'Fitness Coaching';
      case AIContext.performanceAnalysis:
        return 'Performance Analysis';
      case AIContext.careerGuidance:
        return 'Career Guidance';
      case AIContext.general:
        return 'General';
    }
  }

  String get description {
    switch (this) {
      case AIContext.studyAssistant:
        return 'Get personalized study help and explanations';
      case AIContext.quizGeneration:
        return 'Generate practice questions and tests';
      case AIContext.fitnessCoaching:
        return 'Receive fitness guidance and training plans';
      case AIContext.performanceAnalysis:
        return 'Analyze your progress and get improvement tips';
      case AIContext.careerGuidance:
        return 'Get career advice and agency information';
      case AIContext.general:
        return 'General military preparation assistance';
    }
  }

  IconData get icon {
    switch (this) {
      case AIContext.studyAssistant:
        return Icons.school;
      case AIContext.quizGeneration:
        return Icons.quiz;
      case AIContext.fitnessCoaching:
        return Icons.fitness_center;
      case AIContext.performanceAnalysis:
        return Icons.analytics;
      case AIContext.careerGuidance:
        return Icons.work;
      case AIContext.general:
        return Icons.smart_toy;
    }
  }
}

/// AI Response model
class AIResponse {
  final String content;
  final AIContext context;
  final int tokensUsed;
  final bool success;
  final String? error;
  final DateTime timestamp;

  AIResponse({
    required this.content,
    required this.context,
    this.tokensUsed = 0,
    required this.success,
    this.error,
  }) : timestamp = DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'content': content,
      'context': context.name,
      'tokensUsed': tokensUsed,
      'success': success,
      'error': error,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AIResponse.fromJson(Map<String, dynamic> json) {
    return AIResponse(
      content: json['content'],
      context: AIContext.values.firstWhere(
        (e) => e.name == json['context'],
        orElse: () => AIContext.general,
      ),
      tokensUsed: json['tokensUsed'] ?? 0,
      success: json['success'],
      error: json['error'],
    );
  }
}
