import 'package:flutter/material.dart';
import 'package:fit_4_force/core/services/user_storage_service.dart';
import 'package:fit_4_force/features/prep/models/study_material_model.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Enhanced study material service that integrates with Supabase backend
/// for agency-specific educational content
class EnhancedStudyMaterialService {
  static final EnhancedStudyMaterialService _instance =
      EnhancedStudyMaterialService._internal();
  factory EnhancedStudyMaterialService() => _instance;
  EnhancedStudyMaterialService._internal();

  final UserStorageService _storageService = UserStorageService();
  final SupabaseClient _client = Supabase.instance.client;
  final Logger _logger = Logger();

  /// Get agency-specific content sections (categories)
  Future<List<StudyMaterialCategory>> getAgencyCategories() async {
    try {
      final sections = await _storageService.getContentSections();

      // Get material counts for each section
      final categoriesWithCounts = <StudyMaterialCategory>[];

      for (final section in sections) {
        try {
          final materials = await _storageService.getStudyMaterialsBySection(
            section['name'],
          );

          categoriesWithCounts.add(
            StudyMaterialCategory(
              id: section['id'],
              name: section['name'],
              description:
                  section['description'] ??
                  'Study materials for ${section['name']}',
              icon: _getIconForSection(section['name']),
              color: _getColorForSection(section['name']),
              materialCount: materials.length,
            ),
          );
        } catch (e) {
          _logger.w(
            '⚠️ Error getting material count for ${section['name']}: $e',
          );
          // Add category with fallback count
          categoriesWithCounts.add(
            StudyMaterialCategory(
              id: section['id'],
              name: section['name'],
              description:
                  section['description'] ??
                  'Study materials for ${section['name']}',
              icon: _getIconForSection(section['name']),
              color: _getColorForSection(section['name']),
              materialCount: 5, // Fallback count
            ),
          );
        }
      }

      return categoriesWithCounts.isNotEmpty
          ? categoriesWithCounts
          : _getFallbackCategories();
    } catch (e) {
      _logger.e('❌ Error getting agency categories: $e');
      return _getFallbackCategories();
    }
  }

  /// Get study materials for a specific section
  Future<List<StudyMaterialModel>> getMaterialsBySection(
    String sectionName,
  ) async {
    try {
      final materials = await _storageService.getStudyMaterialsBySection(
        sectionName,
      );

      // If no materials found in DB, use sample materials as fallback
      if (materials.isEmpty) {
        return _getSampleMaterialsForSection(sectionName);
      }

      return materials.map((material) {
        return StudyMaterialModel(
          id: material['id'],
          title: material['title'],
          description: material['description'] ?? '',
          category: material['content_sections']['name'],
          agency: material['agencies']['name'],
          contentType: _mapContentType(material['content_type']),
          contentUrl: material['file_url'] ?? '',
          publishedDate: DateTime.parse(material['published_at']),
          isPremium: material['is_premium'] ?? false,
          icon: _getIconForContentType(material['content_type']),
          color: _getColorForSection(material['content_sections']['name']),
          estimatedReadTime: _estimateReadTime(material),
          tags: List<String>.from(material['tags'] ?? []),
          difficulty: material['difficulty_level'],
          rating: (material['average_rating'] ?? 0.0).toDouble(),
          totalRatings: material['total_ratings'] ?? 0,
          viewCount: material['view_count'] ?? 0,
          downloadCount: material['download_count'] ?? 0,
        );
      }).toList();
    } catch (e) {
      _logger.e('❌ Error getting materials by section: $e');
      // Return sample materials for the section as fallback
      return _getSampleMaterialsForSection(sectionName);
    }
  }

  /// Get all study materials for user's agency
  Future<List<StudyMaterialModel>> getAllMaterials({
    String? contentType,
    bool? isPremiumOnly,
    String? difficulty,
  }) async {
    try {
      final materials = await _storageService.getStudyMaterials(
        contentType: contentType,
        isPremiumOnly: isPremiumOnly,
        difficulty: difficulty,
      );

      return materials.map((material) {
        return StudyMaterialModel(
          id: material['id'],
          title: material['title'],
          description: material['description'] ?? '',
          category: material['content_sections']['name'],
          agency: material['agencies']['name'],
          contentType: _mapContentType(material['content_type']),
          contentUrl: material['file_url'] ?? '',
          publishedDate: DateTime.parse(material['published_at']),
          isPremium: material['is_premium'] ?? false,
          icon: _getIconForContentType(material['content_type']),
          color: _getColorForSection(material['content_sections']['name']),
          estimatedReadTime: _estimateReadTime(material),
          tags: List<String>.from(material['tags'] ?? []),
          difficulty: material['difficulty_level'],
          rating: (material['average_rating'] ?? 0.0).toDouble(),
          totalRatings: material['total_ratings'] ?? 0,
          viewCount: material['view_count'] ?? 0,
          downloadCount: material['download_count'] ?? 0,
        );
      }).toList();
    } catch (e) {
      _logger.e('❌ Error getting all materials: $e');
      return [];
    }
  }

  /// Get featured materials for user's agency
  Future<List<StudyMaterialModel>> getFeaturedMaterials() async {
    try {
      final materials = await _storageService.getStudyMaterials(limit: 10);

      // Filter for featured materials (high ratings, recent, popular)
      final featuredMaterials =
          materials.where((material) {
            final rating = (material['average_rating'] ?? 0.0).toDouble();
            final viewCount = material['view_count'] ?? 0;
            return rating >= 4.0 || viewCount > 100;
          }).toList();

      return featuredMaterials.map((material) {
        return StudyMaterialModel(
          id: material['id'],
          title: material['title'],
          description: material['description'] ?? '',
          category: material['content_sections']['name'],
          agency: material['agencies']['name'],
          contentType: _mapContentType(material['content_type']),
          contentUrl: material['file_url'] ?? '',
          publishedDate: DateTime.parse(material['published_at']),
          isPremium: material['is_premium'] ?? false,
          icon: _getIconForContentType(material['content_type']),
          color: _getColorForSection(material['content_sections']['name']),
          estimatedReadTime: _estimateReadTime(material),
          tags: List<String>.from(material['tags'] ?? []),
          difficulty: material['difficulty_level'],
          rating: (material['average_rating'] ?? 0.0).toDouble(),
          totalRatings: material['total_ratings'] ?? 0,
          viewCount: material['view_count'] ?? 0,
          downloadCount: material['download_count'] ?? 0,
        );
      }).toList();
    } catch (e) {
      _logger.e('❌ Error getting featured materials: $e');
      // Return sample featured materials as fallback
      return _getSampleFeaturedMaterials();
    }
  }

  /// Get user's target agency information
  Future<Map<String, dynamic>?> getUserAgency() async {
    try {
      return await _storageService.getUserTargetAgency();
    } catch (e) {
      _logger.e('❌ Error getting user agency: $e');
      return null;
    }
  }

  /// Update user's target agency
  Future<bool> updateUserAgency(String agencyCode) async {
    try {
      return await _storageService.updateUserTargetAgency(agencyCode);
    } catch (e) {
      _logger.e('❌ Error updating user agency: $e');
      return false;
    }
  }

  /// Track progress for a study material
  Future<bool> trackProgress({
    required String materialId,
    required String status,
    int? progressPercentage,
    int? timeSpent,
    int? quizScore,
    int? quizTotal,
  }) async {
    try {
      return await _storageService.updateStudyProgress(
        materialId: materialId,
        status: status,
        progressPercentage: progressPercentage,
        timeSpent: timeSpent,
        quizScore: quizScore,
        quizTotal: quizTotal,
      );
    } catch (e) {
      _logger.e('❌ Error tracking progress: $e');
      return false;
    }
  }

  /// Get progress for a material
  Future<Map<String, dynamic>?> getProgress(String materialId) async {
    try {
      return await _storageService.getStudyProgress(materialId);
    } catch (e) {
      _logger.e('❌ Error getting progress: $e');
      return null;
    }
  }

  /// Get user's rating for a material
  Future<Map<String, dynamic>?> getUserRating(String materialId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return null;

      final response =
          await _client
              .from('content_ratings')
              .select('*')
              .eq('user_id', currentUser.id)
              .eq('material_id', materialId)
              .maybeSingle();

      return response;
    } catch (e) {
      _logger.e('❌ Error getting user rating: $e');
      return null;
    }
  }

  /// Rate a study material
  Future<bool> rateMaterial({
    required String materialId,
    required int rating,
    String? review,
  }) async {
    try {
      return await _storageService.rateStudyMaterial(
        materialId: materialId,
        rating: rating,
        review: review,
      );
    } catch (e) {
      _logger.e('❌ Error rating material: $e');
      return false;
    }
  }

  /// Public: Get sample featured materials for fallback
  List<StudyMaterialModel> getSampleFeaturedMaterials() =>
      _getSampleFeaturedMaterials();

  // Helper Methods

  IconData _getIconForSection(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'general knowledge':
        return Icons.lightbulb_outline;
      case 'aptitude test':
        return Icons.psychology;
      case 'screening/training':
      case 'training insight':
        return Icons.fitness_center;
      case 'ranks & structure':
        return Icons.military_tech;
      case 'interview prep':
      case 'interview simulation':
        return Icons.chat;
      case 'physical fitness':
      case 'physical screening':
        return Icons.directions_run;
      case 'technical knowledge':
        return Icons.engineering;
      case 'campus life':
        return Icons.school;
      case 'career guide':
        return Icons.work;
      case 'recruitment exam':
        return Icons.quiz;
      case 'fire knowledge':
        return Icons.local_fire_department;
      case 'road safety':
        return Icons.traffic;
      default:
        return Icons.book;
    }
  }

  Color _getColorForSection(String sectionName) {
    switch (sectionName.toLowerCase()) {
      case 'general knowledge':
        return Colors.blue;
      case 'aptitude test':
        return Colors.purple;
      case 'screening/training':
      case 'training insight':
        return Colors.green;
      case 'ranks & structure':
        return Colors.indigo;
      case 'interview prep':
      case 'interview simulation':
        return Colors.orange;
      case 'physical fitness':
      case 'physical screening':
        return Colors.red;
      case 'technical knowledge':
        return Colors.teal;
      case 'campus life':
        return Colors.cyan;
      case 'career guide':
        return Colors.brown;
      case 'recruitment exam':
        return Colors.deepPurple;
      case 'fire knowledge':
        return Colors.deepOrange;
      case 'road safety':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  IconData _getIconForContentType(String contentType) {
    switch (contentType.toLowerCase()) {
      case 'pdf':
      case 'document':
        return Icons.picture_as_pdf;
      case 'quiz':
        return Icons.quiz;
      case 'video':
        return Icons.play_circle;
      case 'interactive_video':
        return Icons.smart_display;
      case 'audio':
        return Icons.audiotrack;
      default:
        return Icons.description;
    }
  }

  String _mapContentType(String dbContentType) {
    switch (dbContentType.toLowerCase()) {
      case 'pdf':
        return 'document';
      case 'quiz':
        return 'quiz';
      case 'video':
        return 'video';
      case 'interactive_video':
        return 'interactive';
      case 'audio':
        return 'audio';
      default:
        return 'document';
    }
  }

  int _estimateReadTime(Map<String, dynamic> material) {
    final contentType = material['content_type'];
    final duration = material['duration'];

    if (duration != null) {
      return (duration / 60).ceil(); // Convert seconds to minutes
    }

    switch (contentType) {
      case 'pdf':
      case 'document':
        return 15; // Estimated 15 minutes for documents
      case 'quiz':
        return 10; // Estimated 10 minutes for quizzes
      case 'video':
      case 'interactive_video':
        return 20; // Estimated 20 minutes for videos
      default:
        return 10;
    }
  }

  List<StudyMaterialCategory> _getFallbackCategories() {
    return [
      const StudyMaterialCategory(
        id: 'general-knowledge',
        name: 'General Knowledge',
        description: 'General knowledge and current affairs',
        icon: Icons.lightbulb_outline,
        color: Colors.blue,
        materialCount: 15,
      ),
      const StudyMaterialCategory(
        id: 'aptitude-test',
        name: 'Aptitude Test',
        description: 'Aptitude and reasoning tests',
        icon: Icons.psychology,
        color: Colors.purple,
        materialCount: 12,
      ),
      const StudyMaterialCategory(
        id: 'training-insight',
        name: 'Training Insight',
        description: 'Military training and preparation insights',
        icon: Icons.fitness_center,
        color: Colors.green,
        materialCount: 18,
      ),
      const StudyMaterialCategory(
        id: 'interview-prep',
        name: 'Interview Prep',
        description: 'Interview preparation materials',
        icon: Icons.chat,
        color: Colors.orange,
        materialCount: 10,
      ),
      const StudyMaterialCategory(
        id: 'ranks-structure',
        name: 'Ranks & Structure',
        description: 'Military ranks and organizational structure',
        icon: Icons.military_tech,
        color: Colors.indigo,
        materialCount: 8,
      ),
      const StudyMaterialCategory(
        id: 'physical-fitness',
        name: 'Physical Fitness',
        description: 'Physical fitness requirements and training',
        icon: Icons.directions_run,
        color: Colors.red,
        materialCount: 14,
      ),
      const StudyMaterialCategory(
        id: 'technical-knowledge',
        name: 'Technical Knowledge',
        description: 'Technical and specialized knowledge',
        icon: Icons.engineering,
        color: Colors.teal,
        materialCount: 11,
      ),
      const StudyMaterialCategory(
        id: 'career-guide',
        name: 'Career Guide',
        description: 'Career guidance and opportunities',
        icon: Icons.work,
        color: Colors.brown,
        materialCount: 9,
      ),
    ];
  }

  /// Get sample study materials for a specific section (fallback data)
  List<StudyMaterialModel> _getSampleMaterialsForSection(String sectionName) {
    final now = DateTime.now();
    final sectionColor = _getColorForSection(sectionName);
    final sectionIcon = _getIconForSection(sectionName);

    switch (sectionName.toLowerCase()) {
      case 'general knowledge':
        return [
          StudyMaterialModel(
            id: 'gk-001',
            title: 'Nigerian History and Independence (1960-2024)',
            description:
                'Complete guide to Nigerian history from independence to present day, covering major events, leaders, and developments.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 7)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 35,
            tags: ['history', 'independence', 'nigeria', '1960-2024'],
            difficulty: 'beginner',
            rating: 4.6,
            totalRatings: 245,
            viewCount: 1850,
            downloadCount: 540,
          ),
          StudyMaterialModel(
            id: 'gk-002',
            title: 'Current Affairs: Nigeria and West Africa (2024)',
            description:
                'Latest current affairs covering Nigeria and regional West African developments, politics, and international relations.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 2)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 28,
            tags: ['current affairs', 'west africa', '2024', 'politics'],
            difficulty: 'intermediate',
            rating: 4.3,
            totalRatings: 189,
            viewCount: 1250,
            downloadCount: 380,
          ),
          StudyMaterialModel(
            id: 'gk-003',
            title: 'Nigerian Constitution and Government Structure',
            description:
                'Comprehensive guide to the Nigerian constitution, federal structure, and government institutions.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 5)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 30,
            tags: ['constitution', 'government', 'federal', 'institutions'],
            difficulty: 'intermediate',
            rating: 4.4,
            totalRatings: 167,
            viewCount: 980,
            downloadCount: 290,
          ),
          StudyMaterialModel(
            id: 'gk-004',
            title: 'Geography of Nigeria: States, Capitals, and Resources',
            description:
                'Complete geographical overview of Nigeria including all 36 states, capitals, natural resources, and physical features.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 10)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 25,
            tags: ['geography', 'states', 'capitals', 'resources'],
            difficulty: 'beginner',
            rating: 4.5,
            totalRatings: 203,
            viewCount: 1650,
            downloadCount: 420,
          ),
          StudyMaterialModel(
            id: 'gk-005',
            title: 'Interactive Nigeria Knowledge Quiz',
            description:
                'Test your comprehensive knowledge of Nigerian geography, history, and current affairs.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'quiz',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 1)),
            isPremium: false,
            icon: Icons.quiz,
            color: Colors.purple,
            estimatedReadTime: 20,
            tags: ['quiz', 'interactive', 'knowledge', 'assessment'],
            difficulty: 'intermediate',
            rating: 4.7,
            totalRatings: 312,
            viewCount: 2100,
            downloadCount: 0,
          ),
        ];

      case 'aptitude test':
        return [
          StudyMaterialModel(
            id: 'apt-001',
            title: 'Quantitative Reasoning Practice',
            description:
                'Mathematics and numerical reasoning questions with detailed solutions.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'quiz',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 5)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 30,
            tags: ['mathematics', 'reasoning', 'practice'],
            difficulty: 'intermediate',
            rating: 4.3,
            totalRatings: 156,
            viewCount: 920,
            downloadCount: 410,
          ),
          StudyMaterialModel(
            id: 'apt-002',
            title: 'Verbal Reasoning Guide',
            description:
                'English comprehension, vocabulary, and verbal reasoning exercises.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 10)),
            isPremium: true,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 35,
            tags: ['english', 'verbal', 'comprehension'],
            difficulty: 'advanced',
            rating: 4.6,
            totalRatings: 203,
            viewCount: 1200,
            downloadCount: 520,
          ),
        ];

      case 'training insight':
        return [
          StudyMaterialModel(
            id: 'train-001',
            title: 'Military Training Overview',
            description:
                'What to expect during military training and preparation tips.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'video',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 3)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 15,
            tags: ['training', 'military', 'preparation'],
            difficulty: 'beginner',
            rating: 4.4,
            totalRatings: 98,
            viewCount: 750,
            downloadCount: 320,
          ),
        ];

      default:
        return [
          StudyMaterialModel(
            id: 'default-001',
            title: '$sectionName Study Guide',
            description:
                'Comprehensive study materials for $sectionName preparation.',
            category: sectionName,
            agency: 'Nigerian Army',
            contentType: 'document',
            contentUrl: '',
            publishedDate: now.subtract(const Duration(days: 1)),
            isPremium: false,
            icon: sectionIcon,
            color: sectionColor,
            estimatedReadTime: 20,
            tags: [sectionName.toLowerCase().replaceAll(' ', '-')],
            difficulty: 'intermediate',
            rating: 4.0,
            totalRatings: 50,
            viewCount: 300,
            downloadCount: 150,
          ),
        ];
    }
  }

  /// Get sample featured materials (fallback data)
  List<StudyMaterialModel> _getSampleFeaturedMaterials() {
    final now = DateTime.now();

    return [
      StudyMaterialModel(
        id: 'featured-001',
        title: 'How to Use the Fit4Force App',
        description:
            'Complete video guide on navigating and maximizing your Fit4Force app experience for military exam preparation.',
        category: 'App Guide',
        agency: 'All Agencies',
        contentType: 'video',
        contentUrl: '',
        publishedDate: now.subtract(const Duration(days: 1)),
        isPremium: false,
        icon: Icons.play_circle_filled,
        color: Colors.blue,
        estimatedReadTime: 15,
        tags: ['app-guide', 'tutorial', 'getting-started'],
        difficulty: 'beginner',
        rating: 4.9,
        totalRatings: 320,
        viewCount: 2100,
        downloadCount: 850,
      ),
      StudyMaterialModel(
        id: 'featured-002',
        title: 'Study Tips for Nigerian Military Exams',
        description:
            'Proven strategies and techniques for effective study and exam preparation specifically for Nigerian military recruitment.',
        category: 'Study Tips',
        agency: 'All Agencies',
        contentType: 'document',
        contentUrl: '',
        publishedDate: now.subtract(const Duration(days: 2)),
        isPremium: false,
        icon: Icons.lightbulb,
        color: Colors.orange,
        estimatedReadTime: 20,
        tags: ['study-tips', 'strategies', 'exam-prep'],
        difficulty: 'beginner',
        rating: 4.7,
        totalRatings: 285,
        viewCount: 1850,
        downloadCount: 720,
      ),
      StudyMaterialModel(
        id: 'featured-003',
        title: 'Success Stories: From Civilian to Officer',
        description:
            'Inspiring stories from successful candidates who passed Nigerian military recruitment exams and their journey.',
        category: 'Success Stories',
        agency: 'All Agencies',
        contentType: 'document',
        contentUrl: '',
        publishedDate: now.subtract(const Duration(days: 3)),
        isPremium: false,
        icon: Icons.star,
        color: Colors.green,
        estimatedReadTime: 25,
        tags: ['success-stories', 'inspiration', 'motivation'],
        difficulty: 'beginner',
        rating: 4.8,
        totalRatings: 195,
        viewCount: 1450,
        downloadCount: 620,
      ),
      StudyMaterialModel(
        id: 'featured-004',
        title: 'Nigerian Army Preparation Guide',
        description:
            'Comprehensive guide specifically tailored for Nigerian Army recruitment requirements and exam patterns.',
        category: 'Agency Guide',
        agency: 'Nigerian Army',
        contentType: 'document',
        contentUrl: '',
        publishedDate: now.subtract(const Duration(days: 4)),
        isPremium: false,
        icon: Icons.shield,
        color: Colors.blue.shade700,
        estimatedReadTime: 35,
        tags: ['nigerian-army', 'recruitment', 'preparation'],
        difficulty: 'intermediate',
        rating: 4.6,
        totalRatings: 165,
        viewCount: 1200,
        downloadCount: 480,
      ),
      StudyMaterialModel(
        id: 'featured-005',
        title: 'Quick Reference: Common Exam Topics',
        description:
            'Essential quick reference guide covering the most frequently tested topics in Nigerian military exams.',
        category: 'Quick Reference',
        agency: 'All Agencies',
        contentType: 'document',
        contentUrl: '',
        publishedDate: now.subtract(const Duration(days: 5)),
        isPremium: false,
        icon: Icons.bookmark,
        color: Colors.purple,
        estimatedReadTime: 15,
        tags: ['quick-reference', 'exam-topics', 'cheat-sheet'],
        difficulty: 'beginner',
        rating: 4.5,
        totalRatings: 220,
        viewCount: 1650,
        downloadCount: 780,
      ),
    ];
  }
}
