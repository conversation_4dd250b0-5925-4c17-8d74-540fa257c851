import 'base_model.dart';

class UserModel extends BaseModel {
  final String fullName;
  final String email;
  final int age;
  final String gender;
  final double height;
  final double weight;
  final String targetAgency;
  final String fitnessGoal;
  final bool isPremium;
  final bool isTestUser; // Add test user flag
  final Map<String, bool> notificationPreferences;
  final String? profileImageUrl;
  final List<String> completedQuizzes;
  final List<String> savedWorkouts;
  final DateTime? premiumExpiryDate;

  const UserModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.fullName,
    required this.email,
    required this.age,
    required this.gender,
    required this.height,
    required this.weight,
    required this.targetAgency,
    required this.fitnessGoal,
    required this.isPremium,
    this.isTestUser = false, // Default to false
    required this.notificationPreferences,
    this.profileImageUrl,
    required this.completedQuizzes,
    required this.savedWorkouts,
    this.premiumExpiryDate,
  });

  @override
  List<Object?> get props => [
    ...super.props,
    fullName,
    email,
    age,
    gender,
    height,
    weight,
    targetAgency,
    fitnessGoal,
    isPremium,
    isTestUser,
    notificationPreferences,
    profileImageUrl,
    completedQuizzes,
    savedWorkouts,
    premiumExpiryDate,
  ];

  @override
  Map<String, dynamic> toJson() {
    return {
      ...super.toJson(),
      'fullName': fullName,
      'email': email,
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'targetAgency': targetAgency,
      'fitnessGoal': fitnessGoal,
      'isPremium': isPremium,
      'isTestUser': isTestUser,
      'notificationPreferences': notificationPreferences,
      'profileImageUrl': profileImageUrl,
      'completedQuizzes': completedQuizzes,
      'savedWorkouts': savedWorkouts,
      'premiumExpiryDate': premiumExpiryDate?.toIso8601String(),
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    // Defensive: handle null, empty string, or wrong type for lists/maps
    dynamic safeList(dynamic value) {
      if (value == null || value is String) return <String>[];
      if (value is List) return List<String>.from(value);
      return <String>[];
    }

    dynamic safeMap(dynamic value) {
      if (value == null || value is String) return <String, bool>{};
      if (value is Map) return Map<String, bool>.from(value);
      return <String, bool>{};
    }

    return UserModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      fullName: json['full_name'] as String,
      email: json['email'] as String,
      age: json['age'] as int,
      gender: json['gender'] as String,
      height: (json['height'] as num).toDouble(),
      weight: (json['weight'] as num).toDouble(),
      targetAgency: json['target_agency'] as String,
      fitnessGoal: json['fitness_goal'] as String,
      isPremium: json['is_premium'] as bool,
      isTestUser:
          json['is_test_user'] as bool? ??
          false, // Default to false if not provided
      notificationPreferences: safeMap(json['notification_preferences']),
      profileImageUrl: json['profile_image_url'] as String?,
      completedQuizzes: safeList(json['completed_quizzes']),
      savedWorkouts: safeList(json['saved_workouts']),
      premiumExpiryDate:
          json['premium_expiry_date'] != null
              ? DateTime.parse(json['premium_expiry_date'] as String)
              : null,
    );
  }

  @override
  UserModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? fullName,
    String? email,
    int? age,
    String? gender,
    double? height,
    double? weight,
    String? targetAgency,
    String? fitnessGoal,
    bool? isPremium,
    bool? isTestUser,
    Map<String, bool>? notificationPreferences,
    String? profileImageUrl,
    List<String>? completedQuizzes,
    List<String>? savedWorkouts,
    DateTime? premiumExpiryDate,
  }) {
    return UserModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      fullName: fullName ?? this.fullName,
      email: email ?? this.email,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      targetAgency: targetAgency ?? this.targetAgency,
      fitnessGoal: fitnessGoal ?? this.fitnessGoal,
      isPremium: isPremium ?? this.isPremium,
      isTestUser: isTestUser ?? this.isTestUser,
      notificationPreferences:
          notificationPreferences ?? this.notificationPreferences,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      completedQuizzes: completedQuizzes ?? this.completedQuizzes,
      savedWorkouts: savedWorkouts ?? this.savedWorkouts,
      premiumExpiryDate: premiumExpiryDate ?? this.premiumExpiryDate,
    );
  }

  /// Helper method to check if user has access to premium features
  /// Test users get full access regardless of premium status
  bool get hasAccessToPremiumFeatures => isPremium || isTestUser;

  /// Helper method to check if user can access fitness features
  bool get canAccessFitness => hasAccessToPremiumFeatures;

  /// Helper method to check if user can access 30-day challenge
  bool get canAccess30DayChallenge => hasAccessToPremiumFeatures;
}
