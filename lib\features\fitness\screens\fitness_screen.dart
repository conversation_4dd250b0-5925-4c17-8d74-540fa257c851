import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/services/premium_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';
import 'package:fit_4_force/features/fitness/screens/recovery_screen.dart';
import 'package:fit_4_force/features/fitness/screens/challenges_screen.dart';
import 'package:fit_4_force/features/fitness/screens/nutrition_screen.dart';
import 'package:fit_4_force/core/security/screenshot_protection_service.dart';
import 'package:fit_4_force/core/security/download_prevention_service.dart';
import 'package:fit_4_force/features/fitness/screens/workout_list_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_session_screen.dart';
import 'package:fit_4_force/features/fitness/screens/thirty_day_workout_screen.dart';

class FitnessScreen extends StatefulWidget {
  final UserModel user;

  const FitnessScreen({super.key, required this.user});

  @override
  State<FitnessScreen> createState() => _FitnessScreenState();
}

class _FitnessScreenState extends State<FitnessScreen>
    with
        SingleTickerProviderStateMixin,
        ScreenshotProtectionMixin,
        DownloadPreventionMixin {
  late TabController _tabController;
  final WorkoutService _workoutService = WorkoutService();

  // Workout guidance state
  int _currentExerciseIndex = 0;

  // User progress data - starts at 0 for new users
  Map<String, dynamic> userProgress = {
    'totalWorkouts': 0,
    'totalCaloriesBurned': 0,
    'totalActiveHours': 0.0,
    'averageHeartRate': 0,
    'currentWeight': 0.0,
    'currentBodyFat': 0.0,
    'currentMuscleMass': 0.0,
    'weeklyWorkouts': [0, 0, 0, 0, 0, 0, 0], // Mon-Sun
    'achievements': <String>[],
    'progressPhotos': <Map<String, dynamic>>[],
    'lastUpdated': DateTime.now(),
    // Progressive difficulty system
    'userLevel': 'beginner', // beginner, intermediate, advanced
    'workoutCompletionRate': 0.0,
    'exercisePerformance': <String, Map<String, dynamic>>{},
    'preferredDifficulty': <String, String>{},
    'streakDays': 0,
    'longestStreak': 0,
    'lastWorkoutDate': null,
    // Smart recommendations
    'recommendedWorkouts': <Map<String, dynamic>>[],
    'workoutHistory': <Map<String, dynamic>>[],
    'preferredWorkoutTime': null,
    'workoutGoals': <String>[],
    // Social features
    'friends': <String>[],
    'groupChallenges': <String>[],
    'sharedWorkouts': <Map<String, dynamic>>[],
    'mentorshipStatus': 'none',
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserProgress();
  }

  // Load user progress from database/storage
  Future<void> _loadUserProgress() async {
    try {
      // TODO: Replace with actual database call
      // For now, check if user is new or has existing data
      final savedProgress = await _getUserProgressFromStorage();
      if (savedProgress != null) {
        setState(() {
          userProgress = savedProgress;
        });
      } else {
        // New user - initialize with zeros
        await _initializeNewUserProgress();
      }
    } catch (e) {
      print('Error loading user progress: $e');
      // Fallback to default values for new users
      await _initializeNewUserProgress();
    }
  }

  Future<Map<String, dynamic>?> _getUserProgressFromStorage() async {
    // TODO: Implement actual storage retrieval (SharedPreferences, Supabase, etc.)
    // This is where you'd query your database for user progress
    // For demo purposes, returning null to simulate new user
    return null;
  }

  Future<void> _initializeNewUserProgress() async {
    // Initialize progress for new user
    userProgress = {
      'totalWorkouts': 0,
      'totalCaloriesBurned': 0,
      'totalActiveHours': 0.0,
      'averageHeartRate': 0,
      'currentWeight': 0.0,
      'currentBodyFat': 0.0,
      'currentMuscleMass': 0.0,
      'weeklyWorkouts': [0, 0, 0, 0, 0, 0, 0],
      'achievements': <String>[],
      'progressPhotos': <Map<String, dynamic>>[],
      'lastUpdated': DateTime.now(),
      // Progressive difficulty system
      'userLevel': 'beginner',
      'workoutCompletionRate': 0.0,
      'exercisePerformance': <String, Map<String, dynamic>>{},
      'preferredDifficulty': <String, String>{},
      'streakDays': 0,
      'longestStreak': 0,
      'lastWorkoutDate': null,
      // Smart recommendations
      'recommendedWorkouts': <Map<String, dynamic>>[],
      'workoutHistory': <Map<String, dynamic>>[],
      'preferredWorkoutTime': null,
      'workoutGoals': <String>[],
      // Social features
      'friends': <String>[],
      'groupChallenges': <String>[],
      'sharedWorkouts': <Map<String, dynamic>>[],
      'mentorshipStatus': 'none',
    };
    await _saveUserProgress();
    setState(() {});
  }

  Future<void> _saveUserProgress() async {
    // TODO: Implement actual storage save (SharedPreferences, Supabase, etc.)
    userProgress['lastUpdated'] = DateTime.now();
    // Save to your database here
  }

  // === PROGRESSIVE DIFFICULTY SYSTEM ===

  String _calculateUserLevel() {
    final totalWorkouts = userProgress['totalWorkouts'] as int;
    final streakDays = userProgress['streakDays'] as int;

    if (totalWorkouts >= 50 && streakDays >= 14) {
      return 'advanced';
    } else if (totalWorkouts >= 15 && streakDays >= 7) {
      return 'intermediate';
    }
    return 'beginner';
  }

  void _updateWorkoutStreak() {
    final lastWorkoutDate = userProgress['lastWorkoutDate'];
    final now = DateTime.now();

    if (lastWorkoutDate == null) {
      userProgress['streakDays'] = 1;
      userProgress['longestStreak'] = 1;
    } else {
      final lastDate = lastWorkoutDate as DateTime;
      final daysDifference = now.difference(lastDate).inDays;

      if (daysDifference == 1) {
        userProgress['streakDays'] += 1;
        final currentStreak = userProgress['streakDays'] as int;
        if (currentStreak > (userProgress['longestStreak'] as int)) {
          userProgress['longestStreak'] = currentStreak;
        }
      } else if (daysDifference > 1) {
        userProgress['streakDays'] = 1;
      }
    }

    userProgress['lastWorkoutDate'] = now;
  }

  // === REMOVED: SMART WORKOUT RECOMMENDATIONS ===
  // This functionality has been removed as requested

  List<Map<String, dynamic>> _generateSmartRecommendations() {
    final userLevel = _calculateUserLevel();
    final goals = userProgress['workoutGoals'] as List<String>;
    final workoutHistory =
        userProgress['workoutHistory'] as List<Map<String, dynamic>>;

    // Analyze recent workout patterns
    final recentCategories =
        workoutHistory.take(5).map((w) => w['category'] as String).toSet();

    // Generate recommendations based on goals and level
    List<Map<String, dynamic>> recommendations = [];

    if (goals.contains('weight_loss')) {
      recommendations.addAll(_getHIITRecommendations(userLevel));
    }
    if (goals.contains('muscle_gain')) {
      recommendations.addAll(_getStrengthRecommendations(userLevel));
    }
    if (goals.contains('endurance')) {
      recommendations.addAll(_getEnduranceRecommendations(userLevel));
    }

    // If no goals set, provide balanced recommendations
    if (goals.isEmpty) {
      recommendations.addAll(_getBalancedRecommendations(userLevel));
    }
    recommendations =
        recommendations
            .where((r) => !recentCategories.contains(r['category']))
            .toList();

    // Sort by user level compatibility and add difficulty progression
    recommendations.sort(
      (a, b) => _calculateWorkoutScore(a).compareTo(_calculateWorkoutScore(b)),
    );

    return recommendations.take(3).toList();
  }

  List<Map<String, dynamic>> _getHIITRecommendations(String level) {
    switch (level) {
      case 'beginner':
        return [
          {
            'name': 'Beginner HIIT',
            'duration': 15,
            'intensity': 'medium',
            'category': 'hiit',
          },
          {
            'name': 'Low Impact Cardio',
            'duration': 20,
            'intensity': 'low',
            'category': 'cardio',
          },
        ];
      case 'intermediate':
        return [
          {
            'name': 'Intermediate HIIT',
            'duration': 25,
            'intensity': 'high',
            'category': 'hiit',
          },
          {
            'name': 'Tabata Training',
            'duration': 20,
            'intensity': 'very_high',
            'category': 'hiit',
          },
        ];
      case 'advanced':
        return [
          {
            'name': 'Advanced HIIT',
            'duration': 35,
            'intensity': 'very_high',
            'category': 'hiit',
          },
          {
            'name': 'Metabolic Circuit',
            'duration': 40,
            'intensity': 'extreme',
            'category': 'circuit',
          },
        ];
      default:
        return [];
    }
  }

  List<Map<String, dynamic>> _getStrengthRecommendations(String level) {
    switch (level) {
      case 'beginner':
        return [
          {
            'name': 'Bodyweight Basics',
            'duration': 20,
            'intensity': 'medium',
            'category': 'strength',
          },
          {
            'name': 'Core Foundation',
            'duration': 15,
            'intensity': 'low',
            'category': 'core',
          },
        ];
      case 'intermediate':
        return [
          {
            'name': 'Upper Body Focus',
            'duration': 30,
            'intensity': 'high',
            'category': 'strength',
          },
          {
            'name': 'Lower Body Power',
            'duration': 30,
            'intensity': 'high',
            'category': 'strength',
          },
        ];
      case 'advanced':
        return [
          {
            'name': 'Advanced Calisthenics',
            'duration': 45,
            'intensity': 'very_high',
            'category': 'strength',
          },
          {
            'name': 'Military Training',
            'duration': 40,
            'intensity': 'extreme',
            'category': 'military',
          },
        ];
      default:
        return [];
    }
  }

  List<Map<String, dynamic>> _getEnduranceRecommendations(String level) {
    switch (level) {
      case 'beginner':
        return [
          {
            'name': 'Endurance Builder',
            'duration': 25,
            'intensity': 'low',
            'category': 'endurance',
          },
        ];
      case 'intermediate':
        return [
          {
            'name': 'Cardio Intervals',
            'duration': 35,
            'intensity': 'medium',
            'category': 'cardio',
          },
        ];
      case 'advanced':
        return [
          {
            'name': 'Endurance Challenge',
            'duration': 50,
            'intensity': 'high',
            'category': 'endurance',
          },
        ];
      default:
        return [];
    }
  }

  List<Map<String, dynamic>> _getBalancedRecommendations(String level) {
    // Provide balanced workout recommendations when no specific goals are set
    switch (level) {
      case 'beginner':
        return [
          {
            'name': 'Full Body Basics',
            'duration': 20,
            'intensity': 'low',
            'category': 'strength',
          },
          {
            'name': 'Beginner Cardio',
            'duration': 15,
            'intensity': 'low',
            'category': 'cardio',
          },
          {
            'name': 'Core Starter',
            'duration': 10,
            'intensity': 'low',
            'category': 'core',
          },
        ];
      case 'intermediate':
        return [
          {
            'name': 'Mixed Training',
            'duration': 30,
            'intensity': 'medium',
            'category': 'strength',
          },
          {
            'name': 'HIIT Cardio',
            'duration': 20,
            'intensity': 'high',
            'category': 'hiit',
          },
          {
            'name': 'Core Power',
            'duration': 15,
            'intensity': 'medium',
            'category': 'core',
          },
        ];
      case 'advanced':
        return [
          {
            'name': 'Elite Training',
            'duration': 45,
            'intensity': 'very_high',
            'category': 'military',
          },
          {
            'name': 'Advanced HIIT',
            'duration': 35,
            'intensity': 'extreme',
            'category': 'hiit',
          },
          {
            'name': 'Core Mastery',
            'duration': 25,
            'intensity': 'high',
            'category': 'core',
          },
        ];
      default:
        return [];
    }
  }

  double _calculateWorkoutScore(Map<String, dynamic> workout) {
    // Score based on user preferences and performance
    double score = 0.0;

    // Prefer workouts matching user level
    final userLevel = _calculateUserLevel();
    if (workout['level'] == userLevel) score += 0.4;

    // Prefer workouts matching goals
    final goals = userProgress['workoutGoals'] as List<String>;
    if (goals.contains(workout['category'])) score += 0.3;

    // Add randomness for variety
    score += Random().nextDouble() * 0.3;

    return score;
  }

  void _showSmartRecommendations() {
    final recommendations = _generateSmartRecommendations();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.psychology, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                const Text('Smart Recommendations'),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Based on your level (${_calculateUserLevel()}) and progress:',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 16),
                  if (recommendations.isEmpty) ...[
                    const Text(
                      'Complete a few workouts to get personalized recommendations!',
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _showWorkoutGoalsDialog();
                      },
                      child: const Text('Set Workout Goals'),
                    ),
                  ] else ...[
                    ...recommendations.map(
                      (workout) => Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: ListTile(
                          leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _getIntensityColor(
                                workout['intensity'],
                              ).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _getCategoryIcon(workout['category']),
                              color: _getIntensityColor(workout['intensity']),
                            ),
                          ),
                          title: Text(workout['name'] as String),
                          subtitle: Text(
                            '${workout['duration']} min • ${workout['intensity']}',
                          ),
                          trailing: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _startRecommendedWorkout(workout);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                            ),
                            child: const Text(
                              'Start',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Color _getIntensityColor(String intensity) {
    switch (intensity) {
      case 'low':
        return Colors.green;
      case 'medium':
        return Colors.orange;
      case 'high':
        return Colors.red;
      case 'very_high':
        return Colors.purple;
      case 'extreme':
        return Colors.black;
      default:
        return Colors.blue;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'hiit':
        return Icons.local_fire_department;
      case 'strength':
        return Icons.fitness_center;
      case 'cardio':
        return Icons.directions_run;
      case 'core':
        return Icons.accessibility_new;
      case 'military':
        return Icons.military_tech;
      case 'endurance':
        return Icons.timer;
      default:
        return Icons.sports_gymnastics;
    }
  }

  void _startRecommendedWorkout(Map<String, dynamic> workout) {
    // Add workout to history
    final workoutHistory =
        userProgress['workoutHistory'] as List<Map<String, dynamic>>;
    workoutHistory.add({
      'name': workout['name'],
      'category': workout['category'],
      'duration': workout['duration'],
      'intensity': workout['intensity'],
      'date': DateTime.now().toIso8601String(),
      'completed': false,
    });

    // Start guided workout
    _startGuidedWorkout(workout);
  }

  void _startGuidedWorkout(Map<String, dynamic> workout) {
    // Generate exercises based on workout type
    final exercises = _generateWorkoutExercises(workout);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _buildGuidedWorkoutDialog(workout, exercises),
    );
  }

  List<Map<String, dynamic>> _generateWorkoutExercises(
    Map<String, dynamic> workout,
  ) {
    final category = workout['category'] as String;
    final duration = workout['duration'] as int;
    final intensity = workout['intensity'] as String;

    // Base exercises for each category
    final categoryExercises =
        _allCategories.firstWhere(
              (cat) =>
                  cat['name'].toString().toLowerCase().contains(category) ||
                  category.contains('strength') && cat['name'] == 'Strength' ||
                  category.contains('hiit') &&
                      cat['name'] == 'Fat Loss / HIIT' ||
                  category.contains('core') && cat['name'] == 'Core & Abs' ||
                  category.contains('military') &&
                      cat['name'] == 'Military Fitness',
              orElse: () => _allCategories.first,
            )['exercises']
            as List<Map<String, dynamic>>;

    // Select exercises based on duration and intensity
    final exerciseCount = (duration / 3).round().clamp(
      5,
      15,
    ); // ~3 min per exercise
    final selectedExercises =
        (categoryExercises.toList()..shuffle()).take(exerciseCount).toList();

    // Adjust exercise duration based on intensity
    int exerciseDuration;
    switch (intensity) {
      case 'low':
        exerciseDuration = 20;
        break;
      case 'medium':
        exerciseDuration = 30;
        break;
      case 'high':
        exerciseDuration = 40;
        break;
      case 'very_high':
        exerciseDuration = 50;
        break;
      case 'extreme':
        exerciseDuration = 60;
        break;
      default:
        exerciseDuration = 30;
    }

    return selectedExercises
        .map(
          (exercise) => {
            ...exercise,
            'duration': exerciseDuration,
            'formTip': _getFormTip(exercise['name'] as String),
            'difficulty': _getDifficultyLevel(intensity),
          },
        )
        .toList();
  }

  String _getFormTip(String exerciseName) {
    final tips = {
      'Push-ups': 'Keep your body in a straight line, core engaged',
      'Squats': 'Keep knees behind toes, chest up',
      'Planks': 'Maintain straight line from head to heels',
      'Jumping Jacks': 'Land softly on balls of feet',
      'Burpees': 'Control the movement, don\'t rush',
      'Mountain Climbers': 'Keep hips level, fast but controlled',
      'Lunges': 'Step far enough to keep front knee over ankle',
    };

    // Find matching tip or provide generic one
    for (final key in tips.keys) {
      if (exerciseName.toLowerCase().contains(key.toLowerCase())) {
        return tips[key]!;
      }
    }
    return 'Focus on proper form over speed';
  }

  int _getDifficultyLevel(String intensity) {
    switch (intensity) {
      case 'low':
        return 1;
      case 'medium':
        return 2;
      case 'high':
        return 3;
      case 'very_high':
        return 4;
      case 'extreme':
        return 5;
      default:
        return 2;
    }
  }

  Widget _buildGuidedWorkoutDialog(
    Map<String, dynamic> workout,
    List<Map<String, dynamic>> exercises,
  ) {
    return StatefulBuilder(
      builder: (context, setState) {
        if (_currentExerciseIndex >= exercises.length) {
          // Workout completed
          return AlertDialog(
            title: Row(
              children: [
                const Icon(Icons.celebration, color: Colors.green, size: 32),
                const SizedBox(width: 8),
                const Text('Workout Complete!'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Congratulations! You completed ${workout['name']}'),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Duration: ${workout['duration']} min'),
                          Text('Exercises: ${exercises.length}'),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Intensity: ${workout['intensity']}'),
                          Text(
                            'Calories: ~${(workout['duration'] as int) * 8}',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _completeRecommendedWorkout(workout, exercises);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text(
                  'Finish',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          );
        }

        final currentExercise = exercises[_currentExerciseIndex];
        final progress = (_currentExerciseIndex + 1) / exercises.length;

        return AlertDialog(
          title: Row(
            children: [
              Icon(Icons.fitness_center, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              Text(workout['name'] as String),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 16),

              Text(
                'Exercise ${_currentExerciseIndex + 1} of ${exercises.length}',
                style: const TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: 8),

              Text(
                currentExercise['name'] as String,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Exercise image placeholder
              Container(
                height: 120,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getCategoryIcon(workout['category'] as String),
                  size: 48,
                  color: Colors.grey[400],
                ),
              ),
              const SizedBox(height: 16),

              // Duration
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  'Duration: ${currentExercise['duration']} seconds',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // Form tip
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Form Tip:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(currentExercise['formTip'] as String),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  _currentExerciseIndex = 0;
                });
              },
              child: const Text('Stop Workout'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _currentExerciseIndex++;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: Text(
                _currentExerciseIndex < exercises.length - 1
                    ? 'Next Exercise'
                    : 'Finish',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _completeRecommendedWorkout(
    Map<String, dynamic> workout,
    List<Map<String, dynamic>> exercises,
  ) {
    // Update workout history
    final workoutHistory =
        userProgress['workoutHistory'] as List<Map<String, dynamic>>;
    final lastWorkout = workoutHistory.last;
    lastWorkout['completed'] = true;
    lastWorkout['completedAt'] = DateTime.now().toIso8601String();

    // Update progress
    final duration = (workout['duration'] as int) / 60.0; // convert to hours
    final calories = (workout['duration'] as int) * 8; // rough estimate

    _updateWorkoutProgress(
      caloriesBurned: calories,
      durationHours: duration,
      heartRate: 140 + Random().nextInt(20),
    );

    // Reset exercise index
    setState(() {
      _currentExerciseIndex = 0;
    });

    // Show achievement if applicable
    _checkWorkoutAchievements(workout);
  }

  void _checkWorkoutAchievements(Map<String, dynamic> workout) {
    final achievements = userProgress['achievements'] as List<String>;

    // Smart workout achievement
    if (!achievements.contains('Smart Trainer')) {
      achievements.add('Smart Trainer');
      _showAchievementUnlocked('Smart Trainer', Icons.psychology);
    }

    // Category-specific achievements
    final category = workout['category'] as String;
    if (category == 'hiit' && !achievements.contains('HIIT Master')) {
      achievements.add('HIIT Master');
      _showAchievementUnlocked('HIIT Master', Icons.local_fire_department);
    }
  }

  void _showWorkoutGoalsDialog() {
    final goals = [
      'weight_loss',
      'muscle_gain',
      'endurance',
      'general_fitness',
      'strength',
    ];
    final selectedGoals = Set<String>.from(
      userProgress['workoutGoals'] as List<String>,
    );

    showDialog(
      context: context,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setState) => AlertDialog(
                  title: const Text('Set Your Workout Goals'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children:
                        goals
                            .map(
                              (goal) => CheckboxListTile(
                                title: Text(_formatGoalName(goal)),
                                value: selectedGoals.contains(goal),
                                onChanged: (bool? value) {
                                  setState(() {
                                    if (value == true) {
                                      selectedGoals.add(goal);
                                    } else {
                                      selectedGoals.remove(goal);
                                    }
                                  });
                                },
                              ),
                            )
                            .toList(),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        userProgress['workoutGoals'] = selectedGoals.toList();
                        _saveUserProgress();
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Workout goals updated! You\'ll get better recommendations.',
                            ),
                            backgroundColor: Colors.green,
                          ),
                        );
                      },
                      child: const Text('Save Goals'),
                    ),
                  ],
                ),
          ),
    );
  }

  String _formatGoalName(String goal) {
    switch (goal) {
      case 'weight_loss':
        return 'Weight Loss';
      case 'muscle_gain':
        return 'Muscle Gain';
      case 'endurance':
        return 'Endurance';
      case 'general_fitness':
        return 'General Fitness';
      case 'strength':
        return 'Strength Building';
      default:
        return goal;
    }
  }

  // Method to update progress when user completes a workout
  Future<void> _updateWorkoutProgress({
    required int caloriesBurned,
    required double durationHours,
    int? heartRate,
  }) async {
    setState(() {
      userProgress['totalWorkouts'] += 1;
      userProgress['totalCaloriesBurned'] += caloriesBurned;
      userProgress['totalActiveHours'] += durationHours;

      // Update average heart rate
      if (heartRate != null) {
        final currentAvg = userProgress['averageHeartRate'] as int;
        final totalWorkouts = userProgress['totalWorkouts'] as int;
        userProgress['averageHeartRate'] =
            ((currentAvg * (totalWorkouts - 1)) + heartRate) ~/ totalWorkouts;
      }

      // Update weekly progress (today's workout)
      final today = DateTime.now().weekday - 1; // Monday = 0
      userProgress['weeklyWorkouts'][today] += 1;

      // Check for new achievements
      _checkForNewAchievements();

      // Update workout streak
      _updateWorkoutStreak();
    });

    await _saveUserProgress();
  }

  void _checkForNewAchievements() {
    final achievements = userProgress['achievements'] as List<String>;
    final totalWorkouts = userProgress['totalWorkouts'] as int;
    final streakDays = userProgress['streakDays'] as int;
    final totalCalories = userProgress['totalCaloriesBurned'] as int;

    // First workout achievement
    if (totalWorkouts >= 1 && !achievements.contains('First Workout')) {
      achievements.add('First Workout');
      _showAchievementUnlocked('First Workout', Icons.star);
    }

    // First week achievement (7 workouts)
    if (totalWorkouts >= 7 && !achievements.contains('First Week')) {
      achievements.add('First Week');
      _showAchievementUnlocked('First Week', Icons.calendar_today);
    }

    // Calorie burner achievement (1000+ calories)
    if (totalCalories >= 1000 && !achievements.contains('Calorie Burner')) {
      achievements.add('Calorie Burner');
      _showAchievementUnlocked('Calorie Burner', Icons.local_fire_department);
    }

    // Consistency achievement (workout 5 days in a week)
    final weeklyWorkouts = userProgress['weeklyWorkouts'] as List<int>;
    final workoutDays = weeklyWorkouts.where((w) => w > 0).length;
    if (workoutDays >= 5 && !achievements.contains('Consistent')) {
      achievements.add('Consistent');
      _showAchievementUnlocked('Consistent', Icons.emoji_events);
    }

    // Streak achievements
    if (streakDays >= 7 && !achievements.contains('Week Warrior')) {
      achievements.add('Week Warrior');
      _showAchievementUnlocked('Week Warrior', Icons.local_fire_department);
    }

    if (streakDays >= 30 && !achievements.contains('Month Master')) {
      achievements.add('Month Master');
      _showAchievementUnlocked('Month Master', Icons.workspace_premium);
    }

    // Milestone achievements
    if (totalWorkouts >= 25 && !achievements.contains('Quarter Century')) {
      achievements.add('Quarter Century');
      _showAchievementUnlocked('Quarter Century', Icons.celebration);
    }

    if (totalWorkouts >= 50 && !achievements.contains('Half Century')) {
      achievements.add('Half Century');
      _showAchievementUnlocked('Half Century', Icons.military_tech);
    }

    // Calorie milestones
    if (totalCalories >= 5000 && !achievements.contains('Calorie Crusher')) {
      achievements.add('Calorie Crusher');
      _showAchievementUnlocked('Calorie Crusher', Icons.whatshot);
    }
  }

  void _showAchievementUnlocked(String title, IconData icon) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.amber.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: Colors.amber, size: 48),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Achievement Unlocked!',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Awesome!'),
              ),
            ],
          ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showSearchDialog() {
    final searchController = TextEditingController();
    List<WorkoutModel> searchResults = [];

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Search Workouts'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: searchController,
                    decoration: const InputDecoration(
                      hintText: 'Search by name, category, etc.',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      if (value.length >= 2) {
                        setState(() {
                          searchResults = _workoutService.searchWorkouts(value);
                        });
                      } else {
                        setState(() {
                          searchResults = [];
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  if (searchResults.isNotEmpty)
                    Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: searchResults.length,
                        itemBuilder: (context, index) {
                          final workout = searchResults[index];
                          return ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: workout.color.withOpacity(0.1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(workout.icon, color: workout.color),
                            ),
                            title: Text(workout.name),
                            subtitle: Text(workout.category),
                            onTap: () {
                              Navigator.pop(context);
                              if (PremiumService().hasAccessToPremiumFeatures(
                                widget.user,
                              )) {
                                NavigationService().navigateTo(
                                  '/workout-detail',
                                  arguments: workout.id,
                                );
                              } else {
                                _showUpgradeDialog();
                              }
                            },
                          );
                        },
                      ),
                    )
                  else if (searchController.text.length >= 2)
                    const Text('No workouts found'),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CLOSE'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final layoutType = ResponsiveUtils.getLayoutType(context);
    final supportsMultiPane = ResponsiveUtils.supportsMultiPane(context);

    return ProtectedScreen(
      enableProtection: true,
      screenName: 'FitnessScreen',
      child: Scaffold(
      body:
          (supportsMultiPane && layoutType != LayoutType.mobilePortrait)
              ? _buildMultiPaneLayout(context)
              : _buildSinglePaneLayout(context),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppTheme.primaryColor,
        onPressed: () {
          if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
            NavigationService().navigateTo('/create-workout');
          } else {
            _showUpgradeDialog();
          }
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildSinglePaneLayout(BuildContext context) {
    return Column(
      children: [
        _buildHeader(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildWorkoutsTab(),
              _buildProgressTab(),
              _buildPlansTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMultiPaneLayout(BuildContext context) {
    return ResponsiveMultiPaneLayout(
      primaryPane: Column(
        children: [
          _buildHeader(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildWorkoutsTab(),
                _buildProgressTab(),
                _buildPlansTab(),
              ],
            ),
          ),
        ],
      ),
      secondaryPane: _buildSidePanel(context),
      primaryFlex: 2,
      secondaryFlex: 1,
    );
  }

  Widget _buildSidePanel(BuildContext context) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Quick Actions',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildQuickActionCard(
            'Nutrition Guide',
            Icons.restaurant,
            Colors.orange,
            () {
              if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => NutritionScreen(user: widget.user),
                  ),
                );
              } else {
                _showUpgradeDialog();
              }
            },
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard(
            'Challenges',
            Icons.emoji_events,
            Colors.purple,
            () {
              if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => ChallengesScreen(user: widget.user),
                  ),
                );
              } else {
                _showUpgradeDialog();
              }
            },
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          _buildQuickActionCard('Recovery', Icons.spa, Colors.green, () {
            if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => RecoveryScreen(user: widget.user),
                ),
              );
            } else {
              _showUpgradeDialog();
            }
          }),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return ResponsiveCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveBorderRadius(context),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14.0,
                  tabletFontSize: 15.0,
                  desktopFontSize: 16.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 14, color: Colors.grey[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF4A80F0), // Vibrant blue
            const Color(0xFF1A56E0), // Deep blue
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Fitness Training',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            'Welcome, ${widget.user.fullName.split(' ')[0]}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                              letterSpacing: 0.3,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _calculateUserLevel().toUpperCase(),
                              style: const TextStyle(
                                fontSize: 10,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Action buttons with consistent spacing
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white, width: 1.5),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.search,
                          color: Color(0xFF4A80F0),
                          size: 22,
                        ),
                        onPressed: () {
                          _showSearchDialog();
                        },
                        tooltip: 'Search workouts',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.white, width: 1.5),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.notifications_outlined,
                          color: Color(0xFF4A80F0),
                          size: 22,
                        ),
                        onPressed: () {
                          NavigationService().navigateTo('/notifications');
                        },
                        tooltip: 'Notifications',
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                _buildStatCard('Workouts', '${userProgress['totalWorkouts']}'),
                _buildStatCard(
                  'Calories',
                  '${userProgress['totalCaloriesBurned']}',
                ),
                _buildStatCard('Streak', '${userProgress['streakDays']}d'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 6),
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        decoration: BoxDecoration(
          color: const Color(
            0xFF4A80F0,
          ).withOpacity(0.18), // blue-tinted background
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.white.withOpacity(0.25),
            width: 1,
          ), // subtle border
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 4,
              offset: const Offset(0, 2),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                letterSpacing: 0.5,
                shadows: [
                  Shadow(
                    color: Colors.black38,
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.13), // lighter overlay
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  letterSpacing: 0.3,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      blurRadius: 1,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.05 * 255),
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF4A80F0),
        unselectedLabelColor: AppTheme.textSecondaryLight,
        indicatorColor: const Color(0xFF4A80F0),
        indicatorWeight: 3,
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: 'WORKOUTS'),
          Tab(text: 'PROGRESS'),
          Tab(text: 'PLANS'),
        ],
      ),
    );
  }

  Widget _buildWorkoutsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Today\'s Workout',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildTodayWorkoutCard(),
          const SizedBox(height: 24),
          Text(
            'Workout Categories',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          _buildCategoriesGrid(),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Recent Workouts',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
                    NavigationService().navigateTo('/workout-history');
                  } else {
                    _showUpgradeDialog();
                  }
                },
                child: Text(
                  'See All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildRecentWorkoutsList(),
        ],
      ),
    );
  }

  // --- Reusable categories getter for all workout categories ---
  List<Map<String, dynamic>> get _allCategories => [
    // Copied from _buildCategoriesGrid
    {
      'icon': Icons.local_fire_department,
      'color': const Color(0xFFFFE5E5),
      'iconColor': const Color(0xFFFF5A5A),
      'name': 'Fat Loss / HIIT',
      'count': 30,
      'accent': const Color(0xFFFF5A5A),
      'exercises': [
        {
          'name': 'Jumping Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'High Knees',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/OAJ_J3EZkdY',
        },
        {
          'name': 'Mountain Climbers',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Burpees',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Squat Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Lateral Hops',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/0Q9pK3UjR2I',
        },
        {
          'name': 'Skater Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1i5KqB2-0zE',
        },
        {
          'name': 'Jump Lunges',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Sprint in Place',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Butt Kicks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/vTHMCtgVtFw',
        },
        {
          'name': 'Jump Rope (imaginary rope)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1BZMwQ6yC6o',
        },
        {
          'name': 'Plank Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Star Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'Fast Feet Shuffle',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Jump Tucks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1i5KqB2-0zE',
        },
        {
          'name': 'Box Shuffle',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/0Q9pK3UjR2I',
        },
        {
          'name': 'Cross Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Frog Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Lunge to Knee Drive',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Shadow Boxing',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1BZMwQ6yC6o',
        },
        {
          'name': 'Seal Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'Plank to Stand',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Bear Crawl',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Crab Walk',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Jump Squat Pulse',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': '180° Jump Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Explosive Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Speed Skaters',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1i5KqB2-0zE',
        },
        {
          'name': 'Lateral Step Touch',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/0Q9pK3UjR2I',
        },
        {
          'name': 'Running Burpees',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
      ],
    },
    {
      'icon': Icons.fitness_center,
      'color': const Color(0xFFFFF3E0),
      'iconColor': const Color(0xFFFFA726),
      'name': 'Strength',
      'count': 30,
      'accent': const Color(0xFFFFA726),
      'exercises': [
        {
          'name': 'Standard Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/IODxDxX7oi4',
        },
        {
          'name': 'Diamond Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/J8zj8t2g8tA',
        },
        {
          'name': 'Wide Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8I0xgG8tG8A',
        },
        {
          'name': 'Pike Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2Vv-2Vv-2VvA',
        },
        {
          'name': 'Decline Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/5bK5bK5bKA',
        },
        {
          'name': 'Incline Push-ups (on a chair)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/7t7t7t7t7tA',
        },
        {
          'name': 'Triceps Dips (on chair)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3G3G3G3G3GA',
        },
        {
          'name': 'Shoulder Taps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/4pK4pK4pKA',
        },
        {
          'name': 'Arm Circles (30 sec forward/back)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6V6V6V6V6VA',
        },
        {
          'name': 'Hindu Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8V8V8V8V8VA',
        },
        {
          'name': 'Bodyweight Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/9a9a9a9a9aA',
        },
        {
          'name': 'Jump Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Bulgarian Split Squats (use chair)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Calf Raises',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Glute Bridges',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Wall Sits',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Forward Lunges',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Reverse Lunges',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Sumo Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Pulse Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Burpee to Push-up',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Walkout to Push-up',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Step-ups (on stairs or chair)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Plank Up-Downs',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Bear Crawl Shoulder Taps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Prisoner Squats',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Lunge to Kick',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Squat to Calf Raise',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Dead Bug Hold',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Superman Hold (back strength)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
      ],
    },
    {
      'icon': Icons.military_tech,
      'color': const Color(0xFFE3F0FF),
      'iconColor': const Color(0xFF2196F3),
      'name': 'Military Fitness',
      'count': 30,
      'accent': const Color(0xFF2196F3),
      'exercises': [
        {
          'name': 'Burpees',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Jumping Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'Push-ups (strict form)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/IODxDxX7oi4',
        },
        {
          'name': 'Sit-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/J8zj8t2g8tA',
        },
        {
          'name': 'Plank (1–3 min holds)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8I0xgG8tG8A',
        },
        {
          'name': 'Flutter Kicks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2Vv-2Vv-2VvA',
        },
        {
          'name': 'Mountain Climbers',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Squat Thrusts',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/5bK5bK5bKA',
        },
        {
          'name': 'Lunge Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/7t7t7t7t7tA',
        },
        {
          'name': 'Bear Crawls',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3G3G3G3G3GA',
        },
        {
          'name': 'Crab Walks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/4pK4pK4pKA',
        },
        {
          'name': 'Push-up to T Plank',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6V6V6V6V6VA',
        },
        {
          'name': 'Side Plank Dips',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8V8V8V8V8VA',
        },
        {
          'name': 'Pull-up Hold (if bar available)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/9a9a9a9a9aA',
        },
        {
          'name': 'Air Punches',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Seal Jumps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Suicide Runs (short bursts)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Sprint Intervals',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Frog Hops',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1i5KqB2-0zE',
        },
        {
          'name': 'Hand-release Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Fireman Crawl',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Wall Sit Challenge',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'One-leg Squats (Pistol Prep)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Jump Tucks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Military Jump Jacks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'Plank to Elbow Tap',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Crawl Outs',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Knee-to-Elbow Push-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Ladder Foot Drill',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Core March in Plank',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
      ],
    },
    {
      'icon': Icons.accessibility_new,
      'color': const Color(0xFFE8F5E9),
      'iconColor': const Color(0xFF43A047),
      'name': 'Core & Abs',
      'count': 30,
      'accent': const Color(0xFF43A047),
      'exercises': [
        {
          'name': 'Crunches',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/XyNl8t8t8tA',
        },
        {
          'name': 'Bicycle Crunches',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/1BZMwQ6yC6o',
        },
        {
          'name': 'Russian Twists',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2W4ZNSwoW_4',
        },
        {
          'name': 'Plank (Front)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Side Plank (L/R)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Mountain Climbers',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Plank with Shoulder Taps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'V-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Leg Raises',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Reverse Crunches',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Flutter Kicks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2Vv-2Vv-2VvA',
        },
        {
          'name': 'Scissor Kicks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Knee Hugs',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Sit-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/dZgVxmf6jkA',
        },
        {
          'name': 'Heel Taps',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Plank Hip Dips',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Plank Knee-to-Elbow',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Starfish Crunch',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Lying Windshield Wipers',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Hollow Body Hold',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2Vv-2Vv-2VvA',
        },
        {
          'name': 'Toe Touches',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Crossbody Mountain Climbers',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Bird-Dog (stability)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Superman Lifts',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/A-cFYWvaHr0',
        },
        {
          'name': 'Jackknife Sit-ups',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/6kALZikXxLc',
        },
        {
          'name': 'Up-Down Planks',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
        {
          'name': 'Wall Sit with Knee Lift',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/2Vv-2Vv-2VvA',
        },
        {
          'name': 'L-Sit Hold (on floor or blocks)',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/3XDriUn0udo',
        },
        {
          'name': 'Standing Oblique Crunch',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/8vJ1pIG1gIQ',
        },
        {
          'name': 'Dead Bug',
          'image': 'assets/images/workout_placeholder.png',
          'videoUrl': 'https://youtu.be/nmwgirgXLYM',
        },
      ],
    },
  ];

  // --- Helper to get all exercises from all categories ---
  List<Map<String, dynamic>> get _allExercises {
    final List<Map<String, dynamic>> all = [];
    for (final cat in _allCategories) {
      all.addAll(List<Map<String, dynamic>>.from(cat['exercises'] as List));
    }
    return all;
  }

  // --- Show guided workout session: pick a random workout and start session ---
  void _showGuidedWorkoutSessionDialog() async {
    if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
      // For now, just pick a random workout from all exercises and pass its id
      final allExercises = List<Map<String, dynamic>>.from(_allExercises);
      allExercises.shuffle();
      final selected = allExercises.first;
      // You may want to show a dialog to select duration/level, but for now just start
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (_) => WorkoutSessionScreen(workoutId: selected['id'] ?? 'demo'),
        ),
      );
    } else {
      _showUpgradeDialog();
    }
  }

  Widget _buildWorkoutStat(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF4A80F0),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black54),
        ),
      ],
    );
  }

  Widget _buildTodayWorkoutCard() {
    return Column(
      children: [
        // Enhanced Today's Workout Card with 30-Day Program
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [const Color(0xFF4A80F0), const Color(0xFF1A56E0)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha((0.05 * 255).toInt()),
                blurRadius: 4,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    Icon(Icons.fitness_center, color: Colors.white, size: 36),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: const [
                          Text(
                            'Full Body Workout',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            'Military Fitness • 45 min',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 8),
                    Material(
                      color: Colors.transparent,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.play_arrow,
                            color: Color(0xFF4A80F0),
                            size: 32,
                          ),
                          onPressed: _showWorkoutOptionsDialog,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 24,
                  horizontal: 20,
                ),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20),
                    bottomRight: Radius.circular(20),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildWorkoutStat('8', 'Exercises'),
                    _buildWorkoutStat('350', 'Calories'),
                    _buildWorkoutStat('45', 'Minutes'),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 30-Day Challenge Banner
        _build30DayChallengeBanner(),
      ],
    );
  }

  Widget _build30DayChallengeBanner() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade400, Colors.orange.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.emoji_events,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '30-Day Challenge',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Personalized daily workouts',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: Icon(Icons.arrow_forward, color: Colors.orange.shade600),
              onPressed: _open30DayChallenge,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoriesGrid() {
    final categories = _allCategories;
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.1,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final cat = categories[index];
        return _buildWorkoutCategoryCard(cat);
      },
    );
  }

  Widget _buildWorkoutCategoryCard(Map<String, dynamic> category) {
    final isMobile = ResponsiveUtils.getLayoutType(context) == LayoutType.mobilePortrait;
    final isSmallScreen = MediaQuery.of(context).size.width < 360;

    return InkWell(
      onTap: () {
        if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (_) => WorkoutListScreen(
                categoryName: category['name'] as String,
                accentColor: category['accent'] as Color?,
                exercises: List<Map<String, dynamic>>.from(
                  category['exercises'] as List,
                ),
              ),
            ),
          );
        } else {
          _showUpgradeDialog();
        }
      },
      borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
      child: Container(
        padding: EdgeInsets.all(isMobile ? 12 : 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white,
              Color.lerp(Colors.white, category['color'] as Color, 0.1) ?? Colors.white,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(isMobile ? 12 : 16),
          boxShadow: AppUI.universalShadow,
          border: Border.all(
            color: (category['color'] as Color).withValues(alpha: 0.1 * 255),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced icon container
            Container(
              padding: EdgeInsets.all(isMobile ? 8 : 10),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    (category['color'] as Color).withValues(alpha: 0.2 * 255),
                    (category['color'] as Color).withValues(alpha: 0.1 * 255),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: AppUI.universalShadow,
              ),
              child: Icon(
                category['icon'] as IconData,
                color: category['iconColor'] as Color,
                size: isMobile ? (isSmallScreen ? 20 : 22) : 24,
              ),
            ),
            SizedBox(height: isMobile ? 8 : 12),
            // Enhanced text with better contrast
            Text(
              category['name'] as String,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.black87,
                letterSpacing: 0.2,
                fontSize: isMobile ? (isSmallScreen ? 13 : 14) : 16,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: isMobile ? 2 : 4),
            Text(
              '${category['count']} workouts',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.black54,
                fontWeight: FontWeight.w500,
                fontSize: isMobile ? (isSmallScreen ? 11 : 12) : 13,
              ),
            ),
            const SizedBox(height: 4),
            // Premium badge if needed
            if (!PremiumService().hasAccessToPremiumFeatures(widget.user))
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.amber.withValues(alpha: 0.3 * 255),
                    width: 1,
                  ),
                ),
                child: Text(
                  'Premium',
                  style: TextStyle(
                    color: Colors.amber.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOldCategoriesGrid() {
    final categories = _allCategories;
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final cat = categories[index];
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                (cat['color'] as Color),
                (cat['color'] as Color).withValues(alpha: 0.8 * 255),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppUI.universalShadow,
            border: Border.all(color: Colors.white.withValues(alpha: 0.3 * 255), width: 1),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder:
                          (_) => WorkoutListScreen(
                            categoryName: cat['name'] as String,
                            accentColor: cat['accent'] as Color?,
                            exercises: List<Map<String, dynamic>>.from(
                              cat['exercises'] as List,
                            ),
                          ),
                    ),
                  );
                } else {
                  _showUpgradeDialog();
                }
              },
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.25 * 255),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: (cat['iconColor'] as Color).withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(14),
                      child: Icon(
                        cat['icon'] as IconData,
                        color: cat['iconColor'] as Color,
                        size: 32,
                      ),
                    ),
                    const SizedBox(height: 18),
                    Text(
                      cat['name'] as String,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black26,
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Tap to explore',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentWorkoutsList() {
    // Redesigned: List with colored circle icon, name, duration/calories, and date right-aligned
    final recent = [
      {
        'name': 'Upper Body Strength',
        'duration': '30 min',
        'calories': '250 cal',
        'date': '21/6',
        'icon': Icons.fitness_center,
        'color': const Color(0xFFB3D6FC),
        'iconColor': const Color(0xFF4A80F0),
      },
      {
        'name': '5K Run',
        'duration': '25 min',
        'calories': '300 cal',
        'date': '20/6',
        'icon': Icons.directions_run,
        'color': const Color(0xFFC8E6C9),
        'iconColor': const Color(0xFF43A047),
      },
      {
        'name': 'Core Workout',
        'duration': '20 min',
        'calories': '200 cal',
        'date': '19/6',
        'icon': Icons.accessibility_new,
        'color': const Color(0xFFFFF9C4),
        'iconColor': const Color(0xFFFFA726),
      },
    ];
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recent.length,
      separatorBuilder: (context, i) => const SizedBox(height: 16),
      itemBuilder: (context, index) {
        final w = recent[index];
        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                color: w['color'] as Color,
                shape: BoxShape.circle,
              ),
              padding: const EdgeInsets.all(14),
              child: Icon(
                w['icon'] as IconData,
                color: w['iconColor'] as Color,
                size: 28,
              ),
            ),
            const SizedBox(width: 18),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    w['name'] as String,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                      color: Color(0xFF222B45),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '${w['duration']} • ${w['calories']}',
                    style: const TextStyle(
                      fontSize: 13,
                      color: Color(0xFF7B8D9E),
                    ),
                  ),
                ],
              ),
            ),
            Text(
              w['date'] as String,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildProgressTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with filter options
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Your Progress',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              PopupMenuButton<String>(
                icon: Icon(Icons.filter_list, color: Colors.grey[600]),
                onSelected: (value) {
                  // Handle filter selection
                },
                itemBuilder:
                    (context) => [
                      const PopupMenuItem(
                        value: 'week',
                        child: Text('This Week'),
                      ),
                      const PopupMenuItem(
                        value: 'month',
                        child: Text('This Month'),
                      ),
                      const PopupMenuItem(
                        value: 'year',
                        child: Text('This Year'),
                      ),
                    ],
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Enhanced Progress Overview Cards
          _buildProgressOverviewCards(),
          const SizedBox(height: 24),

          // Weekly Activity Chart
          _buildWeeklyActivitySection(),
          const SizedBox(height: 24),

          // Body Measurements Section
          _buildBodyMeasurementsSection(),
          const SizedBox(height: 24),

          // Achievement Section
          _buildAchievementsSection(),
          const SizedBox(height: 24),

          // Progress Photos Section
          _buildProgressPhotosSection(),
        ],
      ),
    );
  }

  Widget _buildProgressOverviewCards() {
    // Calculate progress changes (this week vs last week)
    final totalWorkouts = userProgress['totalWorkouts'] as int;
    final totalCalories = userProgress['totalCaloriesBurned'] as int;
    final totalHours = userProgress['totalActiveHours'] as double;
    final avgHeartRate = userProgress['averageHeartRate'] as int;

    // For demo, showing some progress. In real app, you'd compare with previous week's data
    final weeklyWorkouts = (userProgress['weeklyWorkouts'] as List<int>).reduce(
      (a, b) => a + b,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.3,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildProgressCard(
          title: 'Workouts',
          value: totalWorkouts.toString(),
          subtitle:
              weeklyWorkouts > 0
                  ? '+$weeklyWorkouts this week'
                  : 'Start your first workout!',
          icon: Icons.fitness_center,
          color: const Color(0xFF4A80F0),
          trend: weeklyWorkouts > 0 ? 'up' : 'stable',
        ),
        _buildProgressCard(
          title: 'Calories Burned',
          value:
              totalCalories > 1000
                  ? '${(totalCalories / 1000).toStringAsFixed(1)}K'
                  : totalCalories.toString(),
          subtitle:
              totalCalories > 0 ? 'Keep burning!' : 'Start burning calories!',
          icon: Icons.local_fire_department,
          color: Colors.orange,
          trend: totalCalories > 0 ? 'up' : 'stable',
        ),
        _buildProgressCard(
          title: 'Active Hours',
          value: totalHours.toStringAsFixed(1),
          subtitle: totalHours > 0 ? 'Great progress!' : 'Get moving!',
          icon: Icons.schedule,
          color: Colors.green,
          trend: totalHours > 0 ? 'up' : 'stable',
        ),
        _buildProgressCard(
          title: 'Avg Heart Rate',
          value: avgHeartRate > 0 ? avgHeartRate.toString() : '--',
          subtitle:
              avgHeartRate > 0
                  ? 'BPM during workouts'
                  : 'Complete workouts to track',
          icon: Icons.favorite,
          color: Colors.red,
          trend: 'stable',
        ),
      ],
    );
  }

  Widget _buildProgressCard({
    required String title,
    required String value,
    required String subtitle,
    required IconData icon,
    required Color color,
    required String trend,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              Icon(
                trend == 'up'
                    ? Icons.trending_up
                    : trend == 'down'
                    ? Icons.trending_down
                    : Icons.trending_flat,
                color:
                    trend == 'up'
                        ? Colors.green
                        : trend == 'down'
                        ? Colors.red
                        : Colors.grey,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(fontSize: 10, color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildWeeklyActivitySection() {
    final weeklyWorkouts = userProgress['weeklyWorkouts'] as List<int>;
    final maxWorkouts =
        weeklyWorkouts.isEmpty
            ? 1
            : weeklyWorkouts.reduce((a, b) => a > b ? a : b);
    final normalizedMax =
        maxWorkouts == 0 ? 1 : maxWorkouts; // Avoid division by zero

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Weekly Activity',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                _showWeeklyDetailsDialog();
              },
              child: const Text('View Details'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              SizedBox(
                height: 120,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildBar(
                      'Mon',
                      weeklyWorkouts[0] == 0
                          ? 10
                          : (weeklyWorkouts[0] / normalizedMax) * 100,
                      weeklyWorkouts[0] > 0,
                    ),
                    _buildBar(
                      'Tue',
                      weeklyWorkouts[1] == 0
                          ? 10
                          : (weeklyWorkouts[1] / normalizedMax) * 100,
                      weeklyWorkouts[1] > 0,
                    ),
                    _buildBar(
                      'Wed',
                      weeklyWorkouts[2] == 0
                          ? 10
                          : (weeklyWorkouts[2] / normalizedMax) * 100,
                      weeklyWorkouts[2] > 0,
                    ),
                    _buildBar(
                      'Thu',
                      weeklyWorkouts[3] == 0
                          ? 10
                          : (weeklyWorkouts[3] / normalizedMax) * 100,
                      weeklyWorkouts[3] > 0,
                    ),
                    _buildBar(
                      'Fri',
                      weeklyWorkouts[4] == 0
                          ? 10
                          : (weeklyWorkouts[4] / normalizedMax) * 100,
                      weeklyWorkouts[4] > 0,
                    ),
                    _buildBar(
                      'Sat',
                      weeklyWorkouts[5] == 0
                          ? 10
                          : (weeklyWorkouts[5] / normalizedMax) * 100,
                      weeklyWorkouts[5] > 0,
                    ),
                    _buildBar(
                      'Sun',
                      weeklyWorkouts[6] == 0
                          ? 10
                          : (weeklyWorkouts[6] / normalizedMax) * 100,
                      weeklyWorkouts[6] > 0,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  _buildActivityLegend('Workout Days', const Color(0xFF4A80F0)),
                  _buildActivityLegend('Rest Days', const Color(0xFFB3D6FC)),
                ],
              ),
              if (weeklyWorkouts.every((w) => w == 0))
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: Text(
                    'Complete your first workout to see progress!',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  void _showWeeklyDetailsDialog() {
    final weeklyWorkouts = userProgress['weeklyWorkouts'] as List<int>;
    final days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Weekly Activity Details'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(
                7,
                (index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(days[index]),
                      Text(
                        '${weeklyWorkouts[index]} workout${weeklyWorkouts[index] == 1 ? '' : 's'}',
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildActivityLegend(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.grey)),
      ],
    );
  }

  Widget _buildBodyMeasurementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Body Measurements',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                _showAddMeasurementDialog();
              },
              child: const Text('Add Entry'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.08),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              _buildMeasurementRow(
                'Weight',
                userProgress['currentWeight'] > 0
                    ? '${userProgress['currentWeight']} kg'
                    : 'Not set',
                userProgress['currentWeight'] > 0
                    ? 'Track progress'
                    : 'Add your weight',
                userProgress['currentWeight'] > 0 ? Colors.blue : Colors.grey,
              ),
              const Divider(height: 24),
              _buildMeasurementRow(
                'Body Fat',
                userProgress['currentBodyFat'] > 0
                    ? '${userProgress['currentBodyFat']}%'
                    : 'Not set',
                userProgress['currentBodyFat'] > 0
                    ? 'Track progress'
                    : 'Add body fat %',
                userProgress['currentBodyFat'] > 0 ? Colors.green : Colors.grey,
              ),
              const Divider(height: 24),
              _buildMeasurementRow(
                'Muscle Mass',
                userProgress['currentMuscleMass'] > 0
                    ? '${userProgress['currentMuscleMass']} kg'
                    : 'Not set',
                userProgress['currentMuscleMass'] > 0
                    ? 'Track progress'
                    : 'Add muscle mass',
                userProgress['currentMuscleMass'] > 0
                    ? Colors.blue
                    : Colors.grey,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMeasurementRow(
    String label,
    String current,
    String change,
    Color changeColor,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              current,
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            Text(change, style: TextStyle(color: changeColor, fontSize: 12)),
          ],
        ),
      ],
    );
  }

  Widget _buildAchievementsSection() {
    final achievements = userProgress['achievements'] as List<String>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Achievements',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 100,
          child:
              achievements.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.emoji_events,
                          color: Colors.grey[400],
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Complete workouts to unlock achievements!',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  )
                  : ListView(
                    scrollDirection: Axis.horizontal,
                    children:
                        achievements.map((achievement) {
                          return _buildAchievementBadge(
                            achievement,
                            _getAchievementIcon(achievement),
                            _getAchievementColor(achievement),
                          );
                        }).toList(),
                  ),
        ),
      ],
    );
  }

  IconData _getAchievementIcon(String achievement) {
    switch (achievement) {
      case 'First Workout':
        return Icons.star;
      case 'First Week':
        return Icons.calendar_today;
      case 'Calorie Burner':
        return Icons.local_fire_department;
      case 'Consistent':
        return Icons.emoji_events;
      default:
        return Icons.star;
    }
  }

  Color _getAchievementColor(String achievement) {
    switch (achievement) {
      case 'First Workout':
        return Colors.amber;
      case 'First Week':
        return Colors.blue;
      case 'Calorie Burner':
        return Colors.orange;
      case 'Consistent':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Widget _buildAchievementBadge(String title, IconData icon, Color color) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressPhotosSection() {
    final progressPhotos =
        userProgress['progressPhotos'] as List<Map<String, dynamic>>;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress Photos',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                _showAddPhotoDialog();
              },
              child: const Text('Add Photo'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child:
              progressPhotos.isEmpty
                  ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.photo_camera,
                          color: Colors.grey[400],
                          size: 32,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Add your first progress photo!',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  )
                  : ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      ...progressPhotos.map(
                        (photo) => _buildPhotoCard(
                          photo['label'] as String,
                          photo['imagePath'] as String,
                        ),
                      ),
                      _buildAddPhotoCard(),
                    ],
                  ),
        ),
      ],
    );
  }

  Widget _buildPhotoCard(String label, String imagePath) {
    return Container(
      width: 90,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.grey[200],
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                color: Colors.grey[300],
              ),
              child: const Center(child: Icon(Icons.photo, color: Colors.grey)),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Text(
              label,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddPhotoCard() {
    return Container(
      width: 90,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
        color: Colors.grey[50],
      ),
      child: InkWell(
        onTap: _showAddPhotoDialog,
        borderRadius: BorderRadius.circular(12),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_a_photo, color: Colors.grey, size: 24),
            SizedBox(height: 8),
            Text(
              'Add Photo',
              style: TextStyle(fontSize: 10, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBar(String day, double height, bool active) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 400),
          width: 18,
          height: height,
          decoration: BoxDecoration(
            color: active ? const Color(0xFF4A80F0) : const Color(0xFFB3D6FC),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          day,
          style: TextStyle(
            color: active ? const Color(0xFF4A80F0) : const Color(0xFFB3D6FC),
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildPlansTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with create plan button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Workout Plans',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              ElevatedButton.icon(
                onPressed: _showCreatePlanDialog,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('Create Plan'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A80F0),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Current Active Plan
          _buildActivePlanSection(),
          const SizedBox(height: 24),

          // Featured Plans
          _buildFeaturedPlansSection(),
          const SizedBox(height: 24),

          // Plan Categories
          _buildPlanCategoriesSection(),
          const SizedBox(height: 24),

          // My Custom Plans
          _buildMyPlansSection(),
        ],
      ),
    );
  }

  Widget _buildActivePlanSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Active Plan',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [const Color(0xFF4A80F0), const Color(0xFF1A56E0)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4A80F0).withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Strength Builder Pro',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Week 3 of 6 • Intermediate',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.fitness_center,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Progress bar
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Progress',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '50%',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: 0.5,
                    backgroundColor: Colors.white.withOpacity(0.3),
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.white,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        // Continue plan
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: const Color(0xFF4A80F0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Continue Plan'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  OutlinedButton(
                    onPressed: () {
                      _showPlanDetailsDialog();
                    },
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.white),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Details',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedPlansSection() {
    final featuredPlans = [
      {
        'name': '30-Day Fat Burn',
        'duration': '30 days',
        'level': 'Beginner',
        'rating': 4.8,
        'users': '12.5K',
        'color': Colors.orange,
        'icon': Icons.local_fire_department,
        'description':
            'High-intensity workouts designed to maximize fat burning',
      },
      {
        'name': 'Muscle Gain Elite',
        'duration': '12 weeks',
        'level': 'Advanced',
        'rating': 4.9,
        'users': '8.2K',
        'color': Colors.purple,
        'icon': Icons.fitness_center,
        'description': 'Build serious muscle mass with progressive overload',
      },
      {
        'name': 'Athlete Performance',
        'duration': '8 weeks',
        'level': 'Expert',
        'rating': 4.7,
        'users': '5.1K',
        'color': Colors.red,
        'icon': Icons.sports,
        'description': 'Train like a pro athlete with sport-specific exercises',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Featured Plans',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            TextButton(
              onPressed: () {
                // Navigate to all plans
              },
              child: const Text('See All'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: featuredPlans.length,
            separatorBuilder: (context, i) => const SizedBox(width: 16),
            itemBuilder: (context, index) {
              final plan = featuredPlans[index];
              return _buildFeaturedPlanCard(plan);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedPlanCard(Map<String, dynamic> plan) {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and rating
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: (plan['color'] as Color).withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: plan['color'] as Color,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    plan['icon'] as IconData,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                Row(
                  children: [
                    const Icon(Icons.star, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      plan['rating'].toString(),
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    plan['name'] as String,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    plan['description'] as String,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      _buildPlanBadge(
                        plan['duration'] as String,
                        Icons.schedule,
                      ),
                      const SizedBox(width: 8),
                      _buildPlanBadge(
                        plan['level'] as String,
                        Icons.signal_cellular_alt,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '${plan['users']} users',
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                  const Spacer(),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        _showPlanPreviewDialog(plan);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: plan['color'] as Color,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Start Plan',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanBadge(String text, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCategoriesSection() {
    final categories = [
      {'name': 'Weight Loss', 'icon': Icons.trending_down, 'count': '15 plans'},
      {
        'name': 'Muscle Gain',
        'icon': Icons.fitness_center,
        'count': '12 plans',
      },
      {'name': 'Endurance', 'icon': Icons.directions_run, 'count': '10 plans'},
      {
        'name': 'Flexibility',
        'icon': Icons.self_improvement,
        'count': '8 plans',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Browse by Category',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 2.5,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
          ),
          itemCount: categories.length,
          itemBuilder: (context, index) {
            final category = categories[index];
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    category['icon'] as IconData,
                    color: const Color(0xFF4A80F0),
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          category['name'] as String,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          category['count'] as String,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildMyPlansSection() {
    final myPlans = [
      {
        'name': 'My Custom HIIT',
        'exercises': 8,
        'duration': '25 min',
        'lastUsed': '2 days ago',
        'difficulty': 'Hard',
      },
      {
        'name': 'Morning Routine',
        'exercises': 5,
        'duration': '15 min',
        'lastUsed': '1 week ago',
        'difficulty': 'Easy',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'My Custom Plans',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: myPlans.length,
          separatorBuilder: (context, i) => const SizedBox(height: 12),
          itemBuilder: (context, index) {
            final plan = myPlans[index];
            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF4A80F0).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.playlist_play,
                      color: Color(0xFF4A80F0),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          plan['name'] as String,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${plan['exercises']} exercises • ${plan['duration']} • ${plan['difficulty']}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Last used: ${plan['lastUsed']}',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert, color: Colors.grey[600]),
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _editCustomPlan(plan);
                          break;
                        case 'duplicate':
                          _duplicateCustomPlan(plan);
                          break;
                        case 'delete':
                          _deleteCustomPlan(plan);
                          break;
                      }
                    },
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Text('Edit'),
                          ),
                          const PopupMenuItem(
                            value: 'duplicate',
                            child: Text('Duplicate'),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Text('Delete'),
                          ),
                        ],
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // New methods for 30-day workout functionality
  void _showWorkoutOptionsDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Choose Workout Type',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Quick Workout Option
                      _buildWorkoutOption(
                        title: 'Quick Workout',
                        subtitle: 'Start a single workout session',
                        icon: Icons.play_circle_filled,
                        color: Theme.of(context).primaryColor,
                        onTap: () {
                          Navigator.pop(context);
                          _showGuidedWorkoutSessionDialog();
                        },
                      ),

                      const SizedBox(height: 16),

                      // 30-Day Challenge Option
                      _buildWorkoutOption(
                        title: '30-Day Challenge',
                        subtitle: 'Personalized daily workouts for 30 days',
                        icon: Icons.emoji_events,
                        color: Colors.orange,
                        onTap: () {
                          Navigator.pop(context);
                          _open30DayChallenge();
                        },
                      ),

                      const SizedBox(height: 16),

                      // Custom Workout Option
                      _buildWorkoutOption(
                        title: 'Custom Workout',
                        subtitle: 'Create your own workout routine',
                        icon: Icons.build_circle,
                        color: Colors.green,
                        onTap: () {
                          Navigator.pop(context);
                          NavigationService().navigateTo('/create-workout');
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildWorkoutOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[200]!),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  void _open30DayChallenge() {
    // Check premium access before opening 30-day challenge
    if (PremiumService().hasAccessToPremiumFeatures(widget.user)) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ThirtyDayWorkoutScreen(user: widget.user),
        ),
      );
    } else {
      _showUpgradeDialog();
    }
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.workspace_premium, color: AppTheme.premiumColor),
                const SizedBox(width: 8),
                const Text('Premium Feature'),
              ],
            ),
            content: const Text(
              'Unlock the complete fitness suite including workout categories, nutrition guides, challenges, recovery plans, and the 30-Day Challenge with Premium membership!',
              style: TextStyle(height: 1.5),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/premium');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.premiumColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Subscribe to Premium'),
              ),
            ],
          ),
    );
  }

  // Dialog methods for enhanced functionality
  void _showAddMeasurementDialog() {
    final weightController = TextEditingController();
    final bodyFatController = TextEditingController();
    final muscleMassController = TextEditingController();

    // Pre-fill with current values if they exist
    if (userProgress['currentWeight'] > 0) {
      weightController.text = userProgress['currentWeight'].toString();
    }
    if (userProgress['currentBodyFat'] > 0) {
      bodyFatController.text = userProgress['currentBodyFat'].toString();
    }
    if (userProgress['currentMuscleMass'] > 0) {
      muscleMassController.text = userProgress['currentMuscleMass'].toString();
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Measurement'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: weightController,
                  decoration: const InputDecoration(
                    labelText: 'Weight (kg)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: bodyFatController,
                  decoration: const InputDecoration(
                    labelText: 'Body Fat (%)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: muscleMassController,
                  decoration: const InputDecoration(
                    labelText: 'Muscle Mass (kg)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Save measurements
                  if (weightController.text.isNotEmpty) {
                    userProgress['currentWeight'] =
                        double.tryParse(weightController.text) ?? 0.0;
                  }
                  if (bodyFatController.text.isNotEmpty) {
                    userProgress['currentBodyFat'] =
                        double.tryParse(bodyFatController.text) ?? 0.0;
                  }
                  if (muscleMassController.text.isNotEmpty) {
                    userProgress['currentMuscleMass'] =
                        double.tryParse(muscleMassController.text) ?? 0.0;
                  }

                  _saveUserProgress();
                  setState(() {});
                  Navigator.pop(context);
                  _showSuccessSnackBar('Measurements updated successfully!');
                },
                child: const Text('Save'),
              ),
            ],
          ),
    );
  }

  void _showAddPhotoDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Progress Photo'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera_alt),
                  title: const Text('Take Photo'),
                  onTap: () {
                    Navigator.pop(context);
                    _takeProgressPhoto();
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choose from Gallery'),
                  onTap: () {
                    Navigator.pop(context);
                    _chooseFromGallery();
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  void _showCreatePlanDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Create Custom Plan'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'Plan Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Difficulty Level',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      ['Beginner', 'Intermediate', 'Advanced', 'Expert']
                          .map(
                            (level) => DropdownMenuItem(
                              value: level,
                              child: Text(level),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {},
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().navigateTo('/plan-builder');
                },
                child: const Text('Create'),
              ),
            ],
          ),
    );
  }

  void _showPlanDetailsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Plan Details'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Strength Builder Pro',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                const Text('Week 3 of 6 • Intermediate Level'),
                const SizedBox(height: 16),
                const Text('Description:'),
                const SizedBox(height: 4),
                const Text(
                  'A comprehensive 6-week program designed to build functional strength.',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Continue'),
              ),
            ],
          ),
    );
  }

  void _showPlanPreviewDialog(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(plan['name'] as String),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(plan['description'] as String),
                const SizedBox(height: 16),
                Text('Duration: ${plan['duration']}'),
                Text('Level: ${plan['level']}'),
                Text('Rating: ${plan['rating']} (${plan['users']} users)'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _startPlan(plan);
                },
                child: const Text('Start Plan'),
              ),
            ],
          ),
    );
  }

  void _takeProgressPhoto() {
    // In a real app, you'd use image_picker here
    // For demo, we'll just add a placeholder photo
    final progressPhotos =
        userProgress['progressPhotos'] as List<Map<String, dynamic>>;
    final weekNumber = progressPhotos.length + 1;

    progressPhotos.add({
      'label': 'Week $weekNumber',
      'imagePath': 'assets/images/progress_week$weekNumber.jpg',
      'date': DateTime.now().toIso8601String(),
    });

    _saveUserProgress();
    setState(() {});
    _showSuccessSnackBar('Progress photo added successfully!');
  }

  void _chooseFromGallery() {
    // In a real app, you'd use image_picker here
    // For demo, we'll just add a placeholder photo
    final progressPhotos =
        userProgress['progressPhotos'] as List<Map<String, dynamic>>;
    final weekNumber = progressPhotos.length + 1;

    progressPhotos.add({
      'label': 'Week $weekNumber',
      'imagePath': 'assets/images/progress_week$weekNumber.jpg',
      'date': DateTime.now().toIso8601String(),
    });

    _saveUserProgress();
    setState(() {});
    _showSuccessSnackBar('Progress photo added successfully!');
  }

  void _startPlan(Map<String, dynamic> plan) {
    _showSuccessSnackBar('Started ${plan['name']}!');
  }

  void _editCustomPlan(Map<String, dynamic> plan) {
    NavigationService().navigateTo('/edit-plan/${plan['name']}');
  }

  void _duplicateCustomPlan(Map<String, dynamic> plan) {
    _showSuccessSnackBar('Plan duplicated successfully!');
  }

  void _deleteCustomPlan(Map<String, dynamic> plan) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Plan'),
            content: Text('Are you sure you want to delete "${plan['name']}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _showSuccessSnackBar('Plan deleted successfully!');
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  // === REAL-TIME WORKOUT GUIDANCE ===
}

                                  