import 'package:flutter/material.dart';
import '../models/nigerian_exam_pattern_model.dart';
import '../services/enhanced_quiz_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_widgets.dart';

/// Enhanced Quiz Screen with Nigerian Exam Patterns and Adaptive Features
class EnhancedQuizScreen extends StatefulWidget {
  final String agencyCode;
  final String category;
  final QuizMode mode;

  const EnhancedQuizScreen({
    super.key,
    required this.agencyCode,
    required this.category,
    required this.mode,
  });

  @override
  State<EnhancedQuizScreen> createState() => _EnhancedQuizScreenState();
}

class _EnhancedQuizScreenState extends State<EnhancedQuizScreen> {
  late EnhancedQuizService _quizService;
  int? _selectedOption;
  bool _showExplanation = false;
  bool _isAnswered = false;

  @override
  void initState() {
    super.initState();
    _quizService = EnhancedQuizService();
    _initializeQuiz();
  }

  Future<void> _initializeQuiz() async {
    await _quizService.initializeQuiz(
      agencyCode: widget.agencyCode,
      category: widget.category,
      mode: widget.mode,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body:
          _quizService.questions.isEmpty
              ? _buildLoadingState()
              : _quizService.isQuizCompleted
              ? _buildResultsScreen(_quizService)
              : _buildQuizContent(_quizService),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('${widget.category} Quiz'),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        if (widget.mode == QuizMode.timed && _quizService.remainingTime > 0)
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color:
                  _quizService.remainingTime < 300 ? Colors.red : Colors.orange,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.timer, size: 16),
                const SizedBox(width: 4),
                Text(
                  _formatTime(_quizService.remainingTime),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading Nigerian exam questions...'),
        ],
      ),
    );
  }

  Widget _buildQuizContent(EnhancedQuizService service) {
    final question = service.currentQuestion;
    if (question == null) return const SizedBox.shrink();

    return ResponsiveContainer(
      child: Column(
        children: [
          _buildProgressIndicator(service),
          Expanded(
            child: SingleChildScrollView(
              padding: ResponsiveUtils.getResponsivePadding(context),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildQuestionHeader(service, question),
                  const SizedBox(height: 24),
                  _buildQuestionText(question),
                  const SizedBox(height: 24),
                  _buildOptions(question),
                  if (_showExplanation) ...[
                    const SizedBox(height: 24),
                    _buildExplanation(question),
                  ],
                  if (question.context.isRegionalSpecific) ...[
                    const SizedBox(height: 16),
                    _buildNigerianContext(question.context),
                  ],
                  const SizedBox(height: 32),
                  _buildActionButtons(service),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(EnhancedQuizService service) {
    final progress =
        service.totalQuestions > 0
            ? (service.currentQuestionIndex + 1) / service.totalQuestions
            : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Question ${service.currentQuestionIndex + 1} of ${service.totalQuestions}',
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              if (widget.mode == QuizMode.adaptive)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getDifficultyColor(
                      service.currentQuestion?.difficulty,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    service.currentQuestion?.difficulty.name.toUpperCase() ??
                        '',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionHeader(
    EnhancedQuizService service,
    NigerianQuestionModel question,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                _getCategoryIcon(question.category),
                color: AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    question.category,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    'Nigerian ${_getAgencyName(question.agencyCode)} Exam',
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
            ),
            if (question.context.isRegionalSpecific)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  question.context.region,
                  style: TextStyle(
                    color: Colors.blue[800],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuestionText(NigerianQuestionModel question) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Text(
          question.question,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            height: 1.4,
          ),
        ),
      ),
    );
  }

  Widget _buildOptions(NigerianQuestionModel question) {
    return Column(
      children:
          question.options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = _selectedOption == index;
            final isCorrect = index == question.correctAnswer;
            final showCorrectAnswer =
                _isAnswered && widget.mode == QuizMode.practice;

            Color? backgroundColor;
            Color? borderColor;
            Color? textColor;

            if (showCorrectAnswer) {
              if (isCorrect) {
                backgroundColor = Colors.green[50];
                borderColor = Colors.green;
                textColor = Colors.green[800];
              } else if (isSelected && !isCorrect) {
                backgroundColor = Colors.red[50];
                borderColor = Colors.red;
                textColor = Colors.red[800];
              }
            } else if (isSelected) {
              backgroundColor = AppTheme.primaryColor.withValues(
                alpha: 0.1 * 255,
              );
              borderColor = AppTheme.primaryColor;
              textColor = AppTheme.primaryColor;
            }

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: _isAnswered ? null : () => _selectOption(index),
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    border: Border.all(
                      color: borderColor ?? Colors.grey[300]!,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              isSelected
                                  ? (borderColor ?? AppTheme.primaryColor)
                                  : Colors.transparent,
                          border: Border.all(
                            color: borderColor ?? Colors.grey[400]!,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            String.fromCharCode(65 + index), // A, B, C, D
                            style: TextStyle(
                              color:
                                  isSelected
                                      ? Colors.white
                                      : (borderColor ?? Colors.grey[600]),
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 16,
                            color: textColor,
                            fontWeight:
                                isSelected
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                      if (showCorrectAnswer && isCorrect)
                        Icon(
                          Icons.check_circle,
                          color: Colors.green[600],
                          size: 20,
                        ),
                      if (showCorrectAnswer && isSelected && !isCorrect)
                        Icon(Icons.cancel, color: Colors.red[600], size: 20),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildExplanation(NigerianQuestionModel question) {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.blue[700], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Explanation',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              question.explanation,
              style: const TextStyle(fontSize: 14, height: 1.4),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNigerianContext(NigerianContext context) {
    return Card(
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: Colors.green[700], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Nigerian Context',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                    fontSize: 16,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (context.states.isNotEmpty) ...[
              Text(
                'Relevant States: ${context.states.join(', ')}',
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 8),
            ],
            Text(
              'Region: ${context.region}',
              style: const TextStyle(fontSize: 14),
            ),
            if (context.localReferences.isNotEmpty) ...[
              const SizedBox(height: 8),
              ...context.localReferences.entries.map(
                (entry) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '${entry.key}: ${entry.value}',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(EnhancedQuizService service) {
    return Row(
      children: [
        if (widget.mode == QuizMode.practice && _isAnswered)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _showExplanation = !_showExplanation;
                });
              },
              icon: Icon(
                _showExplanation ? Icons.visibility_off : Icons.visibility,
              ),
              label: Text(
                _showExplanation ? 'Hide Explanation' : 'Show Explanation',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        if (widget.mode == QuizMode.practice && _isAnswered)
          const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed:
                _selectedOption != null && !_isAnswered
                    ? _submitAnswer
                    : (_isAnswered ? _nextQuestion : null),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: Text(
              _isAnswered ? 'Next Question' : 'Submit Answer',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsScreen(EnhancedQuizService service) {
    // Implementation for results screen would go here
    return const Center(
      child: Text('Quiz Completed! Results screen would be implemented here.'),
    );
  }

  void _selectOption(int index) {
    if (!_isAnswered) {
      setState(() {
        _selectedOption = index;
      });
    }
  }

  void _submitAnswer() {
    if (_selectedOption != null) {
      _quizService.answerQuestion(_selectedOption!);
      setState(() {
        _isAnswered = true;
        if (widget.mode == QuizMode.practice) {
          _showExplanation = true;
        }
      });
    }
  }

  void _nextQuestion() {
    _quizService.nextQuestion();
    setState(() {
      _selectedOption = null;
      _isAnswered = false;
      _showExplanation = false;
    });
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Color _getDifficultyColor(DifficultyLevel? difficulty) {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return Colors.green;
      case DifficultyLevel.intermediate:
        return Colors.orange;
      case DifficultyLevel.advanced:
        return Colors.red;
      case DifficultyLevel.expert:
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'general knowledge':
        return Icons.public;
      case 'mathematics':
        return Icons.calculate;
      case 'english language':
        return Icons.language;
      case 'current affairs':
        return Icons.newspaper;
      case 'geography':
        return Icons.map;
      default:
        return Icons.quiz;
    }
  }

  String _getAgencyName(String agencyCode) {
    switch (agencyCode.toLowerCase()) {
      case 'army':
        return 'Army';
      case 'navy':
        return 'Navy';
      case 'airforce':
        return 'Air Force';
      case 'nda':
        return 'Defence Academy';
      default:
        return 'Military';
    }
  }

  @override
  void dispose() {
    _quizService.dispose();
    super.dispose();
  }
}
