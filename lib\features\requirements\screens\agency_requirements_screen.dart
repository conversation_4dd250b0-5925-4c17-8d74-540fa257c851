import 'package:flutter/material.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AgencyRequirementsScreen extends StatefulWidget {
  final dynamic user;

  const AgencyRequirementsScreen({super.key, this.user});

  @override
  State<AgencyRequirementsScreen> createState() =>
      _AgencyRequirementsScreenState();
}

class _AgencyRequirementsScreenState extends State<AgencyRequirementsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<String, dynamic>? _userAgency;
  Set<String> _completedChecklist = {};

  final List<Map<String, dynamic>> _agencies = [
    {
      'name': 'Nigerian Army',
      'shortName': 'NA',
      'color': const Color(0xFF2E7D32),
      'icon': Icons.security,
    },
    {
      'name': 'Nigerian Navy',
      'shortName': 'NN',
      'color': const Color(0xFF1565C0),
      'icon': Icons.anchor,
    },
    {
      'name': 'Nigerian Air Force',
      'shortName': 'NAF',
      'color': const Color(0xFF0277BD),
      'icon': Icons.flight,
    },
    {
      'name': 'Nigeria Police Force',
      'shortName': 'NPF',
      'color': const Color(0xFF424242),
      'icon': Icons.local_police,
    },
    {
      'name': 'Nigerian Defence Academy',
      'shortName': 'NDA',
      'color': const Color(0xFF8E24AA),
      'icon': Icons.school_outlined,
    },
    {
      'name': 'Defence Space Systems Command',
      'shortName': 'DSSC',
      'color': const Color(0xFF5E35B1),
      'icon': Icons.satellite_alt,
    },
    {
      'name': 'Nigeria Security and Civil Defence Corps',
      'shortName': 'NSCDC',
      'color': const Color(0xFFE65100),
      'icon': Icons.shield,
    },
    {
      'name': 'Nigeria Immigration Service',
      'shortName': 'NIS',
      'color': const Color(0xFF00695C),
      'icon': Icons.flight_takeoff,
    },
    {
      'name': 'Nigeria Customs Service',
      'shortName': 'NCS',
      'color': const Color(0xFF6A1B9A),
      'icon': Icons.business_center,
    },
    {
      'name': 'Federal Fire Service',
      'shortName': 'FFS',
      'color': const Color(0xFFD32F2F),
      'icon': Icons.local_fire_department,
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeUserAgency();
    _tabController = TabController(length: 1, vsync: this);
    _loadChecklistProgress();
  }

  Future<void> _loadChecklistProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final savedChecklist =
        prefs.getStringList('checklist_${widget.user?.id ?? 'default'}') ?? [];
    setState(() {
      _completedChecklist = savedChecklist.toSet();
    });
  }

  Future<void> _saveChecklistProgress() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setStringList(
      'checklist_${widget.user?.id ?? 'default'}',
      _completedChecklist.toList(),
    );
  }

  void _initializeUserAgency() {
    if (widget.user != null) {
      // Get the user's target agency
      String? userAgencyName = widget.user.targetAgency;

      if (userAgencyName != null && userAgencyName.isNotEmpty) {
        // Find matching agency in the list
        for (int i = 0; i < _agencies.length; i++) {
          final agency = _agencies[i];
          // Match by name or short name (case insensitive)
          if (agency['name'].toLowerCase().contains(
                userAgencyName.toLowerCase(),
              ) ||
              agency['shortName'].toLowerCase().contains(
                userAgencyName.toLowerCase(),
              ) ||
              userAgencyName.toLowerCase().contains(
                agency['name'].toLowerCase(),
              ) ||
              userAgencyName.toLowerCase().contains(
                agency['shortName'].toLowerCase(),
              )) {
            _userAgency = agency;
            break;
          }
        }
      }
    }

    // If no user agency found, default to Nigerian Army
    _userAgency ??= _agencies[0];
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentAgency = _userAgency ?? _agencies[0];

    return Scaffold(
      appBar: AppBar(
        title: Text('${currentAgency['name']} Requirements'),
        backgroundColor: currentAgency['color'],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Agency info header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: currentAgency['color'],
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1 * 255),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(currentAgency['icon'], color: Colors.white, size: 32),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentAgency['name'],
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Your Agency Requirements',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8 * 255),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Content
          Expanded(child: _buildAgencyRequirements(currentAgency)),
        ],
      ),
    );
  }

  Widget _buildAgencyRequirements(Map<String, dynamic> agency) {
    final requirements = _getAgencyRequirements(agency['name']);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Agency header
          _buildAgencyHeader(agency),
          const SizedBox(height: 24),

          // Quick tips card
          _buildQuickTipsCard(agency),
          const SizedBox(height: 24),

          // Preparation checklist card
          _buildPreparationChecklistCard(agency),
          const SizedBox(height: 24),

          // Application portal and important dates cards
          _buildApplicationPortalCard(agency),
          const SizedBox(height: 24),

          _buildImportantDatesCard(agency),
          const SizedBox(height: 24),

          // Requirements sections
          ...requirements.map(
            (section) => _buildRequirementSection(
              section['title'],
              section['items'],
              section['icon'],
              agency['color'],
            ),
          ),

          // Application timeline card
          const SizedBox(height: 24),
          _buildApplicationTimelineCard(agency),
        ],
      ),
    );
  }

  Widget _buildAgencyHeader(Map<String, dynamic> agency) {
    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              agency['color'].withValues(alpha: 0.1 * 255),
              agency['color'].withValues(alpha: 0.05 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: agency['color'].withValues(alpha: 0.2 * 255),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: agency['color'],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(agency['icon'], color: Colors.white, size: 32),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveText(
                        agency['name'],
                        mobileFontSize: 20.0,
                        tabletFontSize: 24.0,
                        desktopFontSize: 28.0,
                        fontWeight: FontWeight.bold,
                        color: agency['color'],
                      ),
                      const SizedBox(height: 4),
                      ResponsiveText(
                        _getAgencyDescription(agency['name']),
                        mobileFontSize: 14.0,
                        tabletFontSize: 16.0,
                        desktopFontSize: 18.0,
                        color: Colors.grey[600],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: agency['color'].withValues(alpha: 0.05 * 255),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: agency['color'].withValues(alpha: 0.2 * 255),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: agency['color'],
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Your Path to Success',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: agency['color'],
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getAgencyMotivationalMessage(agency['name']),
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementSection(
    String title,
    List<Map<String, dynamic>> items,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: ResponsiveCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1 * 255),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color, size: 24),
                  const SizedBox(width: 12),
                  ResponsiveText(
                    title,
                    mobileFontSize: 18.0,
                    tabletFontSize: 20.0,
                    desktopFontSize: 22.0,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ],
              ),
            ),

            // Section content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children:
                    items
                        .map((item) => _buildRequirementItem(item, color))
                        .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementItem(Map<String, dynamic> item, Color color) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 4),
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2 * 255),
              shape: BoxShape.circle,
            ),
            child: Icon(
              item['icon'] ?? Icons.check_circle,
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  item['title'],
                  mobileFontSize: 16.0,
                  tabletFontSize: 17.0,
                  desktopFontSize: 18.0,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                if (item['description'] != null) ...[
                  const SizedBox(height: 4),
                  ResponsiveText(
                    item['description'],
                    mobileFontSize: 14.0,
                    tabletFontSize: 15.0,
                    desktopFontSize: 16.0,
                    color: Colors.grey[600],
                    style: const TextStyle(height: 1.4),
                  ),
                ],
                if (item['details'] != null) ...[
                  const SizedBox(height: 8),
                  ...item['details'].map<Widget>(
                    (detail) => Padding(
                      padding: const EdgeInsets.only(left: 16, bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(top: 6),
                            width: 4,
                            height: 4,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ResponsiveText(
                              detail,
                              mobileFontSize: 13.0,
                              tabletFontSize: 14.0,
                              desktopFontSize: 15.0,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getAgencyRequirements(String agencyName) {
    switch (agencyName) {
      case 'Nigerian Army':
        return _getNigerianArmyRequirements();
      case 'Nigerian Navy':
        return _getNigerianNavyRequirements();
      case 'Nigerian Air Force':
        return _getNigerianAirForceRequirements();
      case 'Nigeria Police Force':
        return _getNigerianPoliceRequirements();
      case 'Nigerian Defence Academy':
        return _getNDARequirements();
      case 'Defence Space Systems Command':
        return _getDSSCRequirements();
      case 'Nigeria Security and Civil Defence Corps':
        return _getNSCDCRequirements();
      case 'Nigeria Immigration Service':
        return _getNISRequirements();
      case 'Nigeria Customs Service':
        return _getNCSRequirements();
      case 'Federal Fire Service':
        return _getFederalFireServiceRequirements();
      default:
        return [];
    }
  }

  List<Map<String, dynamic>> _getNigerianArmyRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-22 years for Non-Tradesmen/Women, 18-26 years for Tradesmen/Women',
            'icon': Icons.cake,
          },
          {
            'title': 'Gender',
            'description': 'Both male and female candidates are eligible',
            'icon': Icons.people,
          },
          {
            'title': 'Marital Status',
            'description': 'Single, unmarried candidates only',
            'icon': Icons.person_outline,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Non-Tradesmen/Women',
            'description':
                'Minimum of 5 credits in WAEC/NECO/NABTEB at not more than 2 sittings',
            'details': [
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Any 3 other subjects from Arts, Commercial or Science',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Tradesmen/Women',
            'description':
                'Minimum educational qualification depends on the trade',
            'details': [
              'Trade Test Grade II Certificate in relevant trade',
              'City & Guilds Certificate',
              'Relevant National Diploma (ND)',
              'WAEC/NECO with relevant subjects',
            ],
            'icon': Icons.build,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Minimum height specifications',
            'details': ['Male: 1.68m (5ft 6in)', 'Female: 1.65m (5ft 5in)'],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'Must pass comprehensive physical fitness tests',
            'details': [
              'Push-ups, Sit-ups, Running',
              'General body fitness assessment',
              'Endurance and agility tests',
            ],
            'icon': Icons.sports_gymnastics,
          },
          {
            'title': 'Medical Fitness',
            'description': 'Must be medically and mentally fit',
            'details': [
              'No visible scars, tattoos, or deformities',
              'Good eyesight (correctable to 6/6)',
              'No history of mental illness',
              'Drug-free certification',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Documentation',
        'icon': Icons.folder,
        'items': [
          {
            'title': 'Required Documents',
            'description': 'All original and photocopies required',
            'details': [
              'Birth Certificate or Declaration of Age',
              'First School Leaving Certificate',
              'WAEC/NECO/NABTEB Results',
              'Local Government Certificate of Origin',
              'Medical Certificate of Fitness',
              'Certificate of Good Conduct from Police',
              'Passport photographs (recent)',
            ],
            'icon': Icons.description,
          },
        ],
      },
      {
        'title': 'Selection Process',
        'icon': Icons.assessment,
        'items': [
          {
            'title': 'Screening Stages',
            'description': 'Multi-stage selection process',
            'details': [
              'Online registration and application',
              'Document verification',
              'Written examination (Computer Based Test)',
              'Physical fitness test',
              'Medical examination',
              'Oral interview',
              'Final selection and documentation',
            ],
            'icon': Icons.checklist,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNigerianNavyRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-22 years for Non-Tradesmen/Women, 18-26 years for Tradesmen/Women',
            'icon': Icons.cake,
          },
          {
            'title': 'Swimming Ability',
            'description':
                'Must be able to swim (fundamental requirement for Navy)',
            'icon': Icons.pool,
          },
          {
            'title': 'Marital Status',
            'description': 'Single, unmarried candidates only',
            'icon': Icons.person_outline,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Non-Tradesmen/Women',
            'description': 'Minimum of 5 credits in WAEC/NECO/NABTEB',
            'details': [
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Physics (highly recommended)',
              'Any 2 other relevant subjects',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Tradesmen/Women',
            'description': 'Trade-specific educational requirements',
            'details': [
              'Relevant trade certificates',
              'Technical education qualifications',
              'Marine-related certifications (preferred)',
            ],
            'icon': Icons.build,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Minimum height specifications',
            'details': ['Male: 1.68m (5ft 6in)', 'Female: 1.65m (5ft 5in)'],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'Maritime-specific fitness requirements',
            'details': [
              'Swimming proficiency test',
              'Sea sickness resistance',
              'General physical fitness',
              'Endurance for maritime operations',
            ],
            'icon': Icons.sports_gymnastics,
          },
          {
            'title': 'Medical Fitness',
            'description': 'Maritime service medical standards',
            'details': [
              'No seasickness susceptibility',
              'Good balance and coordination',
              'Excellent eyesight for navigation',
              'No fear of water or heights',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Special Requirements',
        'icon': Icons.anchor,
        'items': [
          {
            'title': 'Maritime Aptitude',
            'description': 'Special considerations for naval service',
            'details': [
              'Comfort with water and marine environment',
              'Ability to work in confined spaces (ships)',
              'Team work and discipline',
              'Navigation and technical aptitude',
            ],
            'icon': Icons.sailing,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNigerianAirForceRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-22 years for Airmen/Airwomen, 18-26 years for Specialists',
            'icon': Icons.cake,
          },
          {
            'title': 'Vision Requirements',
            'description': 'Excellent vision required for aviation duties',
            'icon': Icons.visibility,
          },
          {
            'title': 'Marital Status',
            'description': 'Single, unmarried candidates only',
            'icon': Icons.person_outline,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Airmen/Airwomen',
            'description': 'Strong emphasis on Mathematics and Sciences',
            'details': [
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Physics (compulsory)',
              'Chemistry (recommended)',
              'Any other relevant subject',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Specialist Roles',
            'description': 'Technical and aviation-related qualifications',
            'details': [
              'Aviation-related certificates',
              'Engineering background (preferred)',
              'Technical education qualifications',
              'Computer literacy',
            ],
            'icon': Icons.engineering,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Aviation-specific height standards',
            'details': [
              'Male: 1.68m (5ft 6in)',
              'Female: 1.65m (5ft 5in)',
              'Pilots may have additional requirements',
            ],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'Aviation-specific fitness standards',
            'details': [
              'Excellent physical coordination',
              'No fear of heights',
              'Good balance and spatial awareness',
              'Cardiovascular fitness',
            ],
            'icon': Icons.sports_gymnastics,
          },
          {
            'title': 'Medical Fitness',
            'description': 'Stringent medical standards for aviation',
            'details': [
              'Perfect or correctable vision (6/6)',
              'No color blindness',
              'Excellent hearing',
              'No motion sickness',
              'Psychological fitness for aviation',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Special Requirements',
        'icon': Icons.flight,
        'items': [
          {
            'title': 'Aviation Aptitude',
            'description': 'Special requirements for air force service',
            'details': [
              'Mechanical aptitude',
              'Technical problem-solving skills',
              'Comfort with technology',
              'Leadership potential',
              'Discipline and precision',
            ],
            'icon': Icons.precision_manufacturing,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNigerianPoliceRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-25 years for Constable rank, 18-30 years for Specialist positions',
            'icon': Icons.cake,
          },
          {
            'title': 'Character',
            'description': 'Must possess good moral character',
            'icon': Icons.psychology,
          },
          {
            'title': 'Criminal Record',
            'description': 'No criminal convictions or pending cases',
            'icon': Icons.gavel,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Constable Rank',
            'description': 'Minimum educational qualification',
            'details': [
              'WAEC/NECO/NABTEB with 4 credits',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Any 2 other relevant subjects',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Specialist Positions',
            'description': 'Higher qualifications for specialized roles',
            'details': [
              'Degree/Diploma in relevant field',
              'Legal background (for legal officers)',
              'Forensic science qualifications',
              'IT/Computer science background',
            ],
            'icon': Icons.psychology_alt,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Minimum height for police service',
            'details': ['Male: 1.67m (5ft 6in)', 'Female: 1.64m (5ft 4in)'],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'Law enforcement fitness standards',
            'details': [
              'Physical agility tests',
              'Endurance and strength',
              'Self-defense capabilities',
              'Emergency response fitness',
            ],
            'icon': Icons.sports_martial_arts,
          },
          {
            'title': 'Medical Fitness',
            'description': 'Health requirements for police duty',
            'details': [
              'Mental and emotional stability',
              'Good eyesight and hearing',
              'No substance abuse history',
              'Ability to handle stress',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Additional Requirements',
        'icon': Icons.security,
        'items': [
          {
            'title': 'Security Clearance',
            'description': 'Background investigation and clearance',
            'details': [
              'Comprehensive background check',
              'Reference verification',
              'Community standing assessment',
              'Family background investigation',
            ],
            'icon': Icons.verified_user,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNDARequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 17-21 years for Regular Course, 17-22 years for Short Service Course',
            'icon': Icons.cake,
          },
          {
            'title': 'Marital Status',
            'description': 'Single, unmarried candidates only',
            'icon': Icons.person_outline,
          },
          {
            'title': 'Leadership Potential',
            'description': 'Must demonstrate leadership qualities',
            'icon': Icons.emoji_events,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'UTME Requirement',
            'description': 'Must pass JAMB UTME with high scores',
            'details': [
              'Minimum JAMB score of 250',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Physics (compulsory)',
              'Chemistry (compulsory)',
            ],
            'icon': Icons.quiz,
          },
          {
            'title': 'O\'Level Requirements',
            'description': 'Excellent academic performance required',
            'details': [
              '5 credits in WAEC/NECO at one sitting',
              'English Language (Grade C6 or better)',
              'Mathematics (Grade C6 or better)',
              'Physics (Grade C6 or better)',
              'Chemistry (Grade C6 or better)',
              'One other science subject',
            ],
            'icon': Icons.grade,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Officer standard height requirements',
            'details': ['Male: 1.70m (5ft 7in)', 'Female: 1.65m (5ft 5in)'],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'High physical fitness standards',
            'details': [
              'Excellent physical condition',
              'Military-style fitness tests',
              'Endurance and strength',
              'Agility and coordination',
            ],
            'icon': Icons.sports_gymnastics,
          },
          {
            'title': 'Medical Standards',
            'description': 'Stringent medical requirements',
            'details': [
              'Perfect medical fitness',
              'Excellent vision (6/6 correctable)',
              'No physical deformities',
              'Psychological fitness assessment',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Selection Process',
        'icon': Icons.assessment,
        'items': [
          {
            'title': 'Competitive Examination',
            'description': 'Multi-stage rigorous selection',
            'details': [
              'JAMB UTME (minimum 250)',
              'NDA Entrance Examination',
              'Interview and psychological test',
              'Medical examination',
              'Physical fitness assessment',
              'Final Board Interview',
            ],
            'icon': Icons.school,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getDSSCRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-26 years depending on rank and specialization',
            'icon': Icons.cake,
          },
          {
            'title': 'Security Clearance',
            'description': 'High-level security clearance required',
            'icon': Icons.security,
          },
          {
            'title': 'Technical Aptitude',
            'description': 'Strong technical and analytical skills',
            'icon': Icons.psychology,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Technical Positions',
            'description': 'Strong STEM background required',
            'details': [
              'Degree in Engineering, Physics, Mathematics',
              'Computer Science or IT background',
              'Space science qualifications (preferred)',
              'Advanced mathematics and physics',
            ],
            'icon': Icons.computer,
          },
          {
            'title': 'Minimum O\'Level',
            'description': 'Excellent academic performance',
            'details': [
              'English Language (Grade C6 or better)',
              'Mathematics (Grade B3 or better)',
              'Physics (Grade B3 or better)',
              'Chemistry (recommended)',
              'Further Mathematics (advantage)',
            ],
            'icon': Icons.grade,
          },
        ],
      },
      {
        'title': 'Technical Skills',
        'icon': Icons.build_circle,
        'items': [
          {
            'title': 'Required Skills',
            'description': 'Specialized technical competencies',
            'details': [
              'Programming and software development',
              'Satellite and space technology',
              'Telecommunications expertise',
              'Data analysis and research',
              'Cybersecurity knowledge',
            ],
            'icon': Icons.satellite,
          },
        ],
      },
      {
        'title': 'Physical & Security',
        'icon': Icons.verified_user,
        'items': [
          {
            'title': 'Security Standards',
            'description': 'Highest security clearance requirements',
            'details': [
              'Comprehensive background investigation',
              'Family and associate verification',
              'Financial background check',
              'Psychological evaluation',
              'Polygraph test (if required)',
            ],
            'icon': Icons.shield,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNSCDCRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description': 'Between 18-25 years for entry level positions',
            'icon': Icons.cake,
          },
          {
            'title': 'Character Assessment',
            'description': 'Good moral standing in the community',
            'icon': Icons.psychology,
          },
          {
            'title': 'Commitment',
            'description': 'Dedication to civil protection and security',
            'icon': Icons.volunteer_activism,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Basic Entry',
            'description': 'Minimum educational qualifications',
            'details': [
              'WAEC/NECO/NABTEB with 4 credits',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Any 2 other relevant subjects',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Specialist Roles',
            'description': 'Higher qualifications for specialized positions',
            'details': [
              'Fire service training certificates',
              'Emergency management qualifications',
              'Security and safety certifications',
              'First aid and medical training',
            ],
            'icon': Icons.emergency,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Height Requirement',
            'description': 'Standard height requirements',
            'details': ['Male: 1.67m (5ft 6in)', 'Female: 1.64m (5ft 4in)'],
            'icon': Icons.height,
          },
          {
            'title': 'Physical Fitness',
            'description': 'Civil defence fitness standards',
            'details': [
              'Emergency response fitness',
              'Rescue operation capabilities',
              'Endurance for field work',
              'Basic combat readiness',
            ],
            'icon': Icons.sports_gymnastics,
          },
        ],
      },
      {
        'title': 'Special Training',
        'icon': Icons.school_outlined,
        'items': [
          {
            'title': 'Training Areas',
            'description': 'Specialized civil defence training',
            'details': [
              'Disaster management and response',
              'Fire fighting and prevention',
              'Search and rescue operations',
              'Community policing',
              'Emergency medical response',
            ],
            'icon': Icons.emergency_share,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNISRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description': 'Between 18-25 years for entry level positions',
            'icon': Icons.cake,
          },
          {
            'title': 'Language Skills',
            'description':
                'Good communication skills in English and local languages',
            'icon': Icons.translate,
          },
          {
            'title': 'Travel Requirements',
            'description': 'Willingness to work at various border points',
            'icon': Icons.flight_takeoff,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Immigration Assistant',
            'description': 'Basic educational requirements',
            'details': [
              'WAEC/NECO/NABTEB with 5 credits',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Government or Civic Education',
              'Any 2 other subjects',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Immigration Inspector',
            'description': 'Higher educational qualifications',
            'details': [
              'University degree or HND',
              'Law, Public Administration, or related field',
              'International relations background',
              'Computer literacy',
            ],
            'icon': Icons.gavel,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Physical Standards',
            'description': 'Immigration service physical requirements',
            'details': [
              'Good physical fitness',
              'Ability to work long hours',
              'Mental alertness and attention to detail',
              'Professional appearance',
            ],
            'icon': Icons.visibility,
          },
        ],
      },
      {
        'title': 'Special Skills',
        'icon': Icons.language,
        'items': [
          {
            'title': 'Required Competencies',
            'description': 'Immigration-specific skills',
            'details': [
              'Document examination skills',
              'Interview and interrogation techniques',
              'Knowledge of immigration laws',
              'Computer and database operations',
              'Customer service orientation',
            ],
            'icon': Icons.assignment,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getNCSRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description':
                'Between 18-25 years for Customs Assistant, 18-30 years for higher positions',
            'icon': Icons.cake,
          },
          {
            'title': 'Integrity',
            'description': 'High moral standards and integrity',
            'icon': Icons.verified,
          },
          {
            'title': 'Analytical Skills',
            'description': 'Strong analytical and investigative abilities',
            'icon': Icons.analytics,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Customs Assistant',
            'description': 'Basic entry level requirements',
            'details': [
              'WAEC/NECO/NABTEB with 5 credits',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Economics (recommended)',
              'Government or Commerce',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Customs Inspector',
            'description': 'Higher qualification requirements',
            'details': [
              'University degree or HND',
              'Economics, Business, or related field',
              'Accounting or Finance background',
              'International trade knowledge',
            ],
            'icon': Icons.business,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Physical Fitness',
            'description': 'Customs service physical standards',
            'details': [
              'Good physical condition for field work',
              'Ability to work in various weather conditions',
              'Stamina for long inspections',
              'Professional bearing and appearance',
            ],
            'icon': Icons.sports_gymnastics,
          },
        ],
      },
      {
        'title': 'Professional Skills',
        'icon': Icons.work,
        'items': [
          {
            'title': 'Required Competencies',
            'description': 'Customs-specific professional skills',
            'details': [
              'Understanding of trade and tariff laws',
              'Cargo inspection and examination',
              'Revenue collection procedures',
              'Anti-smuggling operations',
              'Computer operations and data entry',
            ],
            'icon': Icons.inventory,
          },
        ],
      },
    ];
  }

  List<Map<String, dynamic>> _getFederalFireServiceRequirements() {
    return [
      {
        'title': 'General Eligibility',
        'icon': Icons.person,
        'items': [
          {
            'title': 'Nationality',
            'description': 'Must be a Nigerian citizen by birth',
            'icon': Icons.flag,
          },
          {
            'title': 'Age Requirement',
            'description': 'Between 18-25 years for entry level positions',
            'icon': Icons.cake,
          },
          {
            'title': 'Courage',
            'description': 'Demonstrated courage and bravery',
            'icon': Icons.psychology,
          },
          {
            'title': 'Public Service',
            'description': 'Commitment to saving lives and property',
            'icon': Icons.volunteer_activism,
          },
        ],
      },
      {
        'title': 'Educational Requirements',
        'icon': Icons.school,
        'items': [
          {
            'title': 'Fire Fighter',
            'description': 'Basic educational requirements',
            'details': [
              'WAEC/NECO/NABTEB with 4 credits',
              'English Language (compulsory)',
              'Mathematics (compulsory)',
              'Science subjects (Physics/Chemistry)',
              'Basic technical knowledge',
            ],
            'icon': Icons.book,
          },
          {
            'title': 'Fire Prevention Officer',
            'description': 'Higher educational qualifications',
            'details': [
              'University degree or HND',
              'Engineering or Science background',
              'Fire safety certifications',
              'Emergency management training',
            ],
            'icon': Icons.engineering,
          },
        ],
      },
      {
        'title': 'Physical Requirements',
        'icon': Icons.fitness_center,
        'items': [
          {
            'title': 'Physical Fitness',
            'description': 'Demanding physical requirements',
            'details': [
              'Excellent cardiovascular fitness',
              'Upper body and core strength',
              'Agility and coordination',
              'Ability to work in extreme conditions',
              'Stamina for emergency operations',
            ],
            'icon': Icons.local_fire_department,
          },
          {
            'title': 'Medical Fitness',
            'description': 'Specific medical standards',
            'details': [
              'No respiratory problems',
              'Good vision and hearing',
              'No fear of heights or confined spaces',
              'Psychological fitness for emergency work',
            ],
            'icon': Icons.medical_services,
          },
        ],
      },
      {
        'title': 'Specialized Training',
        'icon': Icons.school_outlined,
        'items': [
          {
            'title': 'Fire Service Training',
            'description': 'Comprehensive fire service education',
            'details': [
              'Fire suppression techniques',
              'Rescue operations and procedures',
              'Hazardous materials handling',
              'Emergency medical response',
              'Fire prevention and safety education',
              'Equipment operation and maintenance',
            ],
            'icon': Icons.fire_truck,
          },
        ],
      },
    ];
  }

  /// Get agency-specific description
  String _getAgencyDescription(String agencyName) {
    switch (agencyName) {
      case 'Nigerian Army':
        return 'Defend the territorial integrity of Nigeria with honor';
      case 'Nigerian Navy':
        return 'Guardians of Nigeria\'s maritime domain and waterways';
      case 'Nigerian Air Force':
        return 'Air power excellence for Nigeria\'s security and defense';
      case 'Nigeria Police Force':
        return 'To serve and protect with integrity and professionalism';
      case 'Nigerian Defence Academy':
        return 'Training future military leaders and officers';
      case 'Defence Space Systems Command':
        return 'Advanced space technology for national defense';
      case 'Nigeria Security and Civil Defence Corps':
        return 'Protection of critical infrastructure and civil defense';
      case 'Nigeria Immigration Service':
        return 'Securing Nigeria\'s borders and managing migration';
      case 'Nigeria Customs Service':
        return 'Facilitating trade while securing national revenue';
      case 'Federal Fire Service':
        return 'Life safety, property protection, and emergency response';
      default:
        return 'Serving Nigeria with dedication and excellence';
    }
  }

  /// Get agency-specific motivational message
  String _getAgencyMotivationalMessage(String agencyName) {
    switch (agencyName) {
      case 'Nigerian Army':
        return 'Join an elite force dedicated to protecting Nigeria\'s sovereignty. Every requirement you see here is a step toward becoming a defender of our nation. With determination and preparation, you can achieve your dream of serving in the Nigerian Army.';
      case 'Nigerian Navy':
        return 'Navigate your way to a prestigious career defending Nigeria\'s maritime interests. The requirements below are your compass to joining the brave men and women who protect our territorial waters. Your journey to naval excellence starts here.';
      case 'Nigerian Air Force':
        return 'Soar to new heights with the Nigerian Air Force. These requirements are your runway to an aviation career that protects our airspace and serves our nation from above. Excellence in the skies awaits those who meet these standards.';
      case 'Nigeria Police Force':
        return 'Become a guardian of justice and order in your community. These requirements guide you toward a noble career in law enforcement where you\'ll make a real difference in people\'s lives while serving Nigeria with integrity.';
      case 'Nigerian Defence Academy':
        return 'Shape your future as a military leader at Nigeria\'s premier defense institution. These requirements are your pathway to officer training and a distinguished career leading Nigeria\'s armed forces.';
      case 'Defence Space Systems Command':
        return 'Pioneer the future of Nigeria\'s defense technology. These rigorous requirements prepare you for a cutting-edge career in space systems and advanced defense technologies.';
      case 'Nigeria Security and Civil Defence Corps':
        return 'Stand as a protector of Nigeria\'s critical infrastructure and civilian population. These requirements prepare you for a vital role in national security and emergency response.';
      case 'Nigeria Immigration Service':
        return 'Guard Nigeria\'s gateways and manage our borders with professionalism. These requirements prepare you for a career securing our nation while facilitating legitimate travel and trade.';
      case 'Nigeria Customs Service':
        return 'Play a crucial role in Nigeria\'s economic security and trade facilitation. These requirements guide you toward a career protecting national revenue while enabling legitimate commerce.';
      case 'Federal Fire Service':
        return 'Answer the call to save lives and protect property across Nigeria. These requirements prepare you for a heroic career where every day offers the opportunity to make a life-saving difference.';
      default:
        return 'Prepare yourself for a rewarding career serving Nigeria. These requirements are your roadmap to success in your chosen field of service.';
    }
  }

  /// Build quick tips card for agency-specific guidance
  Widget _buildQuickTipsCard(Map<String, dynamic> agency) {
    final tips = _getAgencyTips(agency['name']);

    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.amber.withValues(alpha: 0.05 * 255),
              Colors.orange.withValues(alpha: 0.02 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.amber.withValues(alpha: 0.3 * 255)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.amber,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.lightbulb,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Quick Tips for Success',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.amber,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...tips.map(
              (tip) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        tip,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 13,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build application timeline card
  Widget _buildApplicationTimelineCard(Map<String, dynamic> agency) {
    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              agency['color'].withValues(alpha: 0.05 * 255),
              agency['color'].withValues(alpha: 0.02 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: agency['color'].withValues(alpha: 0.2 * 255),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: agency['color'],
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.timeline,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Application Process',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: agency['color'],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Follow these steps to complete your application:',
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 12),
            _buildTimelineStep(
              '1',
              'Prepare Documents',
              'Gather all required certificates and documents',
              true,
            ),
            _buildTimelineStep(
              '2',
              'Online Registration',
              'Complete application form on official portal',
              false,
            ),
            _buildTimelineStep(
              '3',
              'Submit Application',
              'Upload documents and pay application fee',
              false,
            ),
            _buildTimelineStep(
              '4',
              'Screening Test',
              'Attend written examination and interviews',
              false,
            ),
            _buildTimelineStep(
              '5',
              'Medical Examination',
              'Complete medical fitness assessment',
              false,
            ),
            _buildTimelineStep(
              '6',
              'Final Selection',
              'Await results and reporting instructions',
              false,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineStep(
    String step,
    String title,
    String description,
    bool isFirst,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color: isFirst ? Colors.green : Colors.grey[300],
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    step,
                    style: TextStyle(
                      color: isFirst ? Colors.white : Colors.grey[600],
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              if (step != '6')
                Container(width: 2, height: 20, color: Colors.grey[300]),
            ],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Get agency-specific tips
  List<String> _getAgencyTips(String agencyName) {
    switch (agencyName) {
      case 'Nigerian Army':
        return [
          'Start physical training at least 3 months before screening',
          'Practice basic computer skills for CBT examination',
          'Maintain good academic records and character references',
          'Prepare for both English and Mathematics aptitude tests',
        ];
      case 'Nigerian Navy':
        return [
          'Learn to swim if you don\'t know how - it\'s mandatory',
          'Study maritime and naval terminology',
          'Build physical endurance for sea duties',
          'Understand basic navigation principles',
        ];
      case 'Nigerian Air Force':
        return [
          'Develop strong mathematics and physics foundation',
          'Maintain excellent eyesight - avoid eye strain',
          'Study aviation and aerospace basics',
          'Practice spatial reasoning and mechanical aptitude',
        ];
      case 'Nigeria Police Force':
        return [
          'Study Nigerian constitution and criminal law basics',
          'Practice crowd control and conflict resolution scenarios',
          'Maintain physical fitness for law enforcement duties',
          'Develop strong communication and interpersonal skills',
        ];
      case 'Nigerian Defence Academy':
        return [
          'Excel in JAMB UTME - aim for 250+ score',
          'Develop leadership qualities through extracurricular activities',
          'Maintain excellent academic performance',
          'Prepare for rigorous physical and mental challenges',
        ];
      case 'Defence Space Systems Command':
        return [
          'Focus on STEM subjects with excellent grades',
          'Learn programming and computer science fundamentals',
          'Study space technology and satellite communications',
          'Maintain highest security clearance standards',
        ];
      case 'Nigeria Security and Civil Defence Corps':
        return [
          'Study disaster management and emergency response',
          'Develop community relations and conflict resolution skills',
          'Maintain physical fitness for field operations',
          'Understand civil protection and infrastructure security',
        ];
      case 'Nigeria Immigration Service':
        return [
          'Learn basic international relations and immigration law',
          'Develop document examination and verification skills',
          'Practice interview and communication techniques',
          'Study Nigerian immigration policies and procedures',
        ];
      case 'Nigeria Customs Service':
        return [
          'Study international trade and customs regulations',
          'Learn cargo inspection and examination procedures',
          'Develop analytical and investigative skills',
          'Understand revenue collection and anti-smuggling operations',
        ];
      case 'Federal Fire Service':
        return [
          'Build exceptional physical strength and endurance',
          'Study fire science and emergency response procedures',
          'Practice working in high-stress emergency situations',
          'Learn basic medical and first aid skills',
        ];
      default:
        return [
          'Research the agency thoroughly before applying',
          'Prepare all required documents well in advance',
          'Maintain good physical and mental health',
        ];
    }
  }

  /// Build preparation checklist card
  Widget _buildPreparationChecklistCard(Map<String, dynamic> agency) {
    final checklist = _getAgencyChecklist(agency['name']);
    final totalItems = checklist.length;
    final completedItems =
        checklist
            .where((item) => _completedChecklist.contains(item['id']))
            .length;
    final progressPercentage =
        totalItems > 0 ? (completedItems / totalItems) : 0.0;

    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.green.withValues(alpha: 0.05 * 255),
              Colors.teal.withValues(alpha: 0.02 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.green.withValues(alpha: 0.3 * 255)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.checklist,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Preparation Checklist',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.green,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1 * 255),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${(progressPercentage * 100).toInt()}% Complete',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                      color: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Progress bar
            Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progressPercentage,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            ...checklist.map((item) => _buildChecklistItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistItem(Map<String, dynamic> item) {
    final isCompleted = _completedChecklist.contains(item['id']);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                if (isCompleted) {
                  _completedChecklist.remove(item['id']);
                } else {
                  _completedChecklist.add(item['id']);
                }
              });
              _saveChecklistProgress();
            },
            child: Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted ? Colors.green : Colors.transparent,
                border: Border.all(
                  color: isCompleted ? Colors.green : Colors.grey[400]!,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(6),
              ),
              child:
                  isCompleted
                      ? const Icon(Icons.check, color: Colors.white, size: 16)
                      : null,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              item['title'],
              style: TextStyle(
                fontSize: 14,
                color: isCompleted ? Colors.grey[600] : Colors.black87,
                decoration: isCompleted ? TextDecoration.lineThrough : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build application portal card
  Widget _buildApplicationPortalCard(Map<String, dynamic> agency) {
    final portalInfo = _getAgencyPortalInfo(agency['name']);

    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.blue.withValues(alpha: 0.05 * 255),
              Colors.indigo.withValues(alpha: 0.02 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.blue.withValues(alpha: 0.3 * 255)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.language,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Official Application Portal',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Apply directly through the official recruitment portal:',
              style: TextStyle(color: Colors.grey[700], fontSize: 14),
            ),
            const SizedBox(height: 12),
            GestureDetector(
              onTap: () => _launchURL(portalInfo['url']),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1 * 255),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3 * 255),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.open_in_new, color: Colors.blue, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            portalInfo['name'],
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            portalInfo['url'],
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue, size: 16),
                const SizedBox(width: 8),
                Text(
                  portalInfo['note'],
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build important dates card
  Widget _buildImportantDatesCard(Map<String, dynamic> agency) {
    final dates = _getAgencyImportantDates(agency['name']);

    return ResponsiveCard(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.purple.withValues(alpha: 0.05 * 255),
              Colors.deepPurple.withValues(alpha: 0.02 * 255),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.purple.withValues(alpha: 0.3 * 255)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                    color: Colors.purple,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.schedule,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Important Dates & Deadlines',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: Colors.purple,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...dates.map(
              (date) => Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.05 * 255),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.purple.withValues(alpha: 0.2 * 255),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(date['icon'], color: Colors.purple, size: 20),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            date['event'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            date['date'],
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.orange.withValues(alpha: 0.3 * 255),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.warning_amber,
                    color: Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Dates are subject to change. Always check the official portal for updates.',
                      style: TextStyle(
                        color: Colors.grey[700],
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Launch URL helper function
  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      // Show error message if URL can't be launched
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Get agency-specific checklist items
  List<Map<String, dynamic>> _getAgencyChecklist(String agencyName) {
    final baseChecklist = [
      {'id': 'documents', 'title': 'Gather all required documents'},
      {'id': 'medical', 'title': 'Complete medical check-up'},
      {'id': 'fitness', 'title': 'Start physical fitness training'},
      {'id': 'portal', 'title': 'Create account on recruitment portal'},
    ];

    switch (agencyName) {
      case 'Nigerian Army':
        return [
          ...baseChecklist,
          {'id': 'combat_training', 'title': 'Practice basic military drills'},
          {'id': 'cbt_prep', 'title': 'Prepare for Computer Based Test'},
        ];
      case 'Nigerian Navy':
        return [
          ...baseChecklist,
          {'id': 'swimming', 'title': 'Learn/improve swimming skills'},
          {'id': 'maritime_study', 'title': 'Study maritime terminology'},
        ];
      case 'Nigerian Air Force':
        return [
          ...baseChecklist,
          {'id': 'aviation_study', 'title': 'Study aviation basics'},
          {
            'id': 'spatial_reasoning',
            'title': 'Practice spatial reasoning tests',
          },
        ];
      case 'Nigeria Police Force':
        return [
          ...baseChecklist,
          {'id': 'law_study', 'title': 'Study Nigerian constitution basics'},
          {
            'id': 'conflict_resolution',
            'title': 'Learn conflict resolution skills',
          },
        ];
      case 'Nigerian Defence Academy':
        return [
          ...baseChecklist,
          {'id': 'jamb_prep', 'title': 'Prepare for JAMB UTME (target 250+)'},
          {'id': 'leadership', 'title': 'Develop leadership experience'},
        ];
      default:
        return baseChecklist;
    }
  }

  /// Get agency portal information
  Map<String, dynamic> _getAgencyPortalInfo(String agencyName) {
    switch (agencyName) {
      case 'Nigerian Army':
        return {
          'name': 'Nigerian Army Recruitment Portal',
          'url': 'https://recruitment.army.mil.ng',
          'note': 'Check regularly for new recruitment announcements',
        };
      case 'Nigerian Navy':
        return {
          'name': 'Nigerian Navy Recruitment Portal',
          'url': 'https://joinnavy.ng',
          'note': 'Maritime recruitment updates posted here',
        };
      case 'Nigerian Air Force':
        return {
          'name': 'Nigerian Air Force Recruitment Portal',
          'url': 'https://airforce.mil.ng/recruitment',
          'note': 'Aviation career opportunities updated regularly',
        };
      case 'Nigeria Police Force':
        return {
          'name': 'Nigeria Police Recruitment Portal',
          'url': 'https://www.npfrecruitment.gov.ng',
          'note': 'Official police recruitment platform',
        };
      case 'Nigerian Defence Academy':
        return {
          'name': 'NDA Admission Portal',
          'url': 'https://nda.edu.ng/admissions',
          'note': 'Officer training admission portal',
        };
      case 'Defence Space Systems Command':
        return {
          'name': 'DSSC Recruitment Portal',
          'url': 'https://www.mod.gov.ng/recruitment',
          'note': 'Space systems and defense technology recruitment',
        };
      case 'Nigeria Security and Civil Defence Corps':
        return {
          'name': 'NSCDC Recruitment Portal',
          'url': 'https://nscdc.gov.ng/recruitment',
          'note': 'Civil defence and security recruitment updates',
        };
      case 'Nigeria Immigration Service':
        return {
          'name': 'NIS Recruitment Portal',
          'url': 'https://www.immigration.gov.ng/recruitment',
          'note': 'Border security and immigration recruitment',
        };
      case 'Nigeria Customs Service':
        return {
          'name': 'NCS Recruitment Portal',
          'url': 'https://customs.gov.ng/recruitment',
          'note': 'Customs and excise recruitment opportunities',
        };
      case 'Federal Fire Service':
        return {
          'name': 'Federal Fire Service Recruitment',
          'url': 'https://federalfireservice.gov.ng/recruitment',
          'note': 'Fire safety and emergency response recruitment',
        };
      default:
        return {
          'name': 'Official Recruitment Portal',
          'url': 'https://portal.gov.ng',
          'note': 'Check agency website for specific portal',
        };
    }
  }

  /// Get important dates for agency
  List<Map<String, dynamic>> _getAgencyImportantDates(String agencyName) {
    // Note: These are example dates - in a real app, these would come from an API
    return [
      {
        'event': 'Application Opens',
        'date': 'Usually January - March',
        'icon': Icons.event_available,
      },
      {
        'event': 'Application Deadline',
        'date': 'Usually March - April',
        'icon': Icons.event_busy,
      },
      {
        'event': 'Screening Exercise',
        'date': 'Usually May - July',
        'icon': Icons.assignment,
      },
      {
        'event': 'Final Selection',
        'date': 'Usually August - September',
        'icon': Icons.how_to_reg,
      },
      {
        'event': 'Training Commencement',
        'date': 'Usually October - November',
        'icon': Icons.school,
      },
    ];
  }
}
