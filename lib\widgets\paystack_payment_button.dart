import 'package:flutter/material.dart';
import '../../core/services/paystack_service.dart';
import '../../core/config/supabase_config.dart';

class PaystackPaymentButton extends StatefulWidget {
  final int amount; // in kobo
  final String email;

  const PaystackPaymentButton({
    super.key,
    required this.amount,
    required this.email,
  });

  @override
  State<PaystackPaymentButton> createState() => _PaystackPaymentButtonState();
}

class _PaystackPaymentButtonState extends State<PaystackPaymentButton> {
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    PaystackService.initialize();
  }

  Future<void> _handlePayment() async {
    setState(() => _loading = true);
    final response = await PaystackService.pay(
      context: context,
      amount: widget.amount,
      email: widget.email,
    );
    setState(() => _loading = false);

    if (response.status) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Payment successful! Reference: ${response.reference}'),
        ),
      );
      // Upgrade user to premium in Supabase
      final user = SupabaseConfig.currentUser;
      if (user != null) {
        await SupabaseConfig.client
            .from(SupabaseConfig.usersTable)
            .update({'is_premium': true})
            .eq('id', user.id);
      }
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Payment failed or cancelled')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: _loading ? null : _handlePayment,
      child: _loading ? CircularProgressIndicator() : Text('Pay with Paystack'),
    );
  }
}
