import '../models/agency_news_model.dart';
import '../../../core/services/premium_service.dart';
import '../../../shared/models/user_model.dart';

/// Premium news service for enhanced news features
class PremiumNewsService {
  static final PremiumService _premiumService = PremiumService();

  /// Check if user has premium access
  static bool hasPremiumAccess(UserModel user) {
    return _premiumService.hasAccessToPremiumFeatures(user);
  }

  /// Premium news features available
  static const Map<String, PremiumFeature> premiumFeatures = {
    'breaking_alerts': PremiumFeature(
      name: 'Instant Breaking News Alerts',
      description: 'Get push notifications for critical updates within minutes',
      icon: 'notifications_active',
      category: 'Notifications',
    ),
    'exclusive_content': PremiumFeature(
      name: 'Exclusive Content & Insider Tips',
      description:
          'Access to exclusive recruitment tips and insider information',
      icon: 'star',
      category: 'Content',
    ),
    'deadline_tracker': PremiumFeature(
      name: 'Smart Deadline Tracker',
      description: 'Advanced deadline management with calendar integration',
      icon: 'schedule',
      category: 'Organization',
    ),
    'interview_prep': PremiumFeature(
      name: 'Interview Preparation Materials',
      description: 'Access to past questions, success stories, and prep guides',
      icon: 'school',
      category: 'Preparation',
    ),
    'priority_support': PremiumFeature(
      name: 'Priority Customer Support',
      description: '24/7 priority support for news and application queries',
      icon: 'support_agent',
      category: 'Support',
    ),
    'analytics': PremiumFeature(
      name: 'Reading Analytics & Insights',
      description: 'Track your reading habits and get personalized insights',
      icon: 'analytics',
      category: 'Analytics',
    ),
    'offline_access': PremiumFeature(
      name: 'Offline News Access',
      description: 'Download news for offline reading during travels',
      icon: 'offline_pin',
      category: 'Accessibility',
    ),
    'early_access': PremiumFeature(
      name: 'Early Access to News',
      description: 'Get news updates 1-2 hours before free users',
      icon: 'fast_forward',
      category: 'Exclusive',
    ),
  };

  /// Get premium news with enhanced features
  static Future<List<AgencyNewsModel>> getPremiumNews(String agency) async {
    // This would fetch premium-exclusive news content
    return [
      AgencyNewsModel(
        id: 'premium_1',
        title: '🔥 EXCLUSIVE: Nigerian Army 84RRI Insider Tips',
        content:
            'Premium subscribers get exclusive access to success strategies from recent recruits and insider preparation tips...',
        agency: agency,
        category: 'Exclusive',
        source: 'Fit4Force Premium',
        publishedDate: DateTime.now().subtract(const Duration(hours: 1)),
        priority: 'high',
        tags: ['premium', 'exclusive', 'insider-tips'],
        isPremium: true,
        createdAt: DateTime.now(),
      ),
      AgencyNewsModel(
        id: 'premium_2',
        title: '⚡ EARLY ACCESS: Interview Dates Released',
        content:
            'Premium members get 2-hour early access to critical announcements. Interview schedule for batch 2025A has been released...',
        agency: agency,
        category: 'Interview',
        source: 'Official Source (Early Access)',
        publishedDate: DateTime.now().subtract(const Duration(minutes: 30)),
        priority: 'high',
        tags: ['premium', 'early-access', 'interview'],
        isPremium: true,
        isBreaking: true,
        createdAt: DateTime.now(),
      ),
    ];
  }

  /// Get premium analytics for user
  static Map<String, dynamic> getPremiumAnalytics(String userId) {
    return {
      'reading_streak': 15,
      'articles_read_this_week': 23,
      'time_saved_with_summaries': '2.5 hours',
      'deadline_alerts_sent': 8,
      'success_rate_improvement': '35%',
      'personalization_accuracy': '92%',
    };
  }

  /// Check if specific feature is available for user
  static bool hasFeatureAccess(UserModel user, String feature) {
    if (!hasPremiumAccess(user)) return false;
    return premiumFeatures.containsKey(feature);
  }
}

/// Model for premium features
class PremiumFeature {
  final String name;
  final String description;
  final String icon;
  final String category;

  const PremiumFeature({
    required this.name,
    required this.description,
    required this.icon,
    required this.category,
  });
}
