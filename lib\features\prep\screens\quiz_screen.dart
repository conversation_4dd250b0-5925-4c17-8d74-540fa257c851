import 'package:flutter/material.dart';
import 'dart:async';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/user_progress_service.dart';
import '../../../core/services/premium_service.dart';
import '../../../shared/models/user_model.dart';
import '../models/quiz_model.dart';
import '../models/quiz_question_model.dart';
import '../data/quiz_questions_database.dart';
import '../data/scenario_quiz_database.dart';
import '../services/ai_quiz_service.dart';
import '../services/question_bookmark_service.dart';
import '../services/reading_preferences_service.dart';
import '../services/offline_mode_service.dart';
import '../../subscription/widgets/premium_upgrade_dialog.dart';
import '../../../core/security/screenshot_protection_service.dart';
import '../../../core/security/download_prevention_service.dart';
import 'quiz_result_screen.dart';

class QuizScreen extends StatefulWidget {
  final QuizModel quiz;
  final UserModel user;
  final String? userAgency;

  const QuizScreen({
    super.key,
    required this.quiz,
    required this.user,
    this.userAgency,
  });

  @override
  State<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends State<QuizScreen>
    with ScreenshotProtectionMixin, DownloadPreventionMixin {
  List<QuizQuestionModel> _questions = [];
  int _currentQuestionIndex = 0;
  int? _selectedAnswerIndex;
  final List<QuizAnswerModel> _answers = [];
  Timer? _timer;
  int _timeRemaining = 60; // seconds
  bool _isAnswered = false;
  bool _showExplanation = false;

  // Progress tracking
  final UserProgressService _progressService = UserProgressService();

  // Premium features
  final AIQuizService _aiQuizService = AIQuizService();
  final QuestionBookmarkService _bookmarkService = QuestionBookmarkService();
  final ReadingPreferencesService _readingPrefs = ReadingPreferencesService();
  final OfflineModeService _offlineService = OfflineModeService();

  bool _isPremiumUser = false;
  int _questionLimit = 5;
  bool _isGeneratingQuestions = false;
  bool _isGeneratingExplanation = false;
  String? _aiExplanation;

  // Gesture navigation
  final PageController _pageController = PageController();

  // Auto-advance timer for reading preferences
  Timer? _autoAdvanceTimer;

  @override
  void initState() {
    super.initState();
    _initializeQuiz();
  }

  Future<void> _initializeQuiz() async {
    // Initialize all services
    await _bookmarkService.initialize();
    await _readingPrefs.initialize();
    await _offlineService.initialize();

    // Check premium status using centralized service
    final premiumService = PremiumService();
    _isPremiumUser = premiumService.hasAccessToPremiumFeatures(widget.user);
    _questionLimit = premiumService.getMaxQuizQuestions(widget.user);

    _loadQuestions();
    _startTimer();
    _setupAutoAdvance();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _autoAdvanceTimer?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  void _loadQuestions() {
    // Determine question count based on user type
    int questionCount;
    if (_isPremiumUser) {
      // Premium users get 60-70 questions
      questionCount = 65; // Middle of the range
    } else {
      // Free users get 10 questions
      questionCount = 10;
    }

    print('🎯 Loading questions for category: ${widget.quiz.category}');
    print('👤 User type: ${_isPremiumUser ? "Premium" : "Free"}');
    print('📊 Question count: $questionCount');

    // Load questions based on quiz category
    switch (widget.quiz.category.toLowerCase()) {
      case 'english':
      case 'english language':
        _questions = QuizQuestionsDatabase.getRandomQuestions(
          'English Language',
          questionCount,
        );
        break;
      case 'mathematics':
      case 'math':
      case 'basic mathematics':
        _questions = QuizQuestionsDatabase.getRandomQuestions(
          'Mathematics',
          questionCount,
        );
        break;
      case 'mixed':
      case 'general':
        if (_isPremiumUser) {
          _questions = QuizQuestionsDatabase.getMixedQuizQuestions(
            widget.userAgency,
            englishCount: 25,
            mathCount: 25,
            historyCount: 15,
          );
        } else {
          _questions = QuizQuestionsDatabase.getMixedQuizQuestions(
            widget.userAgency,
            englishCount: 4,
            mathCount: 4,
            historyCount: 2,
          );
        }
        break;
      case 'military history':
      case 'agency history':
      case 'nigerian military':
        _questions = QuizQuestionsDatabase.getRandomAgencyQuestions(
          widget.userAgency,
          questionCount,
        );
        if (_questions.isEmpty) {
          _questions = QuizQuestionsDatabase.getRandomQuestions(
            'Military History',
            questionCount,
          );
        }
        break;
      case 'scenario-based':
      case 'scenario':
        // Use premium access control for scenario questions
        _questions = ScenarioQuizDatabase.getScenarioQuestionsWithAccess(
          widget.userAgency ?? 'Nigerian Army',
          _isPremiumUser,
        );

        print('🎯 Scenario questions loaded:');
        print('   Premium User: $_isPremiumUser');
        print('   Questions Available: ${_questions.length}');
        print('   Agency: ${widget.userAgency}');

        if (_questions.isEmpty) {
          // Fallback to regular questions if no scenarios available
          _questions = QuizQuestionsDatabase.getMixedQuizQuestions(
            widget.userAgency,
            englishCount: _isPremiumUser ? 25 : 4,
            mathCount: _isPremiumUser ? 25 : 4,
            historyCount: _isPremiumUser ? 15 : 2,
          );
        }
        break;
      default:
        _questions = QuizQuestionsDatabase.getMixedQuizQuestions(
          widget.userAgency,
          englishCount: _isPremiumUser ? 25 : 4,
          mathCount: _isPremiumUser ? 25 : 4,
          historyCount: _isPremiumUser ? 15 : 2,
        );
    }

    // Fallback: If no questions found, try to get any available questions
    if (_questions.isEmpty) {
      print('⚠️ No questions found for category: ${widget.quiz.category}');
      _questions =
          QuizQuestionsDatabase.getAllQuestions().take(questionCount).toList();
    }

    // Final safety check
    if (_questions.isEmpty) {
      print('❌ No questions available at all!');
    } else {
      print(
        '✅ Loaded ${_questions.length} questions for ${widget.quiz.category}',
      );
    }
  }

  void _startTimer() {
    if (_questions.isNotEmpty) {
      _timeRemaining = _questions[_currentQuestionIndex].timeLimit;
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (_timeRemaining > 0 && !_isAnswered) {
          setState(() {
            _timeRemaining--;
          });
        } else if (_timeRemaining == 0 && !_isAnswered) {
          // Time's up, auto-submit with no answer
          _submitAnswer(-1);
        }
      });
    }
  }

  void _submitAnswer(int answerIndex) {
    if (_isAnswered) return;

    setState(() {
      _selectedAnswerIndex = answerIndex;
      _isAnswered = true;
      _showExplanation = true;
    });

    final question = _questions[_currentQuestionIndex];
    final isCorrect = answerIndex >= 0 && question.isCorrect(answerIndex);
    final timeSpent = question.timeLimit - _timeRemaining;

    final answer = QuizAnswerModel(
      questionId: question.id,
      selectedAnswerIndex: answerIndex,
      isCorrect: isCorrect,
      timeSpent: timeSpent,
      answeredAt: DateTime.now(),
    );

    _answers.add(answer);
    _timer?.cancel();

    // Auto-advance after showing explanation
    Timer(const Duration(seconds: 3), () {
      if (mounted) {
        _nextQuestion();
      }
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
        _selectedAnswerIndex = null;
        _isAnswered = false;
        _showExplanation = false;
        _aiExplanation = null; // Reset AI explanation for new question
      });
      _startTimer();
    } else {
      // Check if free user has reached limit and show upgrade prompt
      if (!_isPremiumUser && _questions.length >= _questionLimit) {
        _showQuizLimitReached();
      } else {
        _finishQuiz();
      }
    }
  }

  /// Show quiz limit reached dialog for free users
  void _showQuizLimitReached() {
    PremiumUpgradeDialog.showQuizLimitReached(context).then((upgraded) {
      if (upgraded == true) {
        // User upgraded, could reload with more questions
        _finishQuiz();
      } else {
        // User declined, finish quiz with current questions
        _finishQuiz();
      }
    });
  }

  void _finishQuiz() async {
    final correctAnswers = _answers.where((answer) => answer.isCorrect).length;
    final score =
        (_answers.isNotEmpty) ? (correctAnswers / _answers.length) * 100 : 0.0;
    final totalTimeSpent = _answers.fold<int>(
      0,
      (sum, answer) => sum + answer.timeSpent,
    );

    // Save quiz results to UserProgressService
    try {
      await _saveQuizProgress(correctAnswers, score, totalTimeSpent);
    } catch (e) {
      print('Error saving quiz progress: $e');
    }

    final session = QuizSessionModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      quizId: widget.quiz.id,
      userId: 'current_user', // Replace with actual user ID
      answers: _answers,
      startedAt: DateTime.now().subtract(Duration(seconds: totalTimeSpent)),
      completedAt: DateTime.now(),
      totalQuestions: _questions.length,
      correctAnswers: correctAnswers,
      score: score,
      totalTimeSpent: totalTimeSpent,
      isCompleted: true,
    );

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                QuizResultScreen(session: session, questions: _questions),
      ),
    );
  }

  Future<void> _saveQuizProgress(
    int correctAnswers,
    double score,
    int timeSpent,
  ) async {
    // Increment total quizzes completed
    await _progressService.incrementValue('academics', 'totalQuizzesCompleted');

    // Update total questions attempted and correct answers
    await _progressService.incrementValue(
      'academics',
      'totalQuestionsAttempted',
      increment: _questions.length,
    );
    await _progressService.incrementValue(
      'academics',
      'totalCorrectAnswers',
      increment: correctAnswers,
    );

    // Calculate and update average quiz score
    final currentProgress = await _progressService.loadUserProgress();
    final academicsData = currentProgress['academics'] ?? {};
    final totalQuizzes = (academicsData['totalQuizzesCompleted'] ?? 0) as int;
    final currentAverageScore =
        (academicsData['averageQuizScore'] ?? 0.0) as double;

    // Calculate new average score
    final newAverageScore =
        ((currentAverageScore * (totalQuizzes - 1)) + score) / totalQuizzes;

    // Update study time (convert seconds to minutes)
    final studyTimeMinutes = (timeSpent / 60).round();
    await _progressService.incrementValue(
      'academics',
      'totalStudyHours',
      increment: studyTimeMinutes,
    );

    // Update subject-specific scores
    final category = widget.quiz.category.toLowerCase();
    final subjectScores = Map<String, double>.from(
      academicsData['subjectScores'] ?? {},
    );
    final currentSubjectScore = subjectScores[category] ?? 0.0;
    final subjectQuizCount =
        ((academicsData['${category}QuizCount'] ?? 0) as int) + 1;
    subjectScores[category] =
        ((currentSubjectScore * (subjectQuizCount - 1)) + score) /
        subjectQuizCount;

    // Save quiz history
    final quizHistory = List<Map<String, dynamic>>.from(
      academicsData['quizHistory'] ?? [],
    );
    quizHistory.add({
      'quizId': widget.quiz.id,
      'category': category,
      'score': score,
      'correctAnswers': correctAnswers,
      'totalQuestions': _questions.length,
      'timeSpent': timeSpent,
      'completedAt': DateTime.now().toIso8601String(),
    });

    // Keep only last 50 quiz records
    if (quizHistory.length > 50) {
      quizHistory.removeRange(0, quizHistory.length - 50);
    }

    // Update streak
    final now = DateTime.now();
    final lastQuizDate =
        academicsData['lastQuizDate'] != null
            ? DateTime.parse(academicsData['lastQuizDate'])
            : null;

    bool isStreakActive = false;
    if (lastQuizDate != null) {
      final daysSinceLastQuiz = now.difference(lastQuizDate).inDays;
      isStreakActive = daysSinceLastQuiz <= 1; // Within 24 hours or same day
    }

    await _progressService.updateStreak(
      'academics',
      'currentStreak',
      isStreakActive,
    );

    // Update all academic progress
    await _progressService.updateProgress('academics', {
      'averageQuizScore': newAverageScore,
      'subjectScores': subjectScores,
      'quizHistory': quizHistory,
      'lastQuizDate': now.toIso8601String(),
      '${category}QuizCount': subjectQuizCount,
    });

    // Calculate and update readiness based on performance
    final readiness = _calculateReadiness(
      newAverageScore,
      totalQuizzes,
      subjectScores,
    );
    await _progressService.updateProgress('academics', {
      'readiness': readiness,
    });

    // Check for achievements
    await _checkQuizAchievements(totalQuizzes, score, newAverageScore);
  }

  double _calculateReadiness(
    double averageScore,
    int totalQuizzes,
    Map<String, double> subjectScores,
  ) {
    // Base readiness on average score (60% weight)
    double readiness = averageScore * 0.6;

    // Add bonus for quiz completion (20% weight)
    final quizCompletionBonus = (totalQuizzes / 50.0).clamp(0.0, 1.0) * 20;
    readiness += quizCompletionBonus;

    // Add bonus for balanced subject performance (20% weight)
    if (subjectScores.isNotEmpty) {
      final minSubjectScore = subjectScores.values.reduce(
        (a, b) => a < b ? a : b,
      );
      final balanceBonus = (minSubjectScore / 100.0) * 20;
      readiness += balanceBonus;
    }

    return readiness.clamp(0.0, 100.0);
  }

  Future<void> _checkQuizAchievements(
    int totalQuizzes,
    double score,
    double averageScore,
  ) async {
    // First quiz achievement
    if (totalQuizzes == 1) {
      await _progressService.addAchievement(
        'academics',
        'First Quiz Completed',
      );
    }

    // Quiz milestones
    if (totalQuizzes == 10) {
      await _progressService.addAchievement('academics', 'Quiz Enthusiast');
    } else if (totalQuizzes == 50) {
      await _progressService.addAchievement('academics', 'Quiz Master');
    } else if (totalQuizzes == 100) {
      await _progressService.addAchievement('academics', 'Quiz Legend');
    }

    // High score achievements
    if (score >= 90) {
      await _progressService.addAchievement(
        'academics',
        'Excellent Performance',
      );
    } else if (score == 100) {
      await _progressService.addAchievement('academics', 'Perfect Score');
    }

    // Average score achievements
    if (averageScore >= 80) {
      await _progressService.addAchievement(
        'academics',
        'Consistent Excellence',
      );
    } else if (averageScore >= 70) {
      await _progressService.addAchievement('academics', 'Above Average');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_questions.isEmpty) {
      return ProtectedScreen(
        enableProtection: true,
        screenName: 'QuizScreen',
        child: Scaffold(
        appBar: AppBar(
          title: Text(widget.quiz.title),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'No questions available for this quiz.',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    final progress = (_currentQuestionIndex + 1) / _questions.length;

    return ProtectedScreen(
      enableProtection: true,
      screenName: 'QuizScreen',
      child: protectContent(
        Scaffold(
      appBar: AppBar(
        title: Text(widget.quiz.title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          // Question counter with premium indicator
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${_currentQuestionIndex + 1}/${_questions.length}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (!_isPremiumUser) ...[
                    const SizedBox(height: 2),
                    Text(
                      'Free: $_questionLimit max',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress bar
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),

          // Offline mode indicator
          if (_offlineService.isOffline) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.orange.shade100,
              child: Row(
                children: [
                  Icon(
                    Icons.cloud_off,
                    color: Colors.orange.shade700,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Offline Mode - Using cached content',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  if (_offlineService.hasCachedContent)
                    Icon(Icons.check_circle, color: Colors.green, size: 16),
                ],
              ),
            ),
          ],

          // Timer
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color:
                _timeRemaining <= 10 ? Colors.red.shade50 : Colors.blue.shade50,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.timer,
                  color:
                      _timeRemaining <= 10 ? Colors.red : AppTheme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Time: ${_timeRemaining}s',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color:
                        _timeRemaining <= 10
                            ? Colors.red
                            : AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: PageView.builder(
              controller: _pageController,
              itemCount: _questions.length,
              onPageChanged: (index) {
                if (index != _currentQuestionIndex) {
                  _navigateToQuestion(index);
                }
              },
              itemBuilder: (context, index) {
                final question = _questions[index];
                return Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Question card with reading preferences
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    'Question ${index + 1}',
                                    style: _readingPrefs.getPreferredTextStyle(
                                      TextStyle(
                                        fontSize: 14,
                                        color: AppTheme.primaryColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  const Spacer(),
                                  // Bookmark button
                                  IconButton(
                                    onPressed: () => _toggleBookmark(question),
                                    icon: Icon(
                                      _bookmarkService.isBookmarked(question.id)
                                          ? Icons.bookmark
                                          : Icons.bookmark_border,
                                      color:
                                          _bookmarkService.isBookmarked(
                                                question.id,
                                              )
                                              ? Colors.amber
                                              : Colors.grey,
                                    ),
                                    tooltip:
                                        _bookmarkService.isBookmarked(
                                              question.id,
                                            )
                                            ? 'Remove bookmark'
                                            : 'Bookmark question',
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                question.question,
                                style: _readingPrefs.getPreferredTextStyle(
                                  const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    height: 1.4,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Options
                      Expanded(
                        child: ListView.builder(
                          itemCount: question.options.length,
                          itemBuilder: (context, optionIndex) {
                            return _buildOptionCard(question, optionIndex);
                          },
                        ),
                      ),

                      // Explanation (shown after answering)
                      if (_showExplanation &&
                          index == _currentQuestionIndex) ...[
                        const SizedBox(height: 16),
                        _buildExplanationCard(question),
                      ],

                      // Navigation hints
                      if (!_showExplanation) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.swipe,
                                color: Colors.grey.shade600,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Swipe left/right to navigate questions',
                                style: _readingPrefs.getPreferredTextStyle(
                                  TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton:
          _isPremiumUser
              ? FloatingActionButton.extended(
                onPressed:
                    _isGeneratingQuestions ? null : _generateMoreQuestions,
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                icon:
                    _isGeneratingQuestions
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Icon(Icons.add),
                label: Text(
                  _isGeneratingQuestions ? 'Generating...' : 'More Questions',
                ),
              )
              : null,
        ),
      ),
    );
  }

  /// Setup auto-advance timer based on reading preferences
  void _setupAutoAdvance() {
    if (_readingPrefs.autoAdvanceEnabled && _questions.isNotEmpty) {
      final question = _questions[_currentQuestionIndex];
      final delay = _readingPrefs.getAutoAdvanceDelay(question.question);

      _autoAdvanceTimer?.cancel();
      _autoAdvanceTimer = Timer(delay, () {
        if (!_isAnswered && mounted) {
          // Auto-submit with no answer if time expires
          _submitAnswer(-1);
        }
      });
    }
  }

  /// Navigate to specific question index
  void _navigateToQuestion(int index) {
    if (index >= 0 &&
        index < _questions.length &&
        index != _currentQuestionIndex) {
      setState(() {
        _currentQuestionIndex = index;
        _selectedAnswerIndex = null;
        _isAnswered = false;
        _showExplanation = false;
        _aiExplanation = null;
      });
      _timer?.cancel();
      _autoAdvanceTimer?.cancel();
      _startTimer();
      _setupAutoAdvance();
    }
  }

  /// Toggle bookmark for a question
  Future<void> _toggleBookmark(QuizQuestionModel question) async {
    final wasBookmarked = _bookmarkService.isBookmarked(question.id);
    await _bookmarkService.toggleBookmark(question.id);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            wasBookmarked
                ? 'Question removed from bookmarks'
                : 'Question bookmarked for review',
          ),
          backgroundColor: wasBookmarked ? Colors.orange : Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Build explanation card with AI features
  Widget _buildExplanationCard(QuizQuestionModel question) {
    return Card(
      color:
          _selectedAnswerIndex == question.correctAnswerIndex
              ? Colors.green.shade50
              : Colors.red.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _selectedAnswerIndex == question.correctAnswerIndex
                      ? Icons.check_circle
                      : Icons.cancel,
                  color:
                      _selectedAnswerIndex == question.correctAnswerIndex
                          ? Colors.green
                          : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  _selectedAnswerIndex == question.correctAnswerIndex
                      ? 'Correct!'
                      : 'Incorrect',
                  style: _readingPrefs.getPreferredTextStyle(
                    TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color:
                          _selectedAnswerIndex == question.correctAnswerIndex
                              ? Colors.green
                              : Colors.red,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Correct Answer: ${question.correctAnswer}',
              style: _readingPrefs.getPreferredTextStyle(
                const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _aiExplanation ?? question.explanation,
              style: _readingPrefs.getPreferredTextStyle(
                const TextStyle(fontSize: 14, height: 1.3),
              ),
            ),

            // AI Explanation Button for Premium Users
            if (_isPremiumUser) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed:
                          _isGeneratingExplanation
                              ? null
                              : _generateAIExplanation,
                      icon:
                          _isGeneratingExplanation
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : const Icon(Icons.psychology, size: 16),
                      label: Text(
                        _isGeneratingExplanation
                            ? 'Generating...'
                            : 'AI Explanation',
                      ),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        side: BorderSide(color: AppTheme.primaryColor),
                      ),
                    ),
                  ),
                ],
              ),
            ] else ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _showPremiumUpgrade('AI Explanation'),
                      icon: const Icon(Icons.lock, size: 16),
                      label: const Text('AI Explanation (Premium)'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey,
                        side: const BorderSide(color: Colors.grey),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOptionCard(QuizQuestionModel question, int index) {
    Color? cardColor;
    Color? textColor;
    IconData? icon;

    if (_showExplanation) {
      if (index == question.correctAnswerIndex) {
        cardColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        icon = Icons.check_circle;
      } else if (index == _selectedAnswerIndex &&
          index != question.correctAnswerIndex) {
        cardColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        icon = Icons.cancel;
      }
    } else if (_selectedAnswerIndex == index) {
      cardColor = AppTheme.primaryColor.withValues(alpha: 0.1 * 255);
      textColor = AppTheme.primaryColor;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: _isAnswered ? null : () => _submitAnswer(index),
        borderRadius: BorderRadius.circular(12),
        child: Card(
          color: cardColor,
          elevation: _selectedAnswerIndex == index ? 4 : 1,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color:
                        textColor?.withValues(alpha: 0.2 * 255) ??
                        Colors.grey.shade200,
                    border: Border.all(
                      color: textColor ?? Colors.grey.shade400,
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child:
                        icon != null
                            ? Icon(icon, size: 20, color: textColor)
                            : Text(
                              String.fromCharCode(65 + index), // A, B, C, D
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: textColor ?? Colors.grey.shade600,
                              ),
                            ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    question.options[index],
                    style: _readingPrefs.getPreferredTextStyle(
                      TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: textColor ?? Colors.black87,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Generate AI explanation for current question
  Future<void> _generateAIExplanation() async {
    if (!_isPremiumUser || _isGeneratingExplanation) return;

    setState(() {
      _isGeneratingExplanation = true;
    });

    try {
      final question = _questions[_currentQuestionIndex];
      final aiExplanation = await _aiQuizService.generateDetailedExplanation(
        question: question,
        userAgency: widget.userAgency,
      );

      setState(() {
        _aiExplanation = aiExplanation;
        _isGeneratingExplanation = false;
      });
    } catch (e) {
      setState(() {
        _isGeneratingExplanation = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Failed to generate AI explanation. Please try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show premium upgrade dialog
  void _showPremiumUpgrade(String feature) {
    PremiumUpgradeDialog.show(
      context,
      title: '$feature Locked',
      message:
          'Upgrade to Premium to unlock $feature and other AI-powered features for better exam preparation!',
    );
  }

  /// Generate more questions using AI (for premium users)
  Future<void> _generateMoreQuestions() async {
    if (!_isPremiumUser || _isGeneratingQuestions) return;

    setState(() {
      _isGeneratingQuestions = true;
    });

    try {
      final additionalQuestions = await _aiQuizService.generateQuestions(
        category: widget.quiz.category,
        userAgency: widget.userAgency,
        count: 10,
        difficulty: 'intermediate',
      );

      if (additionalQuestions.isNotEmpty) {
        setState(() {
          _questions.addAll(additionalQuestions);
          _isGeneratingQuestions = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Added ${additionalQuestions.length} new AI-generated questions!',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _isGeneratingQuestions = false;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'No additional questions could be generated at this time.',
              ),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      setState(() {
        _isGeneratingQuestions = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Failed to generate additional questions. Please try again.',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
