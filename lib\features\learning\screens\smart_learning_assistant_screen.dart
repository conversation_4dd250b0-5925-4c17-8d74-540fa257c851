import 'package:flutter/material.dart';
import 'package:fit_4_force/features/ai/services/ai_features_manager.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/services/ai_premium_access_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';

class SmartLearningAssistantScreen extends StatefulWidget {
  final String targetAgency;

  const SmartLearningAssistantScreen({super.key, required this.targetAgency});

  @override
  _SmartLearningAssistantScreenState createState() =>
      _SmartLearningAssistantScreenState();
}

class _SmartLearningAssistantScreenState
    extends State<SmartLearningAssistantScreen> {
  final TextEditingController _questionController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final AIFeaturesManager _aiManager = AIFeaturesManager();
  final AIPremiumAccessService _accessService = AIPremiumAccessService();
  final AuthService _authService = AuthService();

  final List<Map<String, dynamic>> _chatHistory = [];
  bool _isLoading = false;
  UserModel? _currentUser;
  bool _hasAIAccess = false;

  @override
  void initState() {
    super.initState();
    _initializeAI();
  }

  Future<void> _initializeAI() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize user context and AI access
      await _initializeUserContext();

      // Initialize AI manager with target agency
      await _aiManager.initialize(
        learningOnly: true,
        targetAgency: widget.targetAgency,
      );

      // Add welcome message based on AI access
      if (_hasAIAccess) {
        _addMessage({
          'isUser': false,
          'content':
              '🤖 Hello! I\'m your AI-powered Smart Learning Assistant for ${widget.targetAgency} preparation.\n\nI\'m powered by DeepSeek R1 AI and can provide personalized study assistance, generate practice questions, create custom study plans, and much more!\n\nHow can I help you excel in your military preparation today?',
          'timestamp': DateTime.now(),
          'isPremium': true,
        });

        _addMessage({
          'isUser': false,
          'content': 'Here are some AI-powered features you can try:',
          'suggestions': [
            'Create a personalized study plan for ${widget.targetAgency}',
            'Generate practice questions on military topics',
            'Analyze my study performance and weak areas',
            'Design a fitness training plan for military prep',
            'Explain complex military concepts in detail',
            'Get career guidance for ${widget.targetAgency}',
          ],
          'timestamp': DateTime.now(),
          'isPremium': true,
        });
      } else {
        _addMessage({
          'isUser': false,
          'content':
              'Hello! I\'m your Smart Learning Assistant for ${widget.targetAgency} preparation.\n\n🔒 You\'re currently using the basic version. Upgrade to Premium to unlock AI-powered features like personalized study plans, intelligent quiz generation, and advanced performance analysis.',
          'timestamp': DateTime.now(),
          'isPremium': false,
        });

        _addMessage({
          'isUser': false,
          'content': 'Basic features available:',
          'suggestions': [
            'Ask about military ranks in ${widget.targetAgency}',
            'Get basic fitness requirements',
            'Request general study guidance',
            'Take a simple practice quiz',
          ],
          'timestamp': DateTime.now(),
          'isPremium': false,
        });
      }
    } catch (e) {
      _addMessage({
        'isUser': false,
        'content': 'Sorry, I had trouble initializing. Please try again later.',
        'error': e.toString(),
        'timestamp': DateTime.now(),
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _initializeUserContext() async {
    try {
      // Get current user
      _currentUser = await _authService.getCurrentUser();

      // Check AI access
      _hasAIAccess = await _accessService.hasAIAccess(_currentUser);

      debugPrint('🤖 Smart Learning Assistant - AI Access: $_hasAIAccess');
    } catch (e) {
      debugPrint('❌ Error initializing user context: $e');
      _hasAIAccess = false;
    }
  }

  void _addMessage(Map<String, dynamic> message) {
    setState(() {
      _chatHistory.add(message);
    });

    // Scroll to bottom after message is added
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendQuestion(String question) async {
    if (question.trim().isEmpty) return;

    // Check for premium-only features
    if (_requiresPremiumAccess(question) && !_hasAIAccess) {
      _showPremiumUpgradePrompt(question);
      return;
    }

    // Clear input field
    _questionController.clear();

    // Add user message to chat
    _addMessage({
      'isUser': true,
      'content': question,
      'timestamp': DateTime.now(),
    });

    setState(() {
      _isLoading = true;
    });

    try {
      // Get user context from chat history
      String userContext = _generateUserContext();

      // Process question
      final response = await _aiManager.askQuestion(question, userContext);

      // Add AI response to chat with premium status
      _addMessage({
        'isUser': false,
        'content': response['content'],
        'responseType': response['responseType'],
        'suggestions': response['suggestions'],
        'plan': response['plan'],
        'questions': response['questions'],
        'relatedTopics': response['relatedTopics'],
        'isPremium': response['isPremium'] ?? _hasAIAccess,
        'timestamp': DateTime.now(),
      });
    } catch (e) {
      _addMessage({
        'isUser': false,
        'content': 'Sorry, I encountered an error. Please try again.',
        'error': e.toString(),
        'timestamp': DateTime.now(),
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _requiresPremiumAccess(String question) {
    final lowerQuestion = question.toLowerCase();

    // Check for premium-only keywords
    final premiumKeywords = [
      'personalized',
      'custom',
      'analyze',
      'performance',
      'advanced',
      'ai',
      'intelligent',
      'smart',
      'generate',
      'create plan',
      'weak areas',
      'strengths',
      'detailed analysis',
      'coaching',
    ];

    return premiumKeywords.any((keyword) => lowerQuestion.contains(keyword));
  }

  void _showPremiumUpgradePrompt(String question) {
    _addMessage({
      'isUser': true,
      'content': question,
      'timestamp': DateTime.now(),
    });

    _addMessage({
      'isUser': false,
      'content':
          '🔒 This feature requires Premium access!\n\nUpgrade to Premium to unlock:\n• AI-powered personalized responses\n• Advanced study plan generation\n• Performance analysis\n• Custom quiz creation\n• Detailed explanations\n\nTap "Upgrade to Premium" to get started!',
      'timestamp': DateTime.now(),
      'isPremium': false,
      'showUpgrade': true,
    });
  }

  String _generateUserContext() {
    // Extract recent user questions and topics
    List<String> recentQuestions = [];

    for (var message in _chatHistory.reversed) {
      if (message['isUser'] == true && recentQuestions.length < 5) {
        recentQuestions.add(message['content']);
      }
    }

    return recentQuestions.join(' ');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(
              _hasAIAccess ? Icons.smart_toy : Icons.school,
              color: Colors.white,
              size: 24,
            ),
            SizedBox(width: 8),
            Text('Smart Learning Assistant'),
            if (_hasAIAccess) ...[
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange.shade300),
                ),
                child: Text(
                  'AI POWERED',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
        actions: [
          if (!_hasAIAccess)
            IconButton(
              icon: Icon(Icons.star),
              onPressed: () => _accessService.showAIUpgradeDialog(context),
              tooltip: 'Upgrade to Premium',
            ),
        ],
      ),
      body: Column(
        children: [
          // Chat messages area
          Expanded(
            child:
                _chatHistory.isEmpty
                    ? Center(
                      child: Text(
                        'Your learning assistant is getting ready...',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                    : ListView.builder(
                      controller: _scrollController,
                      padding: EdgeInsets.all(16),
                      itemCount: _chatHistory.length,
                      itemBuilder: (context, index) {
                        final message = _chatHistory[index];
                        return _buildMessageWidget(message);
                      },
                    ),
          ),

          // Loading indicator
          if (_isLoading)
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: LinearProgressIndicator(),
            ),

          // Input area
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  offset: Offset(0, -1),
                  blurRadius: 4,
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _questionController,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    onSubmitted: _sendQuestion,
                  ),
                ),
                SizedBox(width: 8),
                FloatingActionButton(
                  onPressed: () => _sendQuestion(_questionController.text),
                  backgroundColor: AppTheme.primaryColor,
                  mini: true,
                  child: Icon(Icons.send),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageWidget(Map<String, dynamic> message) {
    final isUser = message['isUser'] ?? false;
    final content = message['content'] ?? '';
    final isPremium = message['isPremium'] ?? false;
    final showUpgrade = message['showUpgrade'] ?? false;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatarWidget(isPremium),
          SizedBox(width: 8),
          Flexible(
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser ? AppTheme.primaryColor : Colors.grey[200],
                borderRadius: BorderRadius.circular(16),
                border:
                    isPremium && !isUser
                        ? Border.all(color: Colors.orange.shade300, width: 2)
                        : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Premium indicator
                  if (!isUser && isPremium)
                    Container(
                      margin: EdgeInsets.only(bottom: 8),
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.orange.shade300),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star,
                            size: 14,
                            color: Colors.orange.shade600,
                          ),
                          SizedBox(width: 4),
                          Text(
                            'AI-Powered Response',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),

                  Text(
                    content,
                    style: TextStyle(
                      color: isUser ? Colors.white : Colors.black87,
                    ),
                  ),

                  // Upgrade button for premium prompts
                  if (!isUser && showUpgrade)
                    Container(
                      margin: EdgeInsets.only(top: 12),
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed:
                            () => _accessService.showAIUpgradeDialog(context),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange.shade600,
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.star, size: 18),
                            SizedBox(width: 8),
                            Text(
                              'Upgrade to Premium',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ),

                  if (!isUser && message['suggestions'] != null)
                    _buildSuggestions(message['suggestions']),
                  if (!isUser && message['plan'] != null)
                    _buildPlan(message['plan']),
                  if (!isUser && message['questions'] != null)
                    _buildQuiz(message['questions']),
                  if (!isUser && message['relatedTopics'] != null)
                    _buildRelatedTopics(message['relatedTopics']),
                ],
              ),
            ),
          ),
          SizedBox(width: 8),
          if (isUser) _buildUserAvatarWidget(),
        ],
      ),
    );
  }

  Widget _buildAvatarWidget([bool isPremium = false]) {
    return CircleAvatar(
      backgroundColor:
          isPremium ? Colors.orange.shade600 : AppTheme.primaryColor,
      child: Icon(
        isPremium ? Icons.smart_toy : Icons.school,
        color: Colors.white,
      ),
    );
  }

  Widget _buildUserAvatarWidget() {
    return CircleAvatar(
      backgroundColor: Colors.grey[300],
      child: Icon(Icons.person, color: Colors.grey[700]),
    );
  }

  Widget _buildSuggestions(List<dynamic> suggestions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Suggestions:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              suggestions.map((suggestion) {
                return InkWell(
                  onTap: () => _sendQuestion(suggestion),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      suggestion,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildPlan(List<dynamic> plan) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Study Plan:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        ...plan.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 4.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: AppTheme.primaryColor,
                ),
                SizedBox(width: 4),
                Expanded(child: Text(item)),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildQuiz(List<dynamic> questions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Quiz:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        ...questions.map((question) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  question['question'],
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                SizedBox(height: 4),
                ...question['options'].map((option) {
                  bool isCorrect = option == question['answer'];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 2.0),
                    child: Row(
                      children: [
                        Icon(
                          isCorrect
                              ? Icons.check_circle
                              : Icons.circle_outlined,
                          size: 16,
                          color: isCorrect ? Colors.green : Colors.grey,
                        ),
                        SizedBox(width: 4),
                        Text(option),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          );
        }),
      ],
    );
  }

  Widget _buildRelatedTopics(List<dynamic> topics) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 8),
        Text(
          'Related Topics:',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black87),
        ),
        SizedBox(height: 4),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              topics.map((topic) {
                return InkWell(
                  onTap: () => _sendQuestion('Tell me about $topic'),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.blue.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      topic,
                      style: TextStyle(color: Colors.blue, fontSize: 12),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _questionController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
