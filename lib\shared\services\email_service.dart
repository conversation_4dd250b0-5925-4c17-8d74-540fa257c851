import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:fit_4_force/core/config/email_config.dart';
import 'dart:convert';

/// Service for sending emails using a backend email API
///
/// This service handles:
/// - Welcome emails for new users
/// - Premium upgrade confirmation emails
/// - Other notification emails as needed
class EmailService {
  // Using EmailJS as the email service provider
  // You can switch to other providers like SendGrid, AWS SES, etc.

  static const String _baseUrl = 'https://api.emailjs.com/api/v1.0/email/send';
  static const String _serviceId = EmailConfig.emailJsServiceId;
  static const String _publicKey = EmailConfig.emailJsPublicKey;

  /// Send a welcome email to a new user
  static Future<bool> sendWelcomeEmail({
    required String userEmail,
    required String fullName,
  }) async {
    if (!EmailConfig.enableWelcomeEmail) {
      debugPrint('📧 Welcome email disabled in configuration');
      return true;
    }

    try {
      // Extract first name from full name
      final firstName = fullName.split(' ').first;

      final emailData = {
        'service_id': _serviceId,
        'template_id': EmailConfig.welcomeTemplateId,
        'user_id': _publicKey,
        'template_params': {
          'user_name': fullName,
          'first_name': firstName,
          'user_email': userEmail,
          'app_name': EmailConfig.appName,
          'welcome_message': '''Hi $firstName,

Welcome to Fit4Force — Nigeria's first all-in-one mobile app built specially for military, paramilitary, and security service aspirants like YOU.

Whether you're preparing for the Nigerian Army, Navy, Air Force, Police, NSCDC, FRSC, or any other force, Fit4Force is your trusted companion.''',
          'free_features': '''🚀 What You Get (Free Plan):

✅ Access to workout routines
✅ Practice questions & flashcards  
✅ Basic educational content
✅ Motivational tips & career insights''',
          'premium_features': '''🌟 Why Go Premium (Just ₦2,500/month):

📚 Full access to premium study materials (PDFs, quizzes, videos)
💪 Unlock exclusive fitness programs & meal plans
🎯 Access mock exams, flashcards & AI interview prep
🤖 Get support from the AI Assistant Coach & Study Bot
💬 Join the Aspirants' Forum (connect, ask & learn)
🎥 Watch video lessons directly from the app
🏆 Elite military-style workout challenges & competitions
📊 Advanced progress tracking & performance analytics
🎖️ Exclusive webinars with former military officers
📱 Offline access to all premium content
⚡ Priority customer support (24/7)
🔥 Access to "Success Stories" from selected candidates
📈 Personalized study plans based on your target force
🎪 Monthly live Q&A sessions with recruitment experts''',
          'premium_call_to_action':
              'Premium = Everything you need to prepare, succeed & get selected.',
          'login_url': EmailConfig.loginUrl,
          'support_email': EmailConfig.supportEmail,
          'app_url': EmailConfig.appUrl,
        },
      };

      return await _sendEmail(emailData, 'Welcome Email');
    } catch (e) {
      debugPrint('❌ Error sending welcome email: $e');
      return false;
    }
  }

  /// Send a premium upgrade confirmation email
  static Future<bool> sendPremiumUpgradeEmail({
    required String userEmail,
    required String fullName,
    required DateTime expiryDate,
    required bool isYearly,
    required String transactionReference,
  }) async {
    if (!EmailConfig.enablePremiumUpgradeEmail) {
      debugPrint('📧 Premium upgrade email disabled in configuration');
      return true;
    }

    try {
      final firstName = fullName.split(' ').first;
      final subscriptionType = isYearly ? 'Yearly Premium' : 'Monthly Premium';
      final savings = isYearly ? ' (You saved ₦5,000! 🎉)' : '';

      final emailData = {
        'service_id': _serviceId,
        'template_id': EmailConfig.premiumUpgradeTemplateId,
        'user_id': _publicKey,
        'template_params': {
          'user_name': fullName,
          'first_name': firstName,
          'user_email': userEmail,
          'app_name': EmailConfig.appName,
          'congratulations_message': '''🎉 Congratulations $firstName!

Your $subscriptionType subscription is now ACTIVE$savings

You're now part of an ELITE community of serious aspirants who are committed to getting SELECTED!''',
          'subscription_details': '''📋 Your Subscription Details:
• Plan: $subscriptionType
• Valid Until: ${_formatDate(expiryDate)}
• Transaction ID: $transactionReference
• Status: ✅ ACTIVE''',
          'premium_features': '''🚀 Your Premium Arsenal (Now Unlocked):

📚 STUDY LIKE A PRO:
• Complete study materials for ALL forces (Army, Navy, Air Force, Police, NSCDC, FRSC)
• 10,000+ practice questions with detailed explanations
• Mock exams that simulate real recruitment tests
• Downloadable PDFs for offline study

💪 TRAIN LIKE A SOLDIER:
• Military-grade fitness programs
• Specialized meal plans for peak performance
• Elite workout challenges & competitions
• Recovery plans & injury prevention

🤖 AI-POWERED SUCCESS:
• Personal AI Study Bot (available 24/7)
• AI Interview Prep with realistic scenarios
• Personalized study plans based on your target force
• Smart progress tracking & performance analytics

🏆 EXCLUSIVE ACCESS:
• Live webinars with former military officers
• Monthly Q&A sessions with recruitment experts
• Success stories from selected candidates
• Aspirants' Forum - connect with serious candidates nationwide

⚡ PREMIUM PERKS:
• Priority customer support (24/7)
• Offline access to ALL content
• Early access to new features
• Advanced progress analytics & insights''',
          'call_to_action': '''🎯 Ready to DOMINATE your preparation?

Log in now and start accessing your premium features. Remember, every day counts in your preparation journey!''',
          'motivation_message':
              '''💪 Remember: You're not just preparing for a job, you're preparing to serve your nation. 

Make every study session count. Make every workout matter. Your future uniform is waiting! 🇳🇬''',
          'expiry_date': _formatDate(expiryDate),
          'transaction_reference': transactionReference,
          'support_email': EmailConfig.premiumSupportEmail,
          'login_url': EmailConfig.loginUrl,
          'app_url': EmailConfig.appUrl,
        },
      };

      return await _sendEmail(emailData, 'Premium Upgrade Email');
    } catch (e) {
      debugPrint('❌ Error sending premium upgrade email: $e');
      return false;
    }
  }

  /// Send a generic notification email
  static Future<bool> sendNotificationEmail({
    required String userEmail,
    required String fullName,
    required String subject,
    required String message,
    Map<String, dynamic>? additionalParams,
  }) async {
    try {
      final emailData = {
        'service_id': _serviceId,
        'template_id':
            'notification_template', // Create this template in EmailJS
        'user_id': _publicKey,
        'template_params': {
          'user_name': fullName,
          'user_email': userEmail,
          'app_name': 'Fit4Force',
          'subject': subject,
          'message': message,
          ...?additionalParams,
        },
      };

      return await _sendEmail(emailData, 'Notification Email');
    } catch (e) {
      debugPrint('❌ Error sending notification email: $e');
      return false;
    }
  }

  /// Internal method to send email via EmailJS API
  static Future<bool> _sendEmail(
    Map<String, dynamic> emailData,
    String emailType,
  ) async {
    try {
      debugPrint(
        '📧 Preparing to send $emailType to ${emailData['template_params']['user_email']}',
      );

      // Validate required fields
      if (_serviceId.isEmpty || _serviceId == EmailConfig.emailJsServiceId) {
        debugPrint(
          '⚠️ EmailJS service ID not configured. Update EmailConfig.emailJsServiceId',
        );
        if (kDebugMode) {
          debugPrint(
            '📧 [$emailType] Email configuration incomplete - check EMAIL_SETUP_GUIDE.md',
          );
          return true; // Don't fail in debug mode
        }
        return false;
      }

      // In development, just log the email instead of actually sending
      if (kDebugMode) {
        debugPrint('📧 [$emailType] Email ready to send:');
        debugPrint('   To: ${emailData['template_params']['user_email']}');
        debugPrint('   Template: ${emailData['template_id']}');
        debugPrint('   Service: ${emailData['service_id']}');
        debugPrint('   Data: ${jsonEncode(emailData['template_params'])}');
        debugPrint('🚀 Email would be sent in production mode');
        return true; // Simulate success in debug mode
      }

      final response = await http
          .post(
            Uri.parse(_baseUrl),
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Fit4Force-App/1.0',
            },
            body: jsonEncode(emailData),
          )
          .timeout(
            const Duration(seconds: 30),
            onTimeout: () {
              throw Exception('Email service timeout - please try again');
            },
          );

      if (response.statusCode == 200) {
        debugPrint(
          '✅ $emailType sent successfully to ${emailData['template_params']['user_email']}',
        );
        return true;
      } else {
        debugPrint('❌ Failed to send $emailType: ${response.statusCode}');
        debugPrint('   Response: ${response.body}');

        // Parse EmailJS error response
        try {
          final errorData = jsonDecode(response.body);
          if (errorData is Map && errorData.containsKey('message')) {
            debugPrint('   Error details: ${errorData['message']}');
          }
        } catch (e) {
          // Ignore JSON parsing errors
        }

        return false;
      }
    } catch (e) {
      debugPrint('❌ Exception sending $emailType: $e');
      return false;
    }
  }

  /// Format date for email display
  static String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Email templates for different types of emails
class EmailTemplates {
  /// HTML template for welcome email
  static String get welcomeEmailHtml => '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Fit4Force! 🎯</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #4A80F0, #1A56E0); color: white; padding: 40px 20px; text-align: center; }
        .logo { font-size: 32px; font-weight: bold; margin-bottom: 10px; }
        .tagline { font-size: 14px; opacity: 0.9; }
        .content { padding: 30px 20px; }
        .welcome-section { margin-bottom: 30px; }
        .features-section { background-color: #f8f9fa; padding: 25px; border-radius: 12px; margin: 25px 0; border-left: 5px solid #4A80F0; }
        .premium-section { background: linear-gradient(135deg, #FFD700, #FFA500); color: #333; padding: 25px; border-radius: 12px; margin: 25px 0; }
        .feature-list { margin: 15px 0; padding-left: 0; }
        .feature-item { margin: 8px 0; font-size: 14px; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #4A80F0, #1A56E0); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 20px 0; font-weight: bold; font-size: 16px; }
        .cta-button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(74, 128, 240, 0.3); }
        .nigeria-flag { color: #008000; font-weight: bold; }
        .footer { background-color: #333; color: white; padding: 20px; text-align: center; font-size: 14px; }
        .motivational { background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; font-style: italic; color: #2d5a2d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎯 Fit4Force</div>
            <div class="tagline">Nigeria's Premier Military & Paramilitary Prep App</div>
        </div>
        
        <div class="content">
            <div class="welcome-section">
                <div style="white-space: pre-line;">{{welcome_message}}</div>
            </div>
            
            <div class="features-section">
                <div style="white-space: pre-line; font-size: 15px;">{{free_features}}</div>
            </div>
            
            <div class="premium-section">
                <div style="white-space: pre-line; font-size: 15px; line-height: 1.7;">{{premium_features}}</div>
                
                <div style="margin-top: 20px; padding: 15px; background-color: rgba(255,255,255,0.9); border-radius: 8px; text-align: center;">
                    <strong style="font-size: 16px;">{{premium_call_to_action}}</strong>
                </div>
            </div>
            
            <div style="text-align: center;">
                <a href="{{login_url}}" class="cta-button">🚀 Start Your Journey Now</a>
            </div>
            
            <div class="motivational">
                💪 "Success in the military starts with the right preparation. You've taken the first step!" 🇳🇬
            </div>
            
            <p style="margin-top: 30px;">
                Need help getting started? Our support team is here for you at 
                <a href="mailto:{{support_email}}" style="color: #4A80F0;">{{support_email}}</a>
            </p>
            
            <p style="margin-top: 20px;">
                Stay strong and focused,<br>
                <strong>The Fit4Force Team</strong> 💪
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 Fit4Force. All rights reserved.</p>
            <p><strong>Building tomorrow's heroes, one workout at a time.</strong> 🇳🇬</p>
            <p>Visit us: <a href="{{app_url}}" style="color: #4A80F0;">{{app_url}}</a></p>
        </div>
    </div>
</body>
</html>
''';

  /// HTML template for premium upgrade email
  static String get premiumUpgradeEmailHtml => '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Fit4Force Premium! ⭐</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background-color: #f5f5f5; line-height: 1.6; }
        .container { max-width: 600px; margin: 0 auto; background-color: white; }
        .header { background: linear-gradient(135deg, #FFD700, #FFA500); color: #333; padding: 40px 20px; text-align: center; }
        .premium-badge { background-color: rgba(255,255,255,0.9); padding: 10px 20px; border-radius: 20px; display: inline-block; margin-bottom: 15px; font-weight: bold; }
        .content { padding: 30px 20px; }
        .congratulations { background: linear-gradient(135deg, #4A80F0, #1A56E0); color: white; padding: 25px; border-radius: 12px; margin: 20px 0; text-align: center; }
        .subscription-details { background-color: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; border-left: 5px solid #FFD700; }
        .features-grid { background: linear-gradient(135deg, #4A80F0, #1A56E0); color: white; padding: 25px; border-radius: 12px; margin: 20px 0; }
        .feature-category { margin: 20px 0; }
        .feature-category h4 { color: #FFD700; margin-bottom: 10px; font-size: 16px; }
        .feature-item { margin: 5px 0; font-size: 14px; }
        .cta-section { background-color: #e8f5e8; padding: 25px; border-radius: 12px; margin: 20px 0; text-align: center; }
        .cta-button { display: inline-block; background: linear-gradient(135deg, #4A80F0, #1A56E0); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; margin: 15px 0; font-weight: bold; font-size: 16px; }
        .cta-button:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(74, 128, 240, 0.3); }
        .motivation { background: linear-gradient(135deg, #008000, #006400); color: white; padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center; }
        .footer { background-color: #333; color: white; padding: 20px; text-align: center; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="premium-badge">⭐ PREMIUM MEMBER ⭐</div>
            <h1>You're Now ELITE! 🏆</h1>
        </div>
        
        <div class="content">
            <div class="congratulations">
                <div style="white-space: pre-line; font-size: 16px;">{{congratulations_message}}</div>
            </div>
            
            <div class="subscription-details">
                <h3>📋 Subscription Confirmed:</h3>
                <div style="white-space: pre-line; font-size: 15px;">{{subscription_details}}</div>
            </div>
            
            <div class="features-grid">
                <h3 style="text-align: center; color: #FFD700; margin-bottom: 20px;">🎯 Your Premium Arsenal:</h3>
                <div style="white-space: pre-line; font-size: 14px; line-height: 1.8;">{{premium_features}}</div>
            </div>
            
            <div class="cta-section">
                <div style="white-space: pre-line; font-size: 16px; margin-bottom: 15px;">{{call_to_action}}</div>
                <a href="{{login_url}}" class="cta-button">🚀 Access Premium Features</a>
            </div>
            
            <div class="motivation">
                <div style="white-space: pre-line; font-size: 15px;">{{motivation_message}}</div>
            </div>
            
            <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #856404; margin-top: 0;">🏆 Premium Support:</h4>
                <p style="color: #856404; margin-bottom: 0;">
                    As a Premium member, you get priority support! Reach us at 
                    <a href="mailto:{{support_email}}" style="color: #4A80F0;">{{support_email}}</a>
                </p>
            </div>
            
            <p style="margin-top: 30px; text-align: center;">
                <strong>Welcome to the ELITE circle!</strong><br>
                The Fit4Force Premium Team 💪🇳🇬
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 Fit4Force Premium. All rights reserved.</p>
            <p><strong>Building tomorrow's heroes, one workout at a time.</strong> 🇳🇬</p>
            <p>Visit us: <a href="{{app_url}}" style="color: #4A80F0;">{{app_url}}</a></p>
        </div>
    </div>
</body>
</html>
''';
}
