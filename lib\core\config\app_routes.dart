import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/features/auth/screens/login_screen.dart';
import 'package:fit_4_force/features/auth/screens/signup_screen.dart';
import 'package:fit_4_force/features/auth/screens/agency_selection_screen.dart';
import 'package:fit_4_force/features/auth/screens/device_management_screen.dart';
import 'package:fit_4_force/features/fitness/screens/category_detail_screen.dart';
import 'package:fit_4_force/features/fitness/screens/create_workout_screen.dart';
import 'package:fit_4_force/features/fitness/screens/notifications_screen.dart';
import 'package:fit_4_force/features/fitness/screens/plan_detail_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_categories_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_detail_screen.dart';
import 'package:fit_4_force/features/learning/screens/smart_learning_assistant_screen.dart';
import 'package:fit_4_force/features/requirements/screens/agency_requirements_screen.dart';

import 'package:fit_4_force/features/ai/screens/ai_study_planner_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_history_screen.dart';
import 'package:fit_4_force/features/fitness/screens/workout_session_screen.dart';
import 'package:fit_4_force/features/home/<USER>/home_screen.dart'
    as home_feature;
import 'package:fit_4_force/features/prep/screens/pomodoro_timer_screen.dart';
import 'package:fit_4_force/features/prep/screens/flashcards_screen.dart';
import 'package:fit_4_force/features/prep/screens/progress_dashboard_screen.dart'
    as prep_progress;
import 'package:fit_4_force/features/prep/screens/mock_exam_list_screen.dart';
import 'package:fit_4_force/features/profile/screens/profile_screen.dart';
import 'package:fit_4_force/features/settings/screens/settings_screen.dart';
import 'package:fit_4_force/features/subscription/screens/premium_screen.dart';
import 'package:fit_4_force/features/progress/screens/progress_dashboard_screen.dart';
import 'package:fit_4_force/features/demo/screens/responsive_demo_screen.dart';

import 'package:fit_4_force/features/splash/screens/animated_splash_screen.dart';
import 'package:fit_4_force/features/prep/screens/enhanced_quiz_screen.dart';
import 'package:fit_4_force/features/prep/screens/performance_analytics_screen.dart';
import 'package:fit_4_force/features/prep/models/nigerian_exam_pattern_model.dart';
import 'package:fit_4_force/features/onboarding/screens/welcome_onboarding_screen.dart';
import 'package:fit_4_force/features/onboarding/screens/welcome_intro_screen.dart';
import 'package:fit_4_force/features/onboarding/screens/onboarding_test_screen.dart';
import 'package:fit_4_force/features/news/screens/news_detail_screen.dart';
import 'package:fit_4_force/features/news/screens/agency_news_screen.dart';

/// App routes configuration
class AppRoutes {
  // Route names
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String welcomeIntro = '/welcome-intro';
  static const String welcomeOnboarding = '/welcome-onboarding';
  static const String onboardingTest = '/onboarding-test';
  static const String login = '/login';
  static const String signup = '/signup';
  static const String agencySelection = '/agency-selection';
  static const String deviceManagement = '/device-management';
  static const String home = '/home';
  static const String prep = '/prep';
  static const String fit = '/fit';
  static const String coach = '/coach';
  static const String community = '/community';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String premium = '/premium';
  static const String agencyNews = '/agency-news';
  static const String newsDetail = '/news-detail';
  static const String agencyRequirements = '/agency-requirements';

  // Prep routes
  static const String pomodoroTimer = '/pomodoro-timer';
  static const String flashcards = '/flashcards';
  static const String prepProgressDashboard = '/prep-progress-dashboard';
  static const String mockExams = '/mock-exams';
  static const String smartLearningAssistant = '/smart-learning-assistant';

  static const String aiStudyPlanner = '/ai-study-planner';
  static const String enhancedQuiz = '/enhanced-quiz';
  static const String performanceAnalytics = '/performance-analytics';

  // Fitness routes
  static const String notifications = '/notifications';
  static const String workoutCategories = '/workout-categories';
  static const String categoryDetail = '/category-detail';
  static const String workoutDetail = '/workout-detail';
  static const String workoutHistory = '/workout-history';
  static const String workoutSession = '/workout-session';
  static const String createWorkout = '/create-workout';
  static const String planDetail = '/plan-detail';

  // Progress routes
  static const String progressDashboard = '/progress-dashboard';

  // Demo routes
  static const String responsiveDemo = '/responsive-demo';

  /// Get routes for the app
  static Map<String, WidgetBuilder> getRoutes() {
    return {
      splash: (context) => const AnimatedSplashScreen(),
      onboarding: (context) => const WelcomeOnboardingScreen(),
      welcomeIntro: (context) => const WelcomeIntroScreen(),
      welcomeOnboarding: (context) => const WelcomeOnboardingScreen(),
      onboardingTest: (context) => const OnboardingTestScreen(),
      login: (context) => const LoginScreen(),
      signup: (context) => const SignUpScreen(),
      home: (context) => const home_feature.HomeScreen(),
      agencySelection: (context) => const AgencySelectionScreen(),
      deviceManagement: (context) => const DeviceManagementScreen(),
      profile: (context) => const ProfileScreen(),
      settings: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as UserModel;
        return SettingsScreen(user: user);
      },
      premium: (context) => const PremiumScreen(),
      agencyNews: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return AgencyNewsScreen(user: user);
      },
      newsDetail: (context) {
        final args =
            ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
        return NewsDetailScreen(news: args['news'], user: args['user']);
      },
      agencyRequirements: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return AgencyRequirementsScreen(user: user);
      },

      // Fitness routes
      notifications: (context) => const NotificationsScreen(),
      workoutCategories: (context) => const WorkoutCategoriesScreen(),
      categoryDetail: (context) {
        final categoryId = ModalRoute.of(context)!.settings.arguments as String;
        return CategoryDetailScreen(categoryId: categoryId);
      },
      workoutDetail: (context) {
        final workoutId = ModalRoute.of(context)!.settings.arguments as String;
        return WorkoutDetailScreen(workoutId: workoutId);
      },
      workoutHistory: (context) => const WorkoutHistoryScreen(),
      workoutSession: (context) {
        final workoutId = ModalRoute.of(context)!.settings.arguments as String;
        return WorkoutSessionScreen(workoutId: workoutId);
      },
      createWorkout: (context) => const CreateWorkoutScreen(),
      planDetail: (context) {
        final planId = ModalRoute.of(context)!.settings.arguments as String;
        return PlanDetailScreen(planId: planId);
      },

      // Prep routes
      pomodoroTimer: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return PomodoroTimerScreen(user: user);
      },
      flashcards: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return FlashcardsScreen(user: user);
      },
      prepProgressDashboard: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return prep_progress.ProgressDashboardScreen(user: user);
      },
      mockExams: (context) {
        final user = ModalRoute.of(context)!.settings.arguments as dynamic;
        return MockExamListScreen(user: user);
      },
      smartLearningAssistant: (context) {
        final targetAgency =
            ModalRoute.of(context)!.settings.arguments as String? ??
            'Nigerian Military';
        return SmartLearningAssistantScreen(targetAgency: targetAgency);
      },

      aiStudyPlanner: (context) {
        final targetAgency =
            ModalRoute.of(context)!.settings.arguments as String? ??
            'Nigerian Military';
        return AIStudyPlannerScreen(targetAgency: targetAgency);
      },

      // Progress routes
      progressDashboard: (context) => const ProgressDashboardScreen(),

      // Enhanced Quiz routes
      enhancedQuiz: (context) {
        final args =
            ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
        return EnhancedQuizScreen(
          agencyCode: args['agencyCode'] as String,
          category: args['category'] as String,
          mode: args['mode'] as QuizMode,
        );
      },
      performanceAnalytics: (context) {
        final args =
            ModalRoute.of(context)!.settings.arguments as Map<String, dynamic>;
        return PerformanceAnalyticsScreen(
          agencyCode: args['agencyCode'] as String,
          topicPerformance:
              args['topicPerformance'] as Map<String, TopicPerformanceModel>,
        );
      },

      // Demo routes
      responsiveDemo: (context) => const ResponsiveDemoScreen(),
    };
  }
}
