import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/services/premium_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

/// Widget that enforces premium access restrictions
/// Test users get full access regardless of premium status
class PremiumAccessGate extends StatelessWidget {
  final UserModel user;
  final Widget child;
  final String? featureName;
  final String? upgradeMessage;
  final VoidCallback? onUpgradePressed;

  const PremiumAccessGate({
    super.key,
    required this.user,
    required this.child,
    this.featureName,
    this.upgradeMessage,
    this.onUpgradePressed,
  });

  @override
  Widget build(BuildContext context) {
    final premiumService = PremiumService();

    // Test users and premium users get full access
    if (premiumService.hasAccessToPremiumFeatures(user)) {
      // Log successful access
      premiumService.logPremiumAccessAttempt(
        user,
        featureName ?? 'Unknown Feature',
        true,
      );
      return child;
    }

    // Log blocked access
    premiumService.logPremiumAccessAttempt(
      user,
      featureName ?? 'Unknown Feature',
      false,
    );

    // Non-premium users see upgrade prompt
    return _buildUpgradePrompt(context);
  }

  Widget _buildUpgradePrompt(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.premiumColor.withValues(alpha: 0.1),
            AppTheme.premiumColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.premiumColor.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.premiumColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.workspace_premium,
              size: 48,
              color: AppTheme.premiumColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            featureName != null
                ? 'Premium Feature: $featureName'
                : 'Premium Feature',
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            upgradeMessage ??
                'This feature is available to premium members only. Upgrade now to unlock all features!',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: onUpgradePressed ?? () => _navigateToUpgrade(context),
            icon: const Icon(Icons.upgrade),
            label: const Text('Upgrade to Premium'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.premiumColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () => _showFeaturesList(context),
            child: Text(
              'See all premium features',
              style: TextStyle(
                color: AppTheme.premiumColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToUpgrade(BuildContext context) {
    Navigator.pushNamed(context, '/premium');
  }

  void _showFeaturesList(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.workspace_premium, color: AppTheme.premiumColor),
                const SizedBox(width: 8),
                const Text('Premium Features'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildFeatureItem(
                  'Complete Fitness Suite',
                  Icons.fitness_center,
                ),
                _buildFeatureItem(
                  '30-Day Workout Challenge',
                  Icons.calendar_month,
                ),
                _buildFeatureItem(
                  'Personalized Nutrition Plans',
                  Icons.restaurant,
                ),
                _buildFeatureItem('Recovery & Wellness Tools', Icons.spa),
                _buildFeatureItem('Advanced Analytics', Icons.analytics),
                _buildFeatureItem('Unlimited Study Materials', Icons.book),
                _buildFeatureItem(
                  'Priority Community Support',
                  Icons.support_agent,
                ),
                _buildFeatureItem('Offline Access', Icons.cloud_off),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _navigateToUpgrade(context);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.premiumColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Upgrade Now'),
              ),
            ],
          ),
    );
  }

  Widget _buildFeatureItem(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppTheme.premiumColor),
          const SizedBox(width: 12),
          Expanded(child: Text(title)),
          Icon(Icons.check, size: 16, color: Colors.green),
        ],
      ),
    );
  }
}

/// Helper widget for inline premium badges
class PremiumBadge extends StatelessWidget {
  final bool showText;
  final double? size;

  const PremiumBadge({super.key, this.showText = true, this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: showText ? 8 : 4, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.premiumColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.premiumColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.workspace_premium,
            size: size ?? 16,
            color: AppTheme.premiumColor,
          ),
          if (showText) ...[
            const SizedBox(width: 4),
            Text(
              'PREMIUM',
              style: TextStyle(
                fontSize: (size ?? 16) * 0.75,
                fontWeight: FontWeight.bold,
                color: AppTheme.premiumColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
