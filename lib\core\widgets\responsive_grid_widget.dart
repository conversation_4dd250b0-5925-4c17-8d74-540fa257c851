import 'package:flutter/material.dart';
import 'package:fit_4_force/core/utils/responsive_design_system.dart';

/// Real device compatible responsive grid widget
class ResponsiveGridWidget extends StatelessWidget {
  final List<Widget> children;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final double? childAspectRatio;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;

  const ResponsiveGridWidget({
    super.key,
    required this.children,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.childAspectRatio,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Get responsive columns
        final columns = ResponsiveDesignSystem.getGridColumns(
          context,
          mobileColumns: mobileColumns ?? 2,
          tabletColumns: tabletColumns ?? 3,
          desktopColumns: desktopColumns ?? 4,
        );

        // Calculate responsive spacing
        final crossSpacing =
            crossAxisSpacing ??
            ResponsiveDesignSystem.spacing(context, SpacingSize.sm);
        final mainSpacing =
            mainAxisSpacing ??
            ResponsiveDesignSystem.spacing(context, SpacingSize.sm);

        // Calculate responsive padding
        final gridPadding =
            padding ??
            ResponsiveDesignSystem.padding(
              context,
              all: ResponsiveDesignSystem.isSmallMobile(context) ? 8.0 : 16.0,
            );

        // Calculate aspect ratio based on device
        double aspectRatio = childAspectRatio ?? 1.0;
        if (ResponsiveDesignSystem.isSmallMobile(context)) {
          aspectRatio = aspectRatio * 1.1; // Slightly taller on small devices
        }

        // Ensure minimum item width for touch targets
        final availableWidth =
            constraints.maxWidth -
            gridPadding.horizontal -
            (crossSpacing * (columns - 1));
        final itemWidth = availableWidth / columns;

        // Adjust aspect ratio if items would be too small
        if (itemWidth < ResponsiveDesignSystem.minTouchTarget) {
          aspectRatio = aspectRatio * 0.8; // Make items taller if too narrow
        }

        return GridView.builder(
          padding: gridPadding,
          shrinkWrap: shrinkWrap,
          physics: physics,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            childAspectRatio: aspectRatio,
            crossAxisSpacing: crossSpacing,
            mainAxisSpacing: mainSpacing,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        );
      },
    );
  }
}

/// Responsive card widget with proper constraints
class ResponsiveCard extends StatelessWidget {
  final Widget child;
  final Color? color;
  final double? elevation;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? borderRadius;
  final Border? border;
  final List<BoxShadow>? boxShadow;
  final Gradient? gradient;

  const ResponsiveCard({
    super.key,
    required this.child,
    this.color,
    this.elevation,
    this.padding,
    this.margin,
    this.borderRadius,
    this.border,
    this.boxShadow,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding =
        padding ??
        ResponsiveDesignSystem.padding(
          context,
          all: ResponsiveDesignSystem.isSmallMobile(context) ? 12.0 : 16.0,
        );

    final responsiveMargin =
        margin ??
        ResponsiveDesignSystem.padding(
          context,
          all: ResponsiveDesignSystem.isSmallMobile(context) ? 4.0 : 8.0,
        );

    final responsiveBorderRadius =
        borderRadius != null
            ? ResponsiveDesignSystem.borderRadius(context, borderRadius!)
            : ResponsiveDesignSystem.borderRadius(context, 12.0);

    return Container(
      margin: responsiveMargin,
      padding: responsivePadding,
      decoration: BoxDecoration(
        color: gradient == null ? (color ?? Colors.white) : null,
        gradient: gradient,
        borderRadius: BorderRadius.circular(responsiveBorderRadius),
        border: border,
        boxShadow:
            boxShadow ??
            [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05 * 255),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
      ),
      child: child,
    );
  }
}

/// Responsive container with flexible sizing
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final double? minWidth;
  final double? maxWidth;
  final double? minHeight;
  final double? maxHeight;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Decoration? decoration;
  final AlignmentGeometry? alignment;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.minWidth,
    this.maxWidth,
    this.minHeight,
    this.maxHeight,
    this.padding,
    this.margin,
    this.decoration,
    this.alignment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width:
          width != null ? ResponsiveDesignSystem.width(context, width!) : null,
      height:
          height != null
              ? ResponsiveDesignSystem.height(context, height!)
              : null,
      constraints: ResponsiveDesignSystem.responsiveConstraints(
        context,
        minWidth: minWidth,
        maxWidth: maxWidth,
        minHeight: minHeight,
        maxHeight: maxHeight,
      ),
      padding:
          padding != null
              ? ResponsiveDesignSystem.padding(
                context,
                all: ResponsiveDesignSystem.isSmallMobile(context) ? 8.0 : 16.0,
              )
              : null,
      margin: margin,
      decoration: decoration,
      alignment: alignment,
      child: child,
    );
  }
}

/// Responsive icon widget
class ResponsiveIcon extends StatelessWidget {
  final IconData icon;
  final IconSizeType sizeType;
  final Color? color;
  final double? customSize;

  const ResponsiveIcon(
    this.icon, {
    super.key,
    this.sizeType = IconSizeType.medium,
    this.color,
    this.customSize,
  });

  @override
  Widget build(BuildContext context) {
    final size =
        customSize != null
            ? ResponsiveDesignSystem.width(context, customSize!)
            : ResponsiveDesignSystem.iconSize(context, sizeType);

    return Icon(icon, size: size, color: color);
  }
}

/// Responsive button with proper touch targets
class ResponsiveButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsets? padding;
  final double? borderRadius;
  final double? elevation;
  final Size? minimumSize;

  const ResponsiveButton({
    super.key,
    required this.child,
    this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.elevation,
    this.minimumSize,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding =
        padding ??
        ResponsiveDesignSystem.padding(
          context,
          horizontal: 16.0,
          vertical: 12.0,
        );

    final responsiveBorderRadius =
        borderRadius != null
            ? ResponsiveDesignSystem.borderRadius(context, borderRadius!)
            : ResponsiveDesignSystem.borderRadius(context, 8.0);

    final responsiveMinSize =
        minimumSize ??
        Size(
          ResponsiveDesignSystem.minTouchTarget,
          ResponsiveDesignSystem.minTouchTarget,
        );

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: responsivePadding,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(responsiveBorderRadius),
        ),
        elevation: elevation,
        minimumSize: responsiveMinSize,
      ),
      child: child,
    );
  }
}

/// Responsive list tile with proper spacing
class ResponsiveListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsets? contentPadding;

  const ResponsiveListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.contentPadding,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding =
        contentPadding ??
        ResponsiveDesignSystem.padding(
          context,
          horizontal:
              ResponsiveDesignSystem.isSmallMobile(context) ? 12.0 : 16.0,
          vertical: ResponsiveDesignSystem.isSmallMobile(context) ? 8.0 : 12.0,
        );

    return ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      contentPadding: responsivePadding,
      minVerticalPadding: ResponsiveDesignSystem.height(context, 8.0),
    );
  }
}
