import 'package:flutter/material.dart';
import 'package:fit_4_force/core/services/paystack_service.dart';
import 'package:logger/logger.dart';

/// Service for handling payments in the app
class PaymentService {
  final Logger _logger = Logger();
  final PaystackService _paystackService = PaystackService();
  bool _isInitialized = false;

  /// Initialize the payment service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // PaystackService doesn't need explicit initialization
      _isInitialized = true;
      _logger.i('Payment service initialized');
    } catch (e) {
      _logger.e('Error initializing payment service: $e');
      rethrow;
    }
  }

  /// Process a payment
  Future<Map<String, dynamic>> processPayment({
    required BuildContext context,
    required int amount,
    required String email,
    String? reference,
    String? fullName,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      final result = await _paystackService.processPayment(
        email: email,
        fullName: fullName ?? 'User',
        amount: amount / 100.0, // Convert from kobo to naira
      );

      final response = {
        'success': result['status'] == 'success',
        'reference': reference,
        'message': result['message'] ?? 'Payment processed',
        'authorization_url': result['authorization_url'],
      };

      _logger.i('Payment processed: $response');
      return response;
    } catch (e) {
      _logger.e('Error processing payment: $e');
      rethrow;
    }
  }

  /// Verify a payment
  Future<bool> verifyPayment(String reference) async {
    try {
      final result = await _paystackService.verifyTransactionStatus(reference);
      _logger.i('Payment verified: $reference - Success: $result');
      return result;
    } catch (e) {
      _logger.e('Error verifying payment: $e');
      return false;
    }
  }
}
