import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/core/widgets/user_progress_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/subscription/bloc/subscription_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/services/storage_service.dart';
import 'package:fit_4_force/shared/widgets/base_widget.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final Logger _logger = Logger();
  final ImagePicker _imagePicker = ImagePicker();
  final StorageService _storageService = StorageService();
  final UserProgressService _progressService = UserProgressService();
  bool _isUploading = false;
  Map<String, dynamic> _profileProgress = {};

  @override
  void initState() {
    super.initState();
    context.read<SubscriptionBloc>().add(CheckSubscriptionEvent());
    _loadProfileProgress();
  }

  Future<void> _loadProfileProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _profileProgress = progress['profile'] ?? {};
      });
    } catch (e) {
      // Handle error silently, profile progress will remain empty
      print('Error loading profile progress: $e');
    }
  }

  double _calculateProfileCompleteness(UserModel user) {
    double completeness = 0.0;

    // Calculate profile completeness based on available UserModel fields
    final List<bool> fields = [
      user.fullName.isNotEmpty,
      user.email.isNotEmpty,
      user.age > 0,
      user.gender.isNotEmpty,
      user.height > 0,
      user.weight > 0,
      user.targetAgency.isNotEmpty,
      user.fitnessGoal.isNotEmpty,
      user.profileImageUrl != null && user.profileImageUrl!.isNotEmpty,
      user.notificationPreferences.isNotEmpty,
    ];

    completeness = fields.where((field) => field).length / fields.length * 100;

    // Update profile completeness in UserProgressService
    _progressService.updateProgress('profile', {
      'profileCompleteness': completeness,
    });

    return completeness;
  }

  void _showImagePickerOptions(BuildContext context, UserModel user) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => SafeArea(
            child: Wrap(
              children: [
                ListTile(
                  leading: const Icon(Icons.photo_camera),
                  title: const Text('Take a photo'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _pickImage(ImageSource.camera, user);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.photo_library),
                  title: const Text('Choose from gallery'),
                  onTap: () {
                    Navigator.of(context).pop();
                    _pickImage(ImageSource.gallery, user);
                  },
                ),
                if (user.profileImageUrl != null &&
                    user.profileImageUrl!.isNotEmpty)
                  ListTile(
                    leading: const Icon(Icons.delete, color: Colors.red),
                    title: const Text(
                      'Remove photo',
                      style: TextStyle(color: Colors.red),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                      _removeProfileImage(user);
                    },
                  ),
              ],
            ),
          ),
    );
  }

  Future<void> _pickImage(ImageSource source, UserModel user) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (pickedFile == null) return;

      setState(() {
        _isUploading = true;
      });

      final File imageFile = File(pickedFile.path);

      // Store the user ID before the async gap
      String? userId;
      if (mounted) {
        final authState = context.read<AuthBloc>().state;
        userId = authState is Authenticated ? authState.user.id : null;
      }

      if (userId == null) {
        _logger.e('User ID is null');
        setState(() {
          _isUploading = false;
        });
        return;
      }

      // Upload the image to Firebase Storage
      final String? downloadUrl = await _storageService.uploadProfileImage(
        file: imageFile,
        userId: userId,
        onProgress: (progress) {
          _logger.i('Upload progress: ${(progress * 100).toStringAsFixed(2)}%');
        },
      );

      if (downloadUrl == null) {
        _logger.e('Failed to upload profile image');
        setState(() {
          _isUploading = false;
        });
        return;
      }

      setState(() {
        _isUploading = false;
      });

      // Update the user's profile image URL in Firestore
      if (mounted) {
        context.read<AuthBloc>().add(UpdateProfileImageEvent(downloadUrl));

        // Track profile update in UserProgressService
        await _progressService.updateProgress('profile', {
          'hasProfileImage': true,
          'lastProfileUpdate': DateTime.now().toIso8601String(),
        });

        // Check for achievement
        final hasProfileImage = _profileProgress['hasProfileImage'] ?? false;
        if (!hasProfileImage) {
          await _progressService.addAchievement(
            'profile',
            'Profile Picture Added',
          );
          await _loadProfileProgress();
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile image updated successfully')),
        );
      }
    } catch (e) {
      _logger.e('Error picking image: $e');
      setState(() {
        _isUploading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error updating profile image: $e')),
        );
      }
    }
  }

  Future<void> _removeProfileImage(UserModel user) async {
    try {
      if (user.profileImageUrl == null || user.profileImageUrl!.isEmpty) return;

      setState(() {
        _isUploading = true;
      });

      // Delete the image from Firebase Storage
      await _storageService.deleteFileByUrl(user.profileImageUrl!);

      setState(() {
        _isUploading = false;
      });

      // Update the user's profile image URL in Firestore
      if (mounted) {
        context.read<AuthBloc>().add(UpdateProfileImageEvent(null));
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile image removed successfully')),
        );
      }
    } catch (e) {
      _logger.e('Error removing profile image: $e');
      setState(() {
        _isUploading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error removing profile image: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('My Profile'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.of(context).pushNamed(AppRoutes.settings);
            },
          ),
        ],
      ),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, authState) {
          if (authState is Authenticated) {
            final user = authState.user;

            final layoutType = ResponsiveUtils.getLayoutType(context);
            final isLandscape = ResponsiveUtils.isLandscape(context);
            final padding = ResponsiveUtils.getResponsivePadding(context);
            final spacing = ResponsiveUtils.getResponsiveSpacing(context);

            return SingleChildScrollView(
              padding: padding,
              child:
                  layoutType == LayoutType.desktop ||
                          (layoutType == LayoutType.tabletLandscape &&
                              isLandscape)
                      ? _buildDesktopLayout(context, user, spacing)
                      : _buildMobileLayout(context, user, spacing),
            );
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildMobileLayout(
    BuildContext context,
    UserModel user,
    double spacing,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Profile avatar
        BaseAvatar(
          radius: ResponsiveUtils.getResponsiveWidth(
            context,
            mobile: 50,
            tablet: 60,
            desktop: 70,
          ),
          imageUrl: user.profileImageUrl,
          initials: _getInitials(user.fullName),
          onTap: () {
            _showImagePickerOptions(context, user);
          },
          isUploading: _isUploading,
        ),
        SizedBox(height: spacing),

        // User name
        ResponsiveText(
          user.fullName,
          mobileFontSize: 22.0,
          tabletFontSize: 24.0,
          desktopFontSize: 26.0,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: spacing / 3),

        // User email
        ResponsiveText(
          user.email,
          mobileFontSize: 14.0,
          tabletFontSize: 16.0,
          desktopFontSize: 18.0,
          color: Colors.white70,
          textAlign: TextAlign.center,
        ),
        SizedBox(height: spacing / 2),

        // Profile Completeness
        _buildProfileCompletenessCard(user, spacing),
        SizedBox(height: spacing / 2),

        // Premium badge
        if (user.isPremium) ...[
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.premiumColor.withValues(alpha: 0.2 * 255),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.workspace_premium,
                  size: 16,
                  color: AppTheme.premiumDarkColor,
                ),
                const SizedBox(width: 4),
                ResponsiveText(
                  'Premium Member',
                  mobileFontSize: 12.0,
                  tabletFontSize: 13.0,
                  desktopFontSize: 14.0,
                  color: AppTheme.premiumDarkColor,
                  fontWeight: FontWeight.bold,
                ),
              ],
            ),
          ),
        ],
        SizedBox(height: spacing),

        // Divider
        const Divider(),
        SizedBox(height: spacing / 2),

        // User info
        _buildInfoSection(context, user),
        SizedBox(height: spacing),

        // Subscription section
        _buildSubscriptionSection(context, user),
        SizedBox(height: spacing),

        // Sign out button
        ResponsiveButton(
          text: 'Sign Out',
          backgroundColor: Colors.red,
          textColor: Colors.white,
          onPressed: () {
            context.read<AuthBloc>().add(SignOutEvent());
          },
        ),
      ],
    );
  }

  Widget _buildDesktopLayout(
    BuildContext context,
    UserModel user,
    double spacing,
  ) {
    return ResponsiveMultiPaneLayout(
      primaryPane: _buildMobileLayout(context, user, spacing),
      secondaryPane: _buildSideInfo(context, user),
      primaryFlex: 2,
      secondaryFlex: 1,
    );
  }

  Widget _buildSideInfo(BuildContext context, UserModel user) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Quick Stats',
            mobileFontSize: 18.0,
            tabletFontSize: 20.0,
            desktopFontSize: 22.0,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          ResponsiveCard(
            child: Column(
              children: [
                _buildStatItem(
                  'Account Type',
                  user.isPremium ? 'Premium' : 'Free',
                ),
                const Divider(),
                _buildStatItem('Target Agency', user.targetAgency),
                const Divider(),
                _buildStatItem('Fitness Goal', user.fitnessGoal),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveText(
            label,
            mobileFontSize: 12.0,
            tabletFontSize: 13.0,
            desktopFontSize: 14.0,
            color: Colors.grey[600],
          ),
          ResponsiveText(
            value,
            mobileFontSize: 12.0,
            tabletFontSize: 13.0,
            desktopFontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context, UserModel user) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.white, // Ensure text is visible on dark background
          ),
        ),
        const SizedBox(height: 16),
        BaseCard(
          backgroundColor:
              Colors
                  .white, // Explicitly set white background for better contrast
          child: Column(
            children: [
              _buildInfoRow('Age', '${user.age} years'),
              const Divider(),
              _buildInfoRow('Gender', user.gender),
              const Divider(),
              _buildInfoRow('Height', '${user.height} cm'),
              const Divider(),
              _buildInfoRow('Weight', '${user.weight} kg'),
              const Divider(),
              _buildInfoRow('Target Agency', user.targetAgency),
              const Divider(),
              _buildInfoRow('Fitness Goal', user.fitnessGoal),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionSection(BuildContext context, UserModel user) {
    return BlocBuilder<SubscriptionBloc, SubscriptionState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color:
                    Colors.white, // Ensure text is visible on dark background
              ),
            ),
            const SizedBox(height: 16),
            BaseCard(
              backgroundColor:
                  user.isPremium
                      ? AppTheme.premiumColor.withValues(alpha: 0.1 * 255)
                      : Colors
                          .white, // Explicitly set white background for better contrast
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        user.isPremium
                            ? Icons.workspace_premium
                            : Icons.workspace_premium_outlined,
                        size: 24,
                        color:
                            user.isPremium
                                ? AppTheme.premiumDarkColor
                                : Colors.grey,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user.isPremium
                                  ? 'Premium Subscription'
                                  : 'Free Account',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color:
                                    user.isPremium
                                        ? AppTheme.premiumDarkColor
                                        : null,
                              ),
                            ),
                            if (user.isPremium &&
                                user.premiumExpiryDate != null)
                              Text(
                                'Expires on ${DateFormat('MMMM d, yyyy').format(user.premiumExpiryDate!)}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  BaseButton(
                    text:
                        user.isPremium
                            ? 'Manage Subscription'
                            : 'Upgrade to Premium',
                    icon:
                        user.isPremium
                            ? Icons.settings
                            : Icons.workspace_premium,
                    backgroundColor:
                        user.isPremium ? null : AppTheme.premiumColor,
                    onPressed: () {
                      Navigator.of(context).pushNamed(AppRoutes.premium);
                    },
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow(String label, String value) {
    IconData icon;
    switch (label) {
      case 'Age':
        icon = Icons.calendar_today;
        break;
      case 'Gender':
        icon = Icons.person;
        break;
      case 'Height':
        icon = Icons.height;
        break;
      case 'Weight':
        icon = Icons.monitor_weight;
        break;
      case 'Target Agency':
        icon = Icons.security;
        break;
      case 'Fitness Goal':
        icon = Icons.fitness_center;
        break;
      default:
        icon = Icons.info;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                color: AppTheme.textPrimaryLight,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
        ],
      ),
    );
  }

  String _getInitials(String fullName) {
    final names = fullName.split(' ');
    if (names.length >= 2) {
      return '${names[0][0]}${names[1][0]}'.toUpperCase();
    } else if (names.isNotEmpty) {
      return names[0][0].toUpperCase();
    }
    return '';
  }

  Widget _buildProfileCompletenessCard(UserModel user, double spacing) {
    final completeness = _calculateProfileCompleteness(user);

    return Container(
      margin: EdgeInsets.symmetric(horizontal: spacing),
      padding: EdgeInsets.all(spacing),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.account_circle, color: Colors.white, size: 20),
              const SizedBox(width: 8),
              ResponsiveText(
                'Profile Completeness',
                mobileFontSize: 16.0,
                tabletFontSize: 17.0,
                desktopFontSize: 18.0,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              const Spacer(),
              ResponsiveText(
                '${completeness.toInt()}%',
                mobileFontSize: 16.0,
                tabletFontSize: 17.0,
                desktopFontSize: 18.0,
                fontWeight: FontWeight.bold,
                color:
                    completeness >= 80
                        ? Colors.green
                        : completeness >= 50
                        ? Colors.orange
                        : Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 12),
          ProgressBarWidget(
            label: '',
            progress: completeness / 100,
            color:
                completeness >= 80
                    ? Colors.green
                    : completeness >= 50
                    ? Colors.orange
                    : Colors.red,
            subtitle:
                completeness < 100
                    ? 'Complete your profile to unlock more features'
                    : 'Profile completed! Great job!',
          ),
        ],
      ),
    );
  }
}
