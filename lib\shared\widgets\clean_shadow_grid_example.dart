import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_ui.dart';

/// Example of clean, minimal shadow grid layout
/// This demonstrates the improved shadow system with subtle, non-overlapping shadows
class CleanShadowGridExample extends StatelessWidget {
  const CleanShadowGridExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A237E), // Navy blue background
      appBar: AppBar(
        title: const Text('Clean Shadow Grid'),
        backgroundColor: const Color(0xFF1A237E),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildCleanCard(
              title: 'Fitness',
              subtitle: '75% Goal: 100%',
              color: Colors.green,
              icon: Icons.fitness_center,
            ),
            _buildCleanCard(
              title: 'Academics',
              subtitle: '60% Goal: 100%',
              color: Colors.blue,
              icon: Icons.school,
            ),
            _buildCleanCard(
              title: 'Daily Quiz',
              subtitle: 'Test your knowledge',
              color: Colors.purple,
              icon: Icons.quiz,
            ),
            _buildCleanCard(
              title: 'Today\'s Workout',
              subtitle: 'Stay active',
              color: Colors.green,
              icon: Icons.directions_run,
            ),
            _buildCleanCard(
              title: 'Study Materials',
              subtitle: 'Prepare for exams',
              color: Colors.orange,
              icon: Icons.book,
            ),
            _buildCleanCard(
              title: 'Community',
              subtitle: 'Connect with others',
              color: Colors.pink,
              icon: Icons.people,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCleanCard({
    required String title,
    required String subtitle,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        // CLEAN, MINIMAL SHADOW - No overlap, subtle appearance
        boxShadow: AppUI.shadowMedium, // Uses our new minimal shadow system
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.1 * 255),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with subtle background
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1 * 255),
                shape: BoxShape.circle,
                // Even more subtle shadow for inner elements
                boxShadow: AppUI.shadowSmall,
              ),
              child: Icon(
                icon,
                color: color,
                size: 32,
              ),
            ),
            const SizedBox(height: 12),
            // Title
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            // Subtitle
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Clean shadow specifications used throughout the app
class CleanShadowSpecs {
  // MINIMAL SHADOWS - Clean, flat, well-spaced appearance
  
  /// Extra subtle shadow for small elements
  static List<BoxShadow> get minimal => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.03 * 255),
      blurRadius: 2,
      offset: const Offset(0, 1),
      spreadRadius: 0,
    ),
  ];
  
  /// Standard subtle shadow for cards
  static List<BoxShadow> get standard => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.05 * 255),
      blurRadius: 4,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  /// Slightly more prominent shadow for elevated elements
  static List<BoxShadow> get elevated => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.08 * 255),
      blurRadius: 6,
      offset: const Offset(0, 3),
      spreadRadius: 0,
    ),
  ];
  
  /// Guidelines for clean shadow usage:
  /// 
  /// 1. **Blur Radius**: Keep between 2-6px for subtle appearance
  /// 2. **Offset**: Use small Y-offset (1-3px), no X-offset for consistency
  /// 3. **Color Alpha**: Very low opacity (0.03-0.08) for minimal appearance
  /// 4. **Spread Radius**: Always 0 to prevent shadow expansion
  /// 5. **Spacing**: Ensure adequate margin between cards (12-16px minimum)
  /// 6. **Background**: Use light backgrounds to make subtle shadows visible
}
