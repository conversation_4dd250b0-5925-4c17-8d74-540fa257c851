import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// Service to manage onboarding state and preferences
class OnboardingService {
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _onboardingVersionKey = 'onboarding_version';
  static const String _lastOnboardingDateKey = 'last_onboarding_date';
  static const String _onboardingSkippedKey = 'onboarding_skipped';
  static const String _hasEverUsedAppKey = 'has_ever_used_app';

  // Current onboarding version - increment this when you want to show onboarding again
  static const int currentOnboardingVersion = 1;

  /// Check if this is the first time the user has opened the app
  static Future<bool> isFirstTimeUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasEverUsedApp = prefs.getBool(_hasEverUsedAppKey) ?? false;
      return !hasEverUsedApp;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if first time user: $e');
      }
      return true; // Default to first time user if error
    }
  }

  /// Mark that the user has used the app
  static Future<void> markAppAsUsed() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_hasEverUsedAppKey, true);

      if (kDebugMode) {
        print('✅ App marked as used');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking app as used: $e');
      }
    }
  }

  /// Check if onboarding has been completed
  static Future<bool> isOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final completed = prefs.getBool(_onboardingCompletedKey) ?? false;
      final version = prefs.getInt(_onboardingVersionKey) ?? 0;

      // If the onboarding version has changed, show onboarding again
      if (version < currentOnboardingVersion) {
        return false;
      }

      return completed;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking onboarding status: $e');
      }
      return false;
    }
  }

  /// Mark onboarding as completed
  static Future<void> completeOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, true);
      await prefs.setInt(_onboardingVersionKey, currentOnboardingVersion);
      await prefs.setString(
        _lastOnboardingDateKey,
        DateTime.now().toIso8601String(),
      );
      await prefs.setBool(_onboardingSkippedKey, false);

      // Mark app as used when onboarding is completed
      await markAppAsUsed();

      if (kDebugMode) {
        print('✅ Onboarding completed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error completing onboarding: $e');
      }
      rethrow;
    }
  }

  /// Mark onboarding as skipped
  static Future<void> skipOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, true);
      await prefs.setInt(_onboardingVersionKey, currentOnboardingVersion);
      await prefs.setString(
        _lastOnboardingDateKey,
        DateTime.now().toIso8601String(),
      );
      await prefs.setBool(_onboardingSkippedKey, true);

      // Mark app as used when onboarding is skipped
      await markAppAsUsed();

      if (kDebugMode) {
        print('⏭️ Onboarding skipped successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error skipping onboarding: $e');
      }
      rethrow;
    }
  }

  /// Reset onboarding status (useful for testing)
  static Future<void> resetOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_onboardingCompletedKey);
      await prefs.remove(_onboardingVersionKey);
      await prefs.remove(_lastOnboardingDateKey);
      await prefs.remove(_onboardingSkippedKey);
      await prefs.remove(_hasEverUsedAppKey);

      if (kDebugMode) {
        print('🔄 Onboarding reset successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error resetting onboarding: $e');
      }
      rethrow;
    }
  }

  /// Force show onboarding (useful for user settings)
  static Future<void> forceShowOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, false);

      if (kDebugMode) {
        print('🔄 Onboarding will be shown on next app start');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error forcing onboarding display: $e');
      }
      rethrow;
    }
  }

  /// Check if user should see onboarding based on various conditions
  static Future<bool> shouldShowOnboarding() async {
    try {
      // First check if this is a first-time user
      final isFirstTime = await isFirstTimeUser();

      // Only show onboarding for first-time users who haven't completed it
      if (!isFirstTime) {
        return false; // Returning users never see onboarding
      }

      final completed = await isOnboardingCompleted();

      // Always show if not completed and is first time user
      if (!completed) {
        return true;
      }

      // Check if version has been updated (only for first-time users)
      final prefs = await SharedPreferences.getInstance();
      final version = prefs.getInt(_onboardingVersionKey) ?? 0;

      if (version < currentOnboardingVersion) {
        if (kDebugMode) {
          print(
            '📱 Onboarding version updated from $version to $currentOnboardingVersion',
          );
        }
        return true;
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error determining if onboarding should be shown: $e');
      }
      // Default to showing onboarding if there's an error and it's potentially a first-time user
      try {
        final isFirstTime = await isFirstTimeUser();
        return isFirstTime;
      } catch (e2) {
        return true; // Final fallback
      }
    }
  }

  /// Check if onboarding was skipped
  static Future<bool> wasOnboardingSkipped() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_onboardingSkippedKey) ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if onboarding was skipped: $e');
      }
      return false;
    }
  }

  /// Get the last onboarding completion date
  static Future<DateTime?> getLastOnboardingDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dateString = prefs.getString(_lastOnboardingDateKey);
      if (dateString != null) {
        return DateTime.parse(dateString);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting last onboarding date: $e');
      }
      return null;
    }
  }

  /// Get onboarding statistics for analytics
  static Future<Map<String, dynamic>> getOnboardingStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final completed = prefs.getBool(_onboardingCompletedKey) ?? false;
      final version = prefs.getInt(_onboardingVersionKey) ?? 0;
      final skipped = prefs.getBool(_onboardingSkippedKey) ?? false;
      final hasEverUsedApp = prefs.getBool(_hasEverUsedAppKey) ?? false;
      final lastDate = await getLastOnboardingDate();

      return {
        'completed': completed,
        'version': version,
        'current_version': currentOnboardingVersion,
        'skipped': skipped,
        'has_ever_used_app': hasEverUsedApp,
        'is_first_time_user': !hasEverUsedApp,
        'last_completion_date': lastDate?.toIso8601String(),
        'needs_update': version < currentOnboardingVersion,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting onboarding stats: $e');
      }
      return {
        'completed': false,
        'version': 0,
        'current_version': currentOnboardingVersion,
        'skipped': false,
        'has_ever_used_app': false,
        'is_first_time_user': true,
        'last_completion_date': null,
        'needs_update': true,
      };
    }
  }

  /// Save user preferences from onboarding
  static Future<void> saveOnboardingPreferences({
    String? preferredAgency,
    List<String>? interests,
    String? experienceLevel,
    bool? notificationsEnabled,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      if (preferredAgency != null) {
        await prefs.setString('onboarding_preferred_agency', preferredAgency);
      }

      if (interests != null) {
        await prefs.setStringList('onboarding_interests', interests);
      }

      if (experienceLevel != null) {
        await prefs.setString('onboarding_experience_level', experienceLevel);
      }

      if (notificationsEnabled != null) {
        await prefs.setBool(
          'onboarding_notifications_enabled',
          notificationsEnabled,
        );
      }

      if (kDebugMode) {
        print('✅ Onboarding preferences saved');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving onboarding preferences: $e');
      }
    }
  }

  /// Get saved onboarding preferences
  static Future<Map<String, dynamic>> getOnboardingPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'preferred_agency': prefs.getString('onboarding_preferred_agency'),
        'interests': prefs.getStringList('onboarding_interests') ?? [],
        'experience_level': prefs.getString('onboarding_experience_level'),
        'notifications_enabled':
            prefs.getBool('onboarding_notifications_enabled') ?? true,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting onboarding preferences: $e');
      }
      return {
        'preferred_agency': null,
        'interests': <String>[],
        'experience_level': null,
        'notifications_enabled': true,
      };
    }
  }
}
