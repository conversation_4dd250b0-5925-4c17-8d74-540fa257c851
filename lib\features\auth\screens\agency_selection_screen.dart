import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_grid_widget.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class AgencySelectionScreen extends StatefulWidget {
  const AgencySelectionScreen({super.key});

  @override
  State<AgencySelectionScreen> createState() => _AgencySelectionScreenState();
}

class _AgencySelectionScreenState extends State<AgencySelectionScreen> {
  String? _selectedAgency;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: AppTheme.primaryColor),
        title: Text(
          'Select Your Agency',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Choose the agency you are preparing for',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.textSecondaryLight,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: ResponsiveGridWidget(
                mobileColumns: 2,
                tabletColumns: 3,
                desktopColumns: 4,
                childAspectRatio:
                    ResponsiveUtils.isSmallPhone(context) ? 1.0 : 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                padding: const EdgeInsets.all(16.0),
                children:
                    AppConfig.supportedAgencies.map((agency) {
                      final isSelected = _selectedAgency == agency;
                      final agencyColor =
                          AppTheme.agencyColors[agency] ??
                          AppTheme.primaryColor;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedAgency = agency;
                          });
                        },
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          decoration: BoxDecoration(
                            color:
                                isSelected
                                    ? agencyColor.withValues(alpha: 0.2 * 255)
                                    : Colors.white,
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color:
                                  isSelected
                                      ? agencyColor
                                      : Colors.grey.shade300,
                              width: isSelected ? 2 : 1,
                            ),
                            boxShadow:
                                isSelected
                                    ? [
                                      BoxShadow(
                                        color: agencyColor.withValues(
                                          alpha: 0.3 * 255,
                                        ),
                                        blurRadius: 8,
                                        offset: const Offset(0, 4),
                                      ),
                                    ]
                                    : null,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _getAgencyIcon(agency),
                                size: 40,
                                color: agencyColor,
                              ),
                              const SizedBox(height: 12),
                              Text(
                                agency,
                                style: TextStyle(
                                  color:
                                      isSelected
                                          ? agencyColor
                                          : AppTheme.textPrimaryLight,
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                  fontSize: 16,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: BaseButton(
                text: 'Continue',
                onPressed: () {
                  if (_selectedAgency != null) {
                    Navigator.of(context).pushNamed(
                      AppRoutes.signup,
                      arguments: {'selectedAgency': _selectedAgency},
                    );
                  }
                },
                isDisabled: _selectedAgency == null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getAgencyIcon(String agency) {
    switch (agency) {
      case 'Nigerian Army':
        return Icons.military_tech;
      case 'Navy':
        return Icons.sailing;
      case 'Air Force':
        return Icons.airplanemode_active;
      case 'DSSC':
        return Icons.security;
      case 'NDA':
        return Icons.school;
      case 'NSCDC':
        return Icons.shield;
      case 'EFCC':
        return Icons.account_balance;
      case 'Fire Service':
        return Icons.local_fire_department;
      case 'Immigration':
        return Icons.badge;
      case 'Customs':
        return Icons.business_center;
      case 'FRSC':
        return Icons.directions_car;
      case 'Police (POLAC)':
        return Icons.local_police;
      default:
        return Icons.military_tech;
    }
  }
}
