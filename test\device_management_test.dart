import 'package:flutter_test/flutter_test.dart';
import 'package:fit_4_force/core/services/device_id_service.dart';
import 'package:fit_4_force/core/services/device_management_service.dart';
import 'package:fit_4_force/shared/models/user_device_model.dart';

void main() {
  group('Device Management System Tests', () {
    test('DeviceIdService should generate consistent device ID', () async {
      // Test that device ID generation is consistent
      final deviceId1 = await DeviceIdService.getDeviceId();
      final deviceId2 = await DeviceIdService.getDeviceId();
      
      expect(deviceId1, isNotEmpty);
      expect(deviceId2, isNotEmpty);
      expect(deviceId1, equals(deviceId2)); // Should be consistent
    });

    test('DeviceIdService should generate device info', () async {
      final deviceInfo = await DeviceIdService.getDeviceInfo();
      
      expect(deviceInfo, isNotNull);
      expect(deviceInfo['deviceName'], isNotEmpty);
      expect(deviceInfo['deviceType'], isNotEmpty);
      expect(deviceInfo['platform'], isNotEmpty);
    });

    test('UserDeviceModel should create from JSON correctly', () {
      final json = {
        'id': 'test-id',
        'user_id': 'user-123',
        'device_id': 'device-456',
        'device_name': 'Test Device',
        'device_type': 'phone',
        'platform': 'android',
        'is_active': true,
        'last_login': '2024-01-01T00:00:00Z',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
      };

      final device = UserDeviceModel.fromJson(json);

      expect(device.id, equals('test-id'));
      expect(device.userId, equals('user-123'));
      expect(device.deviceId, equals('device-456'));
      expect(device.deviceName, equals('Test Device'));
      expect(device.deviceType, equals('phone'));
      expect(device.platform, equals('android'));
      expect(device.isActive, isTrue);
    });

    test('UserDeviceModel should convert to JSON correctly', () {
      final device = UserDeviceModel(
        id: 'test-id',
        userId: 'user-123',
        deviceId: 'device-456',
        deviceName: 'Test Device',
        deviceType: 'phone',
        platform: 'android',
        isActive: true,
        lastLogin: DateTime.parse('2024-01-01T00:00:00Z'),
        createdAt: DateTime.parse('2024-01-01T00:00:00Z'),
        updatedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      final json = device.toJson();

      expect(json['id'], equals('test-id'));
      expect(json['user_id'], equals('user-123'));
      expect(json['device_id'], equals('device-456'));
      expect(json['device_name'], equals('Test Device'));
      expect(json['device_type'], equals('phone'));
      expect(json['platform'], equals('android'));
      expect(json['is_active'], isTrue);
    });

    test('DeviceRegistrationResponse should handle success case', () {
      final json = {
        'success': true,
        'message': 'Device registered successfully',
        'device_limit_exceeded': false,
        'active_devices': null,
      };

      final response = DeviceRegistrationResponse.fromJson(json);

      expect(response.success, isTrue);
      expect(response.message, equals('Device registered successfully'));
      expect(response.isDeviceLimitExceeded, isFalse);
      expect(response.activeDevices, isNull);
    });

    test('DeviceRegistrationResponse should handle device limit exceeded case', () {
      final json = {
        'success': false,
        'message': 'Device limit exceeded',
        'device_limit_exceeded': true,
        'active_devices': [
          {
            'id': 'device1',
            'user_id': 'user-123',
            'device_id': 'device-001',
            'device_name': 'iPhone 14',
            'device_type': 'phone',
            'platform': 'ios',
            'is_active': true,
            'last_login': '2024-01-01T00:00:00Z',
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
          },
          {
            'id': 'device2',
            'user_id': 'user-123',
            'device_id': 'device-002',
            'device_name': 'MacBook Pro',
            'device_type': 'laptop',
            'platform': 'macos',
            'is_active': true,
            'last_login': '2024-01-01T00:00:00Z',
            'created_at': '2024-01-01T00:00:00Z',
            'updated_at': '2024-01-01T00:00:00Z',
          },
        ],
      };

      final response = DeviceRegistrationResponse.fromJson(json);

      expect(response.success, isFalse);
      expect(response.message, equals('Device limit exceeded'));
      expect(response.isDeviceLimitExceeded, isTrue);
      expect(response.activeDevices, isNotNull);
      expect(response.activeDevices!.length, equals(2));
      expect(response.activeDevices![0].deviceName, equals('iPhone 14'));
      expect(response.activeDevices![1].deviceName, equals('MacBook Pro'));
    });

    test('UserDeviceModel should format last login correctly', () {
      final device = UserDeviceModel(
        id: 'test-id',
        userId: 'user-123',
        deviceId: 'device-456',
        deviceName: 'Test Device',
        deviceType: 'phone',
        platform: 'android',
        isActive: true,
        lastLogin: DateTime.now().subtract(const Duration(hours: 2)),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final formatted = device.getFormattedLastLogin();
      expect(formatted, isNotEmpty);
      expect(formatted, contains('ago')); // Should contain relative time
    });

    test('UserDeviceModel should check if current device correctly', () {
      final device = UserDeviceModel(
        id: 'test-id',
        userId: 'user-123',
        deviceId: 'current-device-id',
        deviceName: 'Test Device',
        deviceType: 'phone',
        platform: 'android',
        isActive: true,
        lastLogin: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // This would need to be mocked in a real test
      // expect(device.isCurrentDevice('current-device-id'), isTrue);
      // expect(device.isCurrentDevice('other-device-id'), isFalse);
    });

    group('Device Management Service Integration Tests', () {
      // Note: These tests would require a test Supabase instance
      // and proper mocking for actual implementation

      test('should handle network errors gracefully', () async {
        // Mock network failure scenario
        // This would test error handling in DeviceManagementService
      });

      test('should validate device registration parameters', () async {
        // Test parameter validation
        expect(() => DeviceManagementService.registerCurrentDevice(''),
            throwsArgumentError);
      });

      test('should handle concurrent device registrations', () async {
        // Test race condition handling
        // This would test the database-level constraints
      });
    });

    group('Device ID Generation Tests', () {
      test('should generate different IDs for different device characteristics', () async {
        // This would test that different devices get different IDs
        // Requires mocking device_info_plus
      });

      test('should handle missing device information gracefully', () async {
        // Test fallback behavior when device info is unavailable
      });

      test('should cache device ID properly', () async {
        // Test SharedPreferences caching
        // Requires mocking SharedPreferences
      });
    });

    group('Error Handling Tests', () {
      test('should handle malformed JSON responses', () {
        expect(() => DeviceRegistrationResponse.fromJson({}), throwsException);
      });

      test('should handle null values in device data', () {
        final json = {
          'id': null,
          'user_id': 'user-123',
          'device_id': 'device-456',
          'device_name': null,
          'device_type': 'phone',
          'platform': 'android',
          'is_active': true,
          'last_login': '2024-01-01T00:00:00Z',
          'created_at': '2024-01-01T00:00:00Z',
          'updated_at': '2024-01-01T00:00:00Z',
        };

        expect(() => UserDeviceModel.fromJson(json), throwsException);
      });
    });
  });
}
