import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:logger/logger.dart';

/// Service to handle persistent authentication state
/// Stores user session locally so users stay logged in between app launches
class PersistentAuthService {
  static const String _userKey = 'logged_in_user';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _lastLoginTimeKey = 'last_login_time';

  final Logger _logger = Logger();

  /// Save user login state
  Future<bool> saveUserSession(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert user to JSON and save
      final userJson = jsonEncode(user.toJson());
      await prefs.setString(_userKey, userJson);

      // Mark as logged in
      await prefs.setBool(_isLoggedInKey, true);

      // Save login timestamp
      await prefs.setInt(
        _lastLoginTime<PERSON>ey,
        DateTime.now().millisecondsSinceEpoch,
      );

      _logger.i('✅ User session saved for: ${user.fullName}');
      return true;
    } catch (e) {
      _logger.e('❌ Error saving user session: $e');
      return false;
    }
  }

  /// Get stored user session
  Future<UserModel?> getStoredUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if user is logged in
      final isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      if (!isLoggedIn) {
        _logger.i('ℹ️ No user session found');
        return null;
      }

      // Get user data
      final userJson = prefs.getString(_userKey);
      if (userJson == null) {
        _logger.w('⚠️ User marked as logged in but no user data found');
        await clearUserSession(); // Clean up inconsistent state
        return null;
      }

      // Parse user data
      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      final user = UserModel.fromJson(userMap);

      // Check if session is still valid (optional: expire after certain time)
      final lastLoginTime = prefs.getInt(_lastLoginTimeKey);
      if (lastLoginTime != null) {
        final sessionAge =
            DateTime.now().millisecondsSinceEpoch - lastLoginTime;
        const maxSessionAge =
            30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds

        if (sessionAge > maxSessionAge) {
          _logger.i('ℹ️ User session expired, clearing...');
          await clearUserSession();
          return null;
        }
      }

      _logger.i('✅ Retrieved stored user session: ${user.fullName}');
      return user;
    } catch (e) {
      _logger.e('❌ Error retrieving user session: $e');
      // Clear corrupted data
      await clearUserSession();
      return null;
    }
  }

  /// Check if user is logged in
  Future<bool> isUserLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_isLoggedInKey) ?? false;
    } catch (e) {
      _logger.e('❌ Error checking login status: $e');
      return false;
    }
  }

  /// Clear user session (logout)
  Future<bool> clearUserSession() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.remove(_userKey);
      await prefs.remove(_isLoggedInKey);
      await prefs.remove(_lastLoginTimeKey);

      _logger.i('✅ User session cleared');
      return true;
    } catch (e) {
      _logger.e('❌ Error clearing user session: $e');
      return false;
    }
  }

  /// Update stored user data (for profile updates, etc.)
  Future<bool> updateStoredUser(UserModel user) async {
    final isLoggedIn = await isUserLoggedIn();
    if (!isLoggedIn) {
      _logger.w('⚠️ Trying to update user data but no active session');
      return false;
    }

    return await saveUserSession(user);
  }

  /// Get last login time
  Future<DateTime?> getLastLoginTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastLoginTimeKey);
      return timestamp != null
          ? DateTime.fromMillisecondsSinceEpoch(timestamp)
          : null;
    } catch (e) {
      _logger.e('❌ Error getting last login time: $e');
      return null;
    }
  }

  /// Check if session is about to expire (within 3 days)
  Future<bool> isSessionNearExpiry() async {
    final lastLogin = await getLastLoginTime();
    if (lastLogin == null) return false;

    final daysSinceLogin = DateTime.now().difference(lastLogin).inDays;
    return daysSinceLogin >= 27; // Warn 3 days before 30-day expiry
  }
}
