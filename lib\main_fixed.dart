import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/subscription/bloc/subscription_bloc.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/services/supabase_subscription_service.dart';
import 'package:fit_4_force/shared/services/supabase_auth_service.dart'
    as shared;
import 'package:fit_4_force/shared/providers/supabase_provider.dart';
import 'package:fit_4_force/core/config/supabase_config.dart';
import 'package:fit_4_force/core/config/environment_config.dart';
import 'package:fit_4_force/core/services/backend_service_manager.dart';
import 'package:fit_4_force/core/testing/backend_test_suite.dart';
import 'package:fit_4_force/core/services/service_locator.dart';
import 'package:fit_4_force/core/services/persistent_auth_service.dart';
import 'package:fit_4_force/features/splash/widgets/splash_screen_with_onboarding.dart';
import 'package:logger/logger.dart';
import 'package:flutter/foundation.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logger
  final logger = Logger();

  // Print environment information in debug mode
  if (kDebugMode) {
    EnvironmentConfig.printEnvironmentInfo();

    // Validate environment configuration
    if (!EnvironmentConfig.validateConfiguration()) {
      logger.e('❌ Environment configuration validation failed');
    }
  }

  // Initialize Supabase with enhanced error handling
  try {
    logger.i('🚀 Initializing Supabase for ${EnvironmentConfig.appName}...');
    await SupabaseConfig.initialize();
    logger.i('✅ Supabase initialized successfully');
    logger.i('🔗 Connected to: ${SupabaseConfig.supabaseUrl}');

    // Test connection
    if (SupabaseConfig.isInitialized) {
      logger.i('🔌 Supabase connection verified');
    } else {
      logger.w('⚠️ Supabase connection could not be verified');
    }
  } catch (e) {
    logger.e('❌ Error initializing Supabase: $e');

    // In debug mode, show more detailed error information
    if (kDebugMode) {
      logger.e('🔍 Debug Info:');
      logger.e('   URL: ${SupabaseConfig.supabaseUrl}');
      logger.e('   Environment: ${EnvironmentConfig.currentEnvironment}');
      logger.e('   Stack trace: $e');
    }

    // Don't crash the app, but log the error
    // The app can still run with limited functionality
  }

  // Initialize backend services
  try {
    logger.i('🔧 Initializing backend services...');
    await BackendServiceManager().initialize();
    logger.i('✅ Backend services initialized successfully');

    // Run development tests (DEBUG MODE ONLY)
    if (kDebugMode) {
      logger.i('🧪 Running backend tests...');
      final testSuite = BackendTestSuite();
      final testResults = await testSuite.runTests();
      testSuite.logTestResults(testResults);
    }
  } catch (e) {
    logger.e('❌ Error initializing backend services: $e');
    // Continue without backend services for now
  }

  // Initialize enhanced service locator
  await setupServiceLocator();

  runApp(
    SupabaseProvider(
      client: SupabaseConfig.client,
      child: const Fit4ForceApp(),
    ),
  );
}

class Fit4ForceApp extends StatelessWidget {
  const Fit4ForceApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) {
            final authBloc = AuthBloc();
            final persistentAuth = PersistentAuthService();

            // Check for stored authentication when app starts
            Future.delayed(const Duration(milliseconds: 100), () async {
              // Check if there's a stored user session
              final storedUser = await persistentAuth.getStoredUser();

              if (storedUser != null) {
                // User has a valid stored session, restore it
                authBloc.add(AuthenticatedEvent(storedUser));
              } else {
                // No stored session, create a test user for development
                final mockUser = UserModel(
                  id: '123456789',
                  createdAt: DateTime.now(),
                  fullName: 'Test User',
                  email: '<EMAIL>',
                  age: 25,
                  gender: 'Male',
                  height: 175.0,
                  weight: 70.0,
                  targetAgency: 'Nigerian Army',
                  fitnessGoal: 'Pass fitness test',
                  isPremium: true, // Give test user premium access
                  notificationPreferences: {'push': true, 'email': true},
                  completedQuizzes: [],
                  savedWorkouts: [],
                );

                // Save the mock user as the current session
                await persistentAuth.saveUserSession(mockUser);
                authBloc.add(AuthenticatedEvent(mockUser));
              }
            });

            return authBloc;
          },
        ),
        BlocProvider(
          create:
              (context) => SubscriptionBloc(
                SupabaseSubscriptionService(
                  authService: getIt<shared.SupabaseAuthService>(),
                ),
              ),
        ),
      ],
      child: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          // Get the singleton instance
          final navigationService = NavigationService();
          if (state is Authenticated) {
            // Only navigate if we have a valid navigator state
            navigationService.navigateToAndRemoveUntil(AppRoutes.home);
          } else if (state is Unauthenticated) {
            navigationService.navigateToAndRemoveUntil(AppRoutes.login);
          }
        },
        builder: (context, state) {
          return MaterialApp(
            title: EnvironmentConfig.appName,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            debugShowCheckedModeBanner: false,

            // Set up responsive design system
            builder: (context, child) {
              return child ?? const SizedBox.shrink();
            },

            // Use the proper splash screen with onboarding
            home: const SplashScreenWithOnboarding(),

            // Set up navigation service
            navigatorKey: NavigationService().navigatorKey,

            // Define all app routes
            routes: AppRoutes.getRoutes(),

            // Handle unknown routes
            onUnknownRoute: (settings) {
              return MaterialPageRoute(
                builder:
                    (context) => Scaffold(
                      appBar: AppBar(title: const Text('Page Not Found')),
                      body: const Center(
                        child: Text('The requested page could not be found.'),
                      ),
                    ),
              );
            },
          );
        },
      ),
    );
  }
}
