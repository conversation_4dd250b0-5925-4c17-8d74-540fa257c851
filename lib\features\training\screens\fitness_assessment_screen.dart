import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class FitnessAssessmentScreen extends StatefulWidget {
  final String? militaryAgency;

  const FitnessAssessmentScreen({super.key, this.militaryAgency});

  @override
  State<FitnessAssessmentScreen> createState() =>
      _FitnessAssessmentScreenState();
}

class _FitnessAssessmentScreenState extends State<FitnessAssessmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _pushUpsController = TextEditingController();
  final _sitUpsController = TextEditingController();
  final _runTimeMinutesController = TextEditingController();
  final _runTimeSecondsController = TextEditingController();
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();

  final AuthService _authService = AuthService();
  final UserProgressService _progressService = UserProgressService();

  String _selectedAgency = 'Nigerian Army';
  bool _isLoading = false;
  Map<String, dynamic> _trainingProgress = {};

  final List<String> _agencies = [
    'Nigerian Army',
    'Nigerian Navy',
    'Nigerian Air Force',
    'Nigerian Police',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.militaryAgency != null) {
      _selectedAgency = widget.militaryAgency!;
    }
    _loadPreviousAssessment();
  }

  Future<void> _loadPreviousAssessment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load from UserProgressService
      final progress = await _progressService.loadUserProgress();
      final trainingData = progress['training'] ?? {};
      final fitnessData = progress['fitness'] ?? {};

      setState(() {
        _trainingProgress = trainingData;

        // Load previous assessment scores if available
        final assessmentScores =
            trainingData['assessmentScores'] as Map<String, dynamic>? ?? {};

        _pushUpsController.text =
            (assessmentScores['pushUps'] ?? '').toString();
        _sitUpsController.text = (assessmentScores['sitUps'] ?? '').toString();

        final runTime = assessmentScores['runTime'] as double? ?? 0.0;
        if (runTime > 0) {
          final minutes = runTime.floor();
          final seconds = ((runTime - minutes) * 60).round();
          _runTimeMinutesController.text = minutes.toString();
          _runTimeSecondsController.text = seconds.toString();
        }

        _heightController.text =
            (fitnessData['currentHeight'] ?? '').toString();
        _weightController.text =
            (fitnessData['currentWeight'] ?? '').toString();
      });
    } catch (e) {
      print('Error loading previous assessment: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _pushUpsController.dispose();
    _sitUpsController.dispose();
    _runTimeMinutesController.dispose();
    _runTimeSecondsController.dispose();
    _heightController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fitness Assessment'),
        backgroundColor: AppTheme.primaryColor,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Complete Your Fitness Assessment',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'This information will be used to create your personalized training plan.',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 24),

                      // Military Agency Selection
                      Text(
                        'Military Agency',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<String>(
                        value: _selectedAgency,
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                        ),
                        items:
                            _agencies.map((agency) {
                              return DropdownMenuItem<String>(
                                value: agency,
                                child: Text(agency),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedAgency = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 24),

                      // Physical Metrics
                      Text(
                        'Physical Metrics',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Push-ups
                      TextFormField(
                        controller: _pushUpsController,
                        decoration: InputDecoration(
                          labelText: 'Push-ups (max in 2 minutes)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.fitness_center),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter the number of push-ups';
                          }
                          final pushUps = int.tryParse(value);
                          if (pushUps == null || pushUps < 0) {
                            return 'Please enter a valid number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Sit-ups
                      TextFormField(
                        controller: _sitUpsController,
                        decoration: InputDecoration(
                          labelText: 'Sit-ups (max in 2 minutes)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.fitness_center),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter the number of sit-ups';
                          }
                          final sitUps = int.tryParse(value);
                          if (sitUps == null || sitUps < 0) {
                            return 'Please enter a valid number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Run Time
                      Text(
                        '2.4km Run Time',
                        style: Theme.of(context).textTheme.titleSmall,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _runTimeMinutesController,
                              decoration: InputDecoration(
                                labelText: 'Minutes',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Required';
                                }
                                final minutes = int.tryParse(value);
                                if (minutes == null || minutes < 0) {
                                  return 'Invalid';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: _runTimeSecondsController,
                              decoration: InputDecoration(
                                labelText: 'Seconds',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Required';
                                }
                                final seconds = int.tryParse(value);
                                if (seconds == null ||
                                    seconds < 0 ||
                                    seconds > 59) {
                                  return 'Invalid';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),

                      // Optional Metrics
                      Text(
                        'Optional Metrics',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 16),

                      // Height
                      TextFormField(
                        controller: _heightController,
                        decoration: InputDecoration(
                          labelText: 'Height (cm)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.height),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final height = double.tryParse(value);
                            if (height == null || height <= 0) {
                              return 'Please enter a valid height';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Weight
                      TextFormField(
                        controller: _weightController,
                        decoration: InputDecoration(
                          labelText: 'Weight (kg)',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.monitor_weight),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value != null && value.isNotEmpty) {
                            final weight = double.tryParse(value);
                            if (weight == null || weight <= 0) {
                              return 'Please enter a valid weight';
                            }
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 32),

                      // Submit Button
                      SizedBox(
                        width: double.infinity,
                        child: BaseButton(
                          text: 'Create My Training Plan',
                          onPressed: _submitAssessment,
                          backgroundColor: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Future<void> _submitAssessment() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _authService.currentUserId;
      if (userId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You must be logged in to submit assessment'),
          ),
        );
        return;
      }

      // Parse form values
      final pushUps = int.tryParse(_pushUpsController.text) ?? 0;
      final sitUps = int.tryParse(_sitUpsController.text) ?? 0;

      final minutes = int.tryParse(_runTimeMinutesController.text) ?? 0;
      final seconds = int.tryParse(_runTimeSecondsController.text) ?? 0;
      final runTime = minutes + (seconds / 60.0);

      final height = double.tryParse(_heightController.text) ?? 0.0;
      final weight = double.tryParse(_weightController.text) ?? 0.0;

      // Calculate fitness score based on performance
      final fitnessScore = _calculateFitnessScore(pushUps, sitUps, runTime);

      // Save assessment to UserProgressService
      await _progressService.updateProgress('training', {
        'assessmentScores': {
          'pushUps': pushUps,
          'sitUps': sitUps,
          'runTime': runTime,
          'fitnessScore': fitnessScore,
          'agency': _selectedAgency,
          'assessmentDate': DateTime.now().toIso8601String(),
        },
        'lastAssessmentDate': DateTime.now().toIso8601String(),
      });

      // Update fitness data
      await _progressService.updateProgress('fitness', {
        'currentHeight': height,
        'currentWeight': weight,
        'fitnessScore': fitnessScore,
        'lastWorkoutDate': DateTime.now().toIso8601String(),
      });

      // Check for achievements
      final previousAssessments =
          (_trainingProgress['assessmentScores'] as Map<String, dynamic>?)
              ?.length ??
          0;
      if (previousAssessments == 0) {
        await _progressService.addAchievement(
          'training',
          'First Fitness Assessment',
        );
      }

      if (fitnessScore >= 80) {
        await _progressService.addAchievement(
          'training',
          'Fitness Excellence - 80+ Score',
        );
      } else if (fitnessScore >= 60) {
        await _progressService.addAchievement(
          'training',
          'Good Fitness Level - 60+ Score',
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Assessment saved! Your fitness score: ${fitnessScore.toInt()}',
            ),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back or to training plan
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving assessment: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  double _calculateFitnessScore(int pushUps, int sitUps, double runTime) {
    // Simple fitness scoring algorithm
    double score = 0.0;

    // Push-ups scoring (out of 30 points)
    if (pushUps >= 50) {
      score += 30;
    } else if (pushUps >= 40)
      score += 25;
    else if (pushUps >= 30)
      score += 20;
    else if (pushUps >= 20)
      score += 15;
    else if (pushUps >= 10)
      score += 10;
    else
      score += 5;

    // Sit-ups scoring (out of 30 points)
    if (sitUps >= 60) {
      score += 30;
    } else if (sitUps >= 50)
      score += 25;
    else if (sitUps >= 40)
      score += 20;
    else if (sitUps >= 30)
      score += 15;
    else if (sitUps >= 20)
      score += 10;
    else
      score += 5;

    // Run time scoring (out of 40 points) - assuming 2-mile run
    if (runTime <= 12) {
      score += 40; // Under 12 minutes
    } else if (runTime <= 14)
      score += 35; // 12-14 minutes
    else if (runTime <= 16)
      score += 30; // 14-16 minutes
    else if (runTime <= 18)
      score += 25; // 16-18 minutes
    else if (runTime <= 20)
      score += 20; // 18-20 minutes
    else if (runTime <= 25)
      score += 15; // 20-25 minutes
    else
      score += 10; // Over 25 minutes

    return score.clamp(0.0, 100.0);
  }
}
