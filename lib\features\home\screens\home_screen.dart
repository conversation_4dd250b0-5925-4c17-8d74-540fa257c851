import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/community/screens/community_screen.dart';
import 'package:fit_4_force/features/fitness/screens/fitness_screen.dart';
import 'package:fit_4_force/features/home/<USER>/dashboard_screen.dart';
import 'package:fit_4_force/features/prep/screens/prep_dashboard_screen.dart';
import 'package:fit_4_force/features/profile/screens/profile_screen.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:fit_4_force/shared/widgets/app_drawer.dart';
import 'package:fit_4_force/shared/widgets/email_verification_banner.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int currentIndex = 0;
  final List<String> tabTitles = [
    'Dashboard',
    'Prep',
    'Fitness',
    'Community',
    'Profile',
  ];

  void _changeTab(int index) {
    setState(() {
      currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is Authenticated) {
          final user = state.user;
          return Scaffold(
            backgroundColor: AppTheme.backgroundLight,
            appBar: AppBar(
              title: Text(tabTitles[currentIndex]),
              actions: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  onPressed: () {
                    Navigator.of(context).pushNamed(AppRoutes.notifications);
                  },
                ),
              ],
            ),
            drawer: AppDrawer(
              user: user,
              onTabChange: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
            ),
            body: Column(
              children: [
                // Email verification banner for unverified users
                const EmailVerificationBanner(),
                // Main content
                Expanded(
                  child: IndexedStack(
                    index: currentIndex,
                    children: [
                      DashboardScreen(user: user, onTabChange: _changeTab),
                      PrepDashboardScreen(user: user),
                      FitnessScreen(user: user),
                      CommunityScreen(user: user),
                      ProfileScreen(),
                    ],
                  ),
                ),
              ],
            ),
            bottomNavigationBar: buildResponsiveBottomNavigation(context),
          );
        }

        // Show a simple loading indicator instead of blocking
        return Scaffold(
          backgroundColor: AppTheme.backgroundLight,
          body: const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Loading your dashboard...'),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildResponsiveBottomNavigation(BuildContext context) {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);

    return BottomNavigationBar(
      currentIndex: currentIndex,
      onTap: (index) {
        setState(() {
          currentIndex = index;
        });
      },
      type: BottomNavigationBarType.fixed,
      selectedFontSize: isSmallScreen ? 10.0 : (isLandscape ? 11.0 : 12.0),
      unselectedFontSize: isSmallScreen ? 9.0 : (isLandscape ? 10.0 : 11.0),
      iconSize: isSmallScreen ? 20.0 : (isLandscape ? 22.0 : 24.0),
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard_outlined),
          activeIcon: const Icon(Icons.dashboard),
          label: isLandscape ? 'Home' : 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.menu_book_outlined),
          activeIcon: const Icon(Icons.menu_book),
          label: 'Prep',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.fitness_center_outlined),
          activeIcon: const Icon(Icons.fitness_center),
          label: 'Fitness',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.forum_outlined),
          activeIcon: const Icon(Icons.forum),
          label: isLandscape ? 'Chat' : 'Community',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.person_outline),
          activeIcon: const Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }

  Widget buildBody(UserModel user) {
    // This is no longer needed with IndexedStack approach
    // But keeping for backward compatibility if needed
    return Container();
  }

  Widget buildPrepTab(UserModel user) {
    return PrepDashboardScreen(user: user);
  }

  Widget buildFitnessTab(UserModel user) {
    // Allow all users to access fitness screen, but with limited functionality for non-premium users
    return FitnessScreen(user: user);
  }
}
