import 'package:flutter/material.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';

/// Service for managing workouts
class WorkoutService {
  // Singleton instance
  static final WorkoutService _instance = WorkoutService._internal();

  factory WorkoutService() {
    return _instance;
  }

  WorkoutService._internal();

  // Mock data for workouts
  final List<WorkoutModel> _workouts = [
    // Fat Loss / HIIT
    WorkoutModel(
      id: 'hiit1',
      name: 'Fat Loss HIIT',
      description: 'High-intensity interval training for maximum calorie burn',
      imageUrl: 'assets/images/workouts/hiit.jpg',
      category: 'Fat Loss / HIIT',
      duration: 30,
      calories: 400,
      exercises: [
        ExerciseModel(
          id: 'hiit1',
          name: 'Jumping Jacks',
          description: 'A classic cardio exercise to warm up',
          imageUrl: 'assets/images/exercises/jumping_jacks.png',
          videoUrl: 'https://example.com/videos/jumping_jacks.mp4',
          duration: 60,
          sets: 3,
          reps: 20,
          restTime: 20,
        ),
        ExerciseModel(
          id: 'hiit2',
          name: 'Burpees',
          description: 'Full-body explosive movement',
          imageUrl: 'assets/images/exercises/burpees.png',
          videoUrl: 'https://example.com/videos/burpees.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
      ],
      icon: Icons.local_fire_department,
      color: Colors.red,
    ),
    ...List.generate(
      25,
      (i) => WorkoutModel(
        id: 'hiit${i + 2}',
        name: 'HIIT Blast ${i + 2}',
        description: 'Intense HIIT session #${i + 2}',
        imageUrl: 'assets/images/workouts/hiit.jpg',
        category: 'Fat Loss / HIIT',
        duration: 25 + (i % 10),
        calories: 350 + (i * 5),
        exercises: [
          ExerciseModel(
            id: 'hiit${i + 2}_1',
            name: 'Mountain Climbers',
            description: 'Fast-paced cardio',
            imageUrl: 'assets/images/exercises/mountain_climbers.png',
            videoUrl: 'https://example.com/videos/mountain_climbers.mp4',
            duration: 60,
            sets: 3,
            reps: 20,
            restTime: 20,
          ),
          ExerciseModel(
            id: 'hiit${i + 2}_2',
            name: 'High Knees',
            description: 'Drive knees up quickly',
            imageUrl: 'assets/images/exercises/high_knees.png',
            videoUrl: 'https://example.com/videos/high_knees.mp4',
            duration: 60,
            sets: 3,
            reps: 20,
            restTime: 20,
          ),
        ],
        icon: Icons.local_fire_department,
        color: Colors.red,
      ),
    ),
    // Strength
    WorkoutModel(
      id: 'strength1',
      name: 'Full Body Strength',
      description: 'A complete workout targeting all major muscle groups',
      imageUrl: 'assets/images/workouts/full_body.jpg',
      category: 'Strength',
      duration: 45,
      calories: 350,
      exercises: [
        ExerciseModel(
          id: 's1',
          name: 'Push-ups',
          description: 'Basic push-up exercise',
          imageUrl: 'assets/images/exercises/pushups.jpg',
          videoUrl: 'https://example.com/videos/pushups.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
        ExerciseModel(
          id: 's2',
          name: 'Squats',
          description: 'Basic squat exercise',
          imageUrl: 'assets/images/exercises/squats.jpg',
          videoUrl: 'https://example.com/videos/squats.mp4',
          duration: 60,
          sets: 3,
          reps: 15,
          restTime: 30,
        ),
      ],
      icon: Icons.fitness_center,
      color: Colors.orange,
    ),
    ...List.generate(
      25,
      (i) => WorkoutModel(
        id: 'strength${i + 2}',
        name: 'Strength Builder ${i + 2}',
        description: 'Strength workout #${i + 2}',
        imageUrl: 'assets/images/workouts/strength.jpg',
        category: 'Strength',
        duration: 40 + (i % 10),
        calories: 300 + (i * 6),
        exercises: [
          ExerciseModel(
            id: 'strength${i + 2}_1',
            name: 'Bench Press',
            description: 'Chest and triceps',
            imageUrl: 'assets/images/exercises/bench_press.png',
            videoUrl: 'https://example.com/videos/bench_press.mp4',
            duration: 60,
            sets: 3,
            reps: 12,
            restTime: 40,
          ),
          ExerciseModel(
            id: 'strength${i + 2}_2',
            name: 'Deadlift',
            description: 'Back and legs',
            imageUrl: 'assets/images/exercises/deadlift.png',
            videoUrl: 'https://example.com/videos/deadlift.mp4',
            duration: 60,
            sets: 3,
            reps: 10,
            restTime: 50,
          ),
        ],
        icon: Icons.fitness_center,
        color: Colors.orange,
      ),
    ),
    // Military Fitness
    WorkoutModel(
      id: 'military1',
      name: 'Military Endurance',
      description: 'Endurance workout inspired by military training',
      imageUrl: 'assets/images/workouts/military.jpg',
      category: 'Military Fitness',
      duration: 40,
      calories: 380,
      exercises: [
        ExerciseModel(
          id: 'm1',
          name: 'Push-ups',
          description: 'Military-style push-ups',
          imageUrl: 'assets/images/exercises/pushups.jpg',
          videoUrl: 'https://example.com/videos/pushups.mp4',
          duration: 60,
          sets: 4,
          reps: 20,
          restTime: 30,
        ),
        ExerciseModel(
          id: 'm2',
          name: '2.4km Run',
          description: 'Timed run for endurance',
          imageUrl: 'assets/images/exercises/running.jpg',
          videoUrl: 'https://example.com/videos/running.mp4',
          duration: 900,
          sets: 1,
          reps: 1,
          restTime: 0,
        ),
      ],
      icon: Icons.military_tech,
      color: Colors.blue,
    ),
    ...List.generate(
      25,
      (i) => WorkoutModel(
        id: 'military${i + 2}',
        name: 'Military Drill ${i + 2}',
        description: 'Military fitness workout #${i + 2}',
        imageUrl: 'assets/images/workouts/military.jpg',
        category: 'Military Fitness',
        duration: 35 + (i % 10),
        calories: 320 + (i * 7),
        exercises: [
          ExerciseModel(
            id: 'military${i + 2}_1',
            name: 'Chin-ups',
            description: 'Upper body strength',
            imageUrl: 'assets/images/exercises/chinups.png',
            videoUrl: 'https://example.com/videos/chinups.mp4',
            duration: 60,
            sets: 3,
            reps: 10,
            restTime: 40,
          ),
          ExerciseModel(
            id: 'military${i + 2}_2',
            name: 'Shuttle Run',
            description: 'Speed and agility',
            imageUrl: 'assets/images/exercises/shuttle_run.png',
            videoUrl: 'https://example.com/videos/shuttle_run.mp4',
            duration: 60,
            sets: 3,
            reps: 5,
            restTime: 30,
          ),
        ],
        icon: Icons.military_tech,
        color: Colors.blue,
      ),
    ),
    // Core and Flexibility
    WorkoutModel(
      id: 'core1',
      name: 'Core & Flexibility',
      description: 'Core strength and flexibility routine',
      imageUrl: 'assets/images/workouts/core.jpg',
      category: 'Core and Flexibility',
      duration: 25,
      calories: 200,
      exercises: [
        ExerciseModel(
          id: 'c1',
          name: 'Crunches',
          description: 'Basic crunch exercise',
          imageUrl: 'assets/images/exercises/crunches.jpg',
          videoUrl: 'https://example.com/videos/crunches.mp4',
          duration: 60,
          sets: 3,
          reps: 20,
          restTime: 30,
        ),
        ExerciseModel(
          id: 'c2',
          name: 'Plank',
          description: 'Hold plank position',
          imageUrl: 'assets/images/exercises/plank.jpg',
          videoUrl: 'https://example.com/videos/plank.mp4',
          duration: 60,
          sets: 3,
          reps: 1,
          restTime: 30,
        ),
        ExerciseModel(
          id: 'c3',
          name: 'Hamstring Stretch',
          description: 'Stretch for flexibility',
          imageUrl: 'assets/images/exercises/hamstring_stretch.jpg',
          videoUrl: 'https://example.com/videos/hamstring_stretch.mp4',
          duration: 60,
          sets: 2,
          reps: 1,
          restTime: 20,
        ),
      ],
      icon: Icons.accessibility_new,
      color: Colors.green,
    ),
    ...List.generate(
      25,
      (i) => WorkoutModel(
        id: 'core${i + 2}',
        name: 'Core Flex ${i + 2}',
        description: 'Core and flexibility workout #${i + 2}',
        imageUrl: 'assets/images/workouts/core.jpg',
        category: 'Core and Flexibility',
        duration: 20 + (i % 10),
        calories: 180 + (i * 4),
        exercises: [
          ExerciseModel(
            id: 'core${i + 2}_1',
            name: 'Russian Twists',
            description: 'Oblique and core strength',
            imageUrl: 'assets/images/exercises/russian_twists.png',
            videoUrl: 'https://example.com/videos/russian_twists.mp4',
            duration: 60,
            sets: 3,
            reps: 20,
            restTime: 30,
          ),
          ExerciseModel(
            id: 'core${i + 2}_2',
            name: 'Cobra Stretch',
            description: 'Flexibility and back stretch',
            imageUrl: 'assets/images/exercises/cobra_stretch.png',
            videoUrl: 'https://example.com/videos/cobra_stretch.mp4',
            duration: 60,
            sets: 2,
            reps: 1,
            restTime: 20,
          ),
        ],
        icon: Icons.accessibility_new,
        color: Colors.green,
      ),
    ),
  ];

  // Mock data for workout categories
  final List<WorkoutCategoryModel> _categories = [
    WorkoutCategoryModel(
      id: '1',
      name: 'Fat Loss / HIIT',
      description: 'High-intensity workouts for fat loss and conditioning',
      icon: Icons.local_fire_department,
      color: Colors.red,
      imageUrl: 'assets/images/categories/hiit.jpg',
      workoutCount: 26,
    ),
    WorkoutCategoryModel(
      id: '2',
      name: 'Strength',
      description: 'Full body and muscle-building strength workouts',
      icon: Icons.fitness_center,
      color: Colors.orange,
      imageUrl: 'assets/images/categories/strength.jpg',
      workoutCount: 26,
    ),
    WorkoutCategoryModel(
      id: '3',
      name: 'Military Fitness',
      description: 'Military-inspired training for endurance and power',
      icon: Icons.military_tech,
      color: Colors.blue,
      imageUrl: 'assets/images/categories/military.jpg',
      workoutCount: 26,
    ),
    WorkoutCategoryModel(
      id: '4',
      name: 'Core and Flexibility',
      description: 'Core strength, stretching, and flexibility routines',
      icon: Icons.accessibility_new,
      color: Colors.green,
      imageUrl: 'assets/images/categories/core.jpg',
      workoutCount: 26,
    ),
  ];

  // Mock data for workout plans
  final List<WorkoutPlanModel> _plans = [
    WorkoutPlanModel(
      id: '1',
      name: 'Basic Training',
      description: 'Prepare for military physical fitness test',
      imageUrl: 'assets/images/plans/basic_training.jpg',
      duration: '4 weeks',
      level: 'Beginner',
      color: Colors.blue,
      workouts: [],
    ),
    WorkoutPlanModel(
      id: '2',
      name: 'Advanced Strength',
      description: 'Build strength and endurance for military service',
      imageUrl: 'assets/images/plans/advanced_strength.jpg',
      duration: '6 weeks',
      level: 'Intermediate',
      color: Colors.orange,
      workouts: [],
    ),
    WorkoutPlanModel(
      id: '3',
      name: 'Elite Performance',
      description: 'Advanced training for special forces candidates',
      imageUrl: 'assets/images/plans/elite_performance.jpg',
      duration: '8 weeks',
      level: 'Advanced',
      color: Colors.red,
      workouts: [],
      isPremium: true,
    ),
    WorkoutPlanModel(
      id: '4',
      name: 'Tactical Fitness',
      description: 'Functional fitness for military operations',
      imageUrl: 'assets/images/plans/tactical_fitness.jpg',
      duration: '6 weeks',
      level: 'Intermediate',
      color: Colors.purple,
      workouts: [],
    ),
  ];

  // Mock data for workout history
  final List<WorkoutHistoryModel> _history = [
    WorkoutHistoryModel(
      id: '1',
      workoutId: '2',
      workoutName: 'Upper Body Strength',
      date: DateTime.now().subtract(const Duration(days: 1)),
      duration: 30,
      calories: 250,
      icon: Icons.fitness_center,
      color: Colors.blue,
    ),
    WorkoutHistoryModel(
      id: '2',
      workoutId: '3',
      workoutName: '5K Run',
      date: DateTime.now().subtract(const Duration(days: 2)),
      duration: 25,
      calories: 300,
      icon: Icons.directions_run,
      color: Colors.green,
    ),
    WorkoutHistoryModel(
      id: '3',
      workoutId: '4',
      workoutName: 'Core Workout',
      date: DateTime.now().subtract(const Duration(days: 3)),
      duration: 20,
      calories: 200,
      icon: Icons.accessibility_new,
      color: Colors.orange,
    ),
  ];

  // Get all workouts
  List<WorkoutModel> getAllWorkouts() {
    return List.from(_workouts);
  }

  // Get workout by ID
  WorkoutModel? getWorkoutById(String id) {
    try {
      return _workouts.firstWhere((workout) => workout.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get workouts by category
  List<WorkoutModel> getWorkoutsByCategory(String category) {
    return _workouts.where((workout) => workout.category == category).toList();
  }

  // Search workouts
  List<WorkoutModel> searchWorkouts(String query) {
    final lowercaseQuery = query.toLowerCase();
    return _workouts.where((workout) {
      return workout.name.toLowerCase().contains(lowercaseQuery) ||
          workout.description.toLowerCase().contains(lowercaseQuery) ||
          workout.category.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  // Get all categories
  List<WorkoutCategoryModel> getAllCategories() {
    return List.from(_categories);
  }

  // Get category by ID
  WorkoutCategoryModel? getCategoryById(String id) {
    try {
      return _categories.firstWhere((category) => category.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get all plans
  List<WorkoutPlanModel> getAllPlans() {
    return List.from(_plans);
  }

  // Get plan by ID
  WorkoutPlanModel? getPlanById(String id) {
    try {
      return _plans.firstWhere((plan) => plan.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get workout history
  List<WorkoutHistoryModel> getWorkoutHistory() {
    return List.from(_history);
  }

  // Add workout to history
  void addWorkoutToHistory(WorkoutHistoryModel workout) {
    _history.add(workout);
  }

  // Create custom workout
  WorkoutModel createCustomWorkout({
    required String name,
    required String description,
    required String category,
    required List<ExerciseModel> exercises,
    required IconData icon,
    required Color color,
  }) {
    final id = DateTime.now().millisecondsSinceEpoch.toString();

    // Calculate duration and calories
    int totalDuration = 0;
    for (var exercise in exercises) {
      totalDuration += (exercise.duration * exercise.sets) ~/ 60;
      totalDuration += (exercise.restTime * (exercise.sets - 1)) ~/ 60;
    }

    // Estimate calories (simplified calculation)
    final calories = totalDuration * 8;

    final workout = WorkoutModel(
      id: id,
      name: name,
      description: description,
      imageUrl: 'assets/images/workouts/custom.jpg',
      category: category,
      duration: totalDuration,
      calories: calories,
      exercises: exercises,
      icon: icon,
      color: color,
    );

    _workouts.add(workout);
    return workout;
  }
}
