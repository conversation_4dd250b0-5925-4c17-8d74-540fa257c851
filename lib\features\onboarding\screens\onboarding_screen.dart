import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  final List<OnboardingPage> _pages = [
    OnboardingPage(
      title: '🪖 Train Smart. Serve Strong.',
      description:
          'Your journey to joining Nigeria\'s Military or Paramilitary forces starts here. Fit4Force is your all-in-one preparation companion.',
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF1E3A8A), // Deep blue
          Color(0xFF3B82F6), // Bright blue
          Color(0xFFDBEAFE), // Light blue
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    ),
    OnboardingPage(
      title: '📚 Work Out & Study — All in One Place',
      description:
          'From home workout routines to past questions, mock tests, and mental fitness drills — we\'ve got you covered.',
      gradient: const LinearGradient(
        begin: Alignment.topRight,
        end: Alignment.bottomLeft,
        colors: [
          Color(0xFF2563EB), // Blue
          Color(0xFF60A5FA), // Light blue
          Color(0xFFFFFFFF), // White
        ],
        stops: [0.0, 0.6, 1.0],
      ),
    ),
    OnboardingPage(
      title: '👥 You\'re Not Alone',
      description:
          'Connect with other military aspirants. Share tips, ask questions, and get inspired in our community forum.',
      gradient: const LinearGradient(
        begin: Alignment.bottomLeft,
        end: Alignment.topRight,
        colors: [
          Color(0xFF1D4ED8), // Deep blue
          Color(0xFF93C5FD), // Light blue
          Color(0xFFF8FAFC), // Very light blue/white
        ],
        stops: [0.0, 0.7, 1.0],
      ),
    ),
    OnboardingPage(
      title: '🔓 Go Premium. Get Ahead.',
      description:
          'Access exclusive materials, expert guides, video lessons, and career mentorship',
      gradient: const LinearGradient(
        begin: Alignment.center,
        end: Alignment.bottomCenter,
        colors: [
          Color(0xFFFFFFFF), // White
          Color(0xFFDDD6FE), // Light purple-blue
          Color(0xFF4338CA), // Deep blue
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600), // Reduced from 800
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800), // Reduced from 1200
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 700), // Reduced from 1000
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2), // Reduced from 0.3
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _slideController,
        curve: Curves.easeOutQuart, // Changed from elasticOut to lighter curve
      ),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.9, // Reduced from 0.8
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _scaleController,
        curve: Curves.easeOutBack, // Changed from elasticOut to lighter curve
      ),
    );

    _startAnimations();
  }

  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  void _resetAnimations() {
    _fadeController.reset();
    _slideController.reset();
    _scaleController.reset();
    _startAnimations();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  void _skipToEnd() {
    Navigator.of(context).pushReplacementNamed(AppRoutes.login);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _pages.length,
                onPageChanged: (int page) {
                  setState(() {
                    _currentPage = page;
                  });
                  _resetAnimations();
                },
                itemBuilder: (context, index) {
                  return _buildPage(_pages[index]);
                },
              ),
            ),
            _buildPageIndicator(),
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (_currentPage > 0)
                    TextButton(
                      onPressed: () {
                        _pageController.previousPage(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: Text(
                        'Back',
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  else
                    const SizedBox(width: 80),
                  BaseButton(
                    text:
                        _currentPage == _pages.length - 1
                            ? 'Get Started'
                            : 'Next',
                    onPressed: _nextPage,
                    width: 150,
                  ),
                  if (_currentPage < _pages.length - 1)
                    TextButton(
                      onPressed: _skipToEnd,
                      child: Text(
                        'Skip',
                        style: TextStyle(
                          color: AppTheme.textSecondaryLight,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  else
                    const SizedBox(width: 80),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPage(OnboardingPage page) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);

    return AnimatedBuilder(
      animation: Listenable.merge([
        _fadeController,
        _slideController,
        _scaleController,
      ]),
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(gradient: page.gradient),
          child: Padding(
            padding: padding,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated floating geometric shapes background
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background floating shapes - Reduced complexity
                    ...List.generate(4, (index) {
                      // Reduced from 8 to 4
                      return Positioned(
                        left: (index % 2 == 0 ? 50.0 : 200.0) + (index * 30),
                        top: (index % 3 == 0 ? 100.0 : 300.0) + (index * 20),
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Transform.scale(
                            scale:
                                _scaleAnimation.value * (0.8 + (index * 0.1)),
                            child: Container(
                              width: 20 + (index * 5),
                              height: 20 + (index * 5),
                              decoration: BoxDecoration(
                                shape:
                                    index % 2 == 0
                                        ? BoxShape.circle
                                        : BoxShape.rectangle,
                                color: Colors.white.withOpacity(
                                  0.1 + (index * 0.05),
                                ),
                                borderRadius:
                                    index % 2 == 0
                                        ? null
                                        : BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),

                    // Main content
                    SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: Container(
                            constraints: BoxConstraints(
                              maxWidth: ResponsiveUtils.getResponsiveWidth(
                                context,
                                mobile: 350,
                                tablet: 500,
                                desktop: 600,
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Title with enhanced animation
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24.0,
                                    vertical: 16.0,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.9),
                                    borderRadius: BorderRadius.circular(20),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.1),
                                        blurRadius: 20,
                                        offset: const Offset(0, 10),
                                      ),
                                    ],
                                  ),
                                  child: ResponsiveText(
                                    page.title,
                                    mobileFontSize: isSmallScreen ? 24.0 : 28.0,
                                    tabletFontSize: 32.0,
                                    desktopFontSize: 36.0,
                                    color: const Color(0xFF1E3A8A),
                                    fontWeight: FontWeight.bold,
                                    textAlign: TextAlign.center,
                                  ),
                                ),

                                SizedBox(height: spacing * 1.5),

                                // Description with glass morphism effect
                                Container(
                                  padding: const EdgeInsets.all(20.0),
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16.0,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withOpacity(0.3),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 15,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: ResponsiveText(
                                    page.description,
                                    mobileFontSize: isSmallScreen ? 16.0 : 18.0,
                                    tabletFontSize: 20.0,
                                    desktopFontSize: 22.0,
                                    color: Colors.white,
                                    textAlign: TextAlign.center,
                                    fontWeight: FontWeight.w500,
                                    style: const TextStyle(height: 1.5),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPageIndicator() {
    final spacing = ResponsiveUtils.getResponsiveSpacing(context);
    final indicatorSize = ResponsiveUtils.isSmallPhone(context) ? 8.0 : 10.0;

    return Padding(
      padding: EdgeInsets.symmetric(vertical: spacing),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(
          _pages.length,
          (index) => Container(
            width: indicatorSize,
            height: indicatorSize,
            margin: const EdgeInsets.symmetric(horizontal: 4),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color:
                  _currentPage == index
                      ? AppTheme.primaryColor
                      : AppTheme.primaryColor.withValues(alpha: 0.3 * 255),
            ),
          ),
        ),
      ),
    );
  }
}

class OnboardingPage {
  final String title;
  final String description;
  final LinearGradient gradient;

  OnboardingPage({
    required this.title,
    required this.description,
    required this.gradient,
  });
}
