import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/screens/workout_day_screen.dart';

class CreateWorkoutPlanScreen extends StatefulWidget {
  const CreateWorkoutPlanScreen({super.key});

  @override
  State<CreateWorkoutPlanScreen> createState() =>
      _CreateWorkoutPlanScreenState();
}

class _CreateWorkoutPlanScreenState extends State<CreateWorkoutPlanScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  String _selectedLevel = 'Beginner';
  String _selectedDuration = '4 weeks';
  final List<String> _selectedExercises = [];
  final List<WorkoutModel> _workouts = [];
  int _currentStep = 0;

  final List<String> _levels = ['Beginner', 'Intermediate', 'Advanced'];
  final List<String> _durations = [
    '2 weeks',
    '4 weeks',
    '6 weeks',
    '8 weeks',
    '12 weeks',
  ];

  // Enhanced exercise library with detailed information
  final List<Map<String, dynamic>> _exerciseLibrary = [
    {
      'id': 'ex1',
      'name': 'Push-Ups',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/pushups.png',
      'muscleGroups': ['chest', 'shoulders', 'arms'],
      'difficulty': 'Beginner',
      'equipment': 'Bodyweight',
      'description':
          'Classic upper body exercise targeting chest, shoulders, and triceps',
    },
    {
      'id': 'ex2',
      'name': 'Jumping Jacks',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/jumping_jacks.png',
      'muscleGroups': ['full body'],
      'difficulty': 'Beginner',
      'equipment': 'Bodyweight',
      'description': 'Full-body cardio exercise to increase heart rate',
    },
    {
      'id': 'ex3',
      'name': 'Squats',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/squats.png',
      'muscleGroups': ['legs', 'glutes'],
      'difficulty': 'Beginner',
      'equipment': 'Bodyweight',
      'description':
          'Fundamental lower body exercise for leg and glute strength',
    },
    {
      'id': 'ex4',
      'name': 'Plank',
      'category': 'Core',
      'imageUrl': 'assets/images/exercises/plank.png',
      'muscleGroups': ['core', 'abs'],
      'difficulty': 'Beginner',
      'equipment': 'Bodyweight',
      'description': 'Isometric core exercise for stability and strength',
    },
    {
      'id': 'ex5',
      'name': 'Lunges',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/lunges.png',
      'muscleGroups': ['legs', 'glutes'],
      'difficulty': 'Intermediate',
      'equipment': 'Bodyweight',
      'description': 'Unilateral leg exercise for balance and strength',
    },
    {
      'id': 'ex6',
      'name': 'Mountain Climbers',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/mountain_climbers.png',
      'muscleGroups': ['core', 'shoulders', 'legs'],
      'difficulty': 'Intermediate',
      'equipment': 'Bodyweight',
      'description':
          'Dynamic cardio exercise targeting core and cardiovascular system',
    },
    {
      'id': 'ex7',
      'name': 'Crunches',
      'category': 'Core',
      'imageUrl': 'assets/images/exercises/crunches.png',
      'muscleGroups': ['core', 'abs'],
      'difficulty': 'Beginner',
      'equipment': 'Bodyweight',
      'description': 'Basic abdominal exercise for core strength',
    },
    {
      'id': 'ex8',
      'name': 'Burpees',
      'category': 'Cardio',
      'imageUrl': 'assets/images/exercises/burpees.png',
      'muscleGroups': ['full body'],
      'difficulty': 'Advanced',
      'equipment': 'Bodyweight',
      'description':
          'High-intensity full-body exercise combining strength and cardio',
    },
    {
      'id': 'ex9',
      'name': 'Pull-ups',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/pullups.png',
      'muscleGroups': ['back', 'arms'],
      'difficulty': 'Advanced',
      'equipment': 'Pull-up Bar',
      'description': 'Upper body pulling exercise for back and bicep strength',
    },
    {
      'id': 'ex10',
      'name': 'Deadlifts',
      'category': 'Strength',
      'imageUrl': 'assets/images/exercises/deadlifts.png',
      'muscleGroups': ['back', 'legs', 'glutes'],
      'difficulty': 'Advanced',
      'equipment': 'Dumbbells',
      'description': 'Compound exercise for posterior chain strength',
    },
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _createWorkoutPlan() {
    if (_formKey.currentState!.validate()) {
      // In a real app, you would save this to your backend
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Workout plan created successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Navigate back to the custom workout screen
      Navigator.pop(context);
    }
  }

  void _addExercise(String exerciseId) {
    setState(() {
      if (_selectedExercises.contains(exerciseId)) {
        _selectedExercises.remove(exerciseId);
      } else {
        _selectedExercises.add(exerciseId);
      }
    });
  }

  void _generateWorkouts() {
    // In a real app, you would generate workouts based on the selected exercises
    // For now, we'll create a simple workout with the selected exercises

    final exercises =
        _selectedExercises.map((id) {
          final exercise = _exerciseLibrary.firstWhere((e) => e['id'] == id);
          return ExerciseModel(
            id: exercise['id'],
            name: exercise['name'],
            description: 'Description for ${exercise['name']}',
            imageUrl: exercise['imageUrl'],
            videoUrl: 'https://example.com/videos/${exercise['id']}.mp4',
            duration: 0,
            sets: 3,
            reps: 12,
            restTime: 30,
          );
        }).toList();

    final workout = WorkoutModel(
      id: 'workout_${DateTime.now().millisecondsSinceEpoch}',
      name: _nameController.text,
      description: _descriptionController.text,
      imageUrl: 'assets/images/workouts/custom.jpg',
      category: 'Custom',
      duration: 30,
      calories: 250,
      exercises: exercises,
      icon: Icons.fitness_center,
      color: Colors.blue,
    );

    setState(() {
      _workouts.add(workout);
      _currentStep = 2; // Move to the review step
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Create Workout Plan')),
      body: Stepper(
        currentStep: _currentStep,
        onStepContinue: () {
          if (_currentStep == 0) {
            if (_formKey.currentState!.validate()) {
              setState(() {
                _currentStep += 1;
              });
            }
          } else if (_currentStep == 1) {
            if (_selectedExercises.isNotEmpty) {
              _generateWorkouts();
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please select at least one exercise'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          } else if (_currentStep == 2) {
            _createWorkoutPlan();
          }
        },
        onStepCancel: () {
          if (_currentStep > 0) {
            setState(() {
              _currentStep -= 1;
            });
          } else {
            Navigator.pop(context);
          }
        },
        steps: [
          Step(
            title: const Text('Basic Information'),
            content: _buildBasicInfoStep(),
            isActive: _currentStep >= 0,
          ),
          Step(
            title: const Text('Select Exercises'),
            content: _buildSelectExercisesStep(),
            isActive: _currentStep >= 1,
          ),
          Step(
            title: const Text('Review Plan'),
            content: _buildReviewPlanStep(),
            isActive: _currentStep >= 2,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoStep() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Plan Name',
              hintText: 'Enter a name for your workout plan',
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _descriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              hintText: 'Describe your workout plan',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Difficulty Level',
              border: OutlineInputBorder(),
            ),
            value: _selectedLevel,
            items:
                _levels.map((level) {
                  return DropdownMenuItem(value: level, child: Text(level));
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedLevel = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Duration',
              border: OutlineInputBorder(),
            ),
            value: _selectedDuration,
            items:
                _durations.map((duration) {
                  return DropdownMenuItem(
                    value: duration,
                    child: Text(duration),
                  );
                }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedDuration = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSelectExercisesStep() {
    // Group exercises by category
    final exercisesByCategory = <String, List<Map<String, dynamic>>>{};

    for (final exercise in _exerciseLibrary) {
      final category = exercise['category'] as String;
      if (!exercisesByCategory.containsKey(category)) {
        exercisesByCategory[category] = [];
      }
      exercisesByCategory[category]!.add(exercise);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with search
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select exercises for your plan',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Selected: ${_selectedExercises.length} exercises',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: _showExerciseSearch,
              icon: Icon(Icons.search, color: AppTheme.primaryColor),
              tooltip: 'Search exercises',
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Quick selection buttons
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildQuickSelectButton(
              'Select All Upper Body',
              () =>
                  _selectByMuscleGroup(['chest', 'shoulders', 'arms', 'back']),
            ),
            _buildQuickSelectButton(
              'Select All Lower Body',
              () => _selectByMuscleGroup(['legs', 'glutes', 'calves']),
            ),
            _buildQuickSelectButton(
              'Select Core',
              () => _selectByMuscleGroup(['core', 'abs']),
            ),
            _buildQuickSelectButton('Clear All', () => _clearAllSelections()),
          ],
        ),
        const SizedBox(height: 20),

        // Exercise categories
        ...exercisesByCategory.entries.map((entry) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getCategoryIcon(entry.key),
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    entry.key,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${entry.value.where((e) => _selectedExercises.contains(e['id'])).length}/${entry.value.length} selected',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 1,
                  childAspectRatio: 3.5,
                  mainAxisSpacing: 8,
                ),
                itemCount: entry.value.length,
                itemBuilder: (context, index) {
                  final exercise = entry.value[index];
                  final isSelected = _selectedExercises.contains(
                    exercise['id'],
                  );

                  return _buildEnhancedExerciseCard(exercise, isSelected);
                },
              ),
              const SizedBox(height: 20),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildReviewPlanStep() {
    if (_workouts.isEmpty) {
      return const Center(child: Text('No workouts generated yet.'));
    }

    final workout = _workouts.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Review Your Plan',
          style: Theme.of(
            context,
          ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 38), // 0.15 opacity
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _nameController.text,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _descriptionController.text.isEmpty
                    ? 'No description provided'
                    : _descriptionController.text,
                style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _selectedLevel,
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange.withValues(alpha: 26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _selectedDuration,
                      style: const TextStyle(
                        color: Colors.orange,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              const Text(
                'Exercises',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 8),
              ...workout.exercises.map((exercise) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4),
                        child: Image.asset(
                          exercise.imageUrl,
                          width: 40,
                          height: 40,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 40,
                              height: 40,
                              color: Colors.grey.shade200,
                              child: const Icon(
                                Icons.fitness_center,
                                color: Colors.grey,
                                size: 20,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          exercise.name,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      Text(
                        '${exercise.sets} × ${exercise.reps}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                );
              }),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder:
                          (context) => WorkoutDayScreen(
                            workoutId: workout.id,
                            dayTitle: 'Day 1: ${workout.name}',
                          ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 48),
                ),
                child: const Text('PREVIEW WORKOUT'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Enhanced exercise selection methods
  void _showExerciseSearch() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Search Exercises'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                children: [
                  TextField(
                    decoration: const InputDecoration(
                      hintText: 'Search by name, muscle group, or equipment...',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      // Implement search functionality
                    },
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _exerciseLibrary.length,
                      itemBuilder: (context, index) {
                        final exercise = _exerciseLibrary[index];
                        final isSelected = _selectedExercises.contains(
                          exercise['id'],
                        );
                        return CheckboxListTile(
                          title: Text(exercise['name']),
                          subtitle: Text(
                            '${exercise['category']} • ${exercise['muscleGroups'].join(', ')}',
                          ),
                          value: isSelected,
                          onChanged: (bool? value) {
                            _addExercise(exercise['id']);
                            Navigator.pop(context);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CLOSE'),
              ),
            ],
          ),
    );
  }

  Widget _buildQuickSelectButton(String label, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: AppTheme.primaryColor),
        foregroundColor: AppTheme.primaryColor,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  void _selectByMuscleGroup(List<String> muscleGroups) {
    setState(() {
      for (final exercise in _exerciseLibrary) {
        final exerciseMuscleGroups = exercise['muscleGroups'] as List<String>;
        if (exerciseMuscleGroups.any((group) => muscleGroups.contains(group))) {
          if (!_selectedExercises.contains(exercise['id'])) {
            _selectedExercises.add(exercise['id']);
          }
        }
      }
    });
  }

  void _clearAllSelections() {
    setState(() {
      _selectedExercises.clear();
    });
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'strength':
        return Icons.fitness_center;
      case 'cardio':
        return Icons.directions_run;
      case 'flexibility':
        return Icons.accessibility_new;
      case 'core':
        return Icons.center_focus_strong;
      default:
        return Icons.sports_gymnastics;
    }
  }

  Widget _buildEnhancedExerciseCard(
    Map<String, dynamic> exercise,
    bool isSelected,
  ) {
    return InkWell(
      onTap: () => _addExercise(exercise['id']),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppTheme.primaryColor.withValues(alpha: 0.1 * 255)
                  : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.05 * 255),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Exercise image/icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey.shade100,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  exercise['imageUrl'],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                      child: Icon(
                        Icons.fitness_center,
                        color: AppTheme.primaryColor,
                        size: 24,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 16),

            // Exercise details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          exercise['name'],
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color:
                                isSelected
                                    ? AppTheme.primaryColor
                                    : Colors.black87,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Muscle groups
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children:
                        (exercise['muscleGroups'] as List<String>).take(3).map((
                          muscle,
                        ) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withValues(
                                alpha: 0.1 * 255,
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              muscle,
                              style: TextStyle(
                                fontSize: 10,
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          );
                        }).toList(),
                  ),
                  const SizedBox(height: 6),

                  // Difficulty and equipment
                  Row(
                    children: [
                      Icon(
                        Icons.trending_up,
                        size: 14,
                        color: _getDifficultyColor(exercise['difficulty']),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        exercise['difficulty'],
                        style: TextStyle(
                          fontSize: 12,
                          color: _getDifficultyColor(exercise['difficulty']),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(
                        Icons.fitness_center,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          exercise['equipment'] ?? 'Bodyweight',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return Colors.green;
      case 'intermediate':
        return Colors.orange;
      case 'advanced':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
