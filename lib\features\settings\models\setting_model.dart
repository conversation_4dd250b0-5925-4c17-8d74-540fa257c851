  import 'package:flutter/material.dart';

/// Enum for different setting types
enum SettingType {
  toggle,
  selection,
  navigation,
  action,
  slider,
  text,
}

/// Model for individual setting items
class SettingModel {
  final String id;
  final String title;
  final String? subtitle;
  final IconData icon;
  final SettingType type;
  final dynamic value;
  final List<String>? options;
  final Function(dynamic)? onChanged;
  final VoidCallback? onTap;
  final Color? iconColor;
  final bool isEnabled;
  final bool showDivider;
  final String? badge;
  final double? min;
  final double? max;

  SettingModel({
    required this.id,
    required this.title,
    this.subtitle,
    required this.icon,
    required this.type,
    this.value,
    this.options,
    this.onChanged,
    this.onTap,
    this.iconColor,
    this.isEnabled = true,
    this.showDivider = true,
    this.badge,
    this.min,
    this.max,
  });

  SettingModel copyWith({
    String? id,
    String? title,
    String? subtitle,
    IconData? icon,
    SettingType? type,
    dynamic value,
    List<String>? options,
    Function(dynamic)? onChanged,
    VoidCallback? onTap,
    Color? iconColor,
    bool? isEnabled,
    bool? showDivider,
    String? badge,
    double? min,
    double? max,
  }) {
    return SettingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      type: type ?? this.type,
      value: value ?? this.value,
      options: options ?? this.options,
      onChanged: onChanged ?? this.onChanged,
      onTap: onTap ?? this.onTap,
      iconColor: iconColor ?? this.iconColor,
      isEnabled: isEnabled ?? this.isEnabled,
      showDivider: showDivider ?? this.showDivider,
      badge: badge ?? this.badge,
      min: min ?? this.min,
      max: max ?? this.max,
    );
  }
}

/// Model for setting sections
class SettingSectionModel {
  final String title;
  final String? subtitle;
  final List<SettingModel> settings;
  final IconData? icon;

  SettingSectionModel({
    required this.title,
    this.subtitle,
    required this.settings,
    this.icon,
  });
}

/// Model for user preferences
class UserPreferences {
  final bool notificationsEnabled;
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool smsNotificationsEnabled;
  final String theme;
  final String language;
  final bool biometricEnabled;
  final bool autoBackup;
  final String backupFrequency;
  final bool dataUsageOptimization;
  final bool offlineMode;
  final double fontSize;
  final bool soundEffects;
  final bool hapticFeedback;
  final bool analyticsEnabled;
  final bool crashReportingEnabled;
  final String downloadQuality;
  final bool autoDownload;
  final bool wifiOnlyDownload;

  UserPreferences({
    this.notificationsEnabled = true,
    this.pushNotificationsEnabled = true,
    this.emailNotificationsEnabled = true,
    this.smsNotificationsEnabled = false,
    this.theme = 'System',
    this.language = 'English',
    this.biometricEnabled = false,
    this.autoBackup = true,
    this.backupFrequency = 'Daily',
    this.dataUsageOptimization = true,
    this.offlineMode = false,
    this.fontSize = 16.0,
    this.soundEffects = true,
    this.hapticFeedback = true,
    this.analyticsEnabled = true,
    this.crashReportingEnabled = true,
    this.downloadQuality = 'High',
    this.autoDownload = false,
    this.wifiOnlyDownload = true,
  });

  UserPreferences copyWith({
    bool? notificationsEnabled,
    bool? pushNotificationsEnabled,
    bool? emailNotificationsEnabled,
    bool? smsNotificationsEnabled,
    String? theme,
    String? language,
    bool? biometricEnabled,
    bool? autoBackup,
    String? backupFrequency,
    bool? dataUsageOptimization,
    bool? offlineMode,
    double? fontSize,
    bool? soundEffects,
    bool? hapticFeedback,
    bool? analyticsEnabled,
    bool? crashReportingEnabled,
    String? downloadQuality,
    bool? autoDownload,
    bool? wifiOnlyDownload,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      emailNotificationsEnabled: emailNotificationsEnabled ?? this.emailNotificationsEnabled,
      smsNotificationsEnabled: smsNotificationsEnabled ?? this.smsNotificationsEnabled,
      theme: theme ?? this.theme,
      language: language ?? this.language,
      biometricEnabled: biometricEnabled ?? this.biometricEnabled,
      autoBackup: autoBackup ?? this.autoBackup,
      backupFrequency: backupFrequency ?? this.backupFrequency,
      dataUsageOptimization: dataUsageOptimization ?? this.dataUsageOptimization,
      offlineMode: offlineMode ?? this.offlineMode,
      fontSize: fontSize ?? this.fontSize,
      soundEffects: soundEffects ?? this.soundEffects,
      hapticFeedback: hapticFeedback ?? this.hapticFeedback,
      analyticsEnabled: analyticsEnabled ?? this.analyticsEnabled,
      crashReportingEnabled: crashReportingEnabled ?? this.crashReportingEnabled,
      downloadQuality: downloadQuality ?? this.downloadQuality,
      autoDownload: autoDownload ?? this.autoDownload,
      wifiOnlyDownload: wifiOnlyDownload ?? this.wifiOnlyDownload,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'smsNotificationsEnabled': smsNotificationsEnabled,
      'theme': theme,
      'language': language,
      'biometricEnabled': biometricEnabled,
      'autoBackup': autoBackup,
      'backupFrequency': backupFrequency,
      'dataUsageOptimization': dataUsageOptimization,
      'offlineMode': offlineMode,
      'fontSize': fontSize,
      'soundEffects': soundEffects,
      'hapticFeedback': hapticFeedback,
      'analyticsEnabled': analyticsEnabled,
      'crashReportingEnabled': crashReportingEnabled,
      'downloadQuality': downloadQuality,
      'autoDownload': autoDownload,
      'wifiOnlyDownload': wifiOnlyDownload,
    };
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      pushNotificationsEnabled: json['pushNotificationsEnabled'] ?? true,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] ?? true,
      smsNotificationsEnabled: json['smsNotificationsEnabled'] ?? false,
      theme: json['theme'] ?? 'System',
      language: json['language'] ?? 'English',
      biometricEnabled: json['biometricEnabled'] ?? false,
      autoBackup: json['autoBackup'] ?? true,
      backupFrequency: json['backupFrequency'] ?? 'Daily',
      dataUsageOptimization: json['dataUsageOptimization'] ?? true,
      offlineMode: json['offlineMode'] ?? false,
      fontSize: json['fontSize']?.toDouble() ?? 16.0,
      soundEffects: json['soundEffects'] ?? true,
      hapticFeedback: json['hapticFeedback'] ?? true,
      analyticsEnabled: json['analyticsEnabled'] ?? true,
      crashReportingEnabled: json['crashReportingEnabled'] ?? true,
      downloadQuality: json['downloadQuality'] ?? 'High',
      autoDownload: json['autoDownload'] ?? false,
      wifiOnlyDownload: json['wifiOnlyDownload'] ?? true,
    );
  }
}
