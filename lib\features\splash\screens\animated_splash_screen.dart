import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../auth/screens/login_screen.dart';
import '../../onboarding/services/onboarding_service.dart';
import '../../../core/config/app_routes.dart';
import '../../../core/utils/responsive_utils.dart';

/// Animated Splash Screen with Fit4Force Shield Logo
class AnimatedSplashScreen extends StatefulWidget {
  const AnimatedSplashScreen({super.key});

  @override
  State<AnimatedSplashScreen> createState() => _AnimatedSplashScreenState();
}

class _AnimatedSplashScreenState extends State<AnimatedSplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _textController;
  late AnimationController _glowController;

  late Animation<double> _textOpacity;
  late Animation<double> _textSlide;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Text animation controller - faster duration
    _textController = AnimationController(
      duration: const Duration(milliseconds: 600), // Reduced from 1000ms
      vsync: this,
    );

    // Glow animation controller - faster duration
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1000), // Reduced from 2000ms
      vsync: this,
    );

    // Text animations
    _textOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _textController, curve: Curves.easeIn));

    _textSlide = Tween<double>(
      begin: 50.0,
      end: 0.0,
    ).animate(CurvedAnimation(parent: _textController, curve: Curves.easeOut));
  }

  void _startAnimationSequence() async {
    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Start text animation
    await _textController.forward();

    // Start glow animation
    _glowController.repeat(reverse: true);

    // Wait for a moment to show the complete animation
    await Future.delayed(const Duration(milliseconds: 1500));

    // Check onboarding status and navigate accordingly
    final shouldShowOnboarding = await OnboardingService.shouldShowOnboarding();

    if (mounted) {
      if (shouldShowOnboarding) {
        // Navigate to welcome onboarding
        Navigator.of(context).pushReplacementNamed(AppRoutes.welcomeOnboarding);
      } else {
        // Navigate to login screen
        Navigator.of(context).pushReplacement(
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => const LoginScreen(),
            transitionDuration: const Duration(milliseconds: 800),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F1419), // Dark navy background
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.0,
            colors: [
              Color(0xFF1A2332), // Lighter center
              Color(0xFF0F1419), // Darker edges
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Spacer(flex: 2),

                // Fit4Force Logo
                Container(
                  margin: EdgeInsets.only(
                    bottom: ResponsiveUtils.getResponsiveSpacing(context),
                  ),
                  child: Image.asset(
                    'assets/images/fit4force_main_logo.png',
                    width: ResponsiveUtils.getResponsiveFontSize(
                      context,
                      mobile: 120,
                      tablet: 140,
                      desktop: 160,
                    ),
                    height: ResponsiveUtils.getResponsiveFontSize(
                      context,
                      mobile: 120,
                      tablet: 140,
                      desktop: 160,
                    ),
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Fallback to simple icon if logo not found
                      return Icon(
                        Icons.fitness_center,
                        size: ResponsiveUtils.getResponsiveFontSize(
                          context,
                          mobile: 60,
                          tablet: 70,
                          desktop: 80,
                        ),
                        color: const Color(0xFF00D4FF),
                      );
                    },
                  ),
                ),

                // Animated App Name
                AnimatedBuilder(
                  animation: _textController,
                  builder: (context, child) {
                    return Transform.translate(
                      offset: Offset(0, _textSlide.value),
                      child: Opacity(
                        opacity: _textOpacity.value,
                        child: Column(
                          children: [
                            // Main App Name
                            ShaderMask(
                              shaderCallback:
                                  (bounds) => const LinearGradient(
                                    colors: [
                                      Color(0xFF00D4FF),
                                      Color(0xFF0099CC),
                                    ],
                                  ).createShader(bounds),
                              child: Text(
                                'FIT4FORCE',
                                style: TextStyle(
                                  fontSize:
                                      ResponsiveUtils.getResponsiveFontSize(
                                        context,
                                        mobile: 28,
                                        tablet: 32,
                                        desktop: 36,
                                      ),
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  letterSpacing: 3,
                                ),
                              ),
                            ),

                            SizedBox(
                              height:
                                  ResponsiveUtils.getResponsiveSpacing(
                                    context,
                                  ) /
                                  3,
                            ),

                            // Tagline
                            Text(
                              'Nigerian Military Recruitment Prep',
                              style: TextStyle(
                                fontSize: ResponsiveUtils.getResponsiveFontSize(
                                  context,
                                  mobile: 14,
                                  tablet: 16,
                                  desktop: 18,
                                ),
                                color: Colors.grey[400],
                                letterSpacing: 1,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            SizedBox(
                              height:
                                  ResponsiveUtils.getResponsiveSpacing(
                                    context,
                                  ) /
                                  4,
                            ),

                            // Version or additional text
                            Text(
                              'Excellence • Discipline • Service',
                              style: TextStyle(
                                fontSize: ResponsiveUtils.getResponsiveFontSize(
                                  context,
                                  mobile: 11,
                                  tablet: 12,
                                  desktop: 13,
                                ),
                                color: Colors.grey[500],
                                letterSpacing: 0.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),

                const Spacer(flex: 1),

                // Loading indicator
                AnimatedBuilder(
                  animation: _textController,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _textOpacity.value,
                      child: Column(
                        children: [
                          SizedBox(
                            width: ResponsiveUtils.getResponsiveFontSize(
                              context,
                              mobile: 28,
                              tablet: 32,
                              desktop: 36,
                            ),
                            height: ResponsiveUtils.getResponsiveFontSize(
                              context,
                              mobile: 28,
                              tablet: 32,
                              desktop: 36,
                            ),
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                Color(0xFF00D4FF),
                              ),
                              backgroundColor: Colors.grey[700],
                            ),
                          ),
                          SizedBox(
                            height:
                                ResponsiveUtils.getResponsiveSpacing(context) /
                                2,
                          ),
                          Text(
                            'Preparing your military journey...',
                            style: TextStyle(
                              fontSize: ResponsiveUtils.getResponsiveFontSize(
                                context,
                                mobile: 13,
                                tablet: 14,
                                desktop: 15,
                              ),
                              color: Colors.grey[400],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const Spacer(flex: 2),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _textController.dispose();
    _glowController.dispose();
    super.dispose();
  }
}

/// Simple Splash Screen (fallback)
class SimpleSplashScreen extends StatelessWidget {
  const SimpleSplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Auto-navigate after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    });

    return Scaffold(
      backgroundColor: const Color(0xFF0F1419),
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),

              // Logo
              Container(
                margin: EdgeInsets.only(
                  bottom: ResponsiveUtils.getResponsiveSpacing(context),
                ),
                child: Image.asset(
                  'assets/images/fit4force_main_logo.png',
                  width: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 120,
                    tablet: 140,
                    desktop: 160,
                  ),
                  height: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 120,
                    tablet: 140,
                    desktop: 160,
                  ),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to simple icon if logo not found
                    return Icon(
                      Icons.fitness_center,
                      size: ResponsiveUtils.getResponsiveFontSize(
                        context,
                        mobile: 60,
                        tablet: 70,
                        desktop: 80,
                      ),
                      color: const Color(0xFF00D4FF),
                    );
                  },
                ),
              ),

              // App Name
              Text(
                'FIT4FORCE',
                style: TextStyle(
                  fontSize: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 24,
                    tablet: 28,
                    desktop: 32,
                  ),
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  letterSpacing: 2,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(
                height: ResponsiveUtils.getResponsiveSpacing(context) / 3,
              ),

              Text(
                'Nigerian Military Recruitment Prep',
                style: TextStyle(
                  fontSize: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 12,
                    tablet: 14,
                    desktop: 16,
                  ),
                  color: Colors.grey[400],
                ),
                textAlign: TextAlign.center,
              ),

              const Spacer(flex: 3),
            ],
          ),
        ),
      ),
    );
  }
}
