import 'package:flutter/material.dart';
import 'package:fit_4_force/shared/widgets/email_test_widget.dart';

/// Email testing page for development
///
/// This page allows you to test email functionality manually.
/// Remove before production.
class EmailTestPage extends StatelessWidget {
  const EmailTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Email Testing'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '📧 Email Testing',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Use this page to test your EmailJS integration before going live.',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),
            Sized<PERSON>ox(height: 16),
            EmailTestWidget(),
          ],
        ),
      ),
    );
  }
}
