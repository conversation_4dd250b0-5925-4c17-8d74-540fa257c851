import 'package:fit_4_force/features/learning/services/smart_learning_assistant.dart';
import 'package:flutter/foundation.dart' show debugPrint;

class AIFeaturesManager {
  // Learning Assistant components
  SmartLearningAssistant? _learningAssistant;

  // Initialization status
  bool _isInitialized = false;
  final Map<String, bool> _componentStatus = {};

  // Singleton pattern
  static final AIFeaturesManager _instance = AIFeaturesManager._internal();

  factory AIFeaturesManager() {
    return _instance;
  }

  AIFeaturesManager._internal();

  Future<void> initialize({
    bool learningOnly = false,
    bool fitnessOnly = false,
    String targetAgency = '',
  }) async {
    if (_isInitialized) return;

    try {
      // Initialize learning components
      if (!fitnessOnly) {
        _learningAssistant = SmartLearningAssistant();

        await Future.wait([
          _initComponent(
            'learning_assistant',
            () => _learningAssistant!.initialize(targetAgency: targetAgency),
          ),
        ]);
      }

      // Initialize fitness components if needed in the future
      if (!learningOnly) {
        // Future fitness AI components will be initialized here
      }

      _isInitialized = true;
      debugPrint('AI Features Manager initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AI Features Manager: $e');
      rethrow;
    }
  }

  Future<void> _initComponent(
    String name,
    Future<void> Function() initFunction,
  ) async {
    try {
      await initFunction();
      _componentStatus[name] = true;
      debugPrint('Component $name initialized successfully');
    } catch (e) {
      _componentStatus[name] = false;
      debugPrint('Error initializing component $name: $e');
    }
  }

  // Learning Assistant Methods

  Future<Map<String, dynamic>> askQuestion(
    String question,
    String userContext,
  ) async {
    _checkInitialization();
    return await _learningAssistant!.processQuery(question, userContext);
  }

  // Additional methods for study plans, quizzes, etc. will be added here

  void _checkInitialization() {
    if (!_isInitialized) {
      throw Exception(
        'AI Features Manager not initialized. Call initialize() first.',
      );
    }
  }

  void dispose() {
    // Clean up resources
    _learningAssistant?.close();
  }
}
