import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:logger/logger.dart';

class SubscriptionService {
  final Logger _logger = Logger();
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<bool> checkSubscriptionStatus(String userId) async {
    try {
      final response =
          await _supabase
              .from('users')
              .select('is_premium, subscription_end_date')
              .eq('id', userId)
              .single();

      final isPremium = response['is_premium'] ?? false;
      final subscriptionEndDate = response['subscription_end_date'];

      if (!isPremium) return false;
      if (subscriptionEndDate == null) return false;

      final endDate = DateTime.parse(subscriptionEndDate);
      return DateTime.now().isBefore(endDate);
    } catch (e) {
      _logger.e('Error checking subscription status: $e');
      return false;
    }
  }

  Future<bool> hasActivePremiumSubscription(String userId) async {
    return await checkSubscriptionStatus(userId);
  }

  Future<bool> activateSubscription({
    required String userId,
    required String paymentReference,
  }) async {
    try {
      final subscriptionEndDate = DateTime.now().add(const Duration(days: 30));

      await _supabase
          .from('users')
          .update({
            'is_premium': true,
            'subscription_start_date': DateTime.now().toIso8601String(),
            'subscription_end_date': subscriptionEndDate.toIso8601String(),
            'last_payment_reference': paymentReference,
          })
          .eq('id', userId);

      return true;
    } catch (e) {
      _logger.e('Error activating subscription: $e');
      return false;
    }
  }

  Future<bool> cancelSubscription(String userId) async {
    try {
      await _supabase
          .from('users')
          .update({
            'is_premium': false,
            'subscription_end_date': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);

      return true;
    } catch (e) {
      _logger.e('Error canceling subscription: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> getSubscriptionDetails(String userId) async {
    try {
      final response =
          await _supabase
              .from('users')
              .select(
                'is_premium, subscription_start_date, subscription_end_date, last_payment_reference',
              )
              .eq('id', userId)
              .single();

      return {
        'isPremium': response['is_premium'] ?? false,
        'subscriptionStartDate': response['subscription_start_date'],
        'subscriptionEndDate': response['subscription_end_date'],
        'lastPaymentReference': response['last_payment_reference'],
        'subscriptionHistory': [], // Can be implemented later if needed
      };
    } catch (e) {
      _logger.e('Error getting subscription details: $e');
      return null;
    }
  }

  /// Get free user quiz question limit
  int get freeUserQuestionLimit => 5;

  /// Get premium user quiz question limit
  int get premiumUserQuestionLimit =>
      50; // Essentially unlimited for practical purposes

  /// Get current user's question limit based on subscription status
  Future<int> getUserQuestionLimit(String userId) async {
    final isPremium = await hasActivePremiumSubscription(userId);
    return isPremium ? premiumUserQuestionLimit : freeUserQuestionLimit;
  }

  /// Check if user can access premium features
  Future<bool> canAccessPremiumFeatures(String userId) async {
    return await hasActivePremiumSubscription(userId);
  }

  /// Check if user can generate AI questions
  Future<bool> canGenerateAIQuestions(String userId) async {
    return await canAccessPremiumFeatures(userId);
  }

  /// Check if user can access AI explanations
  Future<bool> canAccessAIExplanations(String userId) async {
    return await canAccessPremiumFeatures(userId);
  }

  /// Get premium features list
  List<String> get premiumFeatures => [
    'Unlimited quiz questions',
    'AI-generated questions',
    'Detailed AI explanations',
    'Advanced progress analytics',
    'Personalized study plans',
    'Priority customer support',
    'Offline content access',
    'Custom quiz creation',
    // New catchy features
    'Exclusive access to live webinars & workshops',
    'Early access to new features',
    'Downloadable study materials (PDF, eBooks)',
    'Premium-only fitness challenges',
    'Direct chat with expert mentors',
    'Ad-free experience',
    'Certificate of completion for courses',
    'Access to premium community groups',
    'Personalized feedback on practice tests',
    'Special badges & profile highlights',
  ];

  /// Get subscription pricing (in Naira)
  Map<String, dynamic> get subscriptionPricing => {
    'monthly': {
      'price': 2500,
      'currency': '₦',
      'duration': '1 month',
      'features': premiumFeatures,
    },
    'quarterly': {
      'price': 6000,
      'currency': '₦',
      'duration': '3 months',
      'savings': '₦1,500',
      'features': premiumFeatures,
    },
    'yearly': {
      'price': 20000,
      'currency': '₦',
      'duration': '12 months',
      'savings': '₦10,000',
      'features': premiumFeatures,
    },
  };
}
