import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/features/prep/models/study_material_model.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';
import 'package:fit_4_force/shared/widgets/responsive_grid_view.dart';
import 'package:fit_4_force/features/prep/widgets/study_material_viewer.dart';

class AgencyStudyMaterialsScreen extends StatefulWidget {
  final String sectionName;
  final String sectionId;

  const AgencyStudyMaterialsScreen({
    super.key,
    required this.sectionName,
    required this.sectionId,
  });

  @override
  State<AgencyStudyMaterialsScreen> createState() =>
      _AgencyStudyMaterialsScreenState();
}

class _AgencyStudyMaterialsScreenState
    extends State<AgencyStudyMaterialsScreen> {
  final EnhancedStudyMaterialService _materialService =
      EnhancedStudyMaterialService();

  List<StudyMaterialModel> _materials = [];
  Map<String, dynamic>? _userAgency;
  bool _isLoading = true;
  String _selectedFilter = 'all';
  String _selectedDifficulty = 'all';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final materials = await _materialService.getMaterialsBySection(
        widget.sectionName,
      );
      final userAgency = await _materialService.getUserAgency();

      setState(() {
        _materials = materials;
        _userAgency = userAgency;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading materials: $e')));
      }
    }
  }

  List<StudyMaterialModel> get _filteredMaterials {
    var filtered = _materials;

    if (_selectedFilter != 'all') {
      filtered =
          filtered.where((m) => m.contentType == _selectedFilter).toList();
    }

    if (_selectedDifficulty != 'all') {
      filtered =
          filtered.where((m) => m.difficulty == _selectedDifficulty).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.sectionName),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh content',
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingState() : _buildContent(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading study materials...'),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildHeader(),
        _buildFilters(),
        Expanded(child: _buildMaterialsList()),
      ],
    );
  }

  Widget _buildHeader() {
    final agencyName = _userAgency?['agencies']?['name'] ?? 'Your Agency';

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.sectionName,
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Study materials for $agencyName',
            style: const TextStyle(fontSize: 16, color: Colors.white70),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildStatCard('Total Materials', _materials.length.toString()),
              const SizedBox(width: 16),
              _buildStatCard(
                'Free Content',
                _materials.where((m) => !m.isPremium).length.toString(),
              ),
              const SizedBox(width: 16),
              _buildStatCard(
                'Premium Content',
                _materials.where((m) => m.isPremium).length.toString(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              label,
              style: const TextStyle(fontSize: 12, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilters() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildFilterDropdown(
                  'Content Type',
                  _selectedFilter,
                  ['all', 'document', 'quiz', 'video', 'interactive'],
                  (value) => setState(() => _selectedFilter = value!),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildFilterDropdown(
                  'Difficulty',
                  _selectedDifficulty,
                  ['all', 'beginner', 'intermediate', 'advanced'],
                  (value) => setState(() => _selectedDifficulty = value!),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Showing ${_filteredMaterials.length} of ${_materials.length} materials',
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items:
          options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(option == 'all' ? 'All' : _formatOption(option)),
            );
          }).toList(),
      onChanged: onChanged,
    );
  }

  String _formatOption(String option) {
    return option
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  Widget _buildMaterialsList() {
    if (_filteredMaterials.isEmpty) {
      return _buildEmptyState();
    }

    // Group materials by agency
    final materialsByAgency = <String, List<StudyMaterialModel>>{};
    for (var material in _filteredMaterials) {
      if (!materialsByAgency.containsKey(material.agency)) {
        materialsByAgency[material.agency] = [];
      }
      materialsByAgency[material.agency]!.add(material);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: materialsByAgency.length,
      itemBuilder: (context, index) {
        final agency = materialsByAgency.keys.elementAt(index);
        final materials = materialsByAgency[agency]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Text(
                agency,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ),
            ResponsiveGridView(
              mobileColumns: 1,
              tabletColumns: 2,
              desktopColumns: 3,
              childAspectRatio: 1.2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: materials
                  .map((material) => _buildMaterialCard(material))
                  .toList(),
            ),
            const Divider(height: 32),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.library_books_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No study materials available',
              style: TextStyle(
                fontSize: 20,
                color: Colors.grey[700],
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              _materials.isEmpty
                  ? 'Study materials for ${widget.sectionName} will be available soon.'
                  : 'Try adjusting your filters to see more content.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _selectedFilter = 'all';
                  _selectedDifficulty = 'all';
                });
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Reset Filters'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMaterialCard(StudyMaterialModel material) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _openMaterial(material),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: material.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(material.icon, color: material.color, size: 24),
                  ),
                  const Spacer(),
                  if (material.isPremium)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'PREMIUM',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                material.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                material.description,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const Spacer(),
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    '${material.estimatedReadTime} min',
                    style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                  ),
                  const Spacer(),
                  if (material.rating > 0) ...[
                    Icon(Icons.star, size: 16, color: Colors.amber),
                    const SizedBox(width: 4),
                    Text(
                      material.rating.toStringAsFixed(1),
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openMaterial(StudyMaterialModel material) {
    // Track that user accessed this material
    _materialService.trackProgress(
      materialId: material.id,
      status: 'in_progress',
    );

    // Navigate to material viewer screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => StudyMaterialViewer(
              material: material,
              onProgressUpdate: (materialId, status, {progressPercentage}) {
                _materialService.trackProgress(
                  materialId: materialId,
                  status: status,
                  progressPercentage: progressPercentage,
                );
              },
            ),
      ),
    );
  }
}
