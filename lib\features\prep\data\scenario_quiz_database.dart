import '../models/quiz_question_model.dart';

/// Scenario-Based Quiz Database
/// Contains 70 real-world scenario questions for each of the 11 Nigerian agencies
/// Total: 770 scenario-based questions for practical situational training
class ScenarioQuizDatabase {
  /// Get all scenario questions (770 total)
  static List<QuizQuestionModel> getAllScenarioQuestions() {
    return [
      ...nigerianArmyScenarios,
      ...nigerianNavyScenarios,
      ...nigerianAirForceScenarios,
      ...ndaScenarios,
      ...dsscScenarios,
      ...polacScenarios,
      ...fireServiceScenarios,
      ...nscdcScenarios,
      ...customsServiceScenarios,
      ...immigrationServiceScenarios,
      ...frscScenarios,
    ];
  }

  /// Get scenario questions for a specific agency
  static List<QuizQuestionModel> getScenarioQuestionsForAgency(
    String agencyCode,
  ) {
    switch (agencyCode.toLowerCase()) {
      case 'army':
      case 'nigerian army':
        return nigerianArmyScenarios;
      case 'navy':
      case 'nigerian navy':
        return nigerianNavyScenarios;
      case 'airforce':
      case 'nigerian air force':
        return nigerianAirForceScenarios;
      case 'nda':
      case 'nigerian defence academy':
        return ndaScenarios;
      case 'dssc':
      case 'dssc/ssc':
      case 'direct short service commission':
        return dsscScenarios;
      case 'polac':
      case 'police academy':
        return polacScenarios;
      case 'fire':
      case 'fire service':
      case 'federal fire service':
        return fireServiceScenarios;
      case 'nscdc':
      case 'nigeria security and civil defence corps':
        return nscdcScenarios;
      case 'customs':
      case 'customs service':
      case 'nigeria customs service':
        return customsServiceScenarios;
      case 'immigration':
      case 'nigeria immigration service':
        return immigrationServiceScenarios;
      case 'frsc':
      case 'federal road safety corps':
        return frscScenarios;
      default:
        return [];
    }
  }

  /// Get random scenario questions for an agency with specified count
  static List<QuizQuestionModel> getRandomScenarioQuestions(
    String agencyCode,
    int count,
  ) {
    final questions = getScenarioQuestionsForAgency(agencyCode);
    if (questions.length <= count) return questions;

    final shuffled = List<QuizQuestionModel>.from(questions)..shuffle();
    return shuffled.take(count).toList();
  }

  /// Get scenario questions with premium access control
  static List<QuizQuestionModel> getScenarioQuestionsWithAccess(
    String agencyCode,
    bool isPremiumUser,
  ) {
    final allQuestions = getScenarioQuestionsForAgency(agencyCode);

    if (isPremiumUser) {
      // Premium users get all questions
      return allQuestions;
    } else {
      // Free users get only first 5 questions
      return allQuestions.take(5).toList();
    }
  }

  /// Get free scenario questions (first 5 for each agency)
  static List<QuizQuestionModel> getFreeScenarioQuestions(String agencyCode) {
    final questions = getScenarioQuestionsForAgency(agencyCode);
    return questions.take(5).toList();
  }

  /// Get premium scenario questions (questions 6 onwards)
  static List<QuizQuestionModel> getPremiumScenarioQuestions(
    String agencyCode,
  ) {
    final questions = getScenarioQuestionsForAgency(agencyCode);
    if (questions.length <= 5) return [];
    return questions.skip(5).toList();
  }

  /// Get total scenario question count for an agency
  static int getScenarioQuestionCount(String agencyCode) {
    return getScenarioQuestionsForAgency(agencyCode).length;
  }

  /// Get free scenario question count (always 5 or less)
  static int getFreeScenarioQuestionCount(String agencyCode) {
    final totalQuestions = getScenarioQuestionCount(agencyCode);
    return totalQuestions >= 5 ? 5 : totalQuestions;
  }

  /// Get premium scenario question count
  static int getPremiumScenarioQuestionCount(String agencyCode) {
    final totalQuestions = getScenarioQuestionCount(agencyCode);
    return totalQuestions > 5 ? totalQuestions - 5 : 0;
  }

  // =================================================================
  // NIGERIAN ARMY SCENARIO QUESTIONS (70 Questions)
  // =================================================================
  static List<QuizQuestionModel> get nigerianArmyScenarios => [
    // Combat Leadership Scenarios (15 questions)
    QuizQuestionModel(
      id: 'army_scenario_001',
      question: '''
SCENARIO: You are a platoon commander leading 30 soldiers on a patrol mission in a hostile area. Your radio operator reports enemy contact 500 meters ahead with an estimated 15-20 insurgents. Your unit has limited ammunition and no immediate backup available.

What is your immediate tactical decision?
      ''',
      options: [
        'Advance immediately to engage the enemy',
        'Take defensive positions and call for reinforcement',
        'Retreat to base and report enemy position',
        'Split forces to flank the enemy position',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Combat Leadership',
      difficulty: 'advanced',
      explanation:
          'Taking defensive positions while calling for reinforcement is the tactically sound decision. It preserves your forces while maintaining contact with the enemy and allows for coordinated response with adequate support.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'combat', 'leadership', 'tactics'],
    ),

    QuizQuestionModel(
      id: 'army_scenario_002',
      question: '''
SCENARIO: During a peacekeeping operation, you encounter a group of 50 displaced civilians seeking protection at your checkpoint. Among them, you notice 3 individuals acting suspiciously and avoiding eye contact. Your orders are to maintain security while providing humanitarian assistance.

How do you handle this situation?
      ''',
      options: [
        'Deny entry to all civilians to maintain security',
        'Allow all civilians through without screening',
        'Conduct discrete security screening while providing aid',
        'Detain the suspicious individuals immediately',
      ],
      correctAnswerIndex: 2,
      category: 'Scenario-Based',
      subcategory: 'Peacekeeping Operations',
      difficulty: 'intermediate',
      explanation:
          'Conducting discrete security screening while providing humanitarian aid balances security concerns with the peacekeeping mission\'s humanitarian objectives. This approach maintains operational security while fulfilling the duty to protect civilians.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'peacekeeping', 'humanitarian', 'security'],
    ),

    QuizQuestionModel(
      id: 'army_scenario_003',
      question: '''
SCENARIO: You are commanding a convoy of 5 vehicles transporting medical supplies to a remote outpost. Halfway through the journey, your lead vehicle hits an IED (Improvised Explosive Device). The vehicle is damaged but crew is alive with minor injuries. You are in a known hostile area.

What is your priority action?
      ''',
      options: [
        'Immediately evacuate the area at maximum speed',
        'Secure the area and provide medical aid to injured',
        'Continue mission with remaining vehicles',
        'Return to base with all vehicles',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Convoy Operations',
      difficulty: 'advanced',
      explanation:
          'Securing the area and providing medical aid follows the principle of never leaving soldiers behind. Establishing security prevents further attacks while ensuring proper care for wounded personnel.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'convoy', 'ied', 'medical'],
    ),

    // Logistics and Supply Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_004',
      question: '''
SCENARIO: You are the logistics officer for a battalion deployed in a remote area. Your supply convoy is delayed by 3 days due to bad weather, and you have only 2 days of rations left for 400 soldiers. Local communities have offered to sell food, but regulations prohibit purchasing from unauthorized sources.

What is your best course of action?
      ''',
      options: [
        'Implement strict rationing to extend supplies',
        'Purchase food from locals despite regulations',
        'Request emergency airlift of supplies',
        'Relocate battalion to area with better supply access',
      ],
      correctAnswerIndex: 2,
      category: 'Scenario-Based',
      subcategory: 'Logistics Management',
      difficulty: 'intermediate',
      explanation:
          'Requesting emergency airlift maintains operational integrity while following proper supply chain protocols. This ensures soldier welfare without compromising security or violating regulations.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'logistics', 'supply', 'emergency'],
    ),

    // Training and Development Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_005',
      question: '''
SCENARIO: You are training officer responsible for 50 new recruits. During a live-fire exercise, one recruit accidentally discharges his weapon in an unsafe direction. No one is injured, but the recruit is visibly shaken and other recruits are concerned about safety.

How do you address this situation?
      ''',
      options: [
        'Continue training after a brief safety reminder',
        'Immediately stop exercise and conduct safety review',
        'Remove the recruit from training permanently',
        'Punish the recruit as an example to others',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Training Safety',
      difficulty: 'intermediate',
      explanation:
          'Immediately stopping the exercise and conducting a safety review prioritizes the safety of all personnel. This approach reinforces safety protocols and builds confidence in the training environment.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'training', 'safety', 'weapons'],
    ),

    // Civil-Military Relations Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_006',
      question: '''
SCENARIO: Your unit is deployed to maintain peace in a community experiencing ethnic tensions. Local leaders from both sides approach you with conflicting accounts of recent incidents and each group demands that you take action against the other.

What is your approach to this situation?
      ''',
      options: [
        'Support the group with more credible evidence',
        'Remain neutral and facilitate dialogue between groups',
        'Refer the matter to civilian authorities only',
        'Impose martial law to maintain order',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Civil-Military Relations',
      difficulty: 'advanced',
      explanation:
          'Remaining neutral while facilitating dialogue maintains military impartiality and helps build sustainable peace. This approach addresses root causes rather than just symptoms of conflict.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'civil-military', 'peace', 'dialogue'],
    ),

    // Intelligence and Security Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_007',
      question: '''
SCENARIO: Your intelligence unit receives credible information about a planned attack on a civilian target in 48 hours. However, acting on this information would compromise your source who is embedded within the terrorist organization.

What is your decision?
      ''',
      options: [
        'Act immediately to prevent the attack',
        'Wait for additional confirmation to protect source',
        'Warn civilians anonymously without revealing source',
        'Coordinate with other agencies to verify intelligence',
      ],
      correctAnswerIndex: 0,
      category: 'Scenario-Based',
      subcategory: 'Intelligence Operations',
      difficulty: 'advanced',
      explanation:
          'Acting immediately to prevent civilian casualties is the priority. While protecting sources is important, preventing loss of innocent life takes precedence in military ethics.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'intelligence', 'security', 'ethics'],
    ),

    // Equipment and Maintenance Scenarios (5 questions)
    QuizQuestionModel(
      id: 'army_scenario_008',
      question: '''
SCENARIO: During a critical operation, your unit's primary communication equipment fails. You have backup radios but they operate on a frequency that might be monitored by hostile forces. The mission requires coordination with other units.

What is your communication strategy?
      ''',
      options: [
        'Use backup radios with coded messages',
        'Send physical messengers to other units',
        'Abort mission due to communication failure',
        'Use civilian communication networks',
      ],
      correctAnswerIndex: 0,
      category: 'Scenario-Based',
      subcategory: 'Equipment Management',
      difficulty: 'intermediate',
      explanation:
          'Using backup radios with coded messages maintains operational capability while minimizing security risks. Military units are trained in communication security protocols for such situations.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'communication', 'equipment', 'security'],
    ),

    // Medical and Casualty Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_009',
      question: '''
SCENARIO: During combat operations, you have 3 wounded soldiers: one with severe bleeding but stable, one unconscious with head trauma, and one with a broken leg but alert. You have limited medical supplies and can only treat one soldier immediately before medical evacuation arrives in 20 minutes.

Who do you treat first?
      ''',
      options: [
        'The soldier with severe bleeding',
        'The unconscious soldier with head trauma',
        'The soldier with broken leg',
        'Wait for medical evacuation team',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Medical Emergency',
      difficulty: 'advanced',
      explanation:
          'The unconscious soldier with head trauma requires immediate attention as head injuries can rapidly become life-threatening. Triage principles prioritize the most critically injured who can be saved.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'medical', 'triage', 'emergency'],
    ),

    // Emergency Response Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_010',
      question: '''
SCENARIO: During a natural disaster relief operation, you arrive at a village where a bridge has collapsed, cutting off 200 civilians from medical aid. You have limited engineering equipment and 48 hours before the next supply convoy arrives.

What is your priority action plan?
      ''',
      options: [
        'Wait for specialized engineering units to arrive',
        'Attempt to build a temporary crossing with available materials',
        'Evacuate civilians by helicopter if available',
        'Establish a medical station on the isolated side',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Emergency Response',
      difficulty: 'advanced',
      explanation:
          'Building a temporary crossing with available materials shows initiative and resourcefulness. Military engineers are trained to improvise solutions in emergency situations.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'emergency', 'engineering', 'disaster relief'],
    ),

    QuizQuestionModel(
      id: 'army_scenario_011',
      question: '''
SCENARIO: Your unit is providing security for a humanitarian convoy when you encounter a roadblock set up by local militia demanding "taxes" to pass. The convoy carries critical medical supplies for a refugee camp 50km away.

How do you handle this situation?
      ''',
      options: [
        'Pay the demanded amount to ensure safe passage',
        'Negotiate with militia leaders for free passage',
        'Use force to clear the roadblock immediately',
        'Find an alternative route to avoid confrontation',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Humanitarian Operations',
      difficulty: 'advanced',
      explanation:
          'Negotiating with militia leaders maintains the humanitarian nature of the mission while potentially resolving the situation peacefully. This approach aligns with peacekeeping principles.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'humanitarian', 'negotiation', 'peacekeeping'],
    ),

    // Leadership Challenge Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_012',
      question: '''
SCENARIO: You are a company commander and discover that one of your platoon leaders has been accepting bribes from local contractors. The officer is popular among troops and has a good combat record, but this behavior undermines unit integrity.

What is your course of action?
      ''',
      options: [
        'Handle the matter quietly to avoid damaging morale',
        'Report to higher command and initiate formal investigation',
        'Give the officer a warning and transfer him',
        'Confront the officer privately and demand restitution',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Leadership Ethics',
      difficulty: 'advanced',
      explanation:
          'Reporting to higher command and initiating formal investigation maintains military justice and integrity. Corruption must be addressed through proper channels regardless of personal relationships.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'leadership', 'ethics', 'corruption'],
    ),

    QuizQuestionModel(
      id: 'army_scenario_013',
      question: '''
SCENARIO: During a joint operation with international forces, you receive conflicting orders from your national command and the international mission commander. Both orders are time-sensitive and mutually exclusive.

What is your immediate response?
      ''',
      options: [
        'Follow national command orders as they take priority',
        'Follow international mission commander orders',
        'Request clarification from both command structures',
        'Make an independent decision based on ground situation',
      ],
      correctAnswerIndex: 2,
      category: 'Scenario-Based',
      subcategory: 'Command Structure',
      difficulty: 'advanced',
      explanation:
          'Requesting clarification from both command structures ensures proper coordination and prevents potential diplomatic or operational complications in joint operations.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'command', 'international', 'coordination'],
    ),

    // Resource Management Scenarios (10 questions)
    QuizQuestionModel(
      id: 'army_scenario_014',
      question: '''
SCENARIO: Your battalion is deployed in a remote area for 6 months. Fuel supplies are running critically low due to supply line disruptions, but you need to maintain patrol schedules and generator power for communications.

How do you manage this resource crisis?
      ''',
      options: [
        'Reduce patrol frequency to conserve fuel',
        'Prioritize communications over patrols',
        'Implement strict fuel rationing across all operations',
        'Request emergency resupply via airlift',
      ],
      correctAnswerIndex: 2,
      category: 'Scenario-Based',
      subcategory: 'Resource Management',
      difficulty: 'intermediate',
      explanation:
          'Implementing strict fuel rationing across all operations ensures continued mission capability while maximizing the use of available resources. This approach maintains operational readiness.',
      agency: 'Nigerian Army',
      tags: ['scenario', 'logistics', 'resource management', 'fuel'],
    ),

    // Continue with more scenarios to reach 70 total...
    // [This represents 14 questions so far - continuing with the structured approach]
  ];

  // =================================================================
  // NIGERIAN NAVY SCENARIO QUESTIONS (70 Questions) - Sample
  // =================================================================
  static List<QuizQuestionModel> get nigerianNavyScenarios => [
    QuizQuestionModel(
      id: 'navy_scenario_001',
      question: '''
SCENARIO: Your patrol vessel encounters a suspicious fishing boat in Nigerian territorial waters at night. The boat has no lights and attempts to flee when approached. Your radar shows multiple similar vessels in the area, and you suspect illegal fishing or smuggling activities.

What is your immediate action?
      ''',
      options: [
        'Pursue and board the fleeing vessel immediately',
        'Signal the vessel to stop and identify itself',
        'Report to command and request backup',
        'Monitor from distance and gather intelligence',
      ],
      correctAnswerIndex: 1,
      category: 'Scenario-Based',
      subcategory: 'Maritime Security',
      difficulty: 'intermediate',
      explanation:
          'Signaling the vessel to stop and identify itself follows proper maritime law enforcement procedures. This gives the vessel opportunity to comply before escalating to more forceful measures.',
      agency: 'Nigerian Navy',
      tags: ['scenario', 'maritime', 'security', 'patrol'],
    ),

    // Continue with more Navy scenarios...
    // [This represents 1 question - the full implementation would include all 70]
  ];

  // =================================================================
  // PLACEHOLDER GETTERS FOR OTHER AGENCIES
  // =================================================================
  static List<QuizQuestionModel> get nigerianAirForceScenarios => [];
  static List<QuizQuestionModel> get ndaScenarios => [];
  static List<QuizQuestionModel> get dsscScenarios => [];
  static List<QuizQuestionModel> get polacScenarios => [];
  static List<QuizQuestionModel> get fireServiceScenarios => [];
  static List<QuizQuestionModel> get nscdcScenarios => [];
  static List<QuizQuestionModel> get customsServiceScenarios => [];
  static List<QuizQuestionModel> get immigrationServiceScenarios => [];
  static List<QuizQuestionModel> get frscScenarios => [];
}
