import 'package:flutter/material.dart';
import 'package:fit_4_force/core/services/supabase_auth_service.dart';

/// A dismissible banner that reminds users to verify their email
/// Shows at the top of screens for unverified users
class EmailVerificationBanner extends StatefulWidget {
  const EmailVerificationBanner({super.key});

  @override
  State<EmailVerificationBanner> createState() =>
      _EmailVerificationBannerState();
}

class _EmailVerificationBannerState extends State<EmailVerificationBanner> {
  bool _isVisible = true;
  bool _isResending = false;

  @override
  Widget build(BuildContext context) {
    // Only show if user is authenticated but email is not verified
    final user = SupabaseAuthService.currentUser;
    final isEmailVerified = user?.emailConfirmedAt != null;

    if (!_isVisible || isEmailVerified || user == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      color: Colors.orange.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Icon(Icons.mail_outline, color: Colors.orange.shade700, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Verify your email address',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.orange.shade800,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Check your inbox and click the verification link',
                  style: TextStyle(color: Colors.orange.shade700, fontSize: 12),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          if (_isResending)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation(Colors.orange),
              ),
            )
          else
            TextButton(
              onPressed: _resendVerificationEmail,
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange.shade700,
                minimumSize: const Size(60, 30),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              ),
              child: const Text(
                'Resend',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
              ),
            ),
          const SizedBox(width: 4),
          IconButton(
            onPressed: () => setState(() => _isVisible = false),
            icon: Icon(Icons.close, size: 18, color: Colors.orange.shade700),
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
        ],
      ),
    );
  }

  Future<void> _resendVerificationEmail() async {
    if (_isResending) return;

    setState(() => _isResending = true);

    try {
      final user = SupabaseAuthService.currentUser;
      if (user?.email != null) {
        // Resend verification email using Supabase
        await SupabaseAuthService.resendVerificationEmail(user!.email!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Verification email sent! Check your inbox.'),
              backgroundColor: Colors.green.shade600,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to resend email: $e'),
            backgroundColor: Colors.red.shade600,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isResending = false);
      }
    }
  }
}
