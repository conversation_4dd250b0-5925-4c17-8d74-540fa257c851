import 'package:fit_4_force/core/services/supabase_auth_service.dart'
    as real_supa;
import 'package:fit_4_force/shared/models/user_model.dart';

class SupabaseAuthService {
  static Future<UserModel?> signUp({
    required String email,
    required String password,
    required String fullName,
    required int age,
    required String gender,
    required double height,
    required double weight,
    required String targetAgency,
    required String fitnessGoal,
    required Map<String, bool> notificationPreferences,
  }) async {
    final response = await real_supa.SupabaseAuthService.signUp(
      email: email,
      password: password,
      fullName: fullName,
      targetAgency: targetAgency,
      additionalData: {
        'age': age,
        'gender': gender,
        'height': height,
        'weight': weight,
        'fitness_goal': fitnessGoal,
        'notification_preferences': notificationPreferences,
      },
    );
    if (response.user != null) {
      return await real_supa.SupabaseAuthService.getUserProfile(
        userId: response.user!.id,
      );
    }
    return null;
  }

  static Future<UserModel?> signIn({
    required String email,
    required String password,
  }) async {
    final response = await real_supa.SupabaseAuthService.signIn(
      email: email,
      password: password,
    );
    if (response.user != null) {
      return await real_supa.SupabaseAuthService.getUserProfile(
        userId: response.user!.id,
      );
    }
    return null;
  }

  static Future<void> signOut() async {
    await real_supa.SupabaseAuthService.signOut();
  }

  static Future<UserModel?> getCurrentUser() async {
    return await real_supa.SupabaseAuthService.getUserProfile();
  }

  static bool get isAuthenticated =>
      real_supa.SupabaseAuthService.isAuthenticated;
  static String? get currentUserId =>
      real_supa.SupabaseAuthService.currentUserId;

  static Future<void> updatePremiumStatus(
    String userId,
    bool isPremium,
    DateTime? expiryDate,
  ) async {
    await real_supa.SupabaseAuthService.updatePremiumStatus(
      userId,
      isPremium,
      expiryDate,
    );
  }

  // Instance getters and methods for compatibility with instance usage
  String? getCurrentUserId() => SupabaseAuthService.currentUserId;
  bool getIsAuthenticated() => SupabaseAuthService.isAuthenticated;
  Future<void> callUpdatePremiumStatus(
    String userId,
    bool isPremium,
    DateTime? expiryDate,
  ) async {
    await SupabaseAuthService.updatePremiumStatus(
      userId,
      isPremium,
      expiryDate,
    );
  }

  static Future<void> resendVerificationEmail(String email) async {
    await real_supa.SupabaseAuthService.resendVerificationEmail(email);
  }
}
