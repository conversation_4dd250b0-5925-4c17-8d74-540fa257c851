import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../services/question_bookmark_service.dart';
import '../services/reading_preferences_service.dart';
import '../data/quiz_questions_database.dart';

class BookmarkedQuestionsScreen extends StatefulWidget {
  const BookmarkedQuestionsScreen({super.key});

  @override
  State<BookmarkedQuestionsScreen> createState() => _BookmarkedQuestionsScreenState();
}

class _BookmarkedQuestionsScreenState extends State<BookmarkedQuestionsScreen> {
  final QuestionBookmarkService _bookmarkService = QuestionBookmarkService();
  final ReadingPreferencesService _readingPrefs = ReadingPreferencesService();
  
  List<BookmarkedQuestion> _bookmarkedQuestions = [];
  bool _isLoading = true;
  String _selectedCategory = 'All';

  @override
  void initState() {
    super.initState();
    _loadBookmarkedQuestions();
  }

  Future<void> _loadBookmarkedQuestions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await _bookmarkService.initialize();
      await _readingPrefs.initialize();
      
      final allQuestions = QuizQuestionsDatabase.getAllQuestions();
      final bookmarkedQuestions = await _bookmarkService.getBookmarkedQuestionsWithDetails(allQuestions);
      
      setState(() {
        _bookmarkedQuestions = bookmarkedQuestions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading bookmarks: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  List<BookmarkedQuestion> get _filteredQuestions {
    if (_selectedCategory == 'All') {
      return _bookmarkedQuestions;
    }
    return _bookmarkedQuestions.where((bq) => bq.question.category == _selectedCategory).toList();
  }

  List<String> get _availableCategories {
    final categories = _bookmarkedQuestions.map((bq) => bq.question.category).toSet().toList();
    categories.sort();
    return ['All', ...categories];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bookmarked Questions'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showReadingPreferences,
            icon: const Icon(Icons.text_fields),
            tooltip: 'Reading Preferences',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'clear_all',
                child: Row(
                  children: [
                    Icon(Icons.clear_all, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Clear All Bookmarks'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export Bookmarks'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Category filter
          if (_availableCategories.length > 1) ...[
            Container(
              padding: const EdgeInsets.all(16),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: _availableCategories.map((category) {
                    final isSelected = category == _selectedCategory;
                    return Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: FilterChip(
                        label: Text(category),
                        selected: isSelected,
                        onSelected: (selected) {
                          setState(() {
                            _selectedCategory = category;
                          });
                        },
                        backgroundColor: Colors.grey.shade200,
                        selectedColor: AppTheme.primaryColor.withValues(alpha: 0.2 * 255),
                        checkmarkColor: AppTheme.primaryColor,
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],

          // Content
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredQuestions.isEmpty
                    ? _buildEmptyState()
                    : _buildQuestionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _selectedCategory == 'All' 
                ? 'No bookmarked questions yet'
                : 'No bookmarked questions in $_selectedCategory',
            style: _readingPrefs.getPreferredTextStyle(
              TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Bookmark questions during quizzes to review them later',
            style: _readingPrefs.getPreferredTextStyle(
              TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredQuestions.length,
      itemBuilder: (context, index) {
        final bookmarkedQuestion = _filteredQuestions[index];
        return _buildQuestionCard(bookmarkedQuestion, index);
      },
    );
  }

  Widget _buildQuestionCard(BookmarkedQuestion bookmarkedQuestion, int index) {
    final question = bookmarkedQuestion.question;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    question.category,
                    style: _readingPrefs.getPreferredTextStyle(
                      TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => _removeBookmark(question.id),
                  icon: const Icon(Icons.bookmark, color: Colors.amber),
                  tooltip: 'Remove bookmark',
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Question
            Text(
              question.question,
              style: _readingPrefs.getPreferredTextStyle(
                const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  height: 1.4,
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Options
            ...question.options.asMap().entries.map((entry) {
              final optionIndex = entry.key;
              final option = entry.value;
              final isCorrect = optionIndex == question.correctAnswerIndex;
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isCorrect ? Colors.green.shade50 : Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isCorrect ? Colors.green.shade300 : Colors.grey.shade300,
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isCorrect ? Colors.green : Colors.grey.shade300,
                      ),
                      child: Center(
                        child: isCorrect
                            ? const Icon(Icons.check, size: 16, color: Colors.white)
                            : Text(
                                String.fromCharCode(65 + optionIndex),
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        option,
                        style: _readingPrefs.getPreferredTextStyle(
                          TextStyle(
                            fontSize: 14,
                            fontWeight: isCorrect ? FontWeight.w600 : FontWeight.normal,
                            color: isCorrect ? Colors.green.shade800 : Colors.black87,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
            
            const SizedBox(height: 12),
            
            // Explanation
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, size: 16, color: Colors.blue.shade700),
                      const SizedBox(width: 8),
                      Text(
                        'Explanation',
                        style: _readingPrefs.getPreferredTextStyle(
                          TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    question.explanation,
                    style: _readingPrefs.getPreferredTextStyle(
                      const TextStyle(
                        fontSize: 14,
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            // Note (if any)
            if (bookmarkedQuestion.note != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.note, size: 16, color: Colors.amber.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Your Note',
                          style: _readingPrefs.getPreferredTextStyle(
                            TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.amber.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      bookmarkedQuestion.note!,
                      style: _readingPrefs.getPreferredTextStyle(
                        const TextStyle(
                          fontSize: 14,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showReadingPreferences() {
    showModalBottomSheet(
      context: context,
      builder: (context) => ReadingPreferencesBottomSheet(
        readingPrefs: _readingPrefs,
        onChanged: () => setState(() {}),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'clear_all':
        _showClearAllDialog();
        break;
      case 'export':
        _exportBookmarks();
        break;
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Bookmarks'),
        content: const Text('Are you sure you want to remove all bookmarked questions? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _bookmarkService.clearAllBookmarks();
              await _loadBookmarkedQuestions();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('All bookmarks cleared'),
                    backgroundColor: Colors.orange,
                  ),
                );
              }
            },
            child: const Text('Clear All', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _exportBookmarks() async {
    try {
      final exportData = await _bookmarkService.exportBookmarks();
      // In a real app, you would save this to a file or share it
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bookmarks exported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removeBookmark(String questionId) async {
    await _bookmarkService.toggleBookmark(questionId);
    await _loadBookmarkedQuestions();
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bookmark removed'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}

class ReadingPreferencesBottomSheet extends StatefulWidget {
  final ReadingPreferencesService readingPrefs;
  final VoidCallback onChanged;

  const ReadingPreferencesBottomSheet({
    super.key,
    required this.readingPrefs,
    required this.onChanged,
  });

  @override
  State<ReadingPreferencesBottomSheet> createState() => _ReadingPreferencesBottomSheetState();
}

class _ReadingPreferencesBottomSheetState extends State<ReadingPreferencesBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Reading Preferences',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          
          // Text Size
          Text(
            'Text Size: ${(widget.readingPrefs.textSizeMultiplier * 100).round()}%',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          Slider(
            value: widget.readingPrefs.textSizeMultiplier,
            min: 0.8,
            max: 2.0,
            divisions: 12,
            onChanged: (value) {
              setState(() {
                widget.readingPrefs.textSizeMultiplier = value;
              });
              widget.onChanged();
            },
          ),
          
          const SizedBox(height: 16),
          
          // Reading Speed
          Text(
            'Reading Speed: ${widget.readingPrefs.readingSpeed.displayName}',
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: ReadingSpeed.values.map((speed) {
              return ChoiceChip(
                label: Text(speed.displayName),
                selected: widget.readingPrefs.readingSpeed == speed,
                onSelected: (selected) {
                  if (selected) {
                    setState(() {
                      widget.readingPrefs.readingSpeed = speed;
                    });
                    widget.onChanged();
                  }
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Accessibility Options
          SwitchListTile(
            title: const Text('High Contrast'),
            subtitle: const Text('Improve text visibility'),
            value: widget.readingPrefs.highContrastEnabled,
            onChanged: (value) {
              setState(() {
                widget.readingPrefs.highContrastEnabled = value;
              });
              widget.onChanged();
            },
          ),
          
          SwitchListTile(
            title: const Text('Dyslexia-Friendly Font'),
            subtitle: const Text('Use OpenDyslexic font'),
            value: widget.readingPrefs.dyslexiaFontEnabled,
            onChanged: (value) {
              setState(() {
                widget.readingPrefs.dyslexiaFontEnabled = value;
              });
              widget.onChanged();
            },
          ),
          
          const SizedBox(height: 24),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Done'),
            ),
          ),
        ],
      ),
    );
  }
}
