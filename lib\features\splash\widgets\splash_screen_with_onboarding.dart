import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../features/onboarding/services/onboarding_service.dart';
import '../../../features/splash/screens/animated_splash_screen.dart';
import '../../../features/onboarding/screens/welcome_intro_screen.dart';
import '../../../features/auth/screens/login_screen.dart';
import '../../../features/home/<USER>/home_screen.dart';
import '../../../features/auth/bloc/auth_bloc.dart';

/// Wrapper class that handles the splash to onboarding flow
class SplashScreenWithOnboarding extends StatefulWidget {
  const SplashScreenWithOnboarding({super.key});

  @override
  State<SplashScreenWithOnboarding> createState() =>
      _SplashScreenWithOnboardingState();
}

class _SplashScreenWithOnboardingState
    extends State<SplashScreenWithOnboarding> {
  @override
  void initState() {
    super.initState();
    _handleInitialFlow();
  }

  Future<void> _handleInitialFlow() async {
    // Minimal splash screen duration for fastest startup
    await Future.delayed(
      const Duration(milliseconds: 800), // Reduced from 1500ms
    );

    if (!mounted) return;

    // Check onboarding status only - let AuthBloc handle authentication
    final shouldShowOnboarding = await OnboardingService.shouldShowOnboarding();

    if (!mounted) return;

    // Listen to AuthBloc state to determine navigation
    final authState = context.read<AuthBloc>().state;

    if (authState is Authenticated) {
      // User is already authenticated, go to home
      await OnboardingService.markAppAsUsed();
      if (mounted) {
        _navigateToHome();
      }
    } else if (authState is Unauthenticated) {
      // User is not authenticated, check onboarding
      if (mounted) {
        if (shouldShowOnboarding) {
          _navigateToOnboarding();
        } else {
          _navigateToLogin();
        }
      }
    } else {
      // AuthBloc is still loading, wait for it to complete
      // The BlocListener in main.dart will handle navigation
      return;
    }
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => const HomeScreen(),
        transitionDuration: const Duration(
          milliseconds: 300,
        ), // Faster transition
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  void _navigateToOnboarding() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) =>
                const WelcomeIntroScreen(),
        transitionDuration: const Duration(
          milliseconds: 300,
        ), // Faster transition
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  void _navigateToLogin() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder:
            (context, animation, secondaryAnimation) => const LoginScreen(),
        transitionDuration: const Duration(
          milliseconds: 300,
        ), // Faster transition
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return const AnimatedSplashScreen();
  }
}
