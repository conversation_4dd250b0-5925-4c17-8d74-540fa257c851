/// Model representing comprehensive agency history data
class AgencyHistoryModel {
  final String agency;
  final String abbreviation;
  final int establishedYear;
  final String location;
  final String motto;
  final String overview;
  final String foundingStory;
  final List<MilestoneModel> keyMilestones;
  final List<NotableFigureModel> notableFigures;
  final String traditions;
  final String modernRole;
  final List<String> funFacts;
  final List<String> trainingPrograms;
  final List<String> achievements;

  const AgencyHistoryModel({
    required this.agency,
    required this.abbreviation,
    required this.establishedYear,
    required this.location,
    required this.motto,
    required this.overview,
    required this.foundingStory,
    required this.keyMilestones,
    required this.notableFigures,
    required this.traditions,
    required this.modernRole,
    required this.funFacts,
    required this.trainingPrograms,
    required this.achievements,
  });

  factory AgencyHistoryModel.fromJson(Map<String, dynamic> json) {
    return AgencyHistoryModel(
      agency: json['agency'] ?? '',
      abbreviation: json['abbreviation'] ?? '',
      establishedYear: json['establishedYear'] ?? 0,
      location: json['location'] ?? '',
      motto: json['motto'] ?? '',
      overview: json['overview'] ?? '',
      foundingStory: json['foundingStory'] ?? '',
      keyMilestones:
          (json['keyMilestones'] as List?)
              ?.map((item) => MilestoneModel.fromJson(item))
              .toList() ??
          [],
      notableFigures:
          (json['notableFigures'] as List?)
              ?.map((item) => NotableFigureModel.fromJson(item))
              .toList() ??
          [],
      traditions: json['traditions'] ?? '',
      modernRole: json['modernRole'] ?? '',
      funFacts: (json['funFacts'] as List?)?.cast<String>() ?? [],
      trainingPrograms: (json['trainingPrograms'] as List?)?.cast<String>() ?? [],
      achievements: (json['achievements'] as List?)?.cast<String>() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'agency': agency,
      'abbreviation': abbreviation,
      'establishedYear': establishedYear,
      'location': location,
      'motto': motto,
      'overview': overview,
      'foundingStory': foundingStory,
      'keyMilestones': keyMilestones.map((item) => item.toJson()).toList(),
      'notableFigures': notableFigures.map((item) => item.toJson()).toList(),
      'traditions': traditions,
      'modernRole': modernRole,
    };
  }
}

/// Model representing a key milestone in agency history
class MilestoneModel {
  final String year;
  final String event;
  final String significance;

  const MilestoneModel({
    required this.year,
    required this.event,
    required this.significance,
  });

  factory MilestoneModel.fromJson(Map<String, dynamic> json) {
    return MilestoneModel(
      year: json['year'] ?? '',
      event: json['event'] ?? '',
      significance: json['significance'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'year': year, 'event': event, 'significance': significance};
  }
}

/// Model representing a notable figure in agency history
class NotableFigureModel {
  final String name;
  final String role;
  final String achievement;

  const NotableFigureModel({
    required this.name,
    required this.role,
    required this.achievement,
  });

  factory NotableFigureModel.fromJson(Map<String, dynamic> json) {
    return NotableFigureModel(
      name: json['name'] ?? '',
      role: json['role'] ?? '',
      achievement: json['achievement'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'name': name, 'role': role, 'achievement': achievement};
  }
}
