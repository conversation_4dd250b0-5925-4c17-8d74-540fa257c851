import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:vimeo_player_flutter/vimeo_player_flutter.dart';

class ExerciseDetailScreen extends StatefulWidget {
  final Map<String, dynamic> exercise;
  const ExerciseDetailScreen({super.key, required this.exercise});

  @override
  State<ExerciseDetailScreen> createState() => _ExerciseDetailScreenState();
}

class _ExerciseDetailScreenState extends State<ExerciseDetailScreen>
    with TickerProviderStateMixin {
  VideoPlayerController? _controller;
  bool _isVideoInitialized = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();

    final videoUrl = widget.exercise['videoUrl'] as String?;
    if (videoUrl != null && videoUrl.isNotEmpty && videoUrl.contains('.mp4')) {
      _controller = VideoPlayerController.network(videoUrl)
        ..initialize().then((_) {
          setState(() {
            _isVideoInitialized = true;
          });
        });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ex = widget.exercise;
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // Modern App Bar with Hero Image
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            backgroundColor: theme.primaryColor,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Exercise Image or Video Preview
                  _buildHeroSection(ex),
                  // Gradient Overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                  // Exercise Title at bottom
                  Positioned(
                    left: 20,
                    right: 20,
                    bottom: 80,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Hero(
                          tag: 'exercise_title_${ex['id']}',
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              ex['name'] ?? 'Exercise',
                              style: const TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    offset: Offset(0, 2),
                                    blurRadius: 4,
                                    color: Colors.black54,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildQuickStats(ex),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            leading: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black26,
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              ),
            ),
            actions: [
              Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black26,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.favorite_border, color: Colors.white),
                  onPressed: () {
                    // Add to favorites functionality
                  },
                ),
              ),
            ],
          ),

          // Content Body
          SliverToBoxAdapter(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Exercise Description Card
                      _buildDescriptionCard(ex),
                      const SizedBox(height: 20),

                      // Instructions Section
                      _buildModernSection(
                        'How to Perform',
                        ex['instructions'] ?? _generateInstructions(ex['name']),
                        Icons.play_circle_outline,
                        Colors.blue,
                      ),

                      // Focus Area Section
                      _buildModernSection(
                        'Target Muscles',
                        ex['focusArea'] ?? _generateFocusArea(ex['name']),
                        Icons.psychology,
                        Colors.green,
                      ),

                      // Common Mistakes Section
                      _buildModernSection(
                        'Avoid These Mistakes',
                        ex['commonMistakes'] ??
                            _generateCommonMistakes(ex['name']),
                        Icons.warning_amber_rounded,
                        Colors.orange,
                      ),

                      // Breathing Tips Section
                      _buildModernSection(
                        'Breathing Technique',
                        ex['breathingTips'] ??
                            _generateBreathingTips(ex['name']),
                        Icons.air,
                        Colors.purple,
                      ),

                      const SizedBox(height: 20),

                      // Timer Configuration
                      _buildTimerCard(),

                      const SizedBox(height: 100), // Space for floating button
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingStartButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildHeroSection(Map<String, dynamic> ex) {
    final imageUrl = ex['imageUrl'] as String?;
    final videoUrl = ex['videoUrl'] as String?;

    if (videoUrl != null && videoUrl.isNotEmpty) {
      return _buildVideoSection(videoUrl);
    } else if (imageUrl != null && imageUrl.isNotEmpty) {
      return Hero(
        tag: 'exercise_image_${ex['id']}',
        child:
            imageUrl.startsWith('http')
                ? Image.network(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderImage();
                  },
                )
                : Image.asset(
                  imageUrl,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildPlaceholderImage();
                  },
                ),
      );
    } else {
      return _buildPlaceholderImage();
    }
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.7),
          ],
        ),
      ),
      child: const Center(
        child: Icon(Icons.fitness_center, size: 80, color: Colors.white54),
      ),
    );
  }

  Widget _buildQuickStats(Map<String, dynamic> ex) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (ex['duration'] != null && ex['duration'] > 0) ...[
            const Icon(Icons.timer, size: 16, color: Colors.blue),
            const SizedBox(width: 4),
            Text(
              '${ex['duration']}s',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ] else if (ex['reps'] != null && ex['reps'] > 0) ...[
            const Icon(Icons.repeat, size: 16, color: Colors.green),
            const SizedBox(width: 4),
            Text(
              '${ex['reps']} reps',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
          if (ex['sets'] != null && ex['sets'] > 0) ...[
            const SizedBox(width: 12),
            const Icon(Icons.layers, size: 16, color: Colors.orange),
            const SizedBox(width: 4),
            Text(
              '${ex['sets']} sets',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDescriptionCard(Map<String, dynamic> ex) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About This Exercise',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            ex['description'] ?? _generateDescription(ex['name']),
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernSection(
    String title,
    String content,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        tilePadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        childrenPadding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              content,
              style: TextStyle(
                color: Colors.grey[600],
                height: 1.6,
                fontSize: 15,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimerCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.8),
            Theme.of(context).primaryColor,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.timer, color: Colors.white, size: 24),
              SizedBox(width: 8),
              Text(
                'Exercise Timer',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _TimerSelector(),
        ],
      ),
    );
  }

  Widget _buildFloatingStartButton() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      width: double.infinity,
      height: 56,
      child: FloatingActionButton.extended(
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder:
                (_) => _ModernExerciseTimerSheet(exercise: widget.exercise),
          );
        },
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 8,
        icon: const Icon(Icons.play_arrow, size: 28),
        label: const Text(
          'Start Exercise',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  // Video or YouTube preview
  Widget _buildVideoSection(String url) {
    if (url.contains('youtube.com') || url.contains('youtu.be')) {
      // Show a YouTube preview image and open in browser on tap
      final videoId = _extractYouTubeId(url);
      final thumbUrl =
          videoId != null ? 'https://img.youtube.com/vi/$videoId/0.jpg' : null;
      return GestureDetector(
        onTap: () => _launchUrl(url),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (thumbUrl != null)
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.network(
                  thumbUrl,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                ),
              ),
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black45,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 40,
              ),
            ),
          ],
        ),
      );
    } else if (url.contains('vimeo.com')) {
      return _buildVimeoPlayer(url);
    } else if (_controller != null && _isVideoInitialized) {
      return AspectRatio(
        aspectRatio: _controller!.value.aspectRatio,
        child: VideoPlayer(_controller!),
      );
    } else {
      return Container(
        height: 200,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Center(
          child: Icon(Icons.videocam, size: 48, color: Colors.grey),
        ),
      );
    }
  }

  Widget _buildVimeoPlayer(String url) {
    final vimeoId = _extractVimeoId(url);
    if (vimeoId == null) {
      return _buildPlaceholderImage();
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: SizedBox(height: 300, child: VimeoPlayer(videoId: vimeoId)),
    );
  }

  String? _extractYouTubeId(String url) {
    final regExp = RegExp(r'(?:v=|\/)([0-9A-Za-z_-]{11}).*');
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  String? _extractVimeoId(String url) {
    final regExp = RegExp(r'vimeo.com/(\d+)');
    final match = regExp.firstMatch(url);
    return match?.group(1);
  }

  void _launchUrl(String url) async {
    // Use url_launcher package in your project for this to work
    // await launchUrl(Uri.parse(url));
  }

  String _generateDescription(String? name) {
    if (name == null) return 'A great exercise to improve your fitness.';
    return 'This exercise targets specific muscle groups and helps improve your overall strength and endurance. Follow the instructions carefully for maximum benefit.';
  }

  String _generateInstructions(String? name) {
    if (name == null) return '';
    switch (name.toLowerCase()) {
      case 'jumping jacks':
        return '1. Stand with feet together and arms at your sides\n2. Jump while spreading legs shoulder-width apart and raising arms overhead\n3. Jump back to starting position\n4. Repeat in a rhythmic motion';
      case 'push-ups':
        return '1. Start in plank position with hands slightly wider than shoulders\n2. Lower your body until chest nearly touches the floor\n3. Push back up to starting position\n4. Keep your body in a straight line throughout';
      case 'squats':
        return '1. Stand with feet shoulder-width apart\n2. Lower your body by bending your knees and pushing hips back\n3. Go down until thighs are parallel to the floor\n4. Push through heels to return to starting position';
      case 'plank':
        return '1. Start in push-up position\n2. Lower to forearms, keeping elbows under shoulders\n3. Keep body in straight line from head to heels\n4. Hold position while breathing normally';
      default:
        return 'Follow the video demonstration and maintain proper form throughout the movement. Start slowly and focus on technique before increasing speed or intensity.';
    }
  }

  String _generateFocusArea(String? name) {
    if (name == null) return '';
    switch (name.toLowerCase()) {
      case 'jumping jacks':
        return 'Full body cardio exercise targeting legs, arms, and core while improving cardiovascular endurance';
      case 'push-ups':
        return 'Upper body strength focusing on chest, shoulders, triceps, and core stabilization';
      case 'squats':
        return 'Lower body strength targeting quadriceps, glutes, hamstrings, and core';
      case 'plank':
        return 'Core stability exercise strengthening abs, back, shoulders, and improving posture';
      default:
        return 'Multiple muscle groups working together for improved strength and coordination';
    }
  }

  String _generateCommonMistakes(String? name) {
    if (name == null) return '';
    switch (name.toLowerCase()) {
      case 'jumping jacks':
        return '• Landing too hard on feet\n• Not fully extending arms overhead\n• Irregular rhythm or pace\n• Poor posture during movement';
      case 'push-ups':
        return '• Sagging hips or raised buttocks\n• Not going down far enough\n• Placing hands too wide or narrow\n• Holding breath during movement';
      case 'squats':
        return '• Knees caving inward\n• Not going low enough\n• Weight on toes instead of heels\n• Leaning too far forward';
      case 'plank':
        return '• Dropping hips too low\n• Raising hips too high\n• Looking up instead of down\n• Holding breath';
      default:
        return '• Rushing through movements\n• Poor form for speed\n• Not breathing properly\n• Ignoring proper warm-up';
    }
  }

  String _generateBreathingTips(String? name) {
    if (name == null) return '';
    switch (name.toLowerCase()) {
      case 'jumping jacks':
        return 'Inhale for 2 jumps, exhale for 2 jumps. Maintain steady breathing rhythm throughout the exercise.';
      case 'push-ups':
        return 'Inhale as you lower down, exhale as you push up. Keep breathing steady and controlled.';
      case 'squats':
        return 'Inhale as you lower down, exhale as you stand up. Take a deep breath at the top if needed.';
      case 'plank':
        return 'Breathe normally and steadily. Avoid holding your breath - this is crucial for maintaining the position.';
      default:
        return 'Maintain steady, controlled breathing. Exhale during the effort phase, inhale during the return phase.';
    }
  }
}

// Modern Timer selector widget
class _TimerSelector extends StatefulWidget {
  @override
  State<_TimerSelector> createState() => _TimerSelectorState();
}

class _TimerSelectorState extends State<_TimerSelector> {
  String _level = 'Beginner';
  final Map<String, int> _durations = {
    'Beginner': 30,
    'Intermediate': 45,
    'Advanced': 60,
  };

  final Map<String, String> _descriptions = {
    'Beginner': 'Perfect for getting started',
    'Intermediate': 'Ready for more challenge',
    'Advanced': 'Maximum intensity workout',
  };

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Your Level',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...(_durations.keys.map(
          (level) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: () {
                  setState(() {
                    _level = level;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        _level == level
                            ? Colors.white.withOpacity(0.2)
                            : Colors.white.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          _level == level
                              ? Colors.white
                              : Colors.white.withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color:
                              _level == level
                                  ? Colors.white
                                  : Colors.transparent,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child:
                            _level == level
                                ? Icon(
                                  Icons.check,
                                  size: 12,
                                  color: Theme.of(context).primaryColor,
                                )
                                : null,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              level,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${_durations[level]}s • ${_descriptions[level]}',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        )),
      ],
    );
  }
}

// Modern Exercise timer bottom sheet
class _ModernExerciseTimerSheet extends StatefulWidget {
  final Map<String, dynamic> exercise;

  const _ModernExerciseTimerSheet({required this.exercise});

  @override
  State<_ModernExerciseTimerSheet> createState() =>
      _ModernExerciseTimerSheetState();
}

class _ModernExerciseTimerSheetState extends State<_ModernExerciseTimerSheet>
    with TickerProviderStateMixin {
  String _currentLevel = 'Beginner';
  final Map<String, int> _durations = {
    'Beginner': 30,
    'Intermediate': 45,
    'Advanced': 60,
  };

  int _remaining = 30;
  bool _isRunning = false;
  bool _isPaused = false;
  late AnimationController _pulseController;
  late AnimationController _progressController;

  @override
  void initState() {
    super.initState();
    _remaining = _durations[_currentLevel]!;

    _pulseController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: Duration(seconds: _durations[_currentLevel]!),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _startTimer() {
    if (_isPaused) {
      setState(() {
        _isRunning = true;
        _isPaused = false;
      });
      _progressController.forward();
    } else {
      setState(() {
        _isRunning = true;
        _remaining = _durations[_currentLevel]!;
      });
      _progressController.reset();
      _progressController.forward();
    }

    _pulseController.repeat();

    Future.doWhile(() async {
      if (_remaining > 0 && _isRunning && !_isPaused) {
        await Future.delayed(const Duration(seconds: 1));
        if (!mounted) return false;
        setState(() {
          _remaining--;
        });

        if (_remaining == 0) {
          _pulseController.stop();
          _showCompletionDialog();
        }

        return _remaining > 0;
      }
      return false;
    });
  }

  void _pauseTimer() {
    setState(() {
      _isPaused = true;
      _isRunning = false;
    });
    _progressController.stop();
    _pulseController.stop();
  }

  void _resetTimer() {
    setState(() {
      _isRunning = false;
      _isPaused = false;
      _remaining = _durations[_currentLevel]!;
    });
    _progressController.reset();
    _pulseController.stop();
  }

  void _showCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text('🎉 Exercise Complete!'),
            content: Text('Great job completing ${widget.exercise['name']}!'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pop(context);
                },
                child: const Text('Finish'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _resetTimer();
                },
                child: const Text('Do Another Set'),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final progress =
        _durations[_currentLevel]! > 0
            ? (_durations[_currentLevel]! - _remaining) /
                _durations[_currentLevel]!
            : 0.0;

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  // Header
                  Text(
                    widget.exercise['name'] ?? 'Exercise',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const Spacer(),

                  // Timer Circle
                  SizedBox(
                    width: 250,
                    height: 250,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // Background circle
                        CircularProgressIndicator(
                          value: 1.0,
                          strokeWidth: 8,
                          backgroundColor: Colors.grey[200],
                          valueColor: AlwaysStoppedAnimation(Colors.grey[200]!),
                        ),
                        // Progress circle
                        AnimatedBuilder(
                          animation: _pulseController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale:
                                  _isRunning && !_isPaused
                                      ? 1.0 + (_pulseController.value * 0.05)
                                      : 1.0,
                              child: CircularProgressIndicator(
                                value: progress,
                                strokeWidth: 8,
                                valueColor: AlwaysStoppedAnimation(
                                  Theme.of(context).primaryColor,
                                ),
                              ),
                            );
                          },
                        ),
                        // Timer text
                        Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              '$_remaining',
                              style: TextStyle(
                                fontSize: 64,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                            Text(
                              'seconds',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Level selector (only when not running)
                  if (!_isRunning && !_isPaused) ...[
                    Text(
                      'Select Difficulty',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children:
                          _durations.keys.map((level) {
                            final isSelected = _currentLevel == level;
                            return Expanded(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _currentLevel = level;
                                      _remaining = _durations[level]!;
                                    });
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          isSelected
                                              ? Theme.of(context).primaryColor
                                              : Colors.grey[100],
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          level,
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color:
                                                isSelected
                                                    ? Colors.white
                                                    : Colors.black,
                                            fontSize: 12,
                                          ),
                                        ),
                                        Text(
                                          '${_durations[level]}s',
                                          style: TextStyle(
                                            color:
                                                isSelected
                                                    ? Colors.white.withOpacity(
                                                      0.8,
                                                    )
                                                    : Colors.grey[600],
                                            fontSize: 10,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                    const SizedBox(height: 32),
                  ],

                  // Control buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (_isRunning || _isPaused) ...[
                        ElevatedButton.icon(
                          onPressed: _resetTimer,
                          icon: const Icon(Icons.refresh),
                          label: const Text('Reset'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                      ],

                      ElevatedButton.icon(
                        onPressed: _isRunning ? _pauseTimer : _startTimer,
                        icon: Icon(
                          _isRunning
                              ? Icons.pause
                              : _isPaused
                              ? Icons.play_arrow
                              : Icons.play_arrow,
                        ),
                        label: Text(
                          _isRunning
                              ? 'Pause'
                              : _isPaused
                              ? 'Resume'
                              : 'Start',
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).primaryColor,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
