import 'package:flutter/material.dart';

/// A mock payment service for testing and development purposes.
class MockPaymentService {
  Future<bool> processPayment({
    required BuildContext context,
    required String email,
    required String fullName,
    required double amount,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    // Simulate a delay for payment processing
    await Future.delayed(const Duration(seconds: 2));
    // Always succeed for mock
    onSuccess?.call('Mock payment successful!');
    return true;
  }
}
