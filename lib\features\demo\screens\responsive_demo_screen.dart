import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/widgets/responsive_test_widget.dart';

class ResponsiveDemoScreen extends StatefulWidget {
  const ResponsiveDemoScreen({super.key});

  @override
  State<ResponsiveDemoScreen> createState() => _ResponsiveDemoScreenState();
}

class _ResponsiveDemoScreenState extends State<ResponsiveDemoScreen> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return ResponsiveTestWidget(
      child: Scaffold(
        appBar: AppBar(
          title: ResponsiveText(
            'Fit4Force - Responsive Demo',
            mobileFontSize: 16.0,
            tabletFontSize: 18.0,
            desktopFontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          elevation: ResponsiveUtils.getResponsiveElevation(context),
        ),
        body: ResponsiveMultiPaneLayout(
          primaryPane: _buildMainContent(),
          secondaryPane:
              ResponsiveUtils.supportsMultiPane(context)
                  ? _buildSidePanel()
                  : null,
          primaryFlex: 2,
          secondaryFlex: 1,
        ),
        bottomNavigationBar: _buildResponsiveBottomNav(),
      ),
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWelcomeSection(),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildResponsiveGrid(),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildResponsiveCards(),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildResponsiveButtons(),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildResponsiveImages(),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection() {
    final layoutType = ResponsiveUtils.getLayoutType(context);

    return ResponsiveCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Welcome to Fit4Force',
            mobileFontSize: 24.0,
            tabletFontSize: 28.0,
            desktopFontSize: 32.0,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          ResponsiveText(
            'This demo showcases our comprehensive responsive design system.',
            mobileFontSize: 14.0,
            tabletFontSize: 16.0,
            desktopFontSize: 18.0,
            color: Colors.grey[600],
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
              borderRadius: BorderRadius.circular(
                ResponsiveUtils.getResponsiveBorderRadius(context),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: ResponsiveText(
                    'Current Layout: ${layoutType.name}',
                    mobileFontSize: 12.0,
                    tabletFontSize: 14.0,
                    desktopFontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Responsive Grid System',
          mobileFontSize: 18.0,
          tabletFontSize: 20.0,
          desktopFontSize: 22.0,
          fontWeight: FontWeight.bold,
        ),
        SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
        ResponsiveGridView(
          mobileColumns: 2,
          tabletColumns: 3,
          desktopColumns: 4,
          childAspectRatio: 1.2,
          children: List.generate(8, (index) {
            final colors = [
              Colors.blue,
              Colors.green,
              Colors.orange,
              Colors.purple,
            ];
            final icons = [
              Icons.fitness_center,
              Icons.book,
              Icons.group,
              Icons.star,
            ];

            return ResponsiveCard(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colors[index % colors.length].withValues(
                        alpha: 0.1 * 255,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icons[index % icons.length],
                      color: colors[index % colors.length],
                      size: ResponsiveUtils.getResponsiveFontSize(
                        context,
                        mobile: 24,
                        tablet: 28,
                        desktop: 32,
                      ),
                    ),
                  ),
                  SizedBox(
                    height: ResponsiveUtils.getResponsiveSpacing(context) / 3,
                  ),
                  ResponsiveText(
                    'Item ${index + 1}',
                    mobileFontSize: 12.0,
                    tabletFontSize: 14.0,
                    desktopFontSize: 16.0,
                    fontWeight: FontWeight.w600,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildResponsiveCards() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Responsive Cards',
          mobileFontSize: 18.0,
          tabletFontSize: 20.0,
          desktopFontSize: 22.0,
          fontWeight: FontWeight.bold,
        ),
        SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
        ResponsiveGridView(
          mobileColumns: 1,
          tabletColumns: 2,
          desktopColumns: 3,
          childAspectRatio: 1.5,
          crossAxisSpacing: ResponsiveUtils.getResponsiveSpacing(context),
          mainAxisSpacing: ResponsiveUtils.getResponsiveSpacing(context) / 2,
          children: [
            _buildFeatureCard(
              'Fitness Training',
              Icons.fitness_center,
              Colors.green,
            ),
            _buildFeatureCard('Study Materials', Icons.book, Colors.blue),
            _buildFeatureCard('Community', Icons.group, Colors.purple),
          ],
        ),
      ],
    );
  }

  Widget _buildFeatureCard(String title, IconData icon, Color color) {
    return ResponsiveCard(
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(
              ResponsiveUtils.getResponsiveSpacing(context) / 2,
            ),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1 * 255),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: ResponsiveUtils.getResponsiveFontSize(
                context,
                mobile: 32,
                tablet: 36,
                desktop: 40,
              ),
            ),
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
          ResponsiveText(
            title,
            mobileFontSize: 14.0,
            tabletFontSize: 16.0,
            desktopFontSize: 18.0,
            fontWeight: FontWeight.bold,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 3),
          ResponsiveText(
            'Comprehensive ${title.toLowerCase()} features designed for Nigerian military aspirants.',
            mobileFontSize: 12.0,
            tabletFontSize: 13.0,
            desktopFontSize: 14.0,
            color: Colors.grey[600],
            textAlign: TextAlign.center,
            maxLines: 3,
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Responsive Buttons',
          mobileFontSize: 18.0,
          tabletFontSize: 20.0,
          desktopFontSize: 22.0,
          fontWeight: FontWeight.bold,
        ),
        SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
        Wrap(
          spacing: ResponsiveUtils.getResponsiveSpacing(context) / 2,
          runSpacing: ResponsiveUtils.getResponsiveSpacing(context) / 2,
          children: [
            ResponsiveButton(
              text: 'Primary Action',
              backgroundColor: AppTheme.primaryColor,
              textColor: Colors.white,
              onPressed: () {},
            ),
            ResponsiveButton(
              text: 'Secondary',
              backgroundColor: Colors.grey[200],
              textColor: Colors.black87,
              onPressed: () {},
            ),
            ResponsiveButton(
              text: 'With Icon',
              backgroundColor: Colors.green,
              textColor: Colors.white,
              icon: const Icon(Icons.check, color: Colors.white),
              onPressed: () {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildResponsiveImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ResponsiveText(
          'Responsive Images',
          mobileFontSize: 18.0,
          tabletFontSize: 20.0,
          desktopFontSize: 22.0,
          fontWeight: FontWeight.bold,
        ),
        SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context) / 2),
        ResponsiveImage(
          imageUrl: 'assets/images/content/fitness.jpg',
          mobileHeight: 150,
          tabletHeight: 200,
          desktopHeight: 250,
        ),
      ],
    );
  }

  Widget _buildSidePanel() {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ResponsiveText(
            'Quick Stats',
            mobileFontSize: 16.0,
            tabletFontSize: 18.0,
            desktopFontSize: 20.0,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),
          _buildStatItem(
            'Screen Width',
            '${MediaQuery.of(context).size.width.toInt()}px',
          ),
          _buildStatItem(
            'Screen Height',
            '${MediaQuery.of(context).size.height.toInt()}px',
          ),
          _buildStatItem(
            'Device Type',
            ResponsiveUtils.isMobile(context)
                ? 'Mobile'
                : ResponsiveUtils.isTablet(context)
                ? 'Tablet'
                : 'Desktop',
          ),
          _buildStatItem(
            'Layout Type',
            ResponsiveUtils.getLayoutType(context).name,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: ResponsiveUtils.getResponsiveSpacing(context) / 2,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ResponsiveText(
            label,
            mobileFontSize: 12.0,
            tabletFontSize: 13.0,
            desktopFontSize: 14.0,
            color: Colors.grey[600],
          ),
          ResponsiveText(
            value,
            mobileFontSize: 12.0,
            tabletFontSize: 13.0,
            desktopFontSize: 14.0,
            fontWeight: FontWeight.bold,
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveBottomNav() {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);

    return BottomNavigationBar(
      currentIndex: _selectedIndex,
      onTap: (index) => setState(() => _selectedIndex = index),
      type: BottomNavigationBarType.fixed,
      selectedFontSize: isSmallScreen ? 10.0 : (isLandscape ? 11.0 : 12.0),
      unselectedFontSize: isSmallScreen ? 9.0 : (isLandscape ? 10.0 : 11.0),
      iconSize: isSmallScreen ? 20.0 : (isLandscape ? 22.0 : 24.0),
      items: [
        BottomNavigationBarItem(
          icon: const Icon(Icons.dashboard),
          label: isLandscape ? 'Home' : 'Dashboard',
        ),
        BottomNavigationBarItem(
          icon: const Icon(Icons.fitness_center),
          label: 'Fitness',
        ),
        BottomNavigationBarItem(icon: const Icon(Icons.book), label: 'Study'),
        BottomNavigationBarItem(
          icon: const Icon(Icons.group),
          label: isLandscape ? 'Chat' : 'Community',
        ),
      ],
    );
  }
}
