import 'package:flutter/material.dart';
import '../models/personalized_content_model.dart';

/// Service for providing personalized content based on user's target agency
class PersonalizedContentService {
  
  /// Get personalized content for a specific agency
  static List<PersonalizedContentModel> getPersonalizedContent(String? targetAgency) {
    final agency = targetAgency ?? 'Nigerian Army';
    
    switch (agency) {
      case 'Nigerian Army':
        return _getArmyContent();
      case 'Nigerian Navy':
        return _getNavyContent();
      case 'Nigerian Air Force':
        return _getAirForceContent();
      case 'Nigerian Defence Academy':
        return _getNDAContent();
      case 'DSSC/SSC':
        return _getDSSCContent();
      case 'Nigeria Police Academy':
        return _getPoliceContent();
      case 'Fire Service':
        return _getFireServiceContent();
      case 'NSCDC':
        return _getNSCDCContent();
      case 'Customs':
        return _getCustomsContent();
      case 'Immigration':
        return _getImmigrationContent();
      case 'FRSC':
        return _getFRSCContent();
      default:
        return _getGeneralContent();
    }
  }

  /// Get featured content for dashboard
  static List<PersonalizedContentModel> getFeaturedContent(String? targetAgency) {
    final allContent = getPersonalizedContent(targetAgency);
    return allContent.where((content) => content.isFeatured).take(5).toList();
  }

  // =================================================================
  // AGENCY-SPECIFIC CONTENT
  // =================================================================

  static List<PersonalizedContentModel> _getArmyContent() {
    return [
      PersonalizedContentModel(
        id: 'army_001',
        title: 'Army Physical Fitness Standards',
        description: 'Complete guide to Nigerian Army fitness requirements and training protocols',
        category: 'Fitness',
        contentType: ContentType.guide,
        imageUrl: 'assets/images/content/army_fitness.jpg',
        color: const Color(0xFF2E7D32),
        isFeatured: true,
        estimatedReadTime: 8,
        difficulty: 'Intermediate',
        tags: ['fitness', 'army', 'standards', 'training'],
        onTap: (context) => _navigateToFitness(context),
      ),
      PersonalizedContentModel(
        id: 'army_002',
        title: 'Nigerian Army Interview Preparation',
        description: 'Expert tips and common questions for Army recruitment interviews',
        category: 'Career',
        contentType: ContentType.tips,
        imageUrl: 'assets/images/content/army_interview.jpg',
        color: const Color(0xFF1976D2),
        isFeatured: true,
        estimatedReadTime: 12,
        difficulty: 'Advanced',
        tags: ['interview', 'army', 'career', 'preparation'],
        onTap: (context) => _navigateToInterviewPrep(context, 'Nigerian Army'),
      ),
      PersonalizedContentModel(
        id: 'army_003',
        title: 'Military History & Traditions',
        description: 'Essential knowledge about Nigerian Army history and military traditions',
        category: 'Knowledge',
        contentType: ContentType.study,
        imageUrl: 'assets/images/content/army_history.jpg',
        color: const Color(0xFF7B1FA2),
        isFeatured: true,
        estimatedReadTime: 15,
        difficulty: 'Intermediate',
        tags: ['history', 'army', 'traditions', 'knowledge'],
        onTap: (context) => _navigateToStudyMaterials(context, 'Military History'),
      ),
      PersonalizedContentModel(
        id: 'army_004',
        title: 'Combat Leadership Principles',
        description: 'Learn the fundamental leadership principles taught in the Nigerian Army',
        category: 'Leadership',
        contentType: ContentType.course,
        imageUrl: 'assets/images/content/army_leadership.jpg',
        color: const Color(0xFFE65100),
        isFeatured: false,
        estimatedReadTime: 20,
        difficulty: 'Advanced',
        tags: ['leadership', 'army', 'combat', 'principles'],
        onTap: (context) => _navigateToLeadershipCourse(context),
      ),
      PersonalizedContentModel(
        id: 'army_005',
        title: 'Army Rank Structure & Insignia',
        description: 'Complete guide to Nigerian Army ranks, insignia, and hierarchy',
        category: 'Knowledge',
        contentType: ContentType.reference,
        imageUrl: 'assets/images/content/army_ranks.jpg',
        color: const Color(0xFF388E3C),
        isFeatured: false,
        estimatedReadTime: 10,
        difficulty: 'Beginner',
        tags: ['ranks', 'army', 'insignia', 'hierarchy'],
        onTap: (context) => _navigateToRankGuide(context, 'Nigerian Army'),
      ),
    ];
  }

  static List<PersonalizedContentModel> _getNavyContent() {
    return [
      PersonalizedContentModel(
        id: 'navy_001',
        title: 'Naval Swimming Requirements',
        description: 'Master the swimming standards required for Nigerian Navy recruitment',
        category: 'Fitness',
        contentType: ContentType.guide,
        imageUrl: 'assets/images/content/navy_swimming.jpg',
        color: const Color(0xFF1565C0),
        isFeatured: true,
        estimatedReadTime: 10,
        difficulty: 'Intermediate',
        tags: ['swimming', 'navy', 'fitness', 'requirements'],
        onTap: (context) => _navigateToSwimmingGuide(context),
      ),
      PersonalizedContentModel(
        id: 'navy_002',
        title: 'Maritime Operations Basics',
        description: 'Introduction to naval operations and seamanship fundamentals',
        category: 'Knowledge',
        contentType: ContentType.course,
        imageUrl: 'assets/images/content/navy_operations.jpg',
        color: const Color(0xFF0277BD),
        isFeatured: true,
        estimatedReadTime: 18,
        difficulty: 'Advanced',
        tags: ['maritime', 'navy', 'operations', 'seamanship'],
        onTap: (context) => _navigateToMaritimeCourse(context),
      ),
      PersonalizedContentModel(
        id: 'navy_003',
        title: 'Navy Interview Success Guide',
        description: 'Specialized interview preparation for Nigerian Navy candidates',
        category: 'Career',
        contentType: ContentType.tips,
        imageUrl: 'assets/images/content/navy_interview.jpg',
        color: const Color(0xFF1976D2),
        isFeatured: true,
        estimatedReadTime: 12,
        difficulty: 'Advanced',
        tags: ['interview', 'navy', 'career', 'preparation'],
        onTap: (context) => _navigateToInterviewPrep(context, 'Nigerian Navy'),
      ),
    ];
  }

  static List<PersonalizedContentModel> _getAirForceContent() {
    return [
      PersonalizedContentModel(
        id: 'airforce_001',
        title: 'Aviation Fundamentals',
        description: 'Basic principles of flight and aviation for Air Force aspirants',
        category: 'Knowledge',
        contentType: ContentType.course,
        imageUrl: 'assets/images/content/airforce_aviation.jpg',
        color: const Color(0xFF0277BD),
        isFeatured: true,
        estimatedReadTime: 25,
        difficulty: 'Advanced',
        tags: ['aviation', 'airforce', 'flight', 'fundamentals'],
        onTap: (context) => _navigateToAviationCourse(context),
      ),
      PersonalizedContentModel(
        id: 'airforce_002',
        title: 'Air Force Fitness Standards',
        description: 'Physical fitness requirements and training for Nigerian Air Force',
        category: 'Fitness',
        contentType: ContentType.guide,
        imageUrl: 'assets/images/content/airforce_fitness.jpg',
        color: const Color(0xFF2E7D32),
        isFeatured: true,
        estimatedReadTime: 8,
        difficulty: 'Intermediate',
        tags: ['fitness', 'airforce', 'standards', 'training'],
        onTap: (context) => _navigateToFitness(context),
      ),
      PersonalizedContentModel(
        id: 'airforce_003',
        title: 'Air Force Interview Guide',
        description: 'Comprehensive interview preparation for Air Force recruitment',
        category: 'Career',
        contentType: ContentType.tips,
        imageUrl: 'assets/images/content/airforce_interview.jpg',
        color: const Color(0xFF1976D2),
        isFeatured: true,
        estimatedReadTime: 12,
        difficulty: 'Advanced',
        tags: ['interview', 'airforce', 'career', 'preparation'],
        onTap: (context) => _navigateToInterviewPrep(context, 'Nigerian Air Force'),
      ),
    ];
  }

  static List<PersonalizedContentModel> _getNDAContent() {
    return [
      PersonalizedContentModel(
        id: 'nda_001',
        title: 'NDA Entrance Exam Strategy',
        description: 'Complete preparation guide for Nigerian Defence Academy entrance exam',
        category: 'Academics',
        contentType: ContentType.study,
        imageUrl: 'assets/images/content/nda_exam.jpg',
        color: const Color(0xFF6A1B9A),
        isFeatured: true,
        estimatedReadTime: 20,
        difficulty: 'Advanced',
        tags: ['nda', 'exam', 'entrance', 'strategy'],
        onTap: (context) => _navigateToNDAExamPrep(context),
      ),
      PersonalizedContentModel(
        id: 'nda_002',
        title: 'Officer Leadership Training',
        description: 'Leadership principles and officer development at NDA',
        category: 'Leadership',
        contentType: ContentType.course,
        imageUrl: 'assets/images/content/nda_leadership.jpg',
        color: const Color(0xFFE65100),
        isFeatured: true,
        estimatedReadTime: 22,
        difficulty: 'Advanced',
        tags: ['nda', 'leadership', 'officer', 'training'],
        onTap: (context) => _navigateToLeadershipCourse(context),
      ),
    ];
  }

  static List<PersonalizedContentModel> _getPoliceContent() {
    return [
      PersonalizedContentModel(
        id: 'police_001',
        title: 'Law Enforcement Basics',
        description: 'Fundamental principles of law enforcement and police procedures',
        category: 'Knowledge',
        contentType: ContentType.course,
        imageUrl: 'assets/images/content/police_law.jpg',
        color: const Color(0xFF424242),
        isFeatured: true,
        estimatedReadTime: 18,
        difficulty: 'Intermediate',
        tags: ['police', 'law', 'enforcement', 'procedures'],
        onTap: (context) => _navigateToLawEnforcementCourse(context),
      ),
      PersonalizedContentModel(
        id: 'police_002',
        title: 'Police Interview Preparation',
        description: 'Specialized interview guide for Nigeria Police Academy candidates',
        category: 'Career',
        contentType: ContentType.tips,
        imageUrl: 'assets/images/content/police_interview.jpg',
        color: const Color(0xFF1976D2),
        isFeatured: true,
        estimatedReadTime: 12,
        difficulty: 'Advanced',
        tags: ['police', 'interview', 'career', 'preparation'],
        onTap: (context) => _navigateToInterviewPrep(context, 'Nigeria Police Academy'),
      ),
    ];
  }

  static List<PersonalizedContentModel> _getGeneralContent() {
    return [
      PersonalizedContentModel(
        id: 'general_001',
        title: 'General Fitness Training',
        description: 'Universal fitness preparation for all military and paramilitary agencies',
        category: 'Fitness',
        contentType: ContentType.guide,
        imageUrl: 'assets/images/content/general_fitness.jpg',
        color: const Color(0xFF2E7D32),
        isFeatured: true,
        estimatedReadTime: 10,
        difficulty: 'Intermediate',
        tags: ['fitness', 'general', 'training', 'preparation'],
        onTap: (context) => _navigateToFitness(context),
      ),
      PersonalizedContentModel(
        id: 'general_002',
        title: 'Interview Success Tips',
        description: 'Universal interview preparation strategies for all agencies',
        category: 'Career',
        contentType: ContentType.tips,
        imageUrl: 'assets/images/content/general_interview.jpg',
        color: const Color(0xFF1976D2),
        isFeatured: true,
        estimatedReadTime: 12,
        difficulty: 'Intermediate',
        tags: ['interview', 'general', 'career', 'tips'],
        onTap: (context) => _navigateToInterviewPrep(context, 'General'),
      ),
    ];
  }

  // Add other agency content methods...
  static List<PersonalizedContentModel> _getDSSCContent() => _getGeneralContent();
  static List<PersonalizedContentModel> _getFireServiceContent() => _getGeneralContent();
  static List<PersonalizedContentModel> _getNSCDCContent() => _getGeneralContent();
  static List<PersonalizedContentModel> _getCustomsContent() => _getGeneralContent();
  static List<PersonalizedContentModel> _getImmigrationContent() => _getGeneralContent();
  static List<PersonalizedContentModel> _getFRSCContent() => _getGeneralContent();

  // =================================================================
  // NAVIGATION METHODS
  // =================================================================

  static void _navigateToFitness(BuildContext context) {
    Navigator.pushNamed(context, '/workout-categories');
  }

  static void _navigateToInterviewPrep(BuildContext context, String agency) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$agency Interview Preparation - Coming Soon!'),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToStudyMaterials(BuildContext context, String category) {
    Navigator.pushNamed(context, '/prep-dashboard');
  }

  static void _navigateToLeadershipCourse(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Leadership Course - Coming Soon!'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToRankGuide(BuildContext context, String agency) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$agency Rank Guide - Coming Soon!'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToSwimmingGuide(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Swimming Guide - Coming Soon!'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToMaritimeCourse(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Maritime Operations Course - Coming Soon!'),
        backgroundColor: Colors.indigo,
        duration: Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToAviationCourse(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Aviation Fundamentals Course - Coming Soon!'),
        backgroundColor: Colors.lightBlue,
        duration: Duration(seconds: 3),
      ),
    );
  }

  static void _navigateToNDAExamPrep(BuildContext context) {
    Navigator.pushNamed(context, '/prep-dashboard');
  }

  static void _navigateToLawEnforcementCourse(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Law Enforcement Course - Coming Soon!'),
        backgroundColor: Colors.grey,
        duration: Duration(seconds: 3),
      ),
    );
  }
}
