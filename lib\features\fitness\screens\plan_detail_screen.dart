import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/navigation_service.dart';
import 'package:fit_4_force/features/fitness/models/workout_model.dart';
import 'package:fit_4_force/features/fitness/services/workout_service.dart';
import 'package:fit_4_force/shared/models/user_model.dart';

class PlanDetailScreen extends StatefulWidget {
  final String planId;
  final UserModel? user;

  const PlanDetailScreen({super.key, required this.planId, this.user});

  @override
  State<PlanDetailScreen> createState() => _PlanDetailScreenState();
}

class _PlanDetailScreenState extends State<PlanDetailScreen> {
  final WorkoutService _workoutService = WorkoutService();
  late WorkoutPlanModel _plan;
  bool _isLoading = true;
  bool _isPremiumUser = false;

  @override
  void initState() {
    super.initState();
    _loadPlanData();
    _isPremiumUser = widget.user?.isPremium ?? false;
  }

  Future<void> _loadPlanData() async {
    // In a real app, this would be an async call to a service
    final plan = _workoutService.getPlanById(widget.planId);

    if (plan != null) {
      setState(() {
        _plan = plan;
        _isLoading = false;
      });
    } else {
      // Handle plan not found
      NavigationService().goBack();
    }
  }

  void _startPlan() {
    if (_plan.isPremium && !_isPremiumUser) {
      _showPremiumDialog();
      return;
    }

    // In a real app, this would enroll the user in the plan
    // For now, just navigate to the first workout
    if (_plan.workouts.isNotEmpty) {
      NavigationService().navigateTo(
        '/workout-detail',
        arguments: _plan.workouts.first.id,
      );
    }
  }

  void _showPremiumDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.workspace_premium, color: AppTheme.premiumColor),
                const SizedBox(width: 8),
                const Text('Premium Feature'),
              ],
            ),
            content: const Text(
              'This training plan is only available to premium users. Upgrade to access all premium content.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  NavigationService().navigateTo('/premium');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.premiumColor,
                ),
                child: const Text('UPGRADE'),
              ),
            ],
          ),
    );
  }

  void _navigateToWorkoutDetail(WorkoutModel workout) {
    NavigationService().navigateTo('/workout-detail', arguments: workout.id);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Training Plan')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildAppBar(),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPlanStats(),
                  const SizedBox(height: 24),
                  _buildSectionTitle('Description'),
                  const SizedBox(height: 8),
                  Text(
                    _plan.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondaryLight,
                    ),
                  ),
                  const SizedBox(height: 24),
                  _buildSectionTitle('Plan Overview'),
                  const SizedBox(height: 16),
                  _buildPlanOverview(),
                  const SizedBox(height: 24),
                  _buildSectionTitle('Workouts'),
                ],
              ),
            ),
          ),
          _buildWorkoutsList(),
          const SliverToBoxAdapter(
            child: SizedBox(height: 100), // Space for the button
          ),
        ],
      ),
      floatingActionButton: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: ElevatedButton(
          onPressed: _startPlan,
          style: ElevatedButton.styleFrom(
            backgroundColor: _plan.color,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _plan.isPremium && !_isPremiumUser
                    ? Icons.workspace_premium
                    : Icons.play_arrow,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                _plan.isPremium && !_isPremiumUser
                    ? 'UPGRADE TO START'
                    : 'START PLAN',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          _plan.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                color: Colors.black54,
                blurRadius: 2,
                offset: Offset(1, 1),
              ),
            ],
          ),
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            Container(
              color: _plan.color,
              child: Icon(
                Icons.fitness_center,
                size: 100,
                color: Colors.white.withValues(
                  alpha: (0.2 * 255).round().toDouble(),
                ),
              ),
            ),
            // Gradient overlay for better text visibility
            const DecoratedBox(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.transparent, Colors.black54],
                ),
              ),
            ),
            if (_plan.isPremium)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.premiumColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: const [
                      Icon(
                        Icons.workspace_premium,
                        size: 16,
                        color: Colors.white,
                      ),
                      SizedBox(width: 4),
                      Text(
                        'PREMIUM',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlanStats() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatCard(
          Icons.calendar_today_outlined,
          _plan.duration,
          'Duration',
        ),
        _buildStatCard(
          Icons.fitness_center_outlined,
          _plan.workouts.length.toString(),
          'Workouts',
        ),
        _buildStatCard(Icons.signal_cellular_alt, _plan.level, 'Level'),
      ],
    );
  }

  Widget _buildStatCard(IconData icon, String value, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: (0.1 * 255).round().toDouble(),
            ),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: AppTheme.primaryColor,
          ), // Changed for visibility
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryLight,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildPlanOverview() {
    // This would show a visual representation of the plan schedule
    // For now, just a placeholder
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Text(
          'Plan Schedule Overview',
          style: TextStyle(color: AppTheme.textSecondaryLight),
        ),
      ),
    );
  }

  Widget _buildWorkoutsList() {
    if (_plan.workouts.isEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.fitness_center_outlined,
                  size: 64,
                  color: Colors.grey.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  'No workouts available yet',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppTheme.textSecondaryLight,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final workout = _plan.workouts[index];
        return _buildWorkoutItem(workout, index);
      }, childCount: _plan.workouts.length),
    );
  }

  Widget _buildWorkoutItem(WorkoutModel workout, int index) {
    final isLocked = workout.isPremium && !_isPremiumUser;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(
              alpha: (0.1 * 255).round().toDouble(),
            ),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap:
              isLocked
                  ? _showPremiumDialog
                  : () => _navigateToWorkoutDetail(workout),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _plan.color.withValues(
                      alpha: (0.1 * 255).round().toDouble(),
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      'Day ${index + 1}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: _plan.color,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workout.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        workout.description,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.textSecondaryLight,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildWorkoutStat(
                            Icons.timer_outlined,
                            '${workout.duration} min',
                          ),
                          const SizedBox(width: 16),
                          _buildWorkoutStat(
                            Icons.local_fire_department_outlined,
                            '${workout.calories} cal',
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (isLocked)
                  Icon(Icons.lock_outline, color: AppTheme.textSecondaryLight)
                else
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppTheme.textSecondaryLight,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWorkoutStat(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 14, color: AppTheme.textSecondaryLight),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(fontSize: 12, color: AppTheme.textSecondaryLight),
        ),
      ],
    );
  }
}
