import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Debug helper screen to reset onboarding status
/// Only for development and testing purposes
class OnboardingDebugScreen extends StatefulWidget {
  const OnboardingDebugScreen({super.key});

  @override
  State<OnboardingDebugScreen> createState() => _OnboardingDebugScreenState();
}

class _OnboardingDebugScreenState extends State<OnboardingDebugScreen> {
  String _status = 'Loading...';
  
  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  Future<void> _checkStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final completed = prefs.getBool('onboarding_completed') ?? false;
    final version = prefs.getInt('onboarding_version') ?? 0;
    
    setState(() {
      _status = 'Onboarding Completed: $completed\nVersion: $version';
    });
  }

  Future<void> _resetOnboarding() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('onboarding_completed');
    await prefs.remove('onboarding_version');
    await prefs.remove('last_onboarding_date');
    await prefs.remove('onboarding_skipped');
    
    setState(() {
      _status = 'Onboarding Reset! Restart the app to see onboarding.';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Onboarding Debug'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _status,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _resetOnboarding,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: const Text('Reset Onboarding'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: _checkStatus,
              child: const Text('Refresh Status'),
            ),
            const SizedBox(height: 20),
            const Text(
              'Use this to reset onboarding during development.\nAfter resetting, restart the app to see the onboarding flow.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
