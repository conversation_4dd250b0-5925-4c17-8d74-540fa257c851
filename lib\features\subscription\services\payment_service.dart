import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/paystack_service.dart';
import 'package:url_launcher/url_launcher.dart';

class PaymentService {
  static void initialize() {
    // PaystackService doesn't need explicit initialization
  }

  static Future<bool> initializePayment({
    required String email,
    required String fullName,
    required BuildContext context,
  }) async {
    try {
      // Prepare the payment details
      final amount = AppConfig.premiumSubscriptionPrice; // in Naira

      // Use our PaystackService to process the payment
      final paystackService = PaystackService();
      final result = await paystackService.processPayment(
        email: email,
        fullName: fullName,
        amount: amount,
      );

      // Handle the response
      if (context.mounted) {
        if (result['status'] == 'success') {
          final authUrl = result['authorization_url'];
          if (authUrl != null) {
            // Open Paystack checkout page in browser
            final uri = Uri.parse(authUrl);
            if (await canLaunchUrl(uri)) {
              await launchUrl(uri, mode: LaunchMode.externalApplication);

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Payment page opened. Complete your payment to activate Premium access.',
                  ),
                  duration: Duration(seconds: 5),
                ),
              );
              return true;
            } else {
              throw 'Could not launch payment page';
            }
          } else {
            throw 'Invalid payment URL received';
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'Payment initialization failed',
              ),
              backgroundColor: Colors.red,
            ),
          );
          return false;
        }
      }

      return false;
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return false;
    }
  }
}
