import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/setting_model.dart';

/// Service for managing user settings and preferences
class SettingsService {
  static const String _preferencesKey = 'user_preferences';
  static const String _firstLaunchKey = 'first_launch';
  
  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  
  SettingsService._();
  
  SharedPreferences? _prefs;
  UserPreferences _userPreferences = UserPreferences();
  
  /// Initialize the settings service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadPreferences();
  }
  
  /// Get current user preferences
  UserPreferences get userPreferences => _userPreferences;
  
  /// Load preferences from storage
  Future<void> _loadPreferences() async {
    try {
      final prefsJson = _prefs?.getString(_preferencesKey);
      if (prefsJson != null) {
        final prefsMap = jsonDecode(prefsJson) as Map<String, dynamic>;
        _userPreferences = UserPreferences.fromJson(prefsMap);
      }
    } catch (e) {
      print('Error loading preferences: $e');
      _userPreferences = UserPreferences();
    }
  }
  
  /// Save preferences to storage
  Future<void> _savePreferences() async {
    try {
      final prefsJson = jsonEncode(_userPreferences.toJson());
      await _prefs?.setString(_preferencesKey, prefsJson);
    } catch (e) {
      print('Error saving preferences: $e');
    }
  }
  
  /// Update a specific preference
  Future<void> updatePreference<T>(String key, T value) async {
    switch (key) {
      case 'notificationsEnabled':
        _userPreferences = _userPreferences.copyWith(notificationsEnabled: value as bool);
        break;
      case 'pushNotificationsEnabled':
        _userPreferences = _userPreferences.copyWith(pushNotificationsEnabled: value as bool);
        break;
      case 'emailNotificationsEnabled':
        _userPreferences = _userPreferences.copyWith(emailNotificationsEnabled: value as bool);
        break;
      case 'smsNotificationsEnabled':
        _userPreferences = _userPreferences.copyWith(smsNotificationsEnabled: value as bool);
        break;
      case 'theme':
        _userPreferences = _userPreferences.copyWith(theme: value as String);
        break;
      case 'language':
        _userPreferences = _userPreferences.copyWith(language: value as String);
        break;
      case 'biometricEnabled':
        _userPreferences = _userPreferences.copyWith(biometricEnabled: value as bool);
        break;
      case 'autoBackup':
        _userPreferences = _userPreferences.copyWith(autoBackup: value as bool);
        break;
      case 'backupFrequency':
        _userPreferences = _userPreferences.copyWith(backupFrequency: value as String);
        break;
      case 'dataUsageOptimization':
        _userPreferences = _userPreferences.copyWith(dataUsageOptimization: value as bool);
        break;
      case 'offlineMode':
        _userPreferences = _userPreferences.copyWith(offlineMode: value as bool);
        break;
      case 'fontSize':
        _userPreferences = _userPreferences.copyWith(fontSize: value as double);
        break;
      case 'soundEffects':
        _userPreferences = _userPreferences.copyWith(soundEffects: value as bool);
        break;
      case 'hapticFeedback':
        _userPreferences = _userPreferences.copyWith(hapticFeedback: value as bool);
        break;
      case 'analyticsEnabled':
        _userPreferences = _userPreferences.copyWith(analyticsEnabled: value as bool);
        break;
      case 'crashReportingEnabled':
        _userPreferences = _userPreferences.copyWith(crashReportingEnabled: value as bool);
        break;
      case 'downloadQuality':
        _userPreferences = _userPreferences.copyWith(downloadQuality: value as String);
        break;
      case 'autoDownload':
        _userPreferences = _userPreferences.copyWith(autoDownload: value as bool);
        break;
      case 'wifiOnlyDownload':
        _userPreferences = _userPreferences.copyWith(wifiOnlyDownload: value as bool);
        break;
    }
    await _savePreferences();
  }
  
  /// Reset all preferences to default
  Future<void> resetToDefaults() async {
    _userPreferences = UserPreferences();
    await _savePreferences();
  }
  
  /// Check if this is the first app launch
  bool get isFirstLaunch {
    return _prefs?.getBool(_firstLaunchKey) ?? true;
  }
  
  /// Mark first launch as completed
  Future<void> setFirstLaunchCompleted() async {
    await _prefs?.setBool(_firstLaunchKey, false);
  }
  
  /// Get app version
  String get appVersion => '1.0.0';
  
  /// Get build number
  String get buildNumber => '1';
  
  /// Clear all app data
  Future<void> clearAllData() async {
    await _prefs?.clear();
    _userPreferences = UserPreferences();
  }
  
  /// Export settings as JSON
  String exportSettings() {
    return jsonEncode(_userPreferences.toJson());
  }
  
  /// Import settings from JSON
  Future<bool> importSettings(String jsonString) async {
    try {
      final settingsMap = jsonDecode(jsonString) as Map<String, dynamic>;
      _userPreferences = UserPreferences.fromJson(settingsMap);
      await _savePreferences();
      return true;
    } catch (e) {
      print('Error importing settings: $e');
      return false;
    }
  }
  
  /// Get storage usage information
  Future<Map<String, dynamic>> getStorageInfo() async {
    // In a real app, you would calculate actual storage usage
    return {
      'totalSize': '45.2 MB',
      'cacheSize': '12.8 MB',
      'userDataSize': '8.4 MB',
      'appSize': '24.0 MB',
    };
  }
  
  /// Clear app cache
  Future<void> clearCache() async {
    // In a real app, you would clear actual cache files
    // For now, we'll just simulate it
    await Future.delayed(const Duration(seconds: 1));
  }
  
  /// Get device information
  Map<String, String> getDeviceInfo() {
    // In a real app, you would use device_info_plus package
    return {
      'Platform': 'Android',
      'Version': 'Android 12',
      'Model': 'Pixel 6',
      'Manufacturer': 'Google',
    };
  }
  
  /// Check for app updates
  Future<Map<String, dynamic>> checkForUpdates() async {
    // Simulate checking for updates
    await Future.delayed(const Duration(seconds: 2));
    return {
      'hasUpdate': false,
      'latestVersion': '1.0.0',
      'currentVersion': '1.0.0',
      'updateSize': '0 MB',
    };
  }
  
  /// Send feedback
  Future<bool> sendFeedback(String feedback, String email) async {
    // In a real app, you would send this to your backend
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }
  
  /// Report a bug
  Future<bool> reportBug(String description, String email, List<String> attachments) async {
    // In a real app, you would send this to your bug tracking system
    await Future.delayed(const Duration(seconds: 1));
    return true;
  }
}
