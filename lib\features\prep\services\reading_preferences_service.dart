import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage reading preferences and accessibility settings
class ReadingPreferencesService {
  static const String _textSizeKey = 'text_size_multiplier';
  static const String _readingSpeedKey = 'reading_speed';
  static const String _autoAdvanceKey = 'auto_advance_enabled';
  static const String _highContrastKey = 'high_contrast_enabled';
  static const String _dyslexiaFontKey = 'dyslexia_font_enabled';

  static final ReadingPreferencesService _instance =
      ReadingPreferencesService._internal();
  factory ReadingPreferencesService() => _instance;
  ReadingPreferencesService._internal();

  final List<VoidCallback> _listeners = [];

  // Default values
  double _textSizeMultiplier = 1.0;
  ReadingSpeed _readingSpeed = ReadingSpeed.normal;
  bool _autoAdvanceEnabled = false;
  bool _highContrastEnabled = false;
  bool _dyslexiaFontEnabled = false;

  /// Initialize the service and load preferences
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _textSizeMultiplier = prefs.getDouble(_textSizeKey) ?? 1.0;
      _readingSpeed = ReadingSpeed.values[prefs.getInt(_readingSpeedKey) ?? 1];
      _autoAdvanceEnabled = prefs.getBool(_autoAdvanceKey) ?? false;
      _highContrastEnabled = prefs.getBool(_highContrastKey) ?? false;
      _dyslexiaFontEnabled = prefs.getBool(_dyslexiaFontKey) ?? false;

      debugPrint('✅ Reading preferences initialized');
    } catch (e) {
      debugPrint('❌ Error initializing reading preferences: $e');
    }
  }

  /// Text size multiplier (0.8 to 2.0)
  double get textSizeMultiplier => _textSizeMultiplier;

  set textSizeMultiplier(double value) {
    _textSizeMultiplier = value.clamp(0.8, 2.0);
    _savePreferences();
    _notifyListeners();
  }

  /// Reading speed setting
  ReadingSpeed get readingSpeed => _readingSpeed;

  set readingSpeed(ReadingSpeed speed) {
    _readingSpeed = speed;
    _savePreferences();
    _notifyListeners();
  }

  /// Auto-advance questions based on reading speed
  bool get autoAdvanceEnabled => _autoAdvanceEnabled;

  set autoAdvanceEnabled(bool enabled) {
    _autoAdvanceEnabled = enabled;
    _savePreferences();
    _notifyListeners();
  }

  /// High contrast mode for better visibility
  bool get highContrastEnabled => _highContrastEnabled;

  set highContrastEnabled(bool enabled) {
    _highContrastEnabled = enabled;
    _savePreferences();
    _notifyListeners();
  }

  /// Dyslexia-friendly font
  bool get dyslexiaFontEnabled => _dyslexiaFontEnabled;

  set dyslexiaFontEnabled(bool enabled) {
    _dyslexiaFontEnabled = enabled;
    _savePreferences();
    _notifyListeners();
  }

  /// Get reading time for text based on reading speed
  Duration getReadingTime(String text) {
    final wordCount = text.split(' ').length;
    final wordsPerMinute = _readingSpeed.wordsPerMinute;
    final minutes = wordCount / wordsPerMinute;
    return Duration(milliseconds: (minutes * 60 * 1000).round());
  }

  /// Get auto-advance delay for questions
  Duration getAutoAdvanceDelay(String questionText) {
    if (!_autoAdvanceEnabled) return Duration.zero;

    final baseReadingTime = getReadingTime(questionText);
    // Add extra time for thinking
    final thinkingTime = Duration(seconds: 5);
    return baseReadingTime + thinkingTime;
  }

  /// Get scaled font size
  double getScaledFontSize(double baseFontSize) {
    return baseFontSize * _textSizeMultiplier;
  }

  /// Get text style with preferences applied
  TextStyle getPreferredTextStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(
      fontSize: getScaledFontSize(baseStyle.fontSize ?? 16.0),
      fontFamily: _dyslexiaFontEnabled ? 'OpenDyslexic' : baseStyle.fontFamily,
      color:
          _highContrastEnabled
              ? (baseStyle.color?.computeLuminance() ?? 0) > 0.5
                  ? Colors.black
                  : Colors.white
              : baseStyle.color,
    );
  }

  /// Reset all preferences to defaults
  Future<void> resetToDefaults() async {
    _textSizeMultiplier = 1.0;
    _readingSpeed = ReadingSpeed.normal;
    _autoAdvanceEnabled = false;
    _highContrastEnabled = false;
    _dyslexiaFontEnabled = false;

    await _savePreferences();
    _notifyListeners();
  }

  /// Save preferences to persistent storage
  Future<void> _savePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setDouble(_textSizeKey, _textSizeMultiplier);
      await prefs.setInt(_readingSpeedKey, _readingSpeed.index);
      await prefs.setBool(_autoAdvanceKey, _autoAdvanceEnabled);
      await prefs.setBool(_highContrastKey, _highContrastEnabled);
      await prefs.setBool(_dyslexiaFontKey, _dyslexiaFontEnabled);
    } catch (e) {
      debugPrint('❌ Error saving reading preferences: $e');
    }
  }

  /// Add listener for preference changes
  void addListener(VoidCallback listener) {
    _listeners.add(listener);
  }

  /// Remove listener
  void removeListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// Notify all listeners of changes
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  /// Dispose resources
  void dispose() {
    _listeners.clear();
  }
}

/// Reading speed options
enum ReadingSpeed {
  slow(150, 'Slow'),
  normal(200, 'Normal'),
  fast(250, 'Fast'),
  veryFast(300, 'Very Fast');

  const ReadingSpeed(this.wordsPerMinute, this.displayName);

  final int wordsPerMinute;
  final String displayName;
}

/// Extension to get colors for high contrast mode
extension HighContrastColors on Color {
  Color get highContrastVariant {
    final luminance = computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
