import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:html/dom.dart';
import 'package:fit_4_force/features/news/models/agency_news_model.dart';
import 'package:fit_4_force/features/news/services/news_sources_config.dart';

/// Service for scraping news from official agency websites
class NewsScrapingService {
  static const Duration _requestTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;

  /// Scrape news from all agency sources
  Future<List<AgencyNewsModel>> scrapeAllAgencyNews() async {
    final List<AgencyNewsModel> allNews = [];

    for (final entry in NewsSourcesConfig.agencySources.entries) {
      final agencyName = entry.key;
      final source = entry.value;

      try {
        final agencyNews = await _scrapeAgencyNews(agencyName, source);
        allNews.addAll(agencyNews);
      } catch (e) {
        print('Error scraping news for $agencyName: $e');
        // Continue with other agencies even if one fails
      }
    }

    return allNews;
  }

  /// Scrape news for a specific agency
  Future<List<AgencyNewsModel>> _scrapeAgencyNews(
    String agencyName,
    AgencyNewsSource source,
  ) async {
    final List<AgencyNewsModel> news = [];

    // 1. Try RSS feeds first (most reliable)
    for (final rssUrl in source.rssFeeds) {
      try {
        final rssNews = await _parseRSSFeed(rssUrl, agencyName);
        news.addAll(rssNews);
      } catch (e) {
        print('RSS feed error for $agencyName ($rssUrl): $e');
      }
    }

    // 2. Scrape main website
    try {
      final websiteNews = await _scrapeWebsite(
        source.officialWebsite,
        agencyName,
        source.keywordFilters,
      );
      news.addAll(websiteNews);
    } catch (e) {
      print('Website scraping error for $agencyName: $e');
    }

    // 3. Check recruitment page
    try {
      final recruitmentNews = await _scrapeRecruitmentPage(
        source.recruitmentPage,
        agencyName,
      );
      news.addAll(recruitmentNews);
    } catch (e) {
      print(
        'Recruitment page error for $agencyName (${source.recruitmentPage}): $e',
      );
    }

    return _removeDuplicates(news);
  }

  /// Parse RSS feed
  Future<List<AgencyNewsModel>> _parseRSSFeed(
    String rssUrl,
    String agencyName,
  ) async {
    final response = await _makeRequest(rssUrl);
    final document = html_parser.parse(response.body);

    final items = document.querySelectorAll('item');
    final List<AgencyNewsModel> news = [];

    for (final item in items) {
      try {
        final title = item.querySelector('title')?.text.trim() ?? '';
        final description =
            item.querySelector('description')?.text.trim() ?? '';
        final link = item.querySelector('link')?.text.trim() ?? '';
        final pubDate = item.querySelector('pubDate')?.text.trim() ?? '';

        if (title.isNotEmpty && _isRelevantContent(title, description)) {
          news.add(
            AgencyNewsModel(
              id: _generateId(title, agencyName),
              createdAt: DateTime.now(),
              title: title,
              content: description,
              agency: agencyName,
              category: _categorizeNews(title, description),
              source: 'Official Website',
              publishedDate: _parseDate(pubDate),
              priority: _determinePriority(title, description),
              tags: _extractTags(title, description),
              isPinned: _isPinned(title, description),
              isBreaking: _isBreaking(title, description),
              documentUrl: link,
            ),
          );
        }
      } catch (e) {
        print('Error parsing RSS item: $e');
      }
    }

    return news;
  }

  /// Scrape website content
  Future<List<AgencyNewsModel>> _scrapeWebsite(
    String websiteUrl,
    String agencyName,
    List<String> keywords,
  ) async {
    final response = await _makeRequest(websiteUrl);
    final document = html_parser.parse(response.body);

    final List<AgencyNewsModel> news = [];

    // Look for common news/article selectors
    final articleSelectors = [
      'article',
      '.news-item',
      '.post',
      '.article',
      '.news-article',
      '.content-item',
      '[class*="news"]',
      '[class*="article"]',
    ];

    for (final selector in articleSelectors) {
      final articles = document.querySelectorAll(selector);

      for (final article in articles) {
        try {
          final title = _extractTitle(article);
          final content = _extractContent(article);
          final link = _extractLink(article, websiteUrl);

          if (title.isNotEmpty &&
              _containsKeywords('$title $content', keywords)) {
            news.add(
              AgencyNewsModel(
                id: _generateId(title, agencyName),
                createdAt: DateTime.now(),
                title: title,
                content: content,
                agency: agencyName,
                category: _categorizeNews(title, content),
                source: 'Official Website',
                publishedDate: DateTime.now(),
                priority: _determinePriority(title, content),
                tags: _extractTags(title, content),
                isPinned: _isPinned(title, content),
                isBreaking: _isBreaking(title, content),
                documentUrl: link,
              ),
            );
          }
        } catch (e) {
          print('Error parsing article: $e');
        }
      }

      if (news.isNotEmpty) break; // Found articles with this selector
    }

    return news;
  }

  /// Scrape recruitment-specific pages
  Future<List<AgencyNewsModel>> _scrapeRecruitmentPage(
    String recruitmentUrl,
    String agencyName,
  ) async {
    final response = await _makeRequest(recruitmentUrl);
    final document = html_parser.parse(response.body);

    final List<AgencyNewsModel> news = [];

    // Look for recruitment announcements
    final recruitmentSelectors = [
      '.recruitment-notice',
      '.announcement',
      '.notice',
      '[class*="recruitment"]',
      '[class*="application"]',
      '[class*="intake"]',
    ];

    for (final selector in recruitmentSelectors) {
      final notices = document.querySelectorAll(selector);

      for (final notice in notices) {
        try {
          final title = _extractTitle(notice);
          final content = _extractContent(notice);

          if (title.isNotEmpty) {
            news.add(
              AgencyNewsModel(
                id: _generateId(title, agencyName),
                createdAt: DateTime.now(),
                title: title,
                content: content,
                agency: agencyName,
                category: 'Recruitment',
                source: 'Recruitment Portal',
                publishedDate: DateTime.now(),
                priority: 'high',
                tags: ['Recruitment', 'Application', 'Official'],
                isPinned: true,
                isBreaking: _isBreaking(title, content),
                documentUrl: recruitmentUrl,
              ),
            );
          }
        } catch (e) {
          print('Error parsing recruitment notice: $e');
        }
      }
    }

    return news;
  }

  /// Make HTTP request with error handling
  Future<http.Response> _makeRequest(String url) async {
    for (int i = 0; i < _maxRetries; i++) {
      try {
        final response = await http
            .get(
              Uri.parse(url),
              headers: {
                'User-Agent': 'Fit4Force News Aggregator 1.0',
                'Accept':
                    'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              },
            )
            .timeout(_requestTimeout);

        if (response.statusCode == 200) {
          return response;
        } else {
          throw Exception(
            'HTTP ${response.statusCode}: ${response.reasonPhrase}',
          );
        }
      } catch (e) {
        if (i == _maxRetries - 1) rethrow;
        await Future.delayed(Duration(seconds: i + 1));
      }
    }
    throw Exception('Max retries exceeded');
  }

  /// Helper methods for content extraction and analysis

  String _extractTitle(Element element) {
    final titleSelectors = ['h1', 'h2', 'h3', '.title', '.headline', 'title'];
    for (final selector in titleSelectors) {
      final titleElement = element.querySelector(selector);
      if (titleElement != null && titleElement.text.trim().isNotEmpty) {
        return titleElement.text.trim();
      }
    }
    return '';
  }

  String _extractContent(Element element) {
    final contentSelectors = ['.content', '.description', '.excerpt', 'p'];
    for (final selector in contentSelectors) {
      final contentElement = element.querySelector(selector);
      if (contentElement != null && contentElement.text.trim().isNotEmpty) {
        return contentElement.text.trim();
      }
    }
    return element.text.trim();
  }

  String _extractLink(Element element, String baseUrl) {
    final linkElement = element.querySelector('a');
    if (linkElement != null) {
      final href = linkElement.attributes['href'] ?? '';
      if (href.startsWith('http')) {
        return href;
      } else if (href.isNotEmpty) {
        return Uri.parse(baseUrl).resolve(href).toString();
      }
    }
    return baseUrl;
  }

  bool _containsKeywords(String text, List<String> keywords) {
    final lowerText = text.toLowerCase();
    return keywords.any((keyword) => lowerText.contains(keyword.toLowerCase()));
  }

  bool _isRelevantContent(String title, String content) {
    final relevantTerms = [
      'recruitment',
      'intake',
      'application',
      'training',
      'exercise',
      'requirement',
      'deadline',
      'announcement',
      'notice',
      'examination',
      'interview',
      'selection',
      'admission',
      'screening',
    ];

    final combinedText = ('$title $content').toLowerCase();
    return relevantTerms.any((term) => combinedText.contains(term));
  }

  String _categorizeNews(String title, String content) {
    final combinedText = ('$title $content').toLowerCase();

    if (combinedText.contains('recruitment') ||
        combinedText.contains('intake')) {
      return 'Recruitment';
    } else if (combinedText.contains('training') ||
        combinedText.contains('course')) {
      return 'Training';
    } else if (combinedText.contains('interview') ||
        combinedText.contains('screening')) {
      return 'Interview';
    } else if (combinedText.contains('requirement') ||
        combinedText.contains('criteria')) {
      return 'Requirements';
    } else if (combinedText.contains('admission')) {
      return 'Admission';
    }

    return 'Announcement';
  }

  String _determinePriority(String title, String content) {
    final combinedText = ('$title $content').toLowerCase();

    if (combinedText.contains('urgent') ||
        combinedText.contains('breaking') ||
        combinedText.contains('immediate') ||
        combinedText.contains('deadline')) {
      return 'high';
    } else if (combinedText.contains('important') ||
        combinedText.contains('notice')) {
      return 'medium';
    }

    return 'low';
  }

  List<String> _extractTags(String title, String content) {
    final tags = <String>[];
    final combinedText = ('$title $content').toLowerCase();

    final tagMap = {
      'recruitment': 'Recruitment',
      'training': 'Training',
      'application': 'Application',
      'deadline': 'Deadline',
      'interview': 'Interview',
      'examination': 'Examination',
      'physical': 'Physical Fitness',
      'medical': 'Medical',
      'requirement': 'Requirements',
      'admission': 'Admission',
    };

    tagMap.forEach((key, value) {
      if (combinedText.contains(key)) {
        tags.add(value);
      }
    });

    return tags;
  }

  bool _isPinned(String title, String content) {
    final combinedText = ('$title $content').toLowerCase();
    return combinedText.contains('recruitment') ||
        combinedText.contains('application') ||
        combinedText.contains('deadline');
  }

  bool _isBreaking(String title, String content) {
    final combinedText = ('$title $content').toLowerCase();
    return combinedText.contains('breaking') ||
        combinedText.contains('urgent') ||
        combinedText.contains('immediate');
  }

  DateTime _parseDate(String dateString) {
    try {
      // Try different date formats
      if (dateString.isNotEmpty) {
        return DateTime.parse(dateString);
      }
    } catch (e) {
      // If parsing fails, return current time
    }
    return DateTime.now();
  }

  String _generateId(String title, String agency) {
    return '${agency.toLowerCase().replaceAll(' ', '_')}_${title.hashCode.abs()}';
  }

  List<AgencyNewsModel> _removeDuplicates(List<AgencyNewsModel> news) {
    final seen = <String>{};
    return news.where((item) {
      final key = '${item.title}_${item.agency}';
      return seen.add(key);
    }).toList();
  }
}
