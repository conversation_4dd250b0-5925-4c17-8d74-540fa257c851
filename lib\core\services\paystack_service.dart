import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/base_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:dio/dio.dart';
import 'dart:math';

/// Response model for Paystack payment operations
class PaystackResponse {
  final bool status;
  final String reference;
  final String message;

  const PaystackResponse({
    required this.status,
    required this.reference,
    required this.message,
  });
}

/// A real implementation of the Paystack service for payment processing using HTTP API
class PaystackService extends BaseService {
  final Dio _dio = Dio();
  static const String _baseUrl = 'https://api.paystack.co';
  static PaystackService? _instance;

  @override
  String get collectionName => 'transactions';

  PaystackService() {
    _setupDio();
  }

  /// Static initialize method for compatibility
  static void initialize() {
    _instance ??= PaystackService();
    debugPrint('✅ PaystackService initialized');
  }

  /// Get singleton instance
  static PaystackService get instance {
    _instance ??= PaystackService();
    return _instance!;
  }

  /// Static pay method for compatibility with existing payment button
  static Future<PaystackResponse> pay({
    required BuildContext context,
    required num amount, // Accept both int and double
    required String email,
    String? fullName,
  }) async {
    try {
      final service = PaystackService.instance;
      // Convert amount to double (from kobo to naira if needed)
      final amountInNaira = amount is int ? amount / 100.0 : amount.toDouble();
      final result = await service.processPayment(
        email: email,
        fullName: fullName ?? 'User',
        amount: amountInNaira,
      );

      if (result['status'] == 'success') {
        return PaystackResponse(
          status: true,
          reference: result['reference'] ?? '',
          message: 'Payment successful',
        );
      } else {
        return PaystackResponse(
          status: false,
          reference: '',
          message: result['message'] ?? 'Payment failed',
        );
      }
    } catch (e) {
      return PaystackResponse(
        status: false,
        reference: '',
        message: 'Payment error: $e',
      );
    }
  }

  /// Setup Dio with default configuration
  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.headers = {
      'Authorization': 'Bearer ${AppConfig.paystackSecretKey}',
      'Content-Type': 'application/json',
    };
  }

  /// Generate a secure payment reference
  String _generateReference() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999).toString().padLeft(6, '0');
    return 'FIT4FORCE_${timestamp}_$random';
  }

  /// Initialize a payment transaction
  Future<Map<String, dynamic>?> initializeTransaction({
    required String email,
    required int amount, // Amount in kobo
    required String reference,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      debugPrint('🌐 Calling Paystack API to initialize transaction...');
      debugPrint('   URL: $_baseUrl/transaction/initialize');
      debugPrint(
        '   Headers: Authorization Bearer ${AppConfig.paystackSecretKey.substring(0, 10)}...',
      );

      final response = await _dio.post(
        '/transaction/initialize',
        data: {
          'email': email,
          'amount': amount,
          'reference': reference,
          'currency': 'NGN',
          'metadata': metadata ?? {},
          'callback_url':
              'https://fit4force.app/payment/callback', // Your callback URL
        },
      );

      debugPrint('📡 Paystack API Response:');
      debugPrint('   Status Code: ${response.statusCode}');
      debugPrint('   Response Status: ${response.data['status']}');
      debugPrint('   Response Message: ${response.data['message']}');

      if (response.statusCode == 200 && response.data['status'] == true) {
        debugPrint('✅ Transaction initialized successfully');
        return response.data['data'];
      } else {
        debugPrint('❌ Transaction initialization failed');
        debugPrint('   Full response: ${response.data}');
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error initializing transaction: $e');
      return null;
    }
  }

  /// Verify a payment transaction
  Future<Map<String, dynamic>?> verifyTransaction(String reference) async {
    try {
      final response = await _dio.get('/transaction/verify/$reference');

      if (response.statusCode == 200 && response.data['status'] == true) {
        return response.data['data'];
      }
      return null;
    } catch (e) {
      debugPrint('Error verifying transaction: $e');
      return null;
    }
  }

  /// Verify a transaction (legacy method for compatibility)
  Future<bool> verifyTransactionStatus(String reference) async {
    try {
      final response =
          await Supabase.instance.client
              .from(collectionName)
              .select('id')
              .eq('reference', reference)
              .limit(1)
              .maybeSingle();
      return response != null;
    } catch (e) {
      debugPrint('Error verifying transaction: $e');
      return false;
    }
  }

  /// Process a payment using Paystack web checkout
  ///
  /// This method initializes a transaction and returns the authorization URL
  /// for web-based payment processing, which is more reliable than the plugin
  Future<Map<String, dynamic>> processPayment({
    required String email,
    required String fullName,
    required double amount,
    bool isYearly = false,
  }) async {
    try {
      final reference = _generateReference();
      final amountInKobo = (amount * 100).toInt();

      debugPrint('🔄 Processing Paystack payment:');
      debugPrint('   Email: $email');
      debugPrint('   Amount: ₦$amount ($amountInKobo kobo)');
      debugPrint('   Reference: $reference');

      final transactionData = await initializeTransaction(
        email: email,
        amount: amountInKobo,
        reference: reference,
        metadata: {
          'subscription_type': isYearly ? 'yearly' : 'monthly',
          'user_name': fullName,
          'user_id': currentUserId,
        },
      );

      if (transactionData != null) {
        debugPrint('✅ Paystack transaction initialized successfully');
        debugPrint(
          '   Authorization URL: ${transactionData['authorization_url']}',
        );
        return {
          'status': 'success',
          'authorization_url': transactionData['authorization_url'],
          'access_code': transactionData['access_code'],
          'reference': reference,
        };
      } else {
        debugPrint('❌ Failed to initialize Paystack transaction');
        return {'status': 'error', 'message': 'Failed to initialize payment'};
      }
    } catch (e) {
      debugPrint('❌ Error processing payment: $e');
      return {'status': 'error', 'message': 'An error occurred: $e'};
    }
  }

  /// Process subscription payment specifically
  Future<Map<String, dynamic>> processSubscriptionPayment({
    required String email,
    required String fullName,
    bool isYearly = false,
  }) async {
    final amount =
        isYearly
            ? AppConfig.premiumSubscriptionPrice *
                10 // 10 months for yearly (2 months free)
            : AppConfig.premiumSubscriptionPrice;

    return await processPayment(
      email: email,
      fullName: fullName,
      amount: amount,
      isYearly: isYearly,
    );
  }

  /// Check if a payment was successful
  static bool isPaymentSuccessful(Map<String, dynamic> response) {
    return response['status'] == 'success';
  }
}
