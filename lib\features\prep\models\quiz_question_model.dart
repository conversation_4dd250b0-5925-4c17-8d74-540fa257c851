/// Model representing a quiz question
class QuizQuestionModel {
  final String id;
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  final String category;
  final String subcategory;
  final String difficulty; // 'beginner', 'intermediate', 'advanced'
  final String explanation;
  final String? agency; // For agency-specific questions
  final List<String> tags;
  final int timeLimit; // in seconds
  final double points;

  const QuizQuestionModel({
    required this.id,
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
    required this.category,
    required this.subcategory,
    required this.difficulty,
    required this.explanation,
    this.agency,
    this.tags = const [],
    this.timeLimit = 60, // Default 60 seconds
    this.points = 1.0, // Default 1 point
  });

  /// Get the correct answer text
  String get correctAnswer => options[correctAnswerIndex];

  /// Check if the given answer index is correct
  bool isCorrect(int answerIndex) => answerIndex == correctAnswerIndex;

  /// Check if this question is for a specific agency
  bool isAgencySpecific() => agency != null;

  /// Check if this question matches the given agency
  bool matchesAgency(String? userAgency) {
    if (agency == null) return true; // General questions match all agencies
    if (userAgency == null) return false;
    return agency!.toLowerCase() == userAgency.toLowerCase();
  }

  /// Create a copy with modified properties
  QuizQuestionModel copyWith({
    String? id,
    String? question,
    List<String>? options,
    int? correctAnswerIndex,
    String? category,
    String? subcategory,
    String? difficulty,
    String? explanation,
    String? agency,
    List<String>? tags,
    int? timeLimit,
    double? points,
  }) {
    return QuizQuestionModel(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      correctAnswerIndex: correctAnswerIndex ?? this.correctAnswerIndex,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      difficulty: difficulty ?? this.difficulty,
      explanation: explanation ?? this.explanation,
      agency: agency ?? this.agency,
      tags: tags ?? this.tags,
      timeLimit: timeLimit ?? this.timeLimit,
      points: points ?? this.points,
    );
  }

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
      'category': category,
      'subcategory': subcategory,
      'difficulty': difficulty,
      'explanation': explanation,
      'agency': agency,
      'tags': tags,
      'timeLimit': timeLimit,
      'points': points,
    };
  }

  /// Create from map
  factory QuizQuestionModel.fromMap(Map<String, dynamic> map) {
    return QuizQuestionModel(
      id: map['id'] ?? '',
      question: map['question'] ?? '',
      options: List<String>.from(map['options'] ?? []),
      correctAnswerIndex: map['correctAnswerIndex'] ?? 0,
      category: map['category'] ?? '',
      subcategory: map['subcategory'] ?? '',
      difficulty: map['difficulty'] ?? 'beginner',
      explanation: map['explanation'] ?? '',
      agency: map['agency'],
      tags: List<String>.from(map['tags'] ?? []),
      timeLimit: map['timeLimit'] ?? 60,
      points: (map['points'] ?? 1.0).toDouble(),
    );
  }

  @override
  String toString() {
    return 'QuizQuestionModel(id: $id, question: $question, category: $category, difficulty: $difficulty)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is QuizQuestionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Model for quiz answer/response
class QuizAnswerModel {
  final String questionId;
  final int selectedAnswerIndex;
  final bool isCorrect;
  final int timeSpent; // in seconds
  final DateTime answeredAt;

  const QuizAnswerModel({
    required this.questionId,
    required this.selectedAnswerIndex,
    required this.isCorrect,
    required this.timeSpent,
    required this.answeredAt,
  });

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'questionId': questionId,
      'selectedAnswerIndex': selectedAnswerIndex,
      'isCorrect': isCorrect,
      'timeSpent': timeSpent,
      'answeredAt': answeredAt.toIso8601String(),
    };
  }

  /// Create from map
  factory QuizAnswerModel.fromMap(Map<String, dynamic> map) {
    return QuizAnswerModel(
      questionId: map['questionId'] ?? '',
      selectedAnswerIndex: map['selectedAnswerIndex'] ?? 0,
      isCorrect: map['isCorrect'] ?? false,
      timeSpent: map['timeSpent'] ?? 0,
      answeredAt: DateTime.parse(map['answeredAt']),
    );
  }
}

/// Model for quiz session/attempt
class QuizSessionModel {
  final String id;
  final String quizId;
  final String userId;
  final List<QuizAnswerModel> answers;
  final DateTime startedAt;
  final DateTime? completedAt;
  final int totalQuestions;
  final int correctAnswers;
  final double score; // percentage
  final int totalTimeSpent; // in seconds
  final bool isCompleted;

  const QuizSessionModel({
    required this.id,
    required this.quizId,
    required this.userId,
    required this.answers,
    required this.startedAt,
    this.completedAt,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.score,
    required this.totalTimeSpent,
    required this.isCompleted,
  });

  /// Calculate score percentage
  double get scorePercentage => totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0.0;

  /// Get grade based on score
  String get grade {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  /// Check if passed (assuming 50% is passing)
  bool get isPassed => score >= 50.0;

  /// Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'quizId': quizId,
      'userId': userId,
      'answers': answers.map((answer) => answer.toMap()).toList(),
      'startedAt': startedAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'totalQuestions': totalQuestions,
      'correctAnswers': correctAnswers,
      'score': score,
      'totalTimeSpent': totalTimeSpent,
      'isCompleted': isCompleted,
    };
  }

  /// Create from map
  factory QuizSessionModel.fromMap(Map<String, dynamic> map) {
    return QuizSessionModel(
      id: map['id'] ?? '',
      quizId: map['quizId'] ?? '',
      userId: map['userId'] ?? '',
      answers: (map['answers'] as List<dynamic>?)
          ?.map((answer) => QuizAnswerModel.fromMap(answer))
          .toList() ?? [],
      startedAt: DateTime.parse(map['startedAt']),
      completedAt: map['completedAt'] != null ? DateTime.parse(map['completedAt']) : null,
      totalQuestions: map['totalQuestions'] ?? 0,
      correctAnswers: map['correctAnswers'] ?? 0,
      score: (map['score'] ?? 0.0).toDouble(),
      totalTimeSpent: map['totalTimeSpent'] ?? 0,
      isCompleted: map['isCompleted'] ?? false,
    );
  }
}
