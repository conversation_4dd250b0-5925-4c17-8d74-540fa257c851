import 'package:flutter/material.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/core/widgets/user_progress_widgets.dart';
import 'package:fit_4_force/features/prep/models/study_material_model.dart';
import 'package:fit_4_force/features/prep/services/enhanced_study_material_service.dart';
import 'package:fit_4_force/shared/widgets/premium_badge.dart';

class StudyMaterialDetailScreen extends StatefulWidget {
  final StudyMaterialModel material;

  const StudyMaterialDetailScreen({super.key, required this.material});

  @override
  State<StudyMaterialDetailScreen> createState() =>
      _StudyMaterialDetailScreenState();
}

class _StudyMaterialDetailScreenState extends State<StudyMaterialDetailScreen> {
  final EnhancedStudyMaterialService _materialService =
      EnhancedStudyMaterialService();
  final UserProgressService _progressService = UserProgressService();

  Map<String, dynamic>? _userProgress;
  Map<String, dynamic> _academicsProgress = {};
  bool _isLoading = true;
  int _selectedRating = 0;
  DateTime? _readingStartTime;
  final TextEditingController _reviewController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _reviewController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load progress from both services
      final progress = await _materialService.getProgress(widget.material.id);
      final rating = await _materialService.getUserRating(widget.material.id);
      final academicsProgress = await _progressService.loadUserProgress();

      setState(() {
        _userProgress = progress;
        _academicsProgress = academicsProgress['academics'] ?? {};
        _selectedRating = rating?['rating'] ?? 0;
        _reviewController.text = rating?['review'] ?? '';
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.material.title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [if (widget.material.isPremium) const PremiumBadge()],
      ),
      body: _isLoading ? _buildLoadingState() : _buildContent(),
      bottomNavigationBar: _buildActionButtons(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(child: CircularProgressIndicator());
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          _buildAcademicProgressStats(),
          const SizedBox(height: 24),
          _buildDescription(),
          const SizedBox(height: 24),
          _buildMetadata(),
          const SizedBox(height: 24),
          _buildProgress(),
          const SizedBox(height: 24),
          _buildRatingSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [widget.material.color.withValues(alpha: 0.1), Colors.white],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: widget.material.color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: widget.material.color,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  widget.material.icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.material.title,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.material.category} • ${widget.material.agency}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildInfoChip(
                Icons.access_time,
                '${widget.material.estimatedReadTime} min',
              ),
              const SizedBox(width: 8),
              _buildInfoChip(
                Icons.visibility,
                '${widget.material.viewCount} views',
              ),
              const SizedBox(width: 8),
              if (widget.material.rating > 0)
                _buildInfoChip(
                  Icons.star,
                  widget.material.rating.toStringAsFixed(1),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAcademicProgressStats() {
    final totalStudyHours =
        (_academicsProgress['totalStudyHours'] ?? 0.0) as double;
    final materialsRead = (_academicsProgress['materialsRead'] ?? 0) as int;
    final readingStreak = (_academicsProgress['readingStreak'] ?? 0) as int;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Academic Progress',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ProgressStatsCard(
                    title: 'Study Hours',
                    value: totalStudyHours.toInt(),
                    icon: Icons.schedule,
                    color: Colors.blue,
                    emptyStateMessage: 'Start studying to track hours!',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ProgressStatsCard(
                    title: 'Materials Read',
                    value: materialsRead,
                    icon: Icons.book,
                    color: Colors.green,
                    emptyStateMessage: 'Read your first material!',
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ProgressStatsCard(
                    title: 'Reading Streak',
                    value: readingStreak,
                    icon: Icons.local_fire_department,
                    color: Colors.orange,
                    emptyStateMessage: 'Start your streak!',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(IconData icon, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Description',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text(
              widget.material.description,
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
            if (widget.material.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    widget.material.tags.map((tag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: widget.material.color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: widget.material.color.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          tag,
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.material.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMetadata() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Details',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            _buildMetadataRow(
              'Content Type',
              widget.material.contentType.toUpperCase(),
            ),
            _buildMetadataRow(
              'Difficulty',
              widget.material.difficulty?.toUpperCase() ?? 'N/A',
            ),
            _buildMetadataRow(
              'Published',
              _formatDate(widget.material.publishedDate),
            ),
            _buildMetadataRow(
              'Downloads',
              widget.material.downloadCount.toString(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildProgress() {
    if (_userProgress == null) return const SizedBox.shrink();

    final status = _userProgress!['status'] ?? 'not_started';
    final progressPercentage = _userProgress!['progress_percentage'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your Progress',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: progressPercentage / 100,
                    backgroundColor: Colors.grey[200],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      widget.material.color,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text('$progressPercentage%'),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Status: ${_formatStatus(status)}',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Rate this Material',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Row(
              children: List.generate(5, (index) {
                return IconButton(
                  onPressed: () => setState(() => _selectedRating = index + 1),
                  icon: Icon(
                    index < _selectedRating ? Icons.star : Icons.star_border,
                    color: Colors.amber,
                  ),
                );
              }),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _reviewController,
              maxLines: 3,
              decoration: const InputDecoration(
                labelText: 'Write a review (optional)',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: _selectedRating > 0 ? _submitRating : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Submit Rating'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _markAsBookmarked,
              icon: const Icon(Icons.bookmark_border),
              label: const Text('Bookmark'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _openMaterial,
              icon: const Icon(Icons.play_arrow),
              label: const Text('Start Learning'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatStatus(String status) {
    switch (status) {
      case 'not_started':
        return 'Not Started';
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'bookmarked':
        return 'Bookmarked';
      default:
        return status;
    }
  }

  Future<void> _submitRating() async {
    final success = await _materialService.rateMaterial(
      materialId: widget.material.id,
      rating: _selectedRating,
      review:
          _reviewController.text.trim().isEmpty
              ? null
              : _reviewController.text.trim(),
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Rating submitted successfully!'),
          backgroundColor: Colors.green,
        ),
      );
      _loadData(); // Refresh data
    }
  }

  Future<void> _markAsBookmarked() async {
    final success = await _materialService.trackProgress(
      materialId: widget.material.id,
      status: 'bookmarked',
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Material bookmarked!'),
          backgroundColor: Colors.green,
        ),
      );
      _loadData(); // Refresh data
    }
  }

  void _openMaterial() {
    // Track that user started the material
    _materialService.trackProgress(
      materialId: widget.material.id,
      status: 'in_progress',
      progressPercentage: 0,
    );

    // Start tracking reading time
    _readingStartTime = DateTime.now();

    // Show material reading dialog with completion tracking
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(widget.material.title),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Reading: ${widget.material.title}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Text(
                        widget.material.description,
                        style: const TextStyle(fontSize: 16, height: 1.5),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
              ElevatedButton(
                onPressed: () => _completeReading(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Mark as Complete'),
              ),
            ],
          ),
    );
  }

  Future<void> _completeReading() async {
    Navigator.pop(context); // Close dialog

    if (_readingStartTime != null) {
      final readingDuration = DateTime.now().difference(_readingStartTime!);
      final minutesRead = readingDuration.inMinutes.toDouble();

      // Update UserProgressService
      await _progressService.incrementValue('academics', 'materialsRead');
      await _progressService.incrementValue(
        'academics',
        'totalStudyHours',
        increment: minutesRead / 60,
      );

      // Update reading streak if this is a new day
      final lastReadDate = _academicsProgress['lastReadDate'] as String?;
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month}-${today.day}';

      if (lastReadDate != todayString) {
        await _progressService.incrementValue('academics', 'readingStreak');
        await _progressService.updateProgress('academics', {
          'lastReadDate': todayString,
        });
      }

      // Check for achievements
      final newMaterialsRead = (_academicsProgress['materialsRead'] ?? 0) + 1;
      if (newMaterialsRead == 1) {
        await _progressService.addAchievement(
          'academics',
          'First Study Material Read',
        );
      } else if (newMaterialsRead == 10) {
        await _progressService.addAchievement(
          'academics',
          'Study Master - 10 Materials',
        );
      } else if (newMaterialsRead == 50) {
        await _progressService.addAchievement(
          'academics',
          'Knowledge Seeker - 50 Materials',
        );
      }

      // Update material progress
      await _materialService.trackProgress(
        materialId: widget.material.id,
        status: 'completed',
        progressPercentage: 100,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Great job! You completed "${widget.material.title}" in ${readingDuration.inMinutes} minutes!',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }

      // Refresh data
      await _loadData();
    }
  }
}
