import 'package:flutter/material.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';

/// A reusable widget that shows user progress with proper zero-state handling
/// Ensures consistent "start from zero" experience across all screens
class UserProgressWidget extends StatefulWidget {
  final String progressSection; // 'fitness', 'academics', 'training', etc.
  final Widget Function(Map<String, dynamic> progressData) builder;
  final Widget? loadingWidget;
  final Widget? errorWidget;

  const UserProgressWidget({
    super.key,
    required this.progressSection,
    required this.builder,
    this.loadingWidget,
    this.errorWidget,
  });

  @override
  State<UserProgressWidget> createState() => _UserProgressWidgetState();
}

class _UserProgressWidgetState extends State<UserProgressWidget> {
  final UserProgressService _progressService = UserProgressService();
  Map<String, dynamic> _progressData = {};
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadProgress();
  }

  Future<void> _loadProgress() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final fullProgress = await _progressService.loadUserProgress();
      final sectionData = fullProgress[widget.progressSection] ?? {};

      setState(() {
        _progressData = sectionData;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ??
          const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return widget.errorWidget ??
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text('Error loading progress: $_error'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadProgress,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
    }

    return widget.builder(_progressData);
  }
}

/// A specialized widget for showing progress stats cards with zero-state handling
class ProgressStatsCard extends StatelessWidget {
  final String title;
  final dynamic value;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final String? emptyStateMessage;

  const ProgressStatsCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.color,
    this.emptyStateMessage,
  });

  @override
  Widget build(BuildContext context) {
    final isZeroOrEmpty = _isZeroOrEmpty(value);
    final displayValue = _formatValue(value, isZeroOrEmpty);
    final displaySubtitle =
        isZeroOrEmpty ? (emptyStateMessage ?? 'Start your journey!') : subtitle;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
        border:
            isZeroOrEmpty
                ? Border.all(color: Colors.grey.shade300, width: 1)
                : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color:
                      isZeroOrEmpty
                          ? Colors.grey.withOpacity(0.1)
                          : color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: isZeroOrEmpty ? Colors.grey : color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color:
                        isZeroOrEmpty ? Colors.grey.shade600 : Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            displayValue,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isZeroOrEmpty ? Colors.grey.shade500 : color,
            ),
          ),
          if (displaySubtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              displaySubtitle,
              style: TextStyle(
                fontSize: 12,
                color:
                    isZeroOrEmpty ? Colors.grey.shade500 : Colors.grey.shade600,
                fontStyle: isZeroOrEmpty ? FontStyle.italic : FontStyle.normal,
              ),
            ),
          ],
        ],
      ),
    );
  }

  bool _isZeroOrEmpty(dynamic value) {
    if (value == null) return true;
    if (value is num) return value == 0;
    if (value is String) return value.isEmpty || value == '0';
    if (value is List) return value.isEmpty;
    if (value is Map) return value.isEmpty;
    return false;
  }

  String _formatValue(dynamic value, bool isZeroOrEmpty) {
    if (isZeroOrEmpty) return '--';

    if (value is num) {
      if (value is double && value % 1 != 0) {
        return value.toStringAsFixed(1);
      }
      return value.toString();
    }

    return value.toString();
  }
}

/// Widget for showing achievement progress with zero-state
class AchievementProgressWidget extends StatelessWidget {
  final List<String> achievements;
  final String category;
  final int maxDisplay;

  const AchievementProgressWidget({
    super.key,
    required this.achievements,
    required this.category,
    this.maxDisplay = 3,
  });

  @override
  Widget build(BuildContext context) {
    if (achievements.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          children: [
            Icon(
              Icons.emoji_events_outlined,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 8),
            Text(
              'No achievements yet',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Start completing $category activities to earn achievements!',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade500,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    final displayAchievements = achievements.take(maxDisplay).toList();
    final remainingCount = achievements.length - maxDisplay;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...displayAchievements.map(
          (achievement) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.primaryColor.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    achievement,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        if (remainingCount > 0)
          Text(
            '+$remainingCount more',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
      ],
    );
  }
}

/// Widget for showing progress bars with zero-state
class ProgressBarWidget extends StatelessWidget {
  final String label;
  final double progress; // 0.0 to 1.0
  final Color color;
  final String? subtitle;

  const ProgressBarWidget({
    super.key,
    required this.label,
    required this.progress,
    required this.color,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final isZero = progress <= 0.0;
    final percentage = (progress * 100).toInt();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isZero ? Colors.grey.shade600 : Colors.black87,
              ),
            ),
            Text(
              isZero ? 'Not started' : '$percentage%',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isZero ? Colors.grey.shade500 : color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            children: [
              if (progress > 0)
                Expanded(
                  flex: (progress * 100).toInt(),
                  child: Container(
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ),
              if (progress < 1.0)
                Expanded(
                  flex: ((1.0 - progress) * 100).toInt(),
                  child: const SizedBox(),
                ),
            ],
          ),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 4),
          Text(
            subtitle!,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
              fontStyle: isZero ? FontStyle.italic : FontStyle.normal,
            ),
          ),
        ],
      ],
    );
  }
}
