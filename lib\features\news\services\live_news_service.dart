import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as html_parser;
import 'package:xml/xml.dart' as xml;
import '../models/agency_news_model.dart';
import 'news_sources_config.dart';

/// Service for fetching real-time news from official sources
class LiveNewsService {
  static const Duration _timeout = Duration(seconds: 30);
  final Map<String, DateTime> _lastFetchTime = {};
  final Map<String, List<AgencyNewsModel>> _cachedNews = {};

  /// Fetch latest news for a specific agency
  Future<List<AgencyNewsModel>> fetchAgencyNews(String agency) async {
    // Check cache first
    if (_isCacheValid(agency)) {
      return _cachedNews[agency] ?? [];
    }

    final newsSource = NewsSourcesConfig.getAgencySource(agency);
    if (newsSource == null) {
      throw Exception('No news source configured for agency: $agency');
    }

    List<AgencyNewsModel> allNews = [];

    try {
      // Strategy 1: Fetch from RSS feeds
      if (newsSource.rssFeeds.isNotEmpty) {
        final rssNews = await _fetchFromRssFeeds(agency, newsSource.rssFeeds);
        allNews.addAll(rssNews);
      }

      // Strategy 2: Fetch from news APIs with keywords
      final apiNews = await _fetchFromNewsApis(
        agency,
        newsSource.keywordFilters,
      );
      allNews.addAll(apiNews);

      // Strategy 3: Web scraping (if RSS fails)
      if (allNews.isEmpty) {
        final scrapedNews = await _scrapeOfficialWebsite(agency, newsSource);
        allNews.addAll(scrapedNews);
      }

      // Remove duplicates and sort by relevance
      allNews = _removeDuplicatesAndSort(allNews, agency);

      // Cache the results
      _cachedNews[agency] = allNews;
      _lastFetchTime[agency] = DateTime.now();

      return allNews;
    } catch (e) {
      print('Error fetching news for $agency: $e');
      // Return cached data if available, otherwise empty list
      return _cachedNews[agency] ?? [];
    }
  }

  /// Fetch news from RSS feeds
  Future<List<AgencyNewsModel>> _fetchFromRssFeeds(
    String agency,
    List<String> rssUrls,
  ) async {
    List<AgencyNewsModel> news = [];

    for (final url in rssUrls) {
      try {
        final response = await http
            .get(
              Uri.parse(url),
              headers: {'User-Agent': 'Fit4Force News Aggregator'},
            )
            .timeout(_timeout);

        if (response.statusCode == 200) {
          final document = xml.XmlDocument.parse(response.body);
          final items = document.findAllElements('item');

          for (final item in items) {
            final newsItem = _parseRssItem(item, agency);
            if (newsItem != null) {
              news.add(newsItem);
            }
          }
        }
      } catch (e) {
        print('Error fetching RSS from $url: $e');
        continue;
      }
    }

    return news;
  }

  /// Parse RSS item into AgencyNewsModel
  AgencyNewsModel? _parseRssItem(xml.XmlElement item, String agency) {
    try {
      final title = item.findElements('title').first.text;
      final description = item.findElements('description').first.text;
      final link = item.findElements('link').first.text;
      final pubDateStr = item.findElements('pubDate').first.text;

      // Parse publication date
      DateTime? pubDate;
      try {
        pubDate = DateTime.parse(pubDateStr);
      } catch (e) {
        pubDate = DateTime.now(); // Fallback to current time
      }

      // Extract category from content or title
      final category = _extractCategory(title, description);

      // Determine if it's breaking news
      final isBreaking = _isBreakingNews(title, description);

      // Calculate priority
      final priority = _calculatePriority(title, description, agency);

      return AgencyNewsModel(
        id: 'rss_${DateTime.now().millisecondsSinceEpoch}',
        title: _cleanText(title),
        content: _cleanText(description),
        agency: agency,
        category: category,
        source: 'Official RSS Feed',
        publishedDate: pubDate,
        priority: priority,
        isBreaking: isBreaking,
        tags: _extractTags(title, description, agency),
        documentUrl: link,
        createdAt: DateTime.now(),
      );
    } catch (e) {
      print('Error parsing RSS item: $e');
      return null;
    }
  }

  /// Fetch news from news APIs (NewsAPI, Google News, etc.)
  Future<List<AgencyNewsModel>> _fetchFromNewsApis(
    String agency,
    List<String> keywords,
  ) async {
    List<AgencyNewsModel> news = [];

    // NewsAPI.org integration
    final newsApiKey = NewsSourcesConfig.newsApiConfig['newsapi_key'];
    if (newsApiKey != null && newsApiKey != 'YOUR_NEWS_API_KEY') {
      final apiNews = await _fetchFromNewsApi(agency, keywords, newsApiKey);
      news.addAll(apiNews);
    }

    return news;
  }

  /// Fetch from NewsAPI.org
  Future<List<AgencyNewsModel>> _fetchFromNewsApi(
    String agency,
    List<String> keywords,
    String apiKey,
  ) async {
    try {
      final query = keywords.take(3).join(' OR '); // Use top 3 keywords
      final url =
          'https://newsapi.org/v2/everything?'
          'q=$query AND Nigeria&'
          'language=en&'
          'sortBy=publishedAt&'
          'pageSize=20&'
          'apiKey=$apiKey';

      final response = await http.get(Uri.parse(url)).timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final articles = data['articles'] as List;

        return articles.map<AgencyNewsModel>((article) {
          return AgencyNewsModel(
            id: 'api_${DateTime.now().millisecondsSinceEpoch}',
            title: _cleanText(article['title'] ?? ''),
            content: _cleanText(article['description'] ?? ''),
            agency: agency,
            category: _extractCategory(
              article['title'],
              article['description'],
            ),
            source: article['source']['name'] ?? 'News API',
            publishedDate: DateTime.parse(article['publishedAt']),
            priority: _calculatePriority(
              article['title'],
              article['description'],
              agency,
            ),
            tags: _extractTags(
              article['title'],
              article['description'],
              agency,
            ),
            documentUrl: article['url'],
            createdAt: DateTime.now(),
          );
        }).toList();
      }
    } catch (e) {
      print('Error fetching from News API: $e');
    }

    return [];
  }

  /// Web scraping fallback for official websites
  Future<List<AgencyNewsModel>> _scrapeOfficialWebsite(
    String agency,
    AgencyNewsSource source,
  ) async {
    try {
      final response = await http
          .get(
            Uri.parse(source.newsSection),
            headers: {'User-Agent': 'Fit4Force News Aggregator'},
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final document = html_parser.parse(response.body);

        // Common selectors for news articles
        final selectors = [
          'article',
          '.news-item',
          '.post',
          '.article',
          'h2 a, h3 a',
        ];

        List<AgencyNewsModel> news = [];

        for (final selector in selectors) {
          final elements = document.querySelectorAll(selector);

          for (final element in elements.take(10)) {
            // Limit to 10 items per selector
            final title =
                element.querySelector('h1, h2, h3, .title')?.text.trim();
            final content =
                element.querySelector('p, .excerpt, .summary')?.text.trim();
            final link = element.querySelector('a')?.attributes['href'];

            if (title != null && title.isNotEmpty) {
              news.add(
                AgencyNewsModel(
                  id: 'scraped_${DateTime.now().millisecondsSinceEpoch}',
                  title: _cleanText(title),
                  content: _cleanText(content ?? ''),
                  agency: agency,
                  category: _extractCategory(title, content),
                  source: 'Official Website',
                  publishedDate: DateTime.now(), // Approximate date
                  priority: _calculatePriority(title, content, agency),
                  tags: _extractTags(title, content, agency),
                  documentUrl: _resolveUrl(link, source.officialWebsite),
                  createdAt: DateTime.now(),
                ),
              );
            }
          }

          if (news.isNotEmpty) {
            break; // Found articles, no need to try other selectors
          }
        }

        return news;
      }
    } catch (e) {
      print('Error scraping website for $agency: $e');
    }

    return [];
  }

  /// Helper methods
  bool _isCacheValid(String agency) {
    final lastFetch = _lastFetchTime[agency];
    if (lastFetch == null) return false;

    return DateTime.now().difference(lastFetch) < NewsFetchConfig.cacheExpiry;
  }

  List<AgencyNewsModel> _removeDuplicatesAndSort(
    List<AgencyNewsModel> news,
    String agency,
  ) {
    // Remove duplicates based on title similarity
    final unique = <AgencyNewsModel>[];
    final seenTitles = <String>{};

    for (final item in news) {
      final normalizedTitle = item.title.toLowerCase().replaceAll(
        RegExp(r'[^\w\s]'),
        '',
      );

      if (!seenTitles.any(
        (seen) => _titleSimilarity(seen, normalizedTitle) > 0.8,
      )) {
        seenTitles.add(normalizedTitle);
        unique.add(item);
      }
    }

    // Sort by relevance and date
    unique.sort((a, b) {
      // Breaking news first
      if (a.isBreaking && !b.isBreaking) return -1;
      if (!a.isBreaking && b.isBreaking) return 1;

      // Then by priority
      final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
      final aPriority = priorityOrder[a.priority] ?? 2;
      final bPriority = priorityOrder[b.priority] ?? 2;
      if (aPriority != bPriority) return aPriority.compareTo(bPriority);

      // Finally by date
      return b.publishedDate.compareTo(a.publishedDate);
    });

    return unique.take(NewsFetchConfig.maxNewsPerAgency).toList();
  }

  double _titleSimilarity(String title1, String title2) {
    final words1 = title1.split(' ').where((w) => w.length > 3).toSet();
    final words2 = title2.split(' ').where((w) => w.length > 3).toSet();

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  String _extractCategory(String? title, String? content) {
    final text = '${title ?? ''} ${content ?? ''}'.toLowerCase();

    if (text.contains('recruitment') || text.contains('recruit')) {
      return 'Recruitment';
    }
    if (text.contains('training') || text.contains('course')) return 'Training';
    if (text.contains('interview') || text.contains('selection')) {
      return 'Interview';
    }
    if (text.contains('requirement') || text.contains('qualification')) {
      return 'Requirements';
    }
    if (text.contains('deadline') || text.contains('application')) {
      return 'Application';
    }
    if (text.contains('exercise') || text.contains('operation')) {
      return 'Operations';
    }

    return 'General';
  }

  bool _isBreakingNews(String? title, String? content) {
    final text = '${title ?? ''} ${content ?? ''}'.toLowerCase();
    final breakingKeywords = [
      'breaking',
      'urgent',
      'immediate',
      'alert',
      'emergency',
      'deadline extended',
      'new recruitment',
      'interview date',
    ];

    return breakingKeywords.any((keyword) => text.contains(keyword));
  }

  String _calculatePriority(String? title, String? content, String agency) {
    final text = '${title ?? ''} ${content ?? ''}'.toLowerCase();

    // High priority keywords
    final highPriority = [
      'recruitment',
      'interview',
      'deadline',
      'application',
      'breaking',
      'urgent',
      'alert',
      'selection',
    ];

    // Medium priority keywords
    final mediumPriority = [
      'training',
      'course',
      'exercise',
      'requirement',
      'qualification',
      'update',
      'announcement',
    ];

    if (highPriority.any((keyword) => text.contains(keyword))) return 'high';
    if (mediumPriority.any((keyword) => text.contains(keyword))) {
      return 'medium';
    }

    return 'low';
  }

  List<String> _extractTags(String? title, String? content, String agency) {
    final text = '${title ?? ''} ${content ?? ''}'.toLowerCase();
    final agencyKeywords = NewsSourcesConfig.getAgencyKeywords(agency);

    final tags = <String>[];

    // Add agency-specific tags
    for (final keyword in agencyKeywords) {
      if (text.contains(keyword.toLowerCase())) {
        tags.add(keyword.toLowerCase());
      }
    }

    // Add common tags
    final commonTags = {
      'recruitment': 'Recruitment',
      'training': 'Training',
      'interview': 'Interview',
      'application': 'Application',
      'deadline': 'Deadline',
      'requirement': 'Requirements',
    };

    for (final entry in commonTags.entries) {
      if (text.contains(entry.key)) {
        tags.add(entry.value);
      }
    }

    return tags.take(5).toList(); // Limit to 5 tags
  }

  String _cleanText(String text) {
    return text
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
        .trim();
  }

  String? _resolveUrl(String? relativeUrl, String baseUrl) {
    if (relativeUrl == null) return null;
    if (relativeUrl.startsWith('http')) return relativeUrl;

    return '$baseUrl$relativeUrl';
  }

  /// Clear cache for testing or manual refresh
  void clearCache([String? agency]) {
    if (agency != null) {
      _cachedNews.remove(agency);
      _lastFetchTime.remove(agency);
    } else {
      _cachedNews.clear();
      _lastFetchTime.clear();
    }
  }

  /// Get cache status
  Map<String, dynamic> getCacheStatus() {
    return {
      'cached_agencies': _cachedNews.keys.toList(),
      'last_fetch_times': _lastFetchTime,
      'cache_sizes': _cachedNews.map((k, v) => MapEntry(k, v.length)),
    };
  }
}
