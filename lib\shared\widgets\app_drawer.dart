import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/features/prep/screens/quiz_screen.dart';
import 'package:fit_4_force/features/prep/models/quiz_model.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import 'package:url_launcher/url_launcher.dart';

class AppDrawer extends StatelessWidget {
  final UserModel user;
  final Function(int)? onTabChange;

  const AppDrawer({super.key, required this.user, this.onTabChange});

  @override
  Widget build(BuildContext context) {
    final isLandscape = ResponsiveUtils.isLandscape(context);
    final isTablet = ResponsiveUtils.isTablet(context);

    return Drawer(
      width: isTablet ? 320 : (isLandscape ? 280 : null),
      child: Column(
        children: [
          _buildHeader(context),
          // Premium upgrade banner for non-premium users
          if (!user.isPremium) _buildPremiumBanner(context),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Main Navigation
                _buildNavigationSection(context),

                // Military-Specific Features
                _buildMilitarySection(context),

                // Training & Fitness
                _buildTrainingSection(context),

                // Premium Features
                _buildPremiumFeaturesSection(context),

                // Account & Settings
                _buildAccountSection(context),

                // Help & Support
                _buildSupportSection(context),
              ],
            ),
          ),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final isLandscape = ResponsiveUtils.isLandscape(context);

    return DrawerHeader(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF1E3A8A), // Deep blue
            const Color(0xFF3B82F6), // Bright blue
            const Color(0xFF60A5FA), // Light blue
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.6, 1.0],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1 * 255),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: isSmallScreen ? 25 : (isLandscape ? 28 : 30),
                backgroundColor: Colors.white,
                backgroundImage:
                    user.profileImageUrl != null
                        ? NetworkImage(user.profileImageUrl!)
                        : null,
                child:
                    user.profileImageUrl == null
                        ? ResponsiveText(
                          _getInitials(user.fullName),
                          mobileFontSize: isSmallScreen ? 18.0 : 20.0,
                          tabletFontSize: 22.0,
                          desktopFontSize: 24.0,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        )
                        : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      user.fullName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user.targetAgency,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    if (user.isPremium)
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.premiumColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'PREMIUM',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          OutlinedButton(
            onPressed: () {
              Navigator.pop(context); // Close drawer
              Navigator.of(context).pushNamed(AppRoutes.profile);
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('View Profile'),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Navigation'),
        ListTile(
          leading: const Icon(Icons.dashboard),
          title: const Text('Dashboard'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            if (onTabChange != null) {
              onTabChange!(0); // Index 0 is the Dashboard tab
            }
          },
        ),
        ListTile(
          leading: const Icon(Icons.menu_book),
          title: const Text('Prep'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            if (onTabChange != null) {
              onTabChange!(1); // Index 1 is the Prep tab
            }
          },
        ),

        ListTile(
          leading: const Icon(Icons.forum),
          title: const Text('Community'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            if (onTabChange != null) {
              onTabChange!(3); // Index 3 is the Community tab
            }
          },
        ),
      ],
    );
  }

  Widget _buildMilitarySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Military Resources'),
        ListTile(
          leading: const Icon(Icons.military_tech),
          title: const Text('Agency Requirements'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.pushNamed(
              context,
              '/agency-requirements',
              arguments: user,
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.history_edu),
          title: const Text('Military History'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _showComingSoonSnackBar(context, 'Military History');
          },
        ),
        ListTile(
          leading: const Icon(Icons.event_note),
          title: const Text('Recruitment Calendar'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _showComingSoonSnackBar(context, 'Recruitment Calendar');
          },
        ),
        ListTile(
          leading: const Icon(Icons.psychology),
          title: const Text('Aptitude Test Prep'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _showComingSoonSnackBar(context, 'Aptitude Test Prep');
          },
        ),
      ],
    );
  }

  Widget _buildTrainingSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Training & Study'),
        ListTile(
          leading: const Icon(Icons.smart_toy, color: Colors.orange),
          title: const Text('Smart Learning Assistant'),
          subtitle: const Text('AI-powered study help'),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Text(
              'AI',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(context).pushNamed(
              AppRoutes.smartLearningAssistant,
              arguments: user.targetAgency,
            );
          },
        ),

        ListTile(
          leading: const Icon(Icons.schedule, color: Colors.orange),
          title: const Text('AI Study Planner'),
          subtitle: const Text('Personalized study plans'),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Text(
              'PREMIUM',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 8,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(
              context,
            ).pushNamed(AppRoutes.aiStudyPlanner, arguments: user.targetAgency);
          },
        ),
        ListTile(
          leading: const Icon(Icons.flash_on),
          title: const Text('Flashcards'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(
              context,
            ).pushNamed(AppRoutes.flashcards, arguments: user);
          },
        ),
        ListTile(
          leading: const Icon(Icons.assignment),
          title: const Text('Mock Exams'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _navigateToInteractiveQuiz(context);
          },
        ),
      ],
    );
  }

  Widget _buildPremiumFeaturesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Premium Features'),

        ListTile(
          leading: const Icon(Icons.emoji_events, color: AppTheme.premiumColor),
          title: const Text(
            'Fitness Challenges',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          onTap: () {
            Navigator.pop(context); // Close drawer
            if (user.isPremium) {
              _showComingSoonSnackBar(context, 'Fitness Challenges');
            } else {
              Navigator.of(context).pushNamed(AppRoutes.premium);
            }
          },
          trailing:
              user.isPremium
                  ? const Icon(Icons.check_circle, color: Colors.green)
                  : const Icon(
                    Icons.workspace_premium,
                    color: AppTheme.premiumColor,
                    size: 20,
                  ),
        ),
      ],
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Account & Settings'),
        ListTile(
          leading: const Icon(Icons.settings),
          title: const Text('Settings'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(context).pushNamed(AppRoutes.settings);
          },
        ),
        ListTile(
          leading: const Icon(Icons.devices),
          title: const Text('Device Management'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(context).pushNamed(AppRoutes.deviceManagement);
          },
        ),
        ListTile(
          leading: Icon(
            Icons.workspace_premium,
            color: user.isPremium ? AppTheme.premiumColor : null,
          ),
          title: Text(
            user.isPremium ? 'Manage Premium' : 'Upgrade to Premium',
            style: TextStyle(
              color: user.isPremium ? AppTheme.premiumColor : null,
              fontWeight: user.isPremium ? FontWeight.bold : null,
            ),
          ),
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(context).pushNamed(AppRoutes.premium);
          },
        ),
      ],
    );
  }

  Widget _buildSupportSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Help & Support'),
        ListTile(
          leading: const Icon(Icons.help_outline),
          title: const Text('Help Center'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _showComingSoonSnackBar(context, 'Help Center');
          },
        ),
        ListTile(
          leading: const Icon(Icons.feedback_outlined),
          title: const Text('Send Feedback'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _launchUrl('https://lambent-salamander-6b0b6c.netlify.app/contact');
          },
        ),
        ListTile(
          leading: const Icon(Icons.description_outlined),
          title: const Text('Terms of Service'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _launchUrl(
              'https://lambent-salamander-6b0b6c.netlify.app/terms-of-service',
            );
          },
        ),
        ListTile(
          leading: const Icon(Icons.privacy_tip_outlined),
          title: const Text('Privacy Policy'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _launchUrl(
              'https://lambent-salamander-6b0b6c.netlify.app/privacy-policy',
            );
          },
        ),
        const Divider(indent: 16, endIndent: 16),
        ListTile(
          leading: const Icon(Icons.menu_book_outlined, color: Colors.blue),
          title: const Text('Legal Documents'),
          subtitle: const Text('View all legal documents'),
          onTap: () {
            Navigator.pop(context); // Close drawer
            _launchUrl('https://lambent-salamander-6b0b6c.netlify.app');
          },
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Sign Out', style: TextStyle(color: Colors.red)),
            onTap: () {
              Navigator.pop(context); // Close drawer
              _showSignOutDialog(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title.toUpperCase(),
        style: TextStyle(
          color: AppTheme.primaryColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildPremiumBanner(BuildContext context) {
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.premiumColor, AppTheme.premiumColor.withAlpha(200)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppTheme.premiumColor.withAlpha(100),
            blurRadius: 8,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.pop(context); // Close drawer
            Navigator.of(context).pushNamed(AppRoutes.premium);
          },
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(
                  Icons.workspace_premium,
                  color: Colors.white,
                  size: 36,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Upgrade to Premium',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Get full access to all fitness exercises & premium features',
                        style: TextStyle(
                          color: Colors.white.withAlpha(220),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showComingSoonSnackBar(BuildContext context, String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$feature coming soon!'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSignOutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign Out'),
            content: const Text('Are you sure you want to sign out?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('CANCEL'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.read<AuthBloc>().add(SignOutEvent());
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('SIGN OUT'),
              ),
            ],
          ),
    );
  }

  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  String _getInitials(String fullName) {
    List<String> names = fullName.split(' ');
    String initials = '';
    if (names.isNotEmpty) {
      initials += names[0][0];
      if (names.length > 1) {
        initials += names[names.length - 1][0];
      }
    }
    return initials.toUpperCase();
  }

  /// Navigate to the interactive quiz system for mock exams
  void _navigateToInteractiveQuiz(BuildContext context) {
    // Create a mock exam quiz model
    final mockExamQuiz = QuizModel(
      id: 'mock_exam_${DateTime.now().millisecondsSinceEpoch}',
      title: 'Mock Exam - ${user.targetAgency ?? "General"}',
      description:
          'Comprehensive mock exam for ${user.targetAgency ?? "Nigerian military"} recruitment preparation',
      agency: user.targetAgency ?? 'General',
      category:
          'mixed', // This will trigger mixed questions (English, Math, Agency History)
      difficulty: 'medium',
      questions: [], // Questions will be loaded dynamically by the quiz screen
      timeLimit:
          user.isPremium ? 25 : 5, // 25 minutes for premium, 5 minutes for free
      isPremium: false, // Mock exams are available to all users
      publishedDate: DateTime.now(),
      icon: Icons.assignment,
      color: Colors.blue,
      passingScore: 70,
    );

    // Navigate to the interactive quiz screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => QuizScreen(
              quiz: mockExamQuiz,
              user: user,
              userAgency: user.targetAgency,
            ),
      ),
    );
  }
}
