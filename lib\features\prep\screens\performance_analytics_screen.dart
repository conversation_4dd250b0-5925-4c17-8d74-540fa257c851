import 'package:flutter/material.dart';
import '../models/nigerian_exam_pattern_model.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../core/widgets/responsive_widgets.dart';
import '../../../core/services/user_progress_service.dart';

/// Performance Analytics Screen with Topic-based Performance Tracking
class PerformanceAnalyticsScreen extends StatefulWidget {
  final String agencyCode;
  final Map<String, TopicPerformanceModel> topicPerformance;

  const PerformanceAnalyticsScreen({
    super.key,
    required this.agencyCode,
    required this.topicPerformance,
  });

  @override
  State<PerformanceAnalyticsScreen> createState() =>
      _PerformanceAnalyticsScreenState();
}

class _PerformanceAnalyticsScreenState
    extends State<PerformanceAnalyticsScreen> {
  final UserProgressService _progressService = UserProgressService();
  String _selectedTimeframe = 'all';
  String _selectedCategory = 'all';
  Map<String, dynamic> _userProgress = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserProgress();
  }

  Future<void> _loadUserProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _userProgress = progress;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading user progress: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Analytics'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : ResponsiveContainer(
                child: SingleChildScrollView(
                  padding: ResponsiveUtils.getResponsivePadding(context),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildFilters(),
                      const SizedBox(height: 24),
                      _buildOverallPerformance(),
                      const SizedBox(height: 24),
                      _buildTopicBreakdown(),
                      const SizedBox(height: 24),
                      _buildWeakAreasSection(),
                      const SizedBox(height: 24),
                      _buildStrongAreasSection(),
                      const SizedBox(height: 24),
                      _buildRecommendations(),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Filter Performance Data',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ResponsiveGridView(
              mobileColumns: 1,
              tabletColumns: 2,
              desktopColumns: 2,
              childAspectRatio: 3.0,
              children: [
                _buildFilterDropdown(
                  'Timeframe',
                  _selectedTimeframe,
                  ['all', 'last_week', 'last_month', 'last_3_months'],
                  (value) => setState(() => _selectedTimeframe = value!),
                ),
                _buildFilterDropdown(
                  'Category',
                  _selectedCategory,
                  [
                    'all',
                    'general_knowledge',
                    'mathematics',
                    'english',
                    'current_affairs',
                  ],
                  (value) => setState(() => _selectedCategory = value!),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items:
          options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(_formatFilterOption(option)),
            );
          }).toList(),
      onChanged: onChanged,
    );
  }

  Widget _buildOverallPerformance() {
    final academicsData = _userProgress['academics'] ?? {};

    // Get real data from user progress, with fallbacks to zero for new users
    final totalQuizzes = academicsData['totalQuizzesCompleted'] ?? 0;
    final averageScore =
        (academicsData['averageQuizScore'] as num?)?.toDouble() ?? 0.0;
    final totalStudyHours = academicsData['totalStudyHours'] ?? 0;
    final currentStreak = academicsData['currentStreak'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Overall Performance',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ResponsiveGridView(
              mobileColumns: 2,
              tabletColumns: 4,
              desktopColumns: 4,
              childAspectRatio: 1.2,
              children: [
                _buildStatCard(
                  'Quizzes Completed',
                  totalQuizzes.toString(),
                  Icons.quiz,
                  Colors.blue,
                ),
                _buildStatCard(
                  'Average Score',
                  '${averageScore.toStringAsFixed(1)}%',
                  Icons.grade,
                  averageScore >= 70
                      ? Colors.green
                      : averageScore >= 50
                      ? Colors.orange
                      : Colors.red,
                ),
                _buildStatCard(
                  'Study Hours',
                  totalStudyHours.toString(),
                  Icons.schedule,
                  Colors.purple,
                ),
                _buildStatCard(
                  'Current Streak',
                  '$currentStreak days',
                  Icons.local_fire_department,
                  currentStreak > 0 ? Colors.orange : Colors.grey,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1 * 255),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3 * 255)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTopicBreakdown() {
    final filteredPerformance = _getFilteredPerformance();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Topic Performance Breakdown',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...filteredPerformance.entries.map((entry) {
              final topic = entry.value;
              return _buildTopicPerformanceCard(topic);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTopicPerformanceCard(TopicPerformanceModel topic) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                topic.topicName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getMasteryColor(topic.masteryLevel),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${(topic.masteryLevel * 100).toStringAsFixed(0)}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: topic.masteryLevel,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              _getMasteryColor(topic.masteryLevel),
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${topic.correctAnswers}/${topic.totalQuestions} correct',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
              Text(
                'Avg: ${topic.averageTime.toStringAsFixed(1)}s',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
          if (topic.needsReview)
            Container(
              margin: const EdgeInsets.only(top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.warning, size: 16, color: Colors.orange[700]),
                  const SizedBox(width: 4),
                  Text(
                    'Needs Review',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildWeakAreasSection() {
    final weakTopics = _getWeakTopics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_down, color: Colors.red[600]),
                const SizedBox(width: 8),
                const Text(
                  'Areas Needing Improvement',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (weakTopics.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green[600]),
                    const SizedBox(width: 8),
                    const Text('Great job! No weak areas identified.'),
                  ],
                ),
              )
            else
              ...weakTopics.map((topic) => _buildWeakAreaCard(topic)),
          ],
        ),
      ),
    );
  }

  Widget _buildWeakAreaCard(TopicPerformanceModel topic) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red[50],
        border: Border.all(color: Colors.red[200]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  topic.topicName,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  'Accuracy: ${(topic.accuracy * 100).toStringAsFixed(1)}%',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () => _practiceWeakArea(topic),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            ),
            child: const Text('Practice', style: TextStyle(fontSize: 12)),
          ),
        ],
      ),
    );
  }

  Widget _buildStrongAreasSection() {
    final strongTopics = _getStrongTopics();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'Strong Areas',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (strongTopics.isEmpty)
              const Text('Keep practicing to build strong areas!')
            else
              ...strongTopics.map((topic) => _buildStrongAreaCard(topic)),
          ],
        ),
      ),
    );
  }

  Widget _buildStrongAreaCard(TopicPerformanceModel topic) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green[50],
        border: Border.all(color: Colors.green[200]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green[600], size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  topic.topicName,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                Text(
                  'Accuracy: ${(topic.accuracy * 100).toStringAsFixed(1)}%',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green[600],
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Text(
              'Mastered',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations() {
    final recommendations = _generateRecommendations();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber[600]),
                const SizedBox(width: 8),
                const Text(
                  'Personalized Recommendations',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...recommendations.map(
              (recommendation) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_forward,
                      color: Colors.blue[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(recommendation)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  Map<String, TopicPerformanceModel> _getFilteredPerformance() {
    // Apply filters based on timeframe and category
    return widget.topicPerformance; // Simplified for now
  }

  List<TopicPerformanceModel> _getWeakTopics() {
    return widget.topicPerformance.values
        .where((topic) => topic.masteryLevel < 0.7)
        .toList()
      ..sort((a, b) => a.masteryLevel.compareTo(b.masteryLevel));
  }

  List<TopicPerformanceModel> _getStrongTopics() {
    return widget.topicPerformance.values
        .where((topic) => topic.masteryLevel >= 0.8)
        .toList()
      ..sort((a, b) => b.masteryLevel.compareTo(a.masteryLevel));
  }

  List<String> _generateRecommendations() {
    final recommendations = <String>[];
    final weakTopics = _getWeakTopics();
    final strongTopics = _getStrongTopics();

    if (weakTopics.isNotEmpty) {
      recommendations.add(
        'Focus on ${weakTopics.first.topicName} - your weakest area',
      );
      recommendations.add(
        'Practice ${weakTopics.length} topics that need improvement',
      );
    }

    if (strongTopics.isNotEmpty) {
      recommendations.add(
        'Maintain your strength in ${strongTopics.first.topicName}',
      );
    }

    recommendations.add(
      'Take a full practice exam to assess overall readiness',
    );
    recommendations.add(
      'Review Nigerian context questions for better cultural understanding',
    );

    return recommendations;
  }

  Color _getMasteryColor(double mastery) {
    if (mastery >= 0.8) return Colors.green;
    if (mastery >= 0.6) return Colors.orange;
    return Colors.red;
  }

  String _formatFilterOption(String option) {
    return option
        .replaceAll('_', ' ')
        .split(' ')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  void _practiceWeakArea(TopicPerformanceModel topic) {
    // Navigate to practice quiz for this topic
    Navigator.pushNamed(
      context,
      '/enhanced_quiz',
      arguments: {
        'agencyCode': widget.agencyCode,
        'category': topic.category,
        'mode': QuizMode.practice,
      },
    );
  }
}
