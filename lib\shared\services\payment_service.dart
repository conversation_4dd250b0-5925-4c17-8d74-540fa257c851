import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/shared/services/paystack_service.dart';
import 'package:fit_4_force/core/services/persistent_auth_service.dart';
import 'base_service.dart';
import 'package:logger/logger.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class PaymentService extends BaseService {
  final AuthService _authService;
  final PersistentAuthService _persistentAuth = PersistentAuthService();
  final PaystackService _paystackService = PaystackService();
  final Logger _logger = Logger();

  @override
  String get collectionName => 'payments';

  PaymentService(this._authService);

  Future<bool> processPayment({
    required BuildContext context,
    required String email,
    required String fullName,
    required double amount,
    Function(String)? onSuccess,
    Function(String)? onError,
  }) async {
    try {
      _logger.i('Starting Paystack payment process for $email');

      // Process payment through Paystack service
      final response = await _paystackService.processPayment(
        email: email,
        fullName: fullName,
        amount: amount,
      );

      if (response['status'] == 'success') {
        final authUrl = response['authorization_url'];
        final reference = response['reference'];

        _logger.i('Payment initialized successfully');
        _logger.i('Authorization URL: $authUrl');
        _logger.i('Reference: $reference');

        if (authUrl != null) {
          // Open Paystack checkout page in browser
          final uri = Uri.parse(authUrl);
          _logger.i('Attempting to launch URL: $uri');

          if (await canLaunchUrl(uri)) {
            _logger.i('URL can be launched, opening browser...');
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            _logger.i('Browser launched successfully');

            // Show payment instruction dialog
            if (context.mounted) {
              _logger.i('Showing payment instruction dialog');
              _showPaymentInstructionDialog(
                context,
                reference,
                onSuccess,
                onError,
              );
            }
            return true;
          } else {
            _logger.e('Cannot launch URL: $uri');
            throw 'Could not launch payment page';
          }
        } else {
          _logger.e('No authorization URL received from Paystack');
          throw 'Invalid payment URL received';
        }
      } else {
        final errorMessage =
            response['message'] ?? 'Payment initialization failed';
        _logger.w('Payment failed: $errorMessage');
        _logger.w('Full response: $response');
        onError?.call(errorMessage);
        return false;
      }
    } catch (e) {
      _logger.e('Error processing payment: $e');
      onError?.call('Payment processing error: $e');
      return false;
    }
  }

  void _showPaymentInstructionDialog(
    BuildContext context,
    String reference,
    Function(String)? onSuccess,
    Function(String)? onError,
  ) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: const Text('Complete Your Payment'),
            content: const Text(
              'A payment page has been opened in your browser. Complete your payment and return to this app.\n\n'
              'Once payment is completed, tap "Payment Done" below.',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onError?.call('Payment cancelled by user');
                },
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _verifyPayment(context, reference, onSuccess, onError);
                },
                child: const Text('Payment Done'),
              ),
            ],
          ),
    );
  }

  Future<void> _verifyPayment(
    BuildContext context,
    String reference,
    Function(String)? onSuccess,
    Function(String)? onError,
  ) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Verifying payment...'),
                ],
              ),
            ),
      );

      // Verify payment
      final verificationResult = await _paystackService.verifyTransaction(
        reference,
      );

      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog
      }

      if (verificationResult != null &&
          verificationResult['status'] == 'success') {
        // Payment successful - update user's premium status
        final user = await _authService.getCurrentUser();
        if (user != null) {
          await handleSuccessfulPayment(reference, user.id);

          // Update user's premium status locally
          final updatedUser = user.copyWith(
            isPremium: true,
            premiumExpiryDate: DateTime.now().add(const Duration(days: 30)),
          );

          // Save updated user session
          await _persistentAuth.saveUserSession(updatedUser);

          onSuccess?.call(reference);
        }
      } else {
        onError?.call(
          'Payment verification failed. Please contact support if payment was deducted.',
        );
      }
    } catch (e) {
      if (context.mounted) {
        Navigator.of(context).pop(); // Close loading dialog
      }
      onError?.call('Payment verification error: $e');
    }
  }

  Future<void> handleSuccessfulPayment(String reference, String userId) async {
    try {
      // Set premium status for 30 days
      final expiryDate = DateTime.now().add(const Duration(days: 30));
      await _authService.updatePremiumStatus(userId, true, expiryDate);

      // Create a payment record in Supabase
      await Supabase.instance.client.from(collectionName).insert({
        'user_id': userId,
        'reference': reference,
        'amount': AppConfig.premiumSubscriptionPrice,
        'status': 'success',
        'type': 'subscription',
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      _logger.e('Error processing successful payment: $e');
    }
  }

  String generateReference() {
    return 'FIT4FORCE_${DateTime.now().millisecondsSinceEpoch}';
  }

  Future<bool> verifyTransaction(String reference) async {
    try {
      final response =
          await Supabase.instance.client
              .from(collectionName)
              .select('id')
              .eq('reference', reference)
              .limit(1)
              .maybeSingle();
      return response != null;
    } catch (e) {
      _logger.e('Error verifying transaction: $e');
      return false;
    }
  }
}
