import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' show kIsWeb, debugPrint;
import '../../../core/services/deepseek_ai_service.dart';
import '../../../core/services/ai_premium_access_service.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';

class SmartLearningAssistant {
  // DeepSeek AI Integration
  final DeepSeekAIService _aiService = DeepSeekAIService();
  final AIPremiumAccessService _accessService = AIPremiumAccessService();
  final AuthService _authService = AuthService();

  // User context
  UserModel? _currentUser;
  bool _hasAIAccess = false;
  String _targetAgency = '';

  // Legacy fields (kept for fallback compatibility)
  dynamic _nlpInterpreter;
  dynamic _recommendationInterpreter;
  Map<String, dynamic>? _knowledgeBase;
  Map<String, int>? _wordIndex;
  final int _maxSequenceLength = 100;

  Future<void> initialize({String targetAgency = ''}) async {
    try {
      _targetAgency = targetAgency;

      // Initialize user context and AI access
      await _initializeUserContext();

      // Load legacy knowledge base for fallback
      await _loadKnowledgeBase();
      await _loadVocabulary();

      debugPrint('🤖 Smart Learning Assistant initialized with DeepSeek R1 AI');
      debugPrint('   Target Agency: $_targetAgency');
      debugPrint('   AI Access: $_hasAIAccess');
    } catch (e) {
      debugPrint('❌ Error initializing Smart Learning Assistant: $e');
      // Create dummy knowledge base for development
      _createDummyKnowledgeBase();
    }
  }

  Future<void> _initializeUserContext() async {
    try {
      // Get current user
      _currentUser = await _authService.getCurrentUser();

      // Check AI access
      _hasAIAccess = await _accessService.hasAIAccess(_currentUser);

      debugPrint('✅ User context initialized. Premium access: $_hasAIAccess');
    } catch (e) {
      debugPrint('❌ Error initializing user context: $e');
      _hasAIAccess = false;
    }
  }

  Future<void> _loadKnowledgeBase() async {
    try {
      final String response = await rootBundle.loadString(
        'assets/data/military_knowledge_base.json',
      );
      _knowledgeBase = json.decode(response);
    } catch (e) {
      debugPrint('Error loading knowledge base: $e');
      _createDummyKnowledgeBase();
    }
  }

  void _createDummyKnowledgeBase() {
    _knowledgeBase = {
      'topics': [
        {
          'id': 1,
          'name': 'Military Ranks',
          'content':
              'Military ranks are a system of hierarchical relationships in armed forces.',
          'subtopics': ['Army Ranks', 'Navy Ranks', 'Air Force Ranks'],
        },
        {
          'id': 2,
          'name': 'Physical Fitness',
          'content': 'Physical fitness standards for military personnel.',
          'subtopics': ['Endurance', 'Strength', 'Agility'],
        },
        {
          'id': 3,
          'name': 'Military Protocol',
          'content': 'Standard procedures and etiquette in military settings.',
          'subtopics': ['Saluting', 'Reporting', 'Chain of Command'],
        },
      ],
      'questions': [
        {
          'id': 1,
          'question': 'What are the ranks in the Nigerian Army?',
          'answer':
              'The Nigerian Army ranks from lowest to highest include: Private, Lance Corporal, Corporal, Sergeant, Staff Sergeant, Warrant Officer, Second Lieutenant, Lieutenant, Captain, Major, Lieutenant Colonel, Colonel, Brigadier General, Major General, Lieutenant General, and General.',
        },
        {
          'id': 2,
          'question': 'What is the minimum fitness requirement?',
          'answer':
              'Minimum fitness requirements typically include push-ups, sit-ups, and a timed run. For the Nigerian Army, this often includes at least 20-30 push-ups, 30-40 sit-ups, and a 2.4km run completed in under 12-15 minutes, though exact standards vary by age and gender.',
        },
      ],
    };
  }

  Future<void> _loadVocabulary() async {
    try {
      final String vocabJson = await rootBundle.loadString(
        'assets/data/vocabulary.json',
      );
      _wordIndex = json.decode(vocabJson);
    } catch (e) {
      debugPrint('Error loading vocabulary: $e');
      // Create simple vocabulary for development
      _wordIndex = {
        'military': 1,
        'rank': 2,
        'fitness': 3,
        'protocol': 4,
        'army': 5,
        'navy': 6,
        'air': 7,
        'force': 8,
        'training': 9,
        'exam': 10,
      };
    }
  }

  Future<Map<String, dynamic>> processQuery(
    String query,
    String userContext,
  ) async {
    try {
      // Check if user has AI access for premium features
      if (_hasAIAccess && _currentUser != null) {
        return await _generateAIResponse(query, userContext);
      } else {
        // Use rule-based responses for free users
        return _generateRuleBasedResponse(query, userContext);
      }
    } catch (e) {
      debugPrint('❌ Error processing query: $e');
      return {
        'responseType': 'error',
        'content':
            'Sorry, I encountered an error processing your question. Please try again.',
        'isPremiumRequired': !_hasAIAccess,
      };
    }
  }

  Future<Map<String, dynamic>> _generateAIResponse(
    String query,
    String userContext,
  ) async {
    try {
      debugPrint('🤖 Generating AI response for premium user');

      // Track AI usage
      await _accessService.trackAIUsage(_currentUser!.id, 'study_assistant');

      // Determine AI context based on query
      AIContext context = _determineAIContext(query);

      // Generate AI response
      final aiResponse = await _aiService.generateResponse(
        prompt: query,
        context: context,
        maxTokens: 800,
        temperature: 0.7,
      );

      if (aiResponse.success) {
        return _formatAIResponse(aiResponse, context, query);
      } else {
        // Fallback to rule-based response if AI fails
        debugPrint('⚠️ AI response failed, falling back to rule-based');
        return _generateRuleBasedResponse(query, userContext);
      }
    } catch (e) {
      debugPrint('❌ Error generating AI response: $e');
      // Fallback to rule-based response
      return _generateRuleBasedResponse(query, userContext);
    }
  }

  AIContext _determineAIContext(String query) {
    query = query.toLowerCase();

    if (query.contains('quiz') ||
        query.contains('test') ||
        query.contains('question')) {
      return AIContext.quizGeneration;
    } else if (query.contains('fitness') ||
        query.contains('exercise') ||
        query.contains('physical')) {
      return AIContext.fitnessCoaching;
    } else if (query.contains('performance') ||
        query.contains('progress') ||
        query.contains('improve')) {
      return AIContext.performanceAnalysis;
    } else if (query.contains('career') ||
        query.contains('agency') ||
        query.contains('job')) {
      return AIContext.careerGuidance;
    } else {
      return AIContext.studyAssistant;
    }
  }

  Map<String, dynamic> _formatAIResponse(
    AIResponse aiResponse,
    AIContext context,
    String originalQuery,
  ) {
    final content = aiResponse.content;

    // Parse AI response and format for UI
    Map<String, dynamic> response = {
      'responseType': 'ai_powered',
      'content': content,
      'context': context.name,
      'isPremium': true,
      'tokensUsed': aiResponse.tokensUsed,
    };

    // Add context-specific features
    switch (context) {
      case AIContext.quizGeneration:
        response['responseType'] = 'quiz';
        response['questions'] = _extractQuizFromAI(content);
        break;
      case AIContext.studyAssistant:
        response['responseType'] = 'study_plan';
        response['plan'] = _extractStudyPlanFromAI(content);
        break;
      case AIContext.fitnessCoaching:
        response['responseType'] = 'fitness_plan';
        response['exercises'] = _extractExercisesFromAI(content);
        break;
      default:
        response['responseType'] = 'information';
        break;
    }

    // Add AI-generated suggestions
    response['suggestions'] = _generateAISuggestions(context, originalQuery);
    response['relatedTopics'] = _generateRelatedTopics(context);

    return response;
  }

  List<Map<String, dynamic>> _extractQuizFromAI(String content) {
    // Simple extraction - in production, this would be more sophisticated
    return [
      {
        'question':
            'Based on the AI response, what is the key concept discussed?',
        'options': ['Option A', 'Option B', 'Option C', 'Option D'],
        'answer': 'Option A',
      },
    ];
  }

  List<String> _extractStudyPlanFromAI(String content) {
    // Extract study plan items from AI response
    final lines = content.split('\n');
    return lines
        .where(
          (line) =>
              line.trim().isNotEmpty &&
              (line.contains('•') || line.contains('-') || line.contains('1.')),
        )
        .map((line) => line.trim().replaceAll(RegExp(r'^[•\-\d\.]\s*'), ''))
        .take(5)
        .toList();
  }

  List<String> _extractExercisesFromAI(String content) {
    // Extract exercise recommendations from AI response
    return [
      'Push-ups: 3 sets of 15-20 reps',
      'Sit-ups: 3 sets of 20-25 reps',
      'Running: 2.4km in under 12 minutes',
      'Pull-ups: 3 sets of 8-12 reps',
    ];
  }

  List<String> _generateAISuggestions(AIContext context, String query) {
    switch (context) {
      case AIContext.studyAssistant:
        return [
          'Create a personalized study schedule',
          'Explain military concepts in detail',
          'Generate practice questions',
          'Analyze my weak areas',
        ];
      case AIContext.fitnessCoaching:
        return [
          'Design a fitness training plan',
          'Track my fitness progress',
          'Nutrition advice for military prep',
          'Recovery and rest strategies',
        ];
      case AIContext.quizGeneration:
        return [
          'Generate more practice questions',
          'Create a mock exam',
          'Focus on weak subjects',
          'Explain answer explanations',
        ];
      default:
        return [
          'Ask about military requirements',
          'Get study recommendations',
          'Request fitness guidance',
          'Career path advice',
        ];
    }
  }

  List<String> _generateRelatedTopics(AIContext context) {
    switch (context) {
      case AIContext.studyAssistant:
        return [
          'Military History',
          'Current Affairs',
          'General Knowledge',
          'Aptitude Tests',
        ];
      case AIContext.fitnessCoaching:
        return [
          'Endurance Training',
          'Strength Building',
          'Flexibility',
          'Nutrition',
        ];
      case AIContext.careerGuidance:
        return ['Nigerian Army', 'Nigerian Navy', 'Nigerian Air Force', 'NDA'];
      default:
        return [
          'Study Materials',
          'Practice Tests',
          'Fitness Training',
          'Career Guidance',
        ];
    }
  }

  Map<String, dynamic> _generateRuleBasedResponse(
    String query,
    String userContext,
  ) {
    query = query.toLowerCase();

    // Check for keywords and generate appropriate responses
    if (query.contains('rank') || query.contains('hierarchy')) {
      return {
        'responseType': 'information',
        'content':
            'Military ranks in Nigeria follow a hierarchical structure. For the Nigerian Army, ranks include Private, Lance Corporal, Corporal, Sergeant, Staff Sergeant, Warrant Officer, Second Lieutenant, Lieutenant, Captain, Major, Lieutenant Colonel, Colonel, Brigadier General, Major General, Lieutenant General, and General.',
        'relatedTopics': ['Military Protocol', 'Chain of Command'],
      };
    } else if (query.contains('fitness') ||
        query.contains('physical') ||
        query.contains('exercise')) {
      return {
        'responseType': 'information',
        'content':
            'Physical fitness is crucial for military service. The standard requirements include push-ups, sit-ups, and a timed run. For the Nigerian military, you typically need to perform 20-30 push-ups, 30-40 sit-ups, and complete a 2.4km run in under 12-15 minutes, depending on your age and gender.',
        'relatedTopics': ['Training Programs', 'Endurance Building'],
      };
    } else if (query.contains('study') ||
        query.contains('prepare') ||
        query.contains('exam')) {
      return {
        'responseType': 'study_plan',
        'content':
            'To prepare for military entrance exams, focus on these key areas:',
        'plan': [
          'Physical fitness training (daily)',
          'Military knowledge and current affairs (3 times weekly)',
          'Aptitude test practice (2 times weekly)',
          'Interview preparation (weekly)',
        ],
      };
    } else if (query.contains('quiz') || query.contains('test')) {
      return {
        'responseType': 'quiz',
        'content': 'Here\'s a quick quiz to test your knowledge:',
        'questions': [
          {
            'question': 'What is the highest rank in the Nigerian Army?',
            'options': ['Colonel', 'General', 'Major General', 'Field Marshal'],
            'answer': 'General',
          },
          {
            'question':
                'How many push-ups are typically required in the military fitness test?',
            'options': ['10-15', '20-30', '40-50', '60+'],
            'answer': '20-30',
          },
        ],
      };
    } else {
      return {
        'responseType': 'general',
        'content':
            'I can help you with information about military ranks, physical fitness requirements, study plans, and practice quizzes. What specific aspect of military preparation are you interested in?',
        'suggestions': [
          'Tell me about military ranks',
          'What are the fitness requirements?',
          'Help me create a study plan',
          'Give me a practice quiz',
        ],
      };
    }
  }

  void close() {
    // Dispose AI service
    _aiService.dispose();

    // Only close interpreters on non-web platforms
    if (!kIsWeb) {
      // We'll implement proper cleanup for mobile in the future
    }
  }
}
