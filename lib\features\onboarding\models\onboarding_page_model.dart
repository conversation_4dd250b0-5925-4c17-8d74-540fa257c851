import 'package:flutter/material.dart';

/// Model representing a single onboarding page
class OnboardingPageModel {
  final String title;
  final String subtitle;
  final String description;
  final String imagePath;
  final Color backgroundColor;
  final Color accentColor;
  final IconData? icon;
  final List<String>? bulletPoints;

  const OnboardingPageModel({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.imagePath,
    required this.backgroundColor,
    required this.accentColor,
    this.icon,
    this.bulletPoints,
  });

  /// Create a copy of the page model with modified properties
  OnboardingPageModel copyWith({
    String? title,
    String? subtitle,
    String? description,
    String? imagePath,
    Color? backgroundColor,
    Color? accentColor,
    IconData? icon,
    List<String>? bulletPoints,
  }) {
    return OnboardingPageModel(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      description: description ?? this.description,
      imagePath: imagePath ?? this.imagePath,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      accentColor: accentColor ?? this.accentColor,
      icon: icon ?? this.icon,
      bulletPoints: bulletPoints ?? this.bulletPoints,
    );
  }

  /// Convert to map for storage
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'subtitle': subtitle,
      'description': description,
      'imagePath': imagePath,
      'backgroundColor': backgroundColor.value,
      'accentColor': accentColor.value,
      'icon': icon?.codePoint,
      'bulletPoints': bulletPoints,
    };
  }

  /// Create from map
  factory OnboardingPageModel.fromMap(Map<String, dynamic> map) {
    return OnboardingPageModel(
      title: map['title'] ?? '',
      subtitle: map['subtitle'] ?? '',
      description: map['description'] ?? '',
      imagePath: map['imagePath'] ?? '',
      backgroundColor: Color(map['backgroundColor'] ?? 0xFF1E3A8A),
      accentColor: Color(map['accentColor'] ?? 0xFF3B82F6),
      icon: map['icon'] != null ? IconData(map['icon']) : null,
      bulletPoints:
          map['bulletPoints'] != null
              ? List<String>.from(map['bulletPoints'])
              : null,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is OnboardingPageModel &&
        other.title == title &&
        other.subtitle == subtitle &&
        other.description == description &&
        other.imagePath == imagePath &&
        other.backgroundColor == backgroundColor &&
        other.accentColor == accentColor &&
        other.icon == icon;
  }

  @override
  int get hashCode {
    return title.hashCode ^
        subtitle.hashCode ^
        description.hashCode ^
        imagePath.hashCode ^
        backgroundColor.hashCode ^
        accentColor.hashCode ^
        icon.hashCode;
  }

  @override
  String toString() {
    return 'OnboardingPageModel(title: $title, subtitle: $subtitle, description: $description, imagePath: $imagePath, backgroundColor: $backgroundColor, accentColor: $accentColor, icon: $icon, bulletPoints: $bulletPoints)';
  }
}

/// Predefined onboarding pages for Fit4Force app
class OnboardingPages {
  static const List<OnboardingPageModel> defaultPages = [
    OnboardingPageModel(
      title: "Welcome to Fit4Force",
      subtitle: "A place for the serious minded",
      description:
          "Your ultimate companion for Nigerian military recruitment preparation. Join thousands of successful candidates who achieved their dreams.",
      imagePath: "",
      backgroundColor: Color(0xFF1E3A8A),
      accentColor: Color(0xFF3B82F6),
      icon: Icons.shield,
    ),
    OnboardingPageModel(
      title: "Are You Ready?",
      subtitle: "Excellence demands preparation",
      description:
          "Master every aspect of military recruitment - from physical fitness to mental aptitude. Train like a champion, succeed like a warrior.",
      imagePath: "assets/images/military_training.png",
      backgroundColor: Color(0xFF1E40AF),
      accentColor: Color(0xFF60A5FA),
      icon: Icons.fitness_center,
      bulletPoints: [
        "Comprehensive study materials",
        "Physical fitness training",
        "Mock examinations",
        "Progress tracking",
      ],
    ),
    OnboardingPageModel(
      title: "Begin Your Journey",
      subtitle: "Victory belongs to the prepared",
      description:
          "Transform your potential into success. Start your journey to join the ranks of Nigeria's finest military personnel today.",
      imagePath: "assets/images/success_badge.png",
      backgroundColor: Color(0xFF1D4ED8),
      accentColor: Color(0xFF93C5FD),
      icon: Icons.emoji_events,
      bulletPoints: [
        "Personalized training plans",
        "Expert guidance",
        "Community support",
        "Guaranteed results",
      ],
    ),
  ];

  /// Get pages customized for specific military agency
  static List<OnboardingPageModel> getPagesForAgency(String agency) {
    return defaultPages.map((page) {
      if (page.title == "Are You Ready?") {
        return page.copyWith(
          description:
              "Master every aspect of $agency recruitment - from physical fitness to mental aptitude. Train like a champion, succeed like a warrior.",
        );
      } else if (page.title == "Begin Your Journey") {
        return page.copyWith(
          description:
              "Transform your potential into success. Start your journey to join the ranks of $agency today.",
        );
      }
      return page;
    }).toList();
  }

  /// Get motivational quotes for each page
  static List<String> getMotivationalQuotes() {
    return [
      "\"Success is where preparation and opportunity meet.\"",
      "\"The will to win, the desire to succeed, the urge to reach your full potential... these are the keys that will unlock the door to personal excellence.\"",
      "\"Champions are made when nobody's watching.\"",
    ];
  }

  /// Get success statistics for motivation
  static Map<String, String> getSuccessStats() {
    return {
      'candidates_trained': '10,000+',
      'success_rate': '95%',
      'agencies_covered': '9',
      'years_experience': '5+',
    };
  }
}
