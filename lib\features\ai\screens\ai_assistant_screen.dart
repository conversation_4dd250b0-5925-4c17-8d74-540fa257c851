import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../core/services/deepseek_ai_service.dart';
import '../../../core/services/ai_premium_access_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';

/// Main AI Assistant Screen for Fit4Force
/// Provides access to all AI-powered features for premium users
class AIAssistantScreen extends StatefulWidget {
  const AIAssistantScreen({super.key});

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen> {
  final Logger _logger = Logger();
  final DeepSeekAIService _aiService = DeepSeekAIService();
  final AIPremiumAccessService _accessService = AIPremiumAccessService();
  final AuthService _authService = AuthService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  UserModel? _currentUser;
  bool _hasAIAccess = false;
  bool _isLoading = false;
  AIContext _selectedContext = AIContext.general;
  final List<ChatMessage> _messages = [];

  @override
  void initState() {
    super.initState();
    _initializeAIScreen();
  }

  Future<void> _initializeAIScreen() async {
    setState(() => _isLoading = true);

    try {
      // Get current user
      _currentUser = await _authService.getCurrentUser();

      // Check AI access
      _hasAIAccess = await _accessService.hasAIAccess(_currentUser);

      if (_hasAIAccess) {
        _addWelcomeMessage();
      }

      _logger.i('🤖 AI Assistant initialized. Access: $_hasAIAccess');
    } catch (e) {
      _logger.e('❌ Error initializing AI screen: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage(
      content: '''
🇳🇬 Welcome to your AI Military Preparation Assistant!

I'm here to help you excel in your Nigerian military recruitment journey. I can assist with:

• Study guidance and explanations
• Practice question generation
• Fitness coaching and training plans
• Performance analysis and improvement tips
• Career guidance for different military agencies

What would you like to work on today?
''',
      isUser: false,
      timestamp: DateTime.now(),
      context: AIContext.general,
    );

    setState(() {
      _messages.add(welcomeMessage);
    });
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    // Check AI access
    if (!await _accessService.checkAIAccessWithDialog(context, _currentUser)) {
      return;
    }

    final userMessage = _messageController.text.trim();
    _messageController.clear();

    // Add user message
    setState(() {
      _messages.add(
        ChatMessage(
          content: userMessage,
          isUser: true,
          timestamp: DateTime.now(),
          context: _selectedContext,
        ),
      );
      _isLoading = true;
    });

    _scrollToBottom();

    try {
      // Track AI usage
      await _accessService.trackAIUsage(
        _currentUser!.id,
        _selectedContext.name,
      );

      // Generate AI response
      final response = await _aiService.generateResponse(
        prompt: userMessage,
        context: _selectedContext,
        maxTokens: 1000,
        temperature: 0.7,
      );

      // Add AI response
      setState(() {
        _messages.add(
          ChatMessage(
            content: response.content,
            isUser: false,
            timestamp: DateTime.now(),
            context: _selectedContext,
            success: response.success,
          ),
        );
      });

      _scrollToBottom();
    } catch (e) {
      _logger.e('❌ Error sending message: $e');

      setState(() {
        _messages.add(
          ChatMessage(
            content: 'Sorry, I encountered an error. Please try again.',
            isUser: false,
            timestamp: DateTime.now(),
            context: _selectedContext,
            success: false,
          ),
        );
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.smart_toy, color: AppTheme.primaryColor, size: 24),
            const SizedBox(width: 8),
            const Text('AI Assistant'),
            if (_hasAIAccess) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.shade300),
                ),
                child: Text(
                  'PREMIUM',
                  style: TextStyle(
                    color: Colors.green.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          PopupMenuButton<AIContext>(
            icon: Icon(_selectedContext.icon, color: AppTheme.primaryColor),
            onSelected: (context) {
              setState(() => _selectedContext = context);
            },
            itemBuilder:
                (context) =>
                    AIContext.values.map((aiContext) {
                      return PopupMenuItem<AIContext>(
                        value: aiContext,
                        child: Row(
                          children: [
                            Icon(aiContext.icon, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    aiContext.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    aiContext.description,
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
          ),
        ],
      ),
      body: _hasAIAccess ? _buildAIInterface() : _buildUpgradePrompt(),
    );
  }

  Widget _buildAIInterface() {
    return Column(
      children: [
        // Context indicator
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
          child: Row(
            children: [
              Icon(
                _selectedContext.icon,
                size: 16,
                color: AppTheme.primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Mode: ${_selectedContext.name}',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              const Spacer(),
              Text(
                _selectedContext.description,
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
            ],
          ),
        ),

        // Messages
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            itemCount: _messages.length,
            itemBuilder: (context, index) {
              return _buildMessageBubble(_messages[index]);
            },
          ),
        ),

        // Loading indicator
        if (_isLoading)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'AI is thinking...',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          ),

        // Input area
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1 * 255),
                blurRadius: 4,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: 'Ask me anything about military preparation...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide(color: AppTheme.primaryColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: null,
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              FloatingActionButton(
                onPressed: _isLoading ? null : _sendMessage,
                backgroundColor: AppTheme.primaryColor,
                mini: true,
                child: Icon(Icons.send, color: Colors.white, size: 20),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUpgradePrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.smart_toy, size: 80, color: Colors.grey.shade400),
            const SizedBox(height: 24),
            Text(
              'AI Assistant',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Unlock powerful AI features to supercharge your military preparation journey.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _accessService.showAIUpgradeDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
              child: const Text('Upgrade to Premium'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor,
              child: Icon(Icons.smart_toy, color: Colors.white, size: 16),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color:
                    message.isUser
                        ? AppTheme.primaryColor
                        : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: message.isUser ? Colors.white : Colors.black87,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      color:
                          message.isUser
                              ? Colors.white.withValues(alpha: 0.7 * 255)
                              : Colors.grey.shade500,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey.shade300,
              child: Icon(Icons.person, color: Colors.grey.shade600, size: 16),
            ),
          ],
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _aiService.dispose();
    super.dispose();
  }
}

/// Chat message model
class ChatMessage {
  final String content;
  final bool isUser;
  final DateTime timestamp;
  final AIContext context;
  final bool success;

  ChatMessage({
    required this.content,
    required this.isUser,
    required this.timestamp,
    required this.context,
    this.success = true,
  });
}
