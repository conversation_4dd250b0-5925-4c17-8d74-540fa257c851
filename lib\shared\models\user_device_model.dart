import 'base_model.dart';

/// Model representing a user's registered device
class UserDeviceModel extends BaseModel {
  final String userId;
  final String deviceId;
  final String deviceName;
  final String deviceType; // mobile, tablet, desktop
  final String platform; // iOS, Android, Web, Windows, macOS, Linux
  final bool isActive;
  final DateTime lastLogin;
  final String? deviceModel;
  final String? osVersion;
  final String? appVersion;

  const UserDeviceModel({
    required super.id,
    required super.createdAt,
    super.updatedAt,
    required this.userId,
    required this.deviceId,
    required this.deviceName,
    required this.deviceType,
    required this.platform,
    required this.isActive,
    required this.lastLogin,
    this.deviceModel,
    this.osVersion,
    this.appVersion,
  });

  @override
  List<Object?> get props => [
        ...super.props,
        userId,
        deviceId,
        deviceName,
        deviceType,
        platform,
        isActive,
        lastLogin,
        deviceModel,
        osVersion,
        appVersion,
      ];

  /// Create UserDeviceModel from JSON
  factory UserDeviceModel.fromJson(Map<String, dynamic> json) {
    return UserDeviceModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
      userId: json['user_id'] as String,
      deviceId: json['device_id'] as String,
      deviceName: json['device_name'] as String,
      deviceType: json['device_type'] as String,
      platform: json['platform'] as String,
      isActive: json['is_active'] as bool,
      lastLogin: DateTime.parse(json['last_login'] as String),
      deviceModel: json['device_model'] as String?,
      osVersion: json['os_version'] as String?,
      appVersion: json['app_version'] as String?,
    );
  }

  /// Convert UserDeviceModel to JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'user_id': userId,
      'device_id': deviceId,
      'device_name': deviceName,
      'device_type': deviceType,
      'platform': platform,
      'is_active': isActive,
      'last_login': lastLogin.toIso8601String(),
      'device_model': deviceModel,
      'os_version': osVersion,
      'app_version': appVersion,
    };
  }

  /// Create a copy with updated fields
  @override
  UserDeviceModel copyWith({
    String? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? userId,
    String? deviceId,
    String? deviceName,
    String? deviceType,
    String? platform,
    bool? isActive,
    DateTime? lastLogin,
    String? deviceModel,
    String? osVersion,
    String? appVersion,
  }) {
    return UserDeviceModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      userId: userId ?? this.userId,
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      platform: platform ?? this.platform,
      isActive: isActive ?? this.isActive,
      lastLogin: lastLogin ?? this.lastLogin,
      deviceModel: deviceModel ?? this.deviceModel,
      osVersion: osVersion ?? this.osVersion,
      appVersion: appVersion ?? this.appVersion,
    );
  }

  /// Get a user-friendly display name for the device
  String get displayName {
    if (deviceModel != null && deviceModel!.isNotEmpty) {
      return '$deviceName ($deviceModel)';
    }
    return deviceName;
  }

  /// Get platform icon based on platform type
  String get platformIcon {
    switch (platform.toLowerCase()) {
      case 'ios':
        return '🍎';
      case 'android':
        return '🤖';
      case 'windows':
        return '🪟';
      case 'macos':
        return '💻';
      case 'linux':
        return '🐧';
      case 'web':
        return '🌐';
      default:
        return '📱';
    }
  }

  /// Get device type icon
  String get deviceTypeIcon {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return '📱';
      case 'tablet':
        return '📱'; // Could use a tablet icon if available
      case 'desktop':
        return '💻';
      default:
        return '📱';
    }
  }

  /// Get status text for display
  String get statusText {
    return isActive ? 'Active' : 'Inactive';
  }

  /// Get time since last login in a human-readable format
  String get lastLoginText {
    final now = DateTime.now();
    final difference = now.difference(lastLogin);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${(difference.inDays / 7).floor()} weeks ago';
    }
  }

  /// Check if this device is considered stale (inactive for too long)
  bool get isStale {
    final now = DateTime.now();
    final daysSinceLastLogin = now.difference(lastLogin).inDays;
    return daysSinceLastLogin > 7;
  }

  /// Get a summary string for logging
  String get summary {
    return '$deviceName ($platform) - ${isActive ? "Active" : "Inactive"} - Last: $lastLoginText';
  }

  /// Get formatted last login (alias for lastLoginText for backward compatibility)
  String getFormattedLastLogin() {
    return lastLoginText;
  }

  /// Check if this is the current device
  bool isCurrentDevice(String currentDeviceId) {
    return deviceId == currentDeviceId;
  }
}

/// Response model for device registration operations
class DeviceRegistrationResponse {
  final bool success;
  final String action; // 'created', 'updated', 'error'
  final UserDeviceModel? device;
  final String? error;
  final String? message;
  final List<UserDeviceModel>? activeDevices;

  const DeviceRegistrationResponse({
    required this.success,
    required this.action,
    this.device,
    this.error,
    this.message,
    this.activeDevices,
  });

  /// Create from JSON response
  factory DeviceRegistrationResponse.fromJson(Map<String, dynamic> json) {
    return DeviceRegistrationResponse(
      success: json['success'] as bool,
      action: json['action'] as String? ?? 'unknown',
      device: json['device'] != null
          ? UserDeviceModel.fromJson(json['device'] as Map<String, dynamic>)
          : null,
      error: json['error'] as String?,
      message: json['message'] as String?,
      activeDevices: json['active_devices'] != null
          ? (json['active_devices'] as List)
              .map((d) => UserDeviceModel.fromJson(d as Map<String, dynamic>))
              .toList()
          : null,
    );
  }

  /// Check if the response indicates device limit exceeded
  bool get isDeviceLimitExceeded {
    return !success && error == 'device_limit_exceeded';
  }
}

/// Enum for device types
enum DeviceType {
  mobile,
  tablet,
  desktop;

  String get displayName {
    switch (this) {
      case DeviceType.mobile:
        return 'Mobile';
      case DeviceType.tablet:
        return 'Tablet';
      case DeviceType.desktop:
        return 'Desktop';
    }
  }
}

/// Enum for supported platforms
enum DevicePlatform {
  ios,
  android,
  web,
  windows,
  macos,
  linux;

  String get displayName {
    switch (this) {
      case DevicePlatform.ios:
        return 'iOS';
      case DevicePlatform.android:
        return 'Android';
      case DevicePlatform.web:
        return 'Web';
      case DevicePlatform.windows:
        return 'Windows';
      case DevicePlatform.macos:
        return 'macOS';
      case DevicePlatform.linux:
        return 'Linux';
    }
  }
}
