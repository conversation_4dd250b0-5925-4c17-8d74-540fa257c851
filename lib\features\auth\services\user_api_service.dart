import 'package:fit_4_force/core/services/api_service.dart';
import 'package:fit_4_force/features/auth/models/user_model.dart';

/// API service for user-related endpoints
class UserApiService {
  /// Base API service
  final ApiService _apiService;

  /// Constructor
  UserApiService({ApiService? apiService})
    : _apiService = apiService ?? ApiService();

  /// Get user profile
  Future<UserModel> getUserProfile(String userId, String token) async {
    final response = await _apiService.get('/users/$userId', token: token);

    return UserModel.fromJson(response);
  }

  /// Update user profile
  Future<UserModel> updateUserProfile(
    String userId,
    Map<String, dynamic> data,
    String token,
  ) async {
    final response = await _apiService.put(
      '/users/$userId',
      body: data,
      token: token,
    );

    return UserModel.from<PERSON>son(response);
  }

  /// Register a new user
  Future<Map<String, dynamic>> registerUser(Map<String, dynamic> data) async {
    final response = await _apiService.post('/auth/register', body: data);

    return response;
  }

  /// Login user
  Future<Map<String, dynamic>> loginUser(String email, String password) async {
    final response = await _apiService.post(
      '/auth/login',
      body: {'email': email, 'password': password},
    );

    return response;
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    await _apiService.post('/auth/reset-password', body: {'email': email});
  }

  /// Change password
  Future<void> changePassword(
    String oldPassword,
    String newPassword,
    String token,
  ) async {
    await _apiService.post(
      '/auth/change-password',
      body: {'oldPassword': oldPassword, 'newPassword': newPassword},
      token: token,
    );
  }

  /// Logout user
  Future<void> logoutUser(String token) async {
    await _apiService.post('/auth/logout', token: token);
  }
}
