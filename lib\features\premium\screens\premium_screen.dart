import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/shared/widgets/base_button.dart';
// Temporarily disabled due to compatibility issues
// import 'package:fit_4_force/shared/services/paystack_service.dart';
import 'package:fit_4_force/shared/services/payment_service.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';

class PremiumScreen extends StatefulWidget {
  const PremiumScreen({super.key});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  bool _isMonthlySelected = true;
  // Use PaymentService instead of MockPaymentService
  final PaymentService _paymentService = PaymentService(AuthService());
  final AuthService _authService = AuthService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: IconThemeData(color: AppTheme.primaryColor),
        title: Text(
          'Premium Subscription',
          style: TextStyle(
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildHeaderSection(),
                    const SizedBox(height: 24),
                    _buildSubscriptionToggle(),
                    const SizedBox(height: 24),
                    _buildPriceCard(),
                    const SizedBox(height: 32),
                    _buildFeaturesList(),
                    const SizedBox(height: 32),
                    BaseButton(
                      text: 'Subscribe Now',
                      onPressed:
                          _isLoading
                              ? null
                              : () => _showPaymentOptions(context),
                      backgroundColor: AppTheme.premiumColor,
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      onPressed:
                          _isLoading ? null : () => Navigator.pop(context),
                      child: Text(
                        'Maybe Later',
                        style: TextStyle(
                          color: AppTheme.textSecondaryLight,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withValues(alpha: 0.5 * 255),
              child: const Center(child: CircularProgressIndicator()),
            ),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: AppTheme.premiumColor.withValues(alpha: 0.1 * 255),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.workspace_premium,
            size: 64,
            color: AppTheme.premiumColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Upgrade to Premium',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: AppTheme.premiumColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Unlock all premium features and take your military preparation to the next level',
          textAlign: TextAlign.center,
          style: Theme.of(
            context,
          ).textTheme.bodyMedium?.copyWith(color: AppTheme.textSecondaryLight),
        ),
      ],
    );
  }

  Widget _buildSubscriptionToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(30),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleOption(
            title: 'Monthly',
            isSelected: _isMonthlySelected,
            onTap: () {
              setState(() {
                _isMonthlySelected = true;
              });
            },
          ),
          _buildToggleOption(
            title: 'Yearly',
            isSelected: !_isMonthlySelected,
            onTap: () {
              setState(() {
                _isMonthlySelected = false;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.premiumColor : Colors.transparent,
          borderRadius: BorderRadius.circular(30),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : AppTheme.textSecondaryLight,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildPriceCard() {
    final monthlyPrice = AppConfig.premiumSubscriptionPrice;
    final yearlyPrice = monthlyPrice * 10; // 2 months free
    final currentPrice = _isMonthlySelected ? monthlyPrice : yearlyPrice;
    final period = _isMonthlySelected ? 'month' : 'year';
    final savings = _isMonthlySelected ? null : '(Save ₦${monthlyPrice * 2})';

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.premiumColor.withValues(alpha: 0.05 * 255),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.premiumColor.withValues(alpha: 0.3 * 255),
        ),
      ),
      child: Column(
        children: [
          Text(
            '₦${currentPrice.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: AppTheme.premiumColor,
            ),
          ),
          Text(
            'per $period',
            style: TextStyle(fontSize: 16, color: AppTheme.textSecondaryLight),
          ),
          if (savings != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.premiumColor.withValues(alpha: 0.1 * 255),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                savings,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.premiumColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFeaturesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Premium Features',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimaryLight,
          ),
        ),
        const SizedBox(height: 16),
        ...AppConfig.premiumFeatures.map(
          (feature) => _buildFeatureItem(feature),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(String feature) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: AppTheme.premiumColor.withValues(alpha: 0.1 * 255),
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.check, size: 16, color: AppTheme.premiumColor),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textPrimaryLight,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showPaymentOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) {
        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Select Payment Method',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryLight,
                ),
              ),
              const SizedBox(height: 24),
              _buildPaymentOption(
                icon: Icons.credit_card,
                title: 'Credit/Debit Card',
                subtitle: 'Pay with Visa, Mastercard, etc.',
                onTap: () {
                  Navigator.pop(context);
                  _processCardPayment();
                },
              ),
              const Divider(height: 32),
              _buildPaymentOption(
                icon: Icons.account_balance,
                title: 'Bank Transfer',
                subtitle: 'Pay directly from your bank account',
                onTap: () {
                  Navigator.pop(context);
                  _processBankTransfer();
                },
              ),
              const Divider(height: 32),
              _buildPaymentOption(
                icon: Icons.phone_android,
                title: 'USSD Payment',
                subtitle: 'Pay using your bank USSD code',
                onTap: () {
                  Navigator.pop(context);
                  _processUssdPayment();
                },
              ),
              const SizedBox(height: 24),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPaymentOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1 * 255),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: AppTheme.primaryColor),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryLight,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppTheme.textSecondaryLight,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _processCardPayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user
      final user = await _authService.getCurrentUser();

      if (user == null) {
        _showErrorDialog('User not found. Please log in again.');
        return;
      }

      // Calculate amount based on selected subscription period
      final amount =
          _isMonthlySelected
              ? AppConfig.premiumSubscriptionPrice
              : AppConfig.premiumSubscriptionPrice *
                  10; // 10 months for yearly (2 months free)

      // Check if widget is still mounted before proceeding
      if (!mounted) {
        return;
      }

      // Process payment using PaymentService with proper callbacks
      final success = await _paymentService.processPayment(
        context: context,
        email: user.email,
        fullName: user.fullName,
        amount: amount,
        onSuccess: (reference) {
          // This will be called when payment is actually verified
          if (mounted) {
            _showSuccessDialog();
          }
        },
        onError: (error) {
          // Handle error callback
          if (mounted) {
            _showErrorDialog(error);
          }
        },
      );

      // If success is true, it means the payment page was launched successfully
      // The actual payment verification will happen in the onSuccess callback
      if (!success) {
        _showErrorDialog('Failed to open payment page. Please try again.');
      }
    } catch (e) {
      _showErrorDialog('An error occurred: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                const SizedBox(width: 8),
                const Text('Payment Successful'),
              ],
            ),
            content: const Text(
              'Your premium subscription has been activated successfully. Enjoy all premium features!',
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(context); // Close dialog
                  Navigator.pop(context); // Go back to previous screen
                },
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.error, color: Colors.red),
                const SizedBox(width: 8),
                const Text('Payment Failed'),
              ],
            ),
            content: Text(message),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Future<void> _processBankTransfer() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user
      final user = await _authService.getCurrentUser();

      if (user == null) {
        _showErrorDialog('User not found. Please log in again.');
        return;
      }

      // Calculate amount based on selected subscription period
      final amount =
          _isMonthlySelected
              ? AppConfig.premiumSubscriptionPrice
              : AppConfig.premiumSubscriptionPrice *
                  10; // 10 months for yearly (2 months free)

      // Check if widget is still mounted before proceeding
      if (!mounted) {
        return;
      }

      // Show bank transfer details dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.account_balance, color: AppTheme.primaryColor),
                  const SizedBox(width: 8),
                  const Text('Bank Transfer Details'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Please transfer the subscription fee to the following account:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  _buildTransferDetail('Bank Name', 'First Bank of Nigeria'),
                  _buildTransferDetail('Account Name', 'Fit4Force Ltd'),
                  _buildTransferDetail('Account Number', '**********'),
                  _buildTransferDetail(
                    'Amount',
                    '₦${amount.toStringAsFixed(2)}',
                  ),
                  _buildTransferDetail(
                    'Reference',
                    'FIT4FORCE_${user.id.substring(0, 8)}',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'After making the transfer, please contact our support team with your payment reference to activate your subscription.',
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CLOSE'),
                ),
              ],
            ),
      );
    } catch (e) {
      _showErrorDialog('An error occurred: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildTransferDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _processUssdPayment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user
      final user = await _authService.getCurrentUser();

      if (user == null) {
        _showErrorDialog('User not found. Please log in again.');
        return;
      }

      // Calculate amount based on selected subscription period
      final amount =
          _isMonthlySelected
              ? AppConfig.premiumSubscriptionPrice
              : AppConfig.premiumSubscriptionPrice *
                  10; // 10 months for yearly (2 months free)

      // Check if widget is still mounted before proceeding
      if (!mounted) {
        return;
      }

      // Show USSD code dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.phone_android, color: AppTheme.primaryColor),
                  const SizedBox(width: 8),
                  const Text('USSD Payment'),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Please dial the following USSD code on your phone:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '*737*000*${amount.toInt()}#',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Follow the prompts on your phone to complete the payment. After payment, your subscription will be activated automatically.',
                    style: TextStyle(fontStyle: FontStyle.italic),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('CLOSE'),
                ),
              ],
            ),
      );
    } catch (e) {
      _showErrorDialog('An error occurred: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
