import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import '../../../core/services/deepseek_ai_service.dart';
import '../../../core/services/ai_premium_access_service.dart';
import '../../../core/theme/app_theme.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';

/// AI-powered Quiz Generator for Fit4Force
/// Generates custom quizzes based on user preferences and weak areas
class AIQuizGeneratorScreen extends StatefulWidget {
  final String targetAgency;

  const AIQuizGeneratorScreen({super.key, required this.targetAgency});

  @override
  State<AIQuizGeneratorScreen> createState() => _AIQuizGeneratorScreenState();
}

class _AIQuizGeneratorScreenState extends State<AIQuizGeneratorScreen> {
  final Logger _logger = Logger();
  final DeepSeekAIService _aiService = DeepSeekAIService();
  final AIPremiumAccessService _accessService = AIPremiumAccessService();
  final AuthService _authService = AuthService();

  UserModel? _currentUser;
  bool _hasAIAccess = false;
  bool _isLoading = false;
  bool _isGenerating = false;

  // Quiz configuration
  String _selectedTopic = 'General Knowledge';
  String _selectedDifficulty = 'Medium';
  int _numberOfQuestions = 10;
  List<Map<String, dynamic>> _generatedQuiz = [];
  int _currentQuestionIndex = 0;
  final Map<int, String> _userAnswers = {};
  bool _showResults = false;

  final List<String> _topics = [
    'General Knowledge',
    'Military History',
    'Current Affairs',
    'Aptitude Test',
    'Physical Fitness',
    'Military Protocol',
    'Leadership',
    'Nigerian Constitution',
  ];

  final List<String> _difficulties = ['Easy', 'Medium', 'Hard'];
  final List<int> _questionCounts = [5, 10, 15, 20];

  @override
  void initState() {
    super.initState();
    _initializeQuizGenerator();
  }

  Future<void> _initializeQuizGenerator() async {
    setState(() => _isLoading = true);

    try {
      _currentUser = await _authService.getCurrentUser();
      _hasAIAccess = await _accessService.hasAIAccess(_currentUser);
      
      _logger.i('🧠 AI Quiz Generator initialized. Access: $_hasAIAccess');
    } catch (e) {
      _logger.e('❌ Error initializing quiz generator: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateQuiz() async {
    if (!await _accessService.checkAIAccessWithDialog(context, _currentUser)) {
      return;
    }

    setState(() => _isGenerating = true);

    try {
      await _accessService.trackAIUsage(_currentUser!.id, 'quiz_generation');

      final prompt = '''
Generate $_numberOfQuestions multiple-choice questions for ${widget.targetAgency} preparation.

Topic: $_selectedTopic
Difficulty: $_selectedDifficulty
Target Agency: ${widget.targetAgency}

Requirements:
1. Each question should have 4 options (A, B, C, D)
2. Include the correct answer
3. Questions should be relevant to Nigerian military recruitment
4. Difficulty should match the selected level
5. Include explanations for correct answers

Format as JSON array with this structure:
[
  {
    "question": "Question text",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correct_answer": "A",
    "explanation": "Why this answer is correct"
  }
]
''';

      final response = await _aiService.generateResponse(
        prompt: prompt,
        context: AIContext.quizGeneration,
        maxTokens: 2000,
        temperature: 0.8,
      );

      if (response.success) {
        _parseGeneratedQuiz(response.content);
      } else {
        _showErrorMessage('Failed to generate quiz. Please try again.');
      }
    } catch (e) {
      _logger.e('❌ Error generating quiz: $e');
      _showErrorMessage('An error occurred while generating the quiz.');
    } finally {
      setState(() => _isGenerating = false);
    }
  }

  void _parseGeneratedQuiz(String content) {
    try {
      // Simple parsing - in production, use proper JSON parsing
      final questions = <Map<String, dynamic>>[];
      
      // For demo, create sample questions
      for (int i = 0; i < _numberOfQuestions; i++) {
        questions.add({
          'question': 'AI-Generated Question ${i + 1} about $_selectedTopic for ${widget.targetAgency}',
          'options': [
            'Option A - AI Generated',
            'Option B - AI Generated', 
            'Option C - AI Generated',
            'Option D - AI Generated'
          ],
          'correct_answer': 'A',
          'explanation': 'AI-generated explanation for this question.'
        });
      }

      setState(() {
        _generatedQuiz = questions;
        _currentQuestionIndex = 0;
        _userAnswers.clear();
        _showResults = false;
      });
    } catch (e) {
      _logger.e('❌ Error parsing quiz: $e');
      _showErrorMessage('Failed to parse generated quiz.');
    }
  }

  void _answerQuestion(String answer) {
    setState(() {
      _userAnswers[_currentQuestionIndex] = answer;
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _generatedQuiz.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
    } else {
      _showQuizResults();
    }
  }

  void _previousQuestion() {
    if (_currentQuestionIndex > 0) {
      setState(() {
        _currentQuestionIndex--;
      });
    }
  }

  void _showQuizResults() {
    setState(() {
      _showResults = true;
    });
  }

  int _calculateScore() {
    int correct = 0;
    for (int i = 0; i < _generatedQuiz.length; i++) {
      if (_userAnswers[i] == _generatedQuiz[i]['correct_answer']) {
        correct++;
      }
    }
    return correct;
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.quiz, color: Colors.white),
            SizedBox(width: 8),
            Text('AI Quiz Generator'),
            if (_hasAIAccess) ...[
              SizedBox(width: 8),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'AI POWERED',
                  style: TextStyle(
                    color: Colors.orange.shade700,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: AppTheme.primaryColor,
      ),
      body: _hasAIAccess ? _buildQuizInterface() : _buildUpgradePrompt(),
    );
  }

  Widget _buildQuizInterface() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_generatedQuiz.isEmpty) {
      return _buildQuizConfiguration();
    }

    if (_showResults) {
      return _buildQuizResults();
    }

    return _buildQuizQuestion();
  }

  Widget _buildQuizConfiguration() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Configure Your AI Quiz',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 24),
          
          _buildConfigSection('Topic', _selectedTopic, _topics, (value) {
            setState(() => _selectedTopic = value);
          }),
          
          _buildConfigSection('Difficulty', _selectedDifficulty, _difficulties, (value) {
            setState(() => _selectedDifficulty = value);
          }),
          
          _buildNumberSection(),
          
          SizedBox(height: 32),
          
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isGenerating ? null : _generateQuiz,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isGenerating
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        SizedBox(width: 12),
                        Text('Generating AI Quiz...'),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.auto_awesome),
                        SizedBox(width: 8),
                        Text('Generate AI Quiz'),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfigSection(String title, String value, List<String> options, Function(String) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: options.map((option) {
            return DropdownMenuItem(value: option, child: Text(option));
          }).toList(),
          onChanged: (newValue) => onChanged(newValue!),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildNumberSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Number of Questions', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        SizedBox(height: 8),
        DropdownButtonFormField<int>(
          value: _numberOfQuestions,
          decoration: InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _questionCounts.map((count) {
            return DropdownMenuItem(value: count, child: Text('$count questions'));
          }).toList(),
          onChanged: (newValue) => setState(() => _numberOfQuestions = newValue!),
        ),
        SizedBox(height: 16),
      ],
    );
  }

  Widget _buildQuizQuestion() {
    final question = _generatedQuiz[_currentQuestionIndex];
    final options = question['options'] as List<dynamic>;
    final selectedAnswer = _userAnswers[_currentQuestionIndex];

    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LinearProgressIndicator(
            value: (_currentQuestionIndex + 1) / _generatedQuiz.length,
            backgroundColor: Colors.grey.shade300,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          SizedBox(height: 16),
          
          Text(
            'Question ${_currentQuestionIndex + 1} of ${_generatedQuiz.length}',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
          ),
          SizedBox(height: 8),
          
          Text(
            question['question'],
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
          ),
          SizedBox(height: 24),
          
          ...options.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final optionLetter = String.fromCharCode(65 + index); // A, B, C, D
            final isSelected = selectedAnswer == optionLetter;
            
            return Container(
              margin: EdgeInsets.only(bottom: 12),
              child: InkWell(
                onTap: () => _answerQuestion(optionLetter),
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(8),
                    color: isSelected ? AppTheme.primaryColor.withValues(alpha: 0.1 * 255) : null,
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
                        ),
                        child: Center(
                          child: Text(
                            optionLetter,
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(child: Text(option)),
                    ],
                  ),
                ),
              ),
            );
          }),
          
          Spacer(),
          
          Row(
            children: [
              if (_currentQuestionIndex > 0)
                Expanded(
                  child: OutlinedButton(
                    onPressed: _previousQuestion,
                    child: Text('Previous'),
                  ),
                ),
              if (_currentQuestionIndex > 0) SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: selectedAnswer != null
                      ? (_currentQuestionIndex < _generatedQuiz.length - 1 ? _nextQuestion : _showQuizResults)
                      : null,
                  style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
                  child: Text(_currentQuestionIndex < _generatedQuiz.length - 1 ? 'Next' : 'Finish'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuizResults() {
    final score = _calculateScore();
    final percentage = (score / _generatedQuiz.length * 100).round();
    
    return Padding(
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(
            Icons.emoji_events,
            size: 80,
            color: percentage >= 70 ? Colors.green : percentage >= 50 ? Colors.orange : Colors.red,
          ),
          SizedBox(height: 16),
          Text(
            'Quiz Complete!',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'You scored $score out of ${_generatedQuiz.length} ($percentage%)',
            style: TextStyle(fontSize: 18),
          ),
          SizedBox(height: 32),
          
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _generatedQuiz.clear();
                      _showResults = false;
                    });
                  },
                  child: Text('New Quiz'),
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _currentQuestionIndex = 0;
                      _userAnswers.clear();
                      _showResults = false;
                    });
                  },
                  style: ElevatedButton.styleFrom(backgroundColor: AppTheme.primaryColor),
                  child: Text('Retake Quiz'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUpgradePrompt() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.quiz, size: 80, color: Colors.grey.shade400),
            SizedBox(height: 24),
            Text(
              'AI Quiz Generator',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.grey.shade700),
            ),
            SizedBox(height: 16),
            Text(
              'Generate personalized quizzes with AI to test your knowledge and identify weak areas.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
            ),
            SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _accessService.showAIUpgradeDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text('Upgrade to Premium'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _aiService.dispose();
    super.dispose();
  }
}
