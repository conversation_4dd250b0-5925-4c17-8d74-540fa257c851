import 'package:flutter/material.dart';
import 'package:fit_4_force/features/testing/email_test_page.dart';

/// Quick test launcher for development purposes
///
/// This widget provides quick access to testing features.
/// Remove before production.
class QuickTestLauncher extends StatelessWidget {
  const QuickTestLauncher({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const EmailTestPage()),
        );
      },
      label: const Text('Test Emails'),
      icon: const Icon(Icons.email),
      backgroundColor: Colors.orange,
    );
  }
}

/// Debug menu for testing various features
class DebugTestMenu extends StatelessWidget {
  const DebugTestMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('🧪 Debug Menu'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.email, color: Colors.blue),
            title: const Text('Test Emails'),
            subtitle: const Text('Test welcome & premium emails'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const EmailTestPage()),
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.info, color: Colors.grey),
            title: const Text('About'),
            subtitle: const Text('Testing mode only'),
            onTap: () => Navigator.pop(context),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Close'),
        ),
      ],
    );
  }
}

/// Extension to easily add debug menu to any page
extension DebugMenuExtension on Widget {
  Widget withDebugMenu(BuildContext context) {
    return GestureDetector(
      onLongPress: () {
        showDialog(
          context: context,
          builder: (context) => const DebugTestMenu(),
        );
      },
      child: this,
    );
  }
}
