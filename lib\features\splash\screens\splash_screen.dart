import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    await Future.delayed(const Duration(seconds: 2)); // Simulate loading

    if (!mounted) return;

    final authService = context.read<AuthService>();
    final user = await authService.getCurrentUser();

    if (!mounted) return;

    if (user != null) {
      context.read<AuthBloc>().add(AuthenticatedEvent(user));
      Navigator.of(context).pushReplacementNamed(AppRoutes.home);
    } else {
      Navigator.of(context).pushReplacementNamed(AppRoutes.login);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),

              // Fit4Force Logo
              Container(
                margin: EdgeInsets.only(
                  bottom: ResponsiveUtils.getResponsiveSpacing(context),
                ),
                child: Image.asset(
                  'assets/images/fit4force_main_logo.png',
                  width: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 120,
                    tablet: 140,
                    desktop: 160,
                  ),
                  height: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 120,
                    tablet: 140,
                    desktop: 160,
                  ),
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    // Fallback to simple icon if logo not found
                    return Icon(
                      Icons.fitness_center,
                      size: ResponsiveUtils.getResponsiveFontSize(
                        context,
                        mobile: 60,
                        tablet: 70,
                        desktop: 80,
                      ),
                      color: Colors.blue,
                    );
                  },
                ),
              ),

              // App Name
              Text(
                'FIT4FORCE',
                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                  fontSize: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 28,
                    tablet: 32,
                    desktop: 36,
                  ),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(
                height: ResponsiveUtils.getResponsiveSpacing(context) / 3,
              ),

              // Tagline
              Text(
                'Prepare. Train. Succeed.',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[700],
                  fontSize: ResponsiveUtils.getResponsiveFontSize(
                    context,
                    mobile: 14,
                    tablet: 16,
                    desktop: 18,
                  ),
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(context)),

              // Loading indicator
              const CircularProgressIndicator(),

              const Spacer(flex: 2),
            ],
          ),
        ),
      ),
    );
  }
}
