import 'package:fit_4_force/features/news/models/agency_news_model.dart';
import 'live_news_service.dart';
import 'news_sources_config.dart';

class AgencyNewsService {
  final LiveNewsService _liveNewsService = LiveNewsService();

  // Configuration for live news fetching
  static const bool _useLiveNews = true; // Set to false to use only mock data
  static const bool _combineMockWithLive =
      true; // Combine mock data with live data

  // Mock data for demonstration - in real app, this would come from API
  static final List<AgencyNewsModel> _mockNews = [
    // Nigerian Army News
    AgencyNewsModel(
      id: 'news_1',
      createdAt: DateTime.now(),
      title: 'Nigerian Army 84RRI: 2024/2025 Recruitment Exercise',
      content:
          'The Nigerian Army announces the commencement of 84 Regular Recruit Intake (84RRI) for 2024/2025. Applications are now open for qualified Nigerian citizens.',
      agency: 'Nigerian Army',
      category: 'Recruitment',
      source: 'Nigerian Army Headquarters',
      imageUrl: 'assets/images/news/army_recruitment.jpg',
      publishedDate: DateTime.now().subtract(const Duration(hours: 2)),
      applicationDeadline: DateTime.now().add(const Duration(days: 14)),
      priority: 'high',
      tags: ['Recruitment', 'Application', 'Deadline'],
      isPinned: true,
      isBreaking: true,
    ),
    AgencyNewsModel(
      id: 'news_2',
      createdAt: DateTime.now(),
      title: 'Nigerian Army: New Training Requirements',
      content:
          'The Nigerian Army has introduced new physical training requirements for all new recruits effective immediately.',
      agency: 'Nigerian Army',
      category: 'Training',
      source: 'Nigerian Army Training Command',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      priority: 'medium',
      tags: ['Training', 'Requirements', 'Update'],
      isPinned: false,
    ),

    // Nigerian Navy News
    AgencyNewsModel(
      id: 'news_3',
      createdAt: DateTime.now(),
      title: 'Nigerian Navy: Updated Physical Fitness Requirements',
      content:
          'The Nigerian Navy has updated physical fitness requirements for all recruitment exercises effective immediately. All candidates must meet new standards.',
      agency: 'Nigerian Navy',
      category: 'Requirements',
      source: 'Naval Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      priority: 'medium',
      tags: ['Physical Fitness', 'Requirements', 'Update'],
      isPinned: true,
    ),
    AgencyNewsModel(
      id: 'news_4',
      createdAt: DateTime.now(),
      title: 'Nigerian Navy: Maritime Security Training Program',
      content:
          'The Nigerian Navy announces a new maritime security training program for naval officers. Applications open for qualified personnel.',
      agency: 'Nigerian Navy',
      category: 'Training',
      source: 'Nigerian Navy Training Command',
      publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      priority: 'medium',
      tags: ['Training', 'Maritime', 'Security'],
      isPinned: false,
    ),

    // Nigerian Air Force News
    AgencyNewsModel(
      id: 'news_5',
      createdAt: DateTime.now(),
      title: 'Air Force Academy: 2024 Intake Interview Schedule',
      content:
          'The Nigerian Air Force Academy has released the interview schedule for 2024 intake. Candidates are advised to check their status online.',
      agency: 'Nigerian Air Force',
      category: 'Interview',
      source: 'NAF Academy',
      publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      applicationDeadline: DateTime.now().add(const Duration(days: 5)),
      priority: 'high',
      tags: ['Interview', 'Schedule', 'Academy'],
    ),
    AgencyNewsModel(
      id: 'news_6',
      createdAt: DateTime.now(),
      title: 'Nigerian Air Force: New Aviation Technology Course',
      content:
          'The Nigerian Air Force introduces advanced aviation technology courses for technical personnel. Registration now open.',
      agency: 'Nigerian Air Force',
      category: 'Training',
      source: 'NAF Technical School',
      publishedDate: DateTime.now().subtract(const Duration(days: 3)),
      priority: 'medium',
      tags: ['Technology', 'Aviation', 'Course'],
      isPinned: false,
    ),

    // DSSC News
    AgencyNewsModel(
      id: 'news_7',
      createdAt: DateTime.now(),
      title: 'DSSC: New Academic Requirements for 2024',
      content:
          'Defence Space Systems Command announces new academic requirements and qualification criteria for officer positions.',
      agency: 'DSSC',
      category: 'Requirements',
      source: 'DSSC Headquarters',
      publishedDate: DateTime.now().subtract(const Duration(days: 3)),
      priority: 'medium',
      tags: ['Academic', 'Requirements', 'Officer'],
    ),

    // NDA News
    AgencyNewsModel(
      id: 'news_8',
      createdAt: DateTime.now(),
      title: 'NDA: 2024 Admission Exercise Commences',
      content:
          'The Nigerian Defence Academy announces the commencement of 2024 admission exercise. Online applications now open for qualified candidates.',
      agency: 'NDA',
      category: 'Admission',
      source: 'NDA Registrar',
      publishedDate: DateTime.now().subtract(const Duration(hours: 6)),
      applicationDeadline: DateTime.now().add(const Duration(days: 21)),
      priority: 'high',
      tags: ['Admission', 'Application', 'Academy'],
      isPinned: true,
      isBreaking: true,
    ),

    // NSCDC News
    AgencyNewsModel(
      id: 'news_9',
      createdAt: DateTime.now(),
      title: 'NSCDC: Community Policing Training Program',
      content:
          'Nigeria Security and Civil Defence Corps launches new community policing training program for recruited officers.',
      agency: 'NSCDC',
      category: 'Training',
      source: 'NSCDC Training School',
      publishedDate: DateTime.now().subtract(const Duration(days: 4)),
      priority: 'low',
      tags: ['Training', 'Community Policing', 'Program'],
    ),

    // Additional agencies
    AgencyNewsModel(
      id: 'news_10',
      createdAt: DateTime.now(),
      title: 'Fire Service: Emergency Response Training Updates',
      content:
          'Federal Fire Service announces updated emergency response training protocols for all personnel.',
      agency: 'Fire Service',
      category: 'Training',
      source: 'Federal Fire Service HQ',
      publishedDate: DateTime.now().subtract(const Duration(days: 5)),
      priority: 'medium',
      tags: ['Emergency', 'Response', 'Training'],
    ),
  ];

  /// Get all news sorted by priority and date
  Future<List<AgencyNewsModel>> getAllNews() async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    // If live news is enabled, fetch from live news service
    if (_useLiveNews) {
      try {
        // For getAllNews, we could fetch from multiple agencies
        // For now, let's just use mock data and rely on getPersonalizedNews for live data
        final liveNews = <AgencyNewsModel>[];
        if (liveNews.isNotEmpty) {
          liveNews.sort((a, b) {
            // Sort by breaking news first, then pinned, then priority, then date
            if (a.isBreaking && !b.isBreaking) return -1;
            if (!a.isBreaking && b.isBreaking) return 1;

            if (a.isPinned && !b.isPinned) return -1;
            if (!a.isPinned && b.isPinned) return 1;

            final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
            final aPriority = priorityOrder[a.priority] ?? 2;
            final bPriority = priorityOrder[b.priority] ?? 2;

            if (aPriority != bPriority) return aPriority.compareTo(bPriority);

            return b.publishedDate.compareTo(a.publishedDate);
          });
          return liveNews;
        }
      } catch (e) {
        print('Failed to fetch live news: $e');
      }
    }

    // Fallback to mock news
    final sortedNews = List<AgencyNewsModel>.from(_mockNews);
    sortedNews.sort((a, b) {
      // Sort by breaking news first, then pinned, then priority, then date
      if (a.isBreaking && !b.isBreaking) return -1;
      if (!a.isBreaking && b.isBreaking) return 1;

      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
      final aPriority = priorityOrder[a.priority] ?? 2;
      final bPriority = priorityOrder[b.priority] ?? 2;

      if (aPriority != bPriority) return aPriority.compareTo(bPriority);

      return b.publishedDate.compareTo(a.publishedDate);
    });
    return sortedNews;
  }

  /// Get news by specific agency
  Future<List<AgencyNewsModel>> getNewsByAgency(String agency) async {
    final allNews = await getAllNews();
    return allNews.where((news) => news.agency == agency).toList();
  }

  /// Get news by category
  Future<List<AgencyNewsModel>> getNewsByCategory(String category) async {
    final allNews = await getAllNews();
    return allNews.where((news) => news.category == category).toList();
  }

  /// Get breaking news
  Future<List<AgencyNewsModel>> getBreakingNews() async {
    final allNews = await Future.wait([
      getPersonalizedNews('Nigerian Army'),
      getPersonalizedNews('Nigerian Navy'),
      getPersonalizedNews('Nigerian Air Force'),
      getPersonalizedNews('Nigeria Police Force'),
      getPersonalizedNews('Nigeria Security and Civil Defence Corps'),
      getPersonalizedNews('Nigeria Immigration Service'),
      getPersonalizedNews('Nigeria Customs Service'),
      getPersonalizedNews('Federal Fire Service'),
      getPersonalizedNews('Nigeria Correctional Service'),
    ]);

    final flattenedNews = allNews.expand((news) => news).toList();
    return flattenedNews.where((news) => news.isBreaking).toList();
  }

  /// Get news with upcoming deadlines
  Future<List<AgencyNewsModel>> getNewsWithDeadlines() async {
    final allNews = await Future.wait([
      getPersonalizedNews('Nigerian Army'),
      getPersonalizedNews('Nigerian Navy'),
      getPersonalizedNews('Nigerian Air Force'),
      getPersonalizedNews('Nigeria Police Force'),
      getPersonalizedNews('Nigeria Security and Civil Defence Corps'),
      getPersonalizedNews('Nigeria Immigration Service'),
      getPersonalizedNews('Nigeria Customs Service'),
      getPersonalizedNews('Federal Fire Service'),
      getPersonalizedNews('Nigeria Correctional Service'),
    ]);

    final flattenedNews = allNews.expand((news) => news).toList();
    return flattenedNews.where((news) => news.hasUpcomingDeadline).toList();
  }

  /// Search news by title, content, or tags
  Future<List<AgencyNewsModel>> searchNews(String query) async {
    if (query.isEmpty) return getAllNews();

    final allNews = await getAllNews();
    final lowercaseQuery = query.toLowerCase();

    return allNews.where((news) {
      return news.title.toLowerCase().contains(lowercaseQuery) ||
          news.content.toLowerCase().contains(lowercaseQuery) ||
          news.tags.any((tag) => tag.toLowerCase().contains(lowercaseQuery)) ||
          news.agency.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  /// Mark news as read
  Future<void> markAsRead(String newsId, String userId) async {
    // In a real implementation, this would update the read status in the database
    // For now, we'll just simulate the operation
    print('News $newsId marked as read for user $userId');
  }

  /// Get categories list
  List<String> getCategories() {
    return [
      'All',
      'Recruitment',
      'Training',
      'Interview',
      'Examination',
      'Requirement',
      'Deadline',
      'General',
      'Update',
      'Announcement',
    ];
  }

  /// Get agencies list
  List<String> getAgencies() {
    return [
      'All',
      'Nigerian Army',
      'Nigerian Navy',
      'Nigerian Air Force',
      'DSSC',
      'NDA',
      'NSCDC',
      'EFCC',
    ];
  }

  /// Subscribe to agency notifications (for push notifications)
  Future<void> subscribeToAgencyNotifications(String agency) async {
    // In a real app, this would register for push notifications
    print('Subscribed to notifications for $agency');
  }

  /// Get notification settings
  Future<Map<String, bool>> getNotificationSettings() async {
    // Return user's notification preferences
    return {
      'breakingNews': true,
      'deadlineReminders': true,
      'newRecruitment': true,
      'interviewSchedules': true,
      'requirementUpdates': true,
      'trainingPrograms': false,
    };
  }

  /// Update notification settings
  Future<void> updateNotificationSettings(Map<String, bool> settings) async {
    // In a real implementation, this would save to user preferences
    print('Notification settings updated: $settings');
  }

  /// Get agency-specific news - now supports live data fetching
  Future<List<AgencyNewsModel>> getPersonalizedNews(
    String? targetAgency,
  ) async {
    if (targetAgency == null) return getAllNews();

    List<AgencyNewsModel> agencyNews = [];

    // Strategy 1: Fetch live news if enabled
    if (_useLiveNews) {
      try {
        final liveNews = await _liveNewsService.fetchAgencyNews(targetAgency);
        agencyNews.addAll(liveNews);

        print('✅ Fetched ${liveNews.length} live news items for $targetAgency');
      } catch (e) {
        print('⚠️ Failed to fetch live news for $targetAgency: $e');
        print('📋 Falling back to mock data...');
      }
    }

    // Strategy 2: Add mock news (for testing or as fallback)
    if (_combineMockWithLive || agencyNews.isEmpty) {
      final allNews = await getAllNews();
      final mockNews =
          allNews.where((news) => news.agency == targetAgency).toList();
      agencyNews.addAll(mockNews);
    }

    // Remove duplicates and sort
    agencyNews = _removeDuplicateNews(agencyNews);

    // Sort by priority and date
    agencyNews.sort((a, b) {
      // Breaking news first
      if (a.isBreaking && !b.isBreaking) return -1;
      if (!a.isBreaking && b.isBreaking) return 1;

      // Pinned news second
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Priority third
      final priorityOrder = {'high': 0, 'medium': 1, 'low': 2};
      final aPriority = priorityOrder[a.priority] ?? 2;
      final bPriority = priorityOrder[b.priority] ?? 2;
      if (aPriority != bPriority) return aPriority.compareTo(bPriority);

      // Date last
      return b.publishedDate.compareTo(a.publishedDate);
    });

    return agencyNews;
  }

  /// Remove duplicate news items based on title similarity
  List<AgencyNewsModel> _removeDuplicateNews(List<AgencyNewsModel> news) {
    final unique = <AgencyNewsModel>[];
    final seenTitles = <String>{};

    for (final item in news) {
      final normalizedTitle =
          item.title.toLowerCase().replaceAll(RegExp(r'[^\w\s]'), '').trim();

      // Check if we've seen a similar title
      bool isDuplicate = false;
      for (final seenTitle in seenTitles) {
        if (_calculateTitleSimilarity(seenTitle, normalizedTitle) > 0.8) {
          isDuplicate = true;
          break;
        }
      }

      if (!isDuplicate) {
        seenTitles.add(normalizedTitle);
        unique.add(item);
      }
    }

    return unique;
  }

  /// Calculate similarity between two titles (0.0 to 1.0)
  double _calculateTitleSimilarity(String title1, String title2) {
    final words1 = title1.split(' ').where((w) => w.length > 3).toSet();
    final words2 = title2.split(' ').where((w) => w.length > 3).toSet();

    final intersection = words1.intersection(words2).length;
    final union = words1.union(words2).length;

    return union > 0 ? intersection / union : 0.0;
  }

  /// Manually refresh news for a specific agency
  Future<void> refreshAgencyNews(String agency) async {
    _liveNewsService.clearCache(agency);
    await getPersonalizedNews(agency);
  }

  /// Get news source information for an agency
  AgencyNewsSource? getNewsSource(String agency) {
    return NewsSourcesConfig.getAgencySource(agency);
  }

  /// Check if live news is available for an agency
  bool hasLiveNewsSupport(String agency) {
    final source = NewsSourcesConfig.getAgencySource(agency);
    return source != null && source.rssFeeds.isNotEmpty;
  }

  /// Get cache status for debugging
  Map<String, dynamic> getNewsStatus() {
    return {
      'live_news_enabled': _useLiveNews,
      'combine_with_mock': _combineMockWithLive,
      'supported_agencies': NewsSourcesConfig.getSupportedAgencies(),
      'cache_status': _liveNewsService.getCacheStatus(),
    };
  }
}
