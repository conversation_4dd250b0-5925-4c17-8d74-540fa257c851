import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';

/// A responsive text widget that adapts to different screen sizes
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double? scaleFactor;
  final bool softWrap;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
    this.scaleFactor,
    this.softWrap = true,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    
    // Calculate responsive font size
    double fontSize = style?.fontSize ?? 14.0;
    
    if (isSmallScreen) {
      fontSize *= 0.9; // Reduce font size for small screens
    } else if (isTablet) {
      fontSize *= 1.1; // Increase font size for tablets
    }
    
    // Apply custom scale factor if provided
    if (scaleFactor != null) {
      fontSize *= scaleFactor!;
    }
    
    return Text(
      text,
      style: style?.copyWith(fontSize: fontSize) ?? TextStyle(fontSize: fontSize),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
    );
  }
}

/// A responsive heading widget
class ResponsiveHeading extends StatelessWidget {
  final String text;
  final int level; // 1-6, like HTML headings
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;

  const ResponsiveHeading(
    this.text, {
    super.key,
    this.level = 1,
    this.color,
    this.fontWeight,
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    final isSmallScreen = ResponsiveUtils.isSmallPhone(context);
    final isTablet = ResponsiveUtils.isTablet(context);
    
    // Base font sizes for different heading levels
    final baseFontSizes = {
      1: 32.0,
      2: 28.0,
      3: 24.0,
      4: 20.0,
      5: 18.0,
      6: 16.0,
    };
    
    double fontSize = baseFontSizes[level.clamp(1, 6)] ?? 16.0;
    
    if (isSmallScreen) {
      fontSize *= 0.85;
    } else if (isTablet) {
      fontSize *= 1.15;
    }
    
    return Text(
      text,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: fontWeight ?? FontWeight.bold,
        color: color,
      ),
      textAlign: textAlign,
    );
  }
}

/// A responsive body text widget
class ResponsiveBodyText extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveBodyText(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      style: TextStyle(
        fontSize: 16.0,
        color: color ?? Colors.black87,
        height: 1.5,
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

/// A responsive caption text widget
class ResponsiveCaption extends StatelessWidget {
  final String text;
  final Color? color;
  final TextAlign? textAlign;

  const ResponsiveCaption(
    this.text, {
    super.key,
    this.color,
    this.textAlign,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveText(
      text,
      style: TextStyle(
        fontSize: 12.0,
        color: color ?? Colors.grey[600],
      ),
      textAlign: textAlign,
    );
  }
}
