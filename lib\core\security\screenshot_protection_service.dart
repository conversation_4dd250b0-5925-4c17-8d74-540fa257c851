import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service for protecting app content from screenshots and screen recording
class ScreenshotProtectionService {
  static const MethodChannel _channel = MethodChannel('screenshot_protection');
  
  static ScreenshotProtectionService? _instance;
  static ScreenshotProtectionService get instance => _instance ??= ScreenshotProtectionService._();
  
  ScreenshotProtectionService._();
  
  bool _isProtectionEnabled = false;
  
  /// Enable screenshot protection for the current screen
  Future<void> enableProtection() async {
    if (_isProtectionEnabled) return;
    
    try {
      await _channel.invokeMethod('enableProtection');
      _isProtectionEnabled = true;
      print('🔒 Screenshot protection enabled');
    } catch (e) {
      print('⚠️ Failed to enable screenshot protection: $e');
      // Fallback to Flutter-level protection
      _enableFlutterProtection();
    }
  }
  
  /// Disable screenshot protection
  Future<void> disableProtection() async {
    if (!_isProtectionEnabled) return;
    
    try {
      await _channel.invokeMethod('disableProtection');
      _isProtectionEnabled = false;
      print('🔓 Screenshot protection disabled');
    } catch (e) {
      print('⚠️ Failed to disable screenshot protection: $e');
      _disableFlutterProtection();
    }
  }
  
  /// Enable Flutter-level protection (fallback)
  void _enableFlutterProtection() {
    // This will make the app content appear blank in recent apps
    SystemChrome.setApplicationSwitcherDescription(
      const ApplicationSwitcherDescription(
        label: 'Fit4Force',
        primaryColor: 0xFF000000,
      ),
    );
    _isProtectionEnabled = true;
  }
  
  /// Disable Flutter-level protection
  void _disableFlutterProtection() {
    SystemChrome.setApplicationSwitcherDescription(
      const ApplicationSwitcherDescription(
        label: 'Fit4Force',
        primaryColor: 0xFF1976D2,
      ),
    );
    _isProtectionEnabled = false;
  }
  
  /// Check if protection is currently enabled
  bool get isProtectionEnabled => _isProtectionEnabled;
  
  /// Show warning dialog when screenshot is attempted
  void showScreenshotWarning(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.red),
            SizedBox(width: 8),
            Text('Content Protected'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.screenshot_outlined,
              size: 64,
              color: Colors.red.withValues(alpha: 0.7 * 255),
            ),
            SizedBox(height: 16),
            Text(
              'Screenshots and screen recording are not allowed for this content.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 12),
            Text(
              'This protects our educational content and maintains the value of our premium features.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('I Understand'),
          ),
        ],
      ),
    );
  }
  
  /// Show download disabled message
  void showDownloadDisabledMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.block, color: Colors.white),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                'Downloads are disabled to protect our content',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Widget that automatically enables screenshot protection
class ProtectedScreen extends StatefulWidget {
  final Widget child;
  final bool enableProtection;
  final String? screenName;
  
  const ProtectedScreen({
    super.key,
    required this.child,
    this.enableProtection = true,
    this.screenName,
  });
  
  @override
  State<ProtectedScreen> createState() => _ProtectedScreenState();
}

class _ProtectedScreenState extends State<ProtectedScreen>
    with WidgetsBindingObserver {
  final ScreenshotProtectionService _protectionService = 
      ScreenshotProtectionService.instance;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    if (widget.enableProtection) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _protectionService.enableProtection();
      });
    }
  }
  
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (widget.enableProtection) {
      _protectionService.disableProtection();
    }
    super.dispose();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    if (widget.enableProtection) {
      switch (state) {
        case AppLifecycleState.paused:
        case AppLifecycleState.inactive:
          // App is going to background - ensure protection is active
          _protectionService.enableProtection();
          break;
        case AppLifecycleState.resumed:
          // App is back in foreground
          _protectionService.enableProtection();
          break;
        case AppLifecycleState.detached:
          _protectionService.disableProtection();
          break;
        case AppLifecycleState.hidden:
          _protectionService.enableProtection();
          break;
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (!widget.enableProtection) {
      return widget.child;
    }
    
    return Stack(
      children: [
        widget.child,
        // Invisible overlay to detect screenshot gestures
        Positioned.fill(
          child: GestureDetector(
            onLongPress: () {
              // Detect potential screenshot gesture
              _protectionService.showScreenshotWarning(context);
            },
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
      ],
    );
  }
}

/// Mixin for screens that need screenshot protection
mixin ScreenshotProtectionMixin<T extends StatefulWidget> on State<T> {
  final ScreenshotProtectionService _protectionService = 
      ScreenshotProtectionService.instance;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _protectionService.enableProtection();
    });
  }
  
  @override
  void dispose() {
    _protectionService.disableProtection();
    super.dispose();
  }
  
  void showScreenshotWarning() {
    _protectionService.showScreenshotWarning(context);
  }
  
  void showDownloadDisabledMessage() {
    _protectionService.showDownloadDisabledMessage(context);
  }
}
