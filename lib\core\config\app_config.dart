class AppConfig {
  // App Information
  static const String appName = 'FIT4FORCE';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const String baseUrl =
      'https://api.fit4force.com'; // Production API URL
  static const String devBaseUrl =
      'https://dev-api.fit4force.com'; // Development API URL
  static const String stagingBaseUrl =
      'https://staging-api.fit4force.com'; // Staging API URL
  static const int apiTimeout = 30000; // 30 seconds
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-App-Version': appVersion,
  };

  // No Firebase Configuration

  // Payment Configuration
  static const double premiumSubscriptionPrice = 2500.0; // ₦2,500 NGN per month
  static const String paystackPublicKey =
      'pk_live_7c08b7b3f540f90e1b5729f88ae963cabc6079b1'; // Live Paystack public key
  static const String paystackSecretKey =
      '************************************************'; // Live Paystack secret key

  // Feature Flags
  static const bool enableAds = true;
  static const bool enablePushNotifications = true;
  static const bool enableOfflineMode = true;

  // Cache Configuration
  static const int cacheDuration = 7; // days
  static const int maxCacheSize = 100 * 1024 * 1024; // 100 MB

  // Quiz Configuration
  static const int defaultQuizTimeLimit = 30; // minutes
  static const int questionsPerQuiz = 50;
  static const int passingScore = 70; // percentage

  // Fitness Configuration
  static const int defaultWorkoutDuration = 45; // minutes
  static const int maxWorkoutDuration = 120; // minutes
  static const int minRestBetweenExercises = 30; // seconds

  // Community Configuration
  static const int maxPostLength = 1000;
  static const int maxCommentLength = 500;
  static const int postsPerPage = 20;

  // Supported Agencies
  static const List<String> supportedAgencies = [
    'Nigerian Army',
    'Navy',
    'Air Force',
    'DSSC',
    'NDA',
    'NSCDC',
    'EFCC',
    'Fire Service',
    'Immigration',
    'Customs',
    'FRSC',
    'Police (POLAC)',
  ];

  // User Profile Requirements
  static const List<String> requiredProfileFields = [
    'fullName',
    'email',
    'age',
    'gender',
    'height',
    'weight',
    'targetAgency',
    'fitnessGoal',
    'notificationPreferences',
  ];

  // Premium Features
  static const List<String> premiumFeatures = [
    'Access to all AI-powered quizzes',
    'Create and engage in community posts',
    'Comment on community posts',
    'Access to agency-specific tools',
    'Priority support',
    'Ad-free experience',
    'Unlimited quiz attempts',
    'Detailed performance analytics',
    'Download study materials',
    'Access to premium study guides',
  ];
}
