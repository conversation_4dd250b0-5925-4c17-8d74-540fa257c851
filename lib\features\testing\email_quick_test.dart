import 'package:fit_4_force/shared/services/email_service.dart';

/// Simple email test that you can run from anywhere
class EmailQuickTest {
  static Future<void> testWelcomeEmail() async {
    print('🧪 Testing Welcome Email...');

    final success = await EmailService.sendWelcomeEmail(
      userEmail: '<EMAIL>', // Replace with your test email
      fullName: 'John Test User',
    );

    if (success) {
      print('✅ Welcome email sent successfully!');
    } else {
      print('❌ Failed to send welcome email');
    }
  }

  static Future<void> testPremiumEmail() async {
    print('🧪 Testing Premium Email...');

    final success = await EmailService.sendPremiumUpgradeEmail(
      userEmail: '<EMAIL>', // Replace with your test email
      fullName: 'John Premium User',
      expiryDate: DateTime.now().add(const Duration(days: 30)),
      isYearly: false,
      transactionReference: 'TEST_123456',
    );

    if (success) {
      print('✅ Premium email sent successfully!');
    } else {
      print('❌ Failed to send premium email');
    }
  }

  static Future<void> runAllTests() async {
    print('🚀 Running Email Tests...\n');
    await testWelcomeEmail();
    await Future.delayed(const Duration(seconds: 2));
    await testPremiumEmail();
    print('\n✨ Email tests completed!');
  }
}
