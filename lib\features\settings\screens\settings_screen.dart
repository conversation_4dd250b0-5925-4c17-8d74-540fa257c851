import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/core/config/app_routes.dart';
import 'package:fit_4_force/core/theme/app_theme.dart';
import 'package:fit_4_force/core/utils/responsive_utils.dart';
import 'package:fit_4_force/core/widgets/responsive_widgets.dart';
import 'package:fit_4_force/core/utils/legal_documents_helper.dart';
import 'package:fit_4_force/core/services/user_progress_service.dart';
import 'package:fit_4_force/core/widgets/user_progress_widgets.dart';
import 'package:fit_4_force/features/auth/bloc/auth_bloc_fixed.dart';
import 'package:fit_4_force/shared/models/user_model.dart';
import '../models/setting_model.dart';
import '../services/settings_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SettingsScreen extends StatefulWidget {
  final UserModel user;

  const SettingsScreen({super.key, required this.user});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsService _settingsService = SettingsService.instance;
  late UserPreferences _preferences;
  bool _notificationsEnabled = true;
  final bool _darkModeEnabled = false;
  String _appVersion = '';
  final UserProgressService _progressService = UserProgressService();
  Map<String, dynamic> _userProgress = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _preferences = _settingsService.userPreferences;
    _loadAppInfo();
    _loadUserSettings();
    _loadUserProgress();
  }

  Future<void> _loadUserProgress() async {
    try {
      final progress = await _progressService.loadUserProgress();
      setState(() {
        _userProgress = progress;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadAppInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = packageInfo.version;
      });
    } catch (e) {
      setState(() {
        _appVersion = AppConfig.appVersion;
      });
    }
  }

  void _loadUserSettings() {
    final authState = context.read<AuthBloc>().state;
    if (authState is Authenticated) {
      final user = authState.user;
      setState(() {
        _notificationsEnabled = user.notificationPreferences['general'] ?? true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDesktop = ResponsiveUtils.isDesktop(context);
    final padding = ResponsiveUtils.getResponsivePadding(context);

    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          if (state is Authenticated) {
            final user = state.user;

            return SingleChildScrollView(
              padding: padding,
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: isDesktop ? 600 : double.infinity,
                  ),
                  child: Column(
                    children: [
                      // Account settings
                      _buildSectionHeader('Account'),
                      ResponsiveCard(
                        child: Column(
                          children: [
                            ListTile(
                              leading: const Icon(Icons.person),
                              title: const Text('Edit Profile'),
                              onTap: () {
                                Navigator.of(
                                  context,
                                ).pushNamed(AppRoutes.profile);
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.email),
                              title: const Text('Change Email'),
                              onTap: () {
                                // Simplified - just show a message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Feature coming soon!'),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),

                      // Notification settings
                      _buildSectionHeader('Notifications'),
                      ResponsiveCard(
                        child: SwitchListTile(
                          title: const Text('Push Notifications'),
                          subtitle: const Text(
                            'Receive notifications for updates and reminders',
                          ),
                          value: _notificationsEnabled,
                          onChanged: (value) {
                            setState(() {
                              _notificationsEnabled = value;
                            });
                          },
                        ),
                      ),

                      // Subscription settings
                      _buildSectionHeader('Subscription'),
                      ResponsiveCard(
                        child: ListTile(
                          leading: Icon(
                            Icons.workspace_premium,
                            color:
                                user.isPremium ? AppTheme.premiumColor : null,
                          ),
                          title: Text(
                            user.isPremium
                                ? 'Manage Premium'
                                : 'Upgrade to Premium',
                            style: TextStyle(
                              color:
                                  user.isPremium
                                      ? AppTheme.premiumDarkColor
                                      : null,
                              fontWeight:
                                  user.isPremium ? FontWeight.bold : null,
                            ),
                          ),
                          subtitle: Text(
                            user.isPremium
                                ? 'Your subscription is active'
                                : 'Unlock all premium features',
                          ),
                          onTap: () {
                            Navigator.of(context).pushNamed(AppRoutes.premium);
                          },
                        ),
                      ),

                      // Usage Statistics
                      _buildSectionHeader('Usage Statistics'),
                      _isLoading
                          ? const ResponsiveCard(
                            child: CircularProgressIndicator(),
                          )
                          : _buildUsageStatistics(),

                      // App information
                      _buildSectionHeader('About'),
                      ResponsiveCard(
                        child: Column(
                          children: [
                            ListTile(
                              leading: const Icon(Icons.info_outline),
                              title: const Text('App Version'),
                              subtitle: Text(_appVersion),
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.description_outlined),
                              title: const Text('Terms of Service'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchTermsOfService();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Terms of Service',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.privacy_tip_outlined),
                              title: const Text('Privacy Policy'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchPrivacyPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Privacy Policy',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.warning_outlined),
                              title: const Text('Disclaimer'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchDisclaimer();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Disclaimer',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.group_outlined),
                              title: const Text('Community Guidelines'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchCommunityPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Community Guidelines',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                            const Divider(),
                            ListTile(
                              leading: const Icon(Icons.money_off_outlined),
                              title: const Text('Refund Policy'),
                              onTap: () async {
                                final success =
                                    await LegalDocumentsHelper.launchRefundPolicy();
                                if (!success && mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text(
                                        'Unable to open Refund Policy',
                                      ),
                                    ),
                                  );
                                }
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),
                      // Sign out button
                      ResponsiveButton(
                        text: 'Sign Out',
                        backgroundColor: Colors.red,
                        textColor: Colors.white,
                        onPressed: () {
                          context.read<AuthBloc>().add(SignOutEvent());
                        },
                        mobileHeight: 48.0,
                        tabletHeight: 52.0,
                        desktopHeight: 56.0,
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ),
            );
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Align(
        alignment: Alignment.centerLeft,
        child: ResponsiveText(
          title,
          mobileFontSize: 14.0,
          tabletFontSize: 15.0,
          desktopFontSize: 16.0,
          fontWeight: FontWeight.bold,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Widget _buildUsageStatistics() {
    final overallProgress = _userProgress['overall'] ?? {};
    final profileProgress = _userProgress['profile'] ?? {};
    final fitnessProgress = _userProgress['fitness'] ?? {};
    final academicsProgress = _userProgress['academics'] ?? {};
    final communityProgress = _userProgress['community'] ?? {};

    final totalPoints = overallProgress['totalPoints'] ?? 0;
    final currentLevel = overallProgress['level'] ?? 1;
    final totalAchievements = overallProgress['totalAchievements'] ?? 0;
    final timeInApp = profileProgress['totalTimeInApp'] ?? 0;
    final joinDate =
        profileProgress['joinDate'] ?? DateTime.now().toIso8601String();

    // Calculate days since joining
    final joinDateTime = DateTime.tryParse(joinDate) ?? DateTime.now();
    final daysSinceJoining = DateTime.now().difference(joinDateTime).inDays;

    return ResponsiveCard(
      child: Column(
        children: [
          // Overall Progress Stats
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Your Fit4Force Journey',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),

                // Key stats in a grid
                Row(
                  children: [
                    Expanded(
                      child: ProgressStatsCard(
                        title: 'Level',
                        value: currentLevel,
                        icon: Icons.trending_up,
                        color: Colors.purple,
                        emptyStateMessage: 'Start using the app!',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ProgressStatsCard(
                        title: 'Points',
                        value: totalPoints,
                        icon: Icons.stars,
                        color: Colors.amber,
                        emptyStateMessage: 'Earn your first points!',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                Row(
                  children: [
                    Expanded(
                      child: ProgressStatsCard(
                        title: 'Achievements',
                        value: totalAchievements,
                        icon: Icons.emoji_events,
                        color: Colors.orange,
                        emptyStateMessage: 'Unlock achievements!',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ProgressStatsCard(
                        title: 'Days Active',
                        value: daysSinceJoining,
                        icon: Icons.calendar_today,
                        color: Colors.green,
                        emptyStateMessage: 'Welcome!',
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const Divider(),

          // Activity breakdown
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Activity Breakdown',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),

                _buildStatRow(
                  'Workouts Completed',
                  fitnessProgress['totalWorkouts'] ?? 0,
                  Icons.fitness_center,
                ),
                _buildStatRow(
                  'Quizzes Taken',
                  academicsProgress['totalQuizzesCompleted'] ?? 0,
                  Icons.quiz,
                ),
                _buildStatRow(
                  'Materials Read',
                  academicsProgress['materialsRead'] ?? 0,
                  Icons.book,
                ),
                _buildStatRow(
                  'Posts Created',
                  communityProgress['postsCreated'] ?? 0,
                  Icons.post_add,
                ),
                _buildStatRow(
                  'Study Hours',
                  '${(academicsProgress['totalStudyHours'] ?? 0.0).toStringAsFixed(1)}h',
                  Icons.schedule,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, dynamic value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(child: Text(label, style: const TextStyle(fontSize: 14))),
          Text(
            value.toString(),
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
        ],
      ),
    );
  }

  // =================================================================
  // SETTING DEFINITIONS
  // =================================================================

  List<SettingModel> _getAccountSettings() {
    return [
      SettingModel(
        id: 'edit_profile',
        title: 'Edit Profile',
        subtitle: 'Update your personal information',
        icon: Icons.person,
        type: SettingType.navigation,
        onTap: () {
          Navigator.of(context).pushNamed(AppRoutes.profile);
        },
      ),
      SettingModel(
        id: 'change_email',
        title: 'Change Email',
        subtitle:
            widget.user.email.isNotEmpty
                ? widget.user.email
                : '<EMAIL>',
        icon: Icons.email,
        type: SettingType.navigation,
        onTap: () {
          _showChangeEmailDialog();
        },
      ),
      SettingModel(
        id: 'change_password',
        title: 'Change Password',
        subtitle: 'Update your account password',
        icon: Icons.lock,
        type: SettingType.navigation,
        onTap: () {
          _showChangePasswordDialog();
        },
      ),
      SettingModel(
        id: 'subscription',
        title: widget.user.isPremium ? 'Manage Premium' : 'Upgrade to Premium',
        subtitle:
            widget.user.isPremium
                ? 'Your subscription is active'
                : 'Unlock all premium features',
        icon: Icons.workspace_premium,
        iconColor: widget.user.isPremium ? Colors.amber : null,
        type: SettingType.navigation,
        badge: widget.user.isPremium ? null : 'NEW',
        onTap: () {
          Navigator.of(context).pushNamed(AppRoutes.premium);
        },
      ),
    ];
  }

  List<SettingModel> _getPreferenceSettings() {
    return [
      SettingModel(
        id: 'notifications',
        title: 'Notifications',
        subtitle: 'Manage your notification preferences',
        icon: Icons.notifications,
        type: SettingType.navigation,
        onTap: () {
          _showNotificationSettings();
        },
      ),
      SettingModel(
        id: 'appearance',
        title: 'Appearance',
        subtitle: 'Theme, font size, and display settings',
        icon: Icons.palette,
        type: SettingType.navigation,
        onTap: () {
          _showAppearanceSettings();
        },
      ),
      SettingModel(
        id: 'language',
        title: 'Language',
        subtitle: _preferences.language,
        icon: Icons.language,
        type: SettingType.selection,
        value: _preferences.language,
        options: ['English', 'Hausa', 'Yoruba', 'Igbo'],
        onTap: () {
          _showLanguageSelection();
        },
      ),
      SettingModel(
        id: 'sound_effects',
        title: 'Sound Effects',
        subtitle: 'Play sounds for app interactions',
        icon: Icons.volume_up,
        type: SettingType.toggle,
        value: _preferences.soundEffects,
        onChanged: (value) {
          _updatePreference('soundEffects', value);
          if (value) {
            HapticFeedback.lightImpact();
          }
        },
      ),
      SettingModel(
        id: 'haptic_feedback',
        title: 'Haptic Feedback',
        subtitle: 'Vibrate for app interactions',
        icon: Icons.vibration,
        type: SettingType.toggle,
        value: _preferences.hapticFeedback,
        onChanged: (value) {
          _updatePreference('hapticFeedback', value);
          if (value) {
            HapticFeedback.mediumImpact();
          }
        },
      ),
    ];
  }

  List<SettingModel> _getPrivacySettings() {
    return [
      SettingModel(
        id: 'privacy_security',
        title: 'Privacy & Security',
        subtitle: 'Manage your privacy and security settings',
        icon: Icons.security,
        type: SettingType.navigation,
        onTap: () {
          _showPrivacySettings();
        },
      ),
      SettingModel(
        id: 'biometric_auth',
        title: 'Biometric Authentication',
        subtitle: 'Use fingerprint or face unlock',
        icon: Icons.fingerprint,
        type: SettingType.toggle,
        value: _preferences.biometricEnabled,
        onChanged: (value) {
          _updatePreference('biometricEnabled', value);
        },
      ),
      SettingModel(
        id: 'analytics',
        title: 'Analytics',
        subtitle: 'Help improve the app with usage data',
        icon: Icons.analytics,
        type: SettingType.toggle,
        value: _preferences.analyticsEnabled,
        onChanged: (value) {
          _updatePreference('analyticsEnabled', value);
        },
      ),
      SettingModel(
        id: 'crash_reporting',
        title: 'Crash Reporting',
        subtitle: 'Automatically send crash reports',
        icon: Icons.bug_report,
        type: SettingType.toggle,
        value: _preferences.crashReportingEnabled,
        onChanged: (value) {
          _updatePreference('crashReportingEnabled', value);
        },
      ),
    ];
  }

  List<SettingModel> _getDataSettings() {
    return [
      SettingModel(
        id: 'data_storage',
        title: 'Data & Storage',
        subtitle: 'Manage app data and storage usage',
        icon: Icons.storage,
        type: SettingType.navigation,
        onTap: () {
          _showDataStorageSettings();
        },
      ),
      SettingModel(
        id: 'auto_backup',
        title: 'Auto Backup',
        subtitle: 'Automatically backup your progress',
        icon: Icons.backup,
        type: SettingType.toggle,
        value: _preferences.autoBackup,
        onChanged: (value) {
          _updatePreference('autoBackup', value);
        },
      ),
      SettingModel(
        id: 'wifi_only_download',
        title: 'WiFi Only Downloads',
        subtitle: 'Download content only on WiFi',
        icon: Icons.wifi,
        type: SettingType.toggle,
        value: _preferences.wifiOnlyDownload,
        onChanged: (value) {
          _updatePreference('wifiOnlyDownload', value);
        },
      ),
      SettingModel(
        id: 'data_optimization',
        title: 'Data Usage Optimization',
        subtitle: 'Reduce data usage for mobile connections',
        icon: Icons.data_usage,
        type: SettingType.toggle,
        value: _preferences.dataUsageOptimization,
        onChanged: (value) {
          _updatePreference('dataUsageOptimization', value);
        },
      ),
    ];
  }

  List<SettingModel> _getSupportSettings() {
    return [
      SettingModel(
        id: 'help_support',
        title: 'Help & Support',
        subtitle: 'Get help and contact support',
        icon: Icons.help,
        type: SettingType.navigation,
        onTap: () {
          _showHelpSupport();
        },
      ),
      SettingModel(
        id: 'send_feedback',
        title: 'Send Feedback',
        subtitle: 'Share your thoughts and suggestions',
        icon: Icons.feedback,
        type: SettingType.navigation,
        onTap: () {
          _showFeedbackDialog();
        },
      ),
      SettingModel(
        id: 'report_bug',
        title: 'Report a Bug',
        subtitle: 'Help us fix issues you encounter',
        icon: Icons.bug_report,
        iconColor: Colors.red,
        type: SettingType.navigation,
        onTap: () {
          _showBugReportDialog();
        },
      ),
      SettingModel(
        id: 'rate_app',
        title: 'Rate the App',
        subtitle: 'Rate us on the app store',
        icon: Icons.star,
        iconColor: Colors.amber,
        type: SettingType.navigation,
        onTap: () {
          _showRateAppDialog();
        },
      ),
    ];
  }

  List<SettingModel> _getAboutSettings() {
    return [
      SettingModel(
        id: 'about',
        title: 'About Fit4Force',
        subtitle: 'App information and credits',
        icon: Icons.info,
        type: SettingType.navigation,
        onTap: () {
          _showAboutDialog();
        },
      ),
      SettingModel(
        id: 'app_version',
        title: 'App Version',
        subtitle: _settingsService.appVersion,
        icon: Icons.info_outline,
        type: SettingType.navigation,
        onTap: () {
          _showAppInfoDialog();
        },
      ),
      SettingModel(
        id: 'terms_service',
        title: 'Terms of Service',
        subtitle: 'Read our terms and conditions',
        icon: Icons.description,
        type: SettingType.navigation,
        onTap: () async {
          final success = await LegalDocumentsHelper.launchTermsOfService();
          if (!success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Unable to open Terms of Service')),
            );
          }
        },
      ),
      SettingModel(
        id: 'privacy_policy',
        title: 'Privacy Policy',
        subtitle: 'Read our privacy policy',
        icon: Icons.privacy_tip,
        type: SettingType.navigation,
        onTap: () async {
          final success = await LegalDocumentsHelper.launchPrivacyPolicy();
          if (!success && mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Unable to open Privacy Policy')),
            );
          }
        },
      ),
      SettingModel(
        id: 'sign_out',
        title: 'Sign Out',
        subtitle: 'Sign out of your account',
        icon: Icons.logout,
        iconColor: Colors.red,
        type: SettingType.action,
        showDivider: false,
        onTap: () {
          _showSignOutDialog();
        },
      ),
    ];
  }

  // =================================================================
  // HELPER METHODS
  // =================================================================

  Future<void> _updatePreference(String key, dynamic value) async {
    await _settingsService.updatePreference(key, value);
    setState(() {
      _preferences = _settingsService.userPreferences;
    });
  }

  void _showChangeEmailDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Email'),
            content: const Text(
              'Email change functionality will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Change Password'),
            content: const Text(
              'Password change functionality will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Notification Settings'),
            content: const Text(
              'Detailed notification settings will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showAppearanceSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Appearance Settings'),
            content: const Text(
              'Theme and appearance settings will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showLanguageSelection() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Select Language'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  ['English', 'Hausa', 'Yoruba', 'Igbo'].map((language) {
                    return RadioListTile<String>(
                      title: Text(language),
                      value: language,
                      groupValue: _preferences.language,
                      onChanged: (value) {
                        if (value != null) {
                          _updatePreference('language', value);
                          Navigator.pop(context);
                        }
                      },
                    );
                  }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
            ],
          ),
    );
  }

  void _showPrivacySettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Privacy & Security'),
            content: const Text(
              'Detailed privacy and security settings will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showDataStorageSettings() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Data & Storage'),
            content: const Text(
              'Storage management features will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showHelpSupport() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Help & Support'),
            content: const Text(
              'Help and support features will be available soon.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  void _showFeedbackDialog() {
    final TextEditingController feedbackController = TextEditingController();
    final TextEditingController emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Send Feedback'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email (optional)',
                    hintText: '<EMAIL>',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: feedbackController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    labelText: 'Your Feedback',
                    hintText: 'Tell us what you think...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (feedbackController.text.isNotEmpty) {
                    final success = await _settingsService.sendFeedback(
                      feedbackController.text,
                      emailController.text,
                    );
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success
                              ? 'Feedback sent successfully!'
                              : 'Failed to send feedback',
                        ),
                        backgroundColor: success ? Colors.green : Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('Send'),
              ),
            ],
          ),
    );
  }

  void _showBugReportDialog() {
    final TextEditingController descriptionController = TextEditingController();
    final TextEditingController emailController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Report a Bug'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: emailController,
                  decoration: const InputDecoration(
                    labelText: 'Email (optional)',
                    hintText: '<EMAIL>',
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: descriptionController,
                  maxLines: 4,
                  decoration: const InputDecoration(
                    labelText: 'Bug Description',
                    hintText: 'Describe the issue you encountered...',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  if (descriptionController.text.isNotEmpty) {
                    final success = await _settingsService.reportBug(
                      descriptionController.text,
                      emailController.text,
                      [],
                    );
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success
                              ? 'Bug report sent successfully!'
                              : 'Failed to send bug report',
                        ),
                        backgroundColor: success ? Colors.green : Colors.red,
                      ),
                    );
                  }
                },
                child: const Text('Send Report'),
              ),
            ],
          ),
    );
  }

  void _showRateAppDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Rate Fit4Force'),
            content: const Text(
              'Would you like to rate our app on the app store?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Later'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // In a real app, this would open the app store
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your support!'),
                    ),
                  );
                },
                child: const Text('Rate Now'),
              ),
            ],
          ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('About Fit4Force'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Fit4Force',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text('Version ${_settingsService.appVersion}'),
                const SizedBox(height: 16),
                const Text(
                  'Your comprehensive companion for Nigerian military and paramilitary recruitment preparation.',
                ),
                const SizedBox(height: 16),
                const Text(
                  'Developed by Nehemiah Technologies',
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 8),
                const Text('Email: <EMAIL>'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  void _showAppInfoDialog() {
    final deviceInfo = _settingsService.getDeviceInfo();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('App Information'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow('App Version', _settingsService.appVersion),
                _buildInfoRow('Build Number', _settingsService.buildNumber),
                const Divider(),
                ...deviceInfo.entries.map(
                  (entry) => _buildInfoRow(entry.key, entry.value),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sign Out'),
            content: const Text('Are you sure you want to sign out?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  context.read<AuthBloc>().add(SignOutEvent());
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text(
                  'Sign Out',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }
}
