import 'package:flutter/material.dart';
import 'package:fit_4_force/core/config/app_config.dart';
import 'package:fit_4_force/shared/services/auth_service.dart';
import 'package:fit_4_force/shared/services/base_service.dart';
import 'package:fit_4_force/shared/services/email_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:dio/dio.dart';
import 'dart:math';

/// A real implementation of the Paystack service for payment processing using HTTP API
class PaystackService extends BaseService {
  final AuthService _authService = AuthService();
  final Dio _dio = Dio();
  static const String _baseUrl = 'https://api.paystack.co';

  @override
  String get collectionName => 'transactions';

  PaystackService() {
    _setupDio();
  }

  /// Setup Dio with default configuration
  void _setupDio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.headers = {
      'Authorization': 'Bearer ${AppConfig.paystackSecretKey}',
      'Content-Type': 'application/json',
    };
  }

  /// Generate a secure payment reference
  String _generateReference() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = Random().nextInt(999999).toString().padLeft(6, '0');
    return 'FIT4FORCE_${timestamp}_$random';
  }

  /// Initialize a payment transaction
  Future<Map<String, dynamic>?> initializeTransaction({
    required String email,
    required int amount, // Amount in kobo
    required String reference,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _dio.post(
        '/transaction/initialize',
        data: {
          'email': email,
          'amount': amount,
          'reference': reference,
          'currency': 'NGN',
          'metadata': metadata ?? {},
          'callback_url':
              'https://fit4force.app/payment/callback', // Your callback URL
        },
      );

      if (response.statusCode == 200 && response.data['status'] == true) {
        return response.data['data'];
      }
      return null;
    } catch (e) {
      debugPrint('Error initializing transaction: $e');
      return null;
    }
  }

  /// Verify a payment transaction
  Future<Map<String, dynamic>?> verifyTransaction(String reference) async {
    try {
      final response = await _dio.get('/transaction/verify/$reference');

      if (response.statusCode == 200 && response.data['status'] == true) {
        return response.data['data'];
      }
      return null;
    } catch (e) {
      debugPrint('Error verifying transaction: $e');
      return null;
    }
  }

  /// Handle a successful payment
  Future<void> _handleSuccessfulPayment(
    String reference, {
    bool isYearly = false,
  }) async {
    if (currentUserId == null) return;

    try {
      final expiryDate = DateTime.now().add(
        Duration(days: isYearly ? 365 : 30),
      );

      // Update user's premium status
      await _authService.updatePremiumStatus(currentUserId!, true, expiryDate);

      // Create a subscription record in Supabase
      await Supabase.instance.client.from('subscriptions').insert({
        'user_id': currentUserId,
        'reference': reference,
        'amount':
            isYearly
                ? AppConfig.premiumSubscriptionPrice *
                    10 // 10 months for yearly (2 months free)
                : AppConfig.premiumSubscriptionPrice,
        'start_date': DateTime.now().toIso8601String(),
        'expiry_date': expiryDate.toIso8601String(),
        'status': 'active',
        'plan': isYearly ? 'yearly' : 'monthly',
        'created_at': DateTime.now().toIso8601String(),
      });

      // Get user details for email
      try {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          // Send premium upgrade confirmation email
          await EmailService.sendPremiumUpgradeEmail(
            userEmail: user.email,
            fullName: user.fullName,
            expiryDate: expiryDate,
            isYearly: isYearly,
            transactionReference: reference,
          );
          debugPrint('✅ Premium upgrade email sent to ${user.email}');
        }
      } catch (e) {
        debugPrint('⚠️ Failed to send premium upgrade email: $e');
        // Don't fail the payment process if email fails
      }
    } catch (e) {
      debugPrint('Error processing successful payment: $e');
    }
  }

  /// Verify a transaction (legacy method for compatibility)
  Future<bool> verifyTransactionStatus(String reference) async {
    try {
      final response =
          await Supabase.instance.client
              .from(collectionName)
              .select('id')
              .eq('reference', reference)
              .limit(1)
              .maybeSingle();
      return response != null;
    } catch (e) {
      debugPrint('Error verifying transaction: $e');
      return false;
    }
  }

  /// Process a payment using Paystack web checkout
  ///
  /// This method initializes a transaction and returns the authorization URL
  /// for web-based payment processing, which is more reliable than the plugin
  Future<Map<String, dynamic>> processPayment({
    required String email,
    required String fullName,
    required double amount,
    bool isYearly = false,
  }) async {
    try {
      // Generate a unique reference for this transaction
      final reference = _generateReference();

      // Convert amount to kobo (Paystack uses kobo for NGN)
      final amountInKobo = (amount * 100).toInt();

      // Initialize the transaction
      final transactionData = await initializeTransaction(
        email: email,
        amount: amountInKobo,
        reference: reference,
        metadata: {
          'user_name': fullName,
          'subscription_type': isYearly ? 'yearly' : 'monthly',
          'app': 'fit4force',
        },
      );

      if (transactionData != null) {
        return {
          'status': 'success',
          'reference': reference,
          'authorization_url': transactionData['authorization_url'],
          'access_code': transactionData['access_code'],
        };
      } else {
        return {'status': 'error', 'message': 'Failed to initialize payment'};
      }
    } catch (e) {
      debugPrint('Paystack error: $e');
      return {'status': 'error', 'message': 'An error occurred: $e'};
    }
  }

  /// Complete payment processing after successful payment
  Future<bool> completePayment({
    required String reference,
    bool isYearly = false,
  }) async {
    try {
      // Verify the transaction
      final transactionData = await verifyTransaction(reference);

      if (transactionData != null && transactionData['status'] == 'success') {
        // Payment was successful
        await _handleSuccessfulPayment(reference, isYearly: isYearly);
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error completing payment: $e');
      return false;
    }
  }
}
