import 'package:flutter/material.dart';
import 'package:flutter_paystack/flutter_paystack.dart';
import 'package:fit_4_force/shared/extensions/context_extensions.dart';
import 'package:fit_4_force/shared/patches/paystack_pin_widget_patch.dart';

/// A wrapper for the Flutter Paystack package
/// 
/// This wrapper provides custom implementations of the Flutter Paystack widgets
/// to fix compatibility issues with the latest Flutter version
class PaystackWrapper {
  static final PaystackPlugin _plugin = PaystackPlugin();
  static bool _isInitialized = false;
  
  /// Initialize the Paystack plugin
  static Future<void> initialize({required String publicKey}) async {
    if (!_isInitialized) {
      await _plugin.initialize(publicKey: publicKey);
      _isInitialized = true;
      debugPrint('✅ Paystack initialized with public key: $publicKey');
    }
  }
  
  /// Charge a card
  static Future<Map<String, dynamic>> chargeCard({
    required BuildContext context,
    required int amount,
    required String email,
    String? reference,
  }) async {
    try {
      // Create a charge
      Charge charge = Charge()
        ..amount = amount
        ..email = email
        ..reference = reference ?? 'fit4force_${DateTime.now().millisecondsSinceEpoch}'
        ..currency = 'NGN';
      
      // Process the payment
      final CheckoutResponse response = await _plugin.checkout(
        context,
        method: CheckoutMethod.card,
        charge: charge,
        fullscreen: false,
        logo: Image.asset(
          'assets/images/fit4force_logo.png',
          width: 60,
          height: 60,
        ),
      );
      
      if (response.status) {
        return {
          'status': 'success',
          'reference': response.reference,
          'message': 'Payment successful',
        };
      } else {
        return {
          'status': 'failed',
          'message': 'Payment failed or was cancelled',
        };
      }
    } catch (e) {
      debugPrint('❌ Paystack error: $e');
      return {
        'status': 'error',
        'message': 'An error occurred: $e',
      };
    }
  }
  
  /// Show the PIN dialog - this is for custom PIN implementation if needed
  static Future<String?> showPinDialog(BuildContext context) async {
    return await showDialog<String>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => CustomPinWidget(
        onPinComplete: (pin) {
          Navigator.of(context).pop(pin);
          return true;
        },
      ),
    );
      ),
    );
    
    // Return a success response
    return {
      'status': 'success',
      'reference': reference ?? 'mock_reference_${DateTime.now().millisecondsSinceEpoch}',
      'message': 'Payment successful',
    };
  }
  
  /// Show the PIN dialog
  static Future<void> showPinDialog(BuildContext context) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Enter PIN',
                style: context.textTheme().titleLarge,
              ),
              const SizedBox(height: 20.0),
              // Use our custom PinWidget with the required count parameter
              const PinWidget(count: 0),
              const SizedBox(height: 20.0),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
