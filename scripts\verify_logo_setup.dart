import 'dart:io';

/// <PERSON>rip<PERSON> to verify that logo files are properly set up
void main() {
  print('🎨 FIT4FORCE LOGO VERIFICATION SCRIPT');
  print('=====================================\n');

  final logoDir = Directory('assets/images');
  
  if (!logoDir.existsSync()) {
    print('❌ ERROR: assets/images directory not found!');
    return;
  }

  // Required logo files
  final requiredFiles = [
    'fit4force_shield_logo.png',
    'fit4force_icon.png',
  ];

  // Optional files
  final optionalFiles = [
    'fit4force_text_logo.png',
    'fit4force_icon_48.png',
    'fit4force_icon_72.png',
    'fit4force_icon_96.png',
    'fit4force_icon_144.png',
    'fit4force_icon_192.png',
  ];

  print('📋 CHECKING REQUIRED LOGO FILES:');
  print('--------------------------------');
  
  bool allRequiredPresent = true;
  
  for (final fileName in requiredFiles) {
    final file = File('assets/images/$fileName');
    
    if (file.existsSync()) {
      final size = file.lengthSync();
      final isPlaceholder = _isPlaceholderFile(file);
      
      if (isPlaceholder) {
        print('⚠️  $fileName - PLACEHOLDER FILE (needs replacement)');
        allRequiredPresent = false;
      } else {
        print('✅ $fileName - Present (${_formatFileSize(size)})');
      }
    } else {
      print('❌ $fileName - MISSING');
      allRequiredPresent = false;
    }
  }

  print('\n📋 CHECKING OPTIONAL LOGO FILES:');
  print('--------------------------------');
  
  for (final fileName in optionalFiles) {
    final file = File('assets/images/$fileName');
    
    if (file.existsSync()) {
      final size = file.lengthSync();
      print('✅ $fileName - Present (${_formatFileSize(size)})');
    } else {
      print('⚪ $fileName - Not present (optional)');
    }
  }

  print('\n🎯 VERIFICATION SUMMARY:');
  print('=======================');
  
  if (allRequiredPresent) {
    print('✅ ALL REQUIRED LOGOS PRESENT!');
    print('🚀 Your app is ready to build with your logos.');
    print('\n📱 Next steps:');
    print('   1. Run: flutter clean');
    print('   2. Run: flutter pub get');
    print('   3. Run: flutter build apk --release');
  } else {
    print('❌ LOGO SETUP INCOMPLETE');
    print('🔧 Action required:');
    print('   1. Replace placeholder files with your actual logos');
    print('   2. Ensure files are PNG format with transparent backgrounds');
    print('   3. Run this script again to verify');
    print('\n📖 See assets/images/README.md for detailed instructions');
  }

  print('\n🎨 LOGO USAGE IN APP:');
  print('====================');
  print('• Login Screen: fit4force_shield_logo.png (top center)');
  print('• Splash Screen: fit4force_shield_logo.png (animated)');
  print('• App Icon: fit4force_icon.png (launcher)');
  print('• Drawer Menu: fit4force_shield_logo.png (header)');
}

bool _isPlaceholderFile(File file) {
  try {
    final content = file.readAsStringSync();
    return content.contains('PLACEHOLDER') || content.contains('placeholder');
  } catch (e) {
    // If we can't read as string, it's likely a real image file
    return false;
  }
}

String _formatFileSize(int bytes) {
  if (bytes < 1024) return '${bytes}B';
  if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)}KB';
  return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
}
